/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./node_modules/.pnpm/react-tweet@3.2.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-tweet/dist/twitter-theme/tweet-container.module.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.tweet-container_root__HFruu {
  width: 100%;
  min-width: 250px;
  max-width: 550px;
  overflow: hidden;
  /* Base font styles */
  color: var(--tweet-font-color);
  font-family: var(--tweet-font-family);
  font-weight: 400;
  box-sizing: border-box;
  border: var(--tweet-border);
  border-radius: 12px;
  margin: var(--tweet-container-margin);
  background-color: var(--tweet-bg-color);
  transition-property: background-color, box-shadow;
  transition-duration: 0.2s;
}
.tweet-container_root__HFruu:hover {
  background-color: var(--tweet-bg-color-hover);
}
.tweet-container_article__Cn4dR {
  position: relative;
  box-sizing: inherit;
  padding: 0.75rem 1rem;
}

/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/.pnpm/react-tweet@3.2.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-tweet/dist/twitter-theme/theme.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.react-tweet-theme {
  --tweet-container-margin: 1.5rem 0;

  /* Header */
  --tweet-header-font-size: 0.9375rem;
  --tweet-header-line-height: 1.25rem;

  /* Text */
  --tweet-body-font-size: 1.25rem;
  --tweet-body-font-weight: 400;
  --tweet-body-line-height: 1.5rem;
  --tweet-body-margin: 0;

  /* Quoted Tweet */
  --tweet-quoted-container-margin: 0.75rem 0;
  --tweet-quoted-body-font-size: 0.938rem;
  --tweet-quoted-body-font-weight: 400;
  --tweet-quoted-body-line-height: 1.25rem;
  --tweet-quoted-body-margin: 0.25rem 0 0.75rem 0;

  /* Info */
  --tweet-info-font-size: 0.9375rem;
  --tweet-info-line-height: 1.25rem;

  /* Actions like the like, reply and copy buttons */
  --tweet-actions-font-size: 0.875rem;
  --tweet-actions-line-height: 1rem;
  --tweet-actions-font-weight: 700;
  --tweet-actions-icon-size: 1.25em;
  --tweet-actions-icon-wrapper-size: calc(
    var(--tweet-actions-icon-size) + 0.75em
  );

  /* Reply button */
  --tweet-replies-font-size: 0.875rem;
  --tweet-replies-line-height: 1rem;
  --tweet-replies-font-weight: 700;
}

:where(.react-tweet-theme) * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:is([data-theme='light'], .light) :where(.react-tweet-theme),
:where(.react-tweet-theme) {
  --tweet-skeleton-gradient: linear-gradient(
    270deg,
    #fafafa,
    #eaeaea,
    #eaeaea,
    #fafafa
  );
  --tweet-border: 1px solid rgb(207, 217, 222);
  --tweet-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Helvetica, Arial, sans-serif;
  --tweet-font-color: rgb(15, 20, 25);
  --tweet-font-color-secondary: rgb(83, 100, 113);
  --tweet-bg-color: #fff;
  --tweet-bg-color-hover: rgb(247, 249, 249);
  --tweet-quoted-bg-color-hover: rgba(0, 0, 0, 0.03);
  --tweet-color-blue-primary: rgb(29, 155, 240);
  --tweet-color-blue-primary-hover: rgb(26, 140, 216);
  --tweet-color-blue-secondary: rgb(0, 111, 214);
  --tweet-color-blue-secondary-hover: rgba(0, 111, 214, 0.1);
  --tweet-color-red-primary: rgb(249, 24, 128);
  --tweet-color-red-primary-hover: rgba(249, 24, 128, 0.1);
  --tweet-color-green-primary: rgb(0, 186, 124);
  --tweet-color-green-primary-hover: rgba(0, 186, 124, 0.1);
  --tweet-twitter-icon-color: var(--tweet-font-color);
  --tweet-verified-old-color: rgb(130, 154, 171);
  --tweet-verified-blue-color: var(--tweet-color-blue-primary);
}

:is([data-theme='dark'], .dark) :where(.react-tweet-theme) {
  --tweet-skeleton-gradient: linear-gradient(
    270deg,
    #15202b,
    rgb(30, 39, 50),
    rgb(30, 39, 50),
    rgb(21, 32, 43)
  );
  --tweet-border: 1px solid rgb(66, 83, 100);
  --tweet-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Helvetica, Arial, sans-serif;
  --tweet-font-color: rgb(247, 249, 249);
  --tweet-font-color-secondary: rgb(139, 152, 165);
  --tweet-bg-color: rgb(21, 32, 43);
  --tweet-bg-color-hover: rgb(30, 39, 50);
  --tweet-quoted-bg-color-hover: rgba(255, 255, 255, 0.03);
  --tweet-color-blue-primary: rgb(29, 155, 240);
  --tweet-color-blue-primary-hover: rgb(26, 140, 216);
  --tweet-color-blue-secondary: rgb(107, 201, 251);
  --tweet-color-blue-secondary-hover: rgba(107, 201, 251, 0.1);
  --tweet-color-red-primary: rgb(249, 24, 128);
  --tweet-color-red-primary-hover: rgba(249, 24, 128, 0.1);
  --tweet-color-green-primary: rgb(0, 186, 124);
  --tweet-color-green-primary-hover: rgba(0, 186, 124, 0.1);
  --tweet-twitter-icon-color: var(--tweet-font-color);
  --tweet-verified-old-color: rgb(130, 154, 171);
  --tweet-verified-blue-color: #fff;
}

@media (prefers-color-scheme: dark) {
  :where(.react-tweet-theme) {
    --tweet-skeleton-gradient: linear-gradient(
      270deg,
      #15202b,
      rgb(30, 39, 50),
      rgb(30, 39, 50),
      rgb(21, 32, 43)
    );
    --tweet-border: 1px solid rgb(66, 83, 100);
    --tweet-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      Helvetica, Arial, sans-serif;
    --tweet-font-color: rgb(247, 249, 249);
    --tweet-font-color-secondary: rgb(139, 152, 165);
    --tweet-bg-color: rgb(21, 32, 43);
    --tweet-bg-color-hover: rgb(30, 39, 50);
    --tweet-color-blue-primary: rgb(29, 155, 240);
    --tweet-color-blue-primary-hover: rgb(26, 140, 216);
    --tweet-color-blue-secondary: rgb(107, 201, 251);
    --tweet-color-blue-secondary-hover: rgba(107, 201, 251, 0.1);
    --tweet-color-red-primary: rgb(249, 24, 128);
    --tweet-color-red-primary-hover: rgba(249, 24, 128, 0.1);
    --tweet-color-green-primary: rgb(0, 186, 124);
    --tweet-color-green-primary-hover: rgba(0, 186, 124, 0.1);
    --tweet-twitter-icon-color: var(--tweet-font-color);
    --tweet-verified-old-color: rgb(130, 154, 171);
    --tweet-verified-blue-color: #fff;
  }
}

/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./node_modules/.pnpm/react-tweet@3.2.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-tweet/dist/twitter-theme/skeleton.module.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.skeleton_skeleton__GX01T {
  display: block;
  width: 100%;
  border-radius: 5px;
  background-image: var(--tweet-skeleton-gradient);
  background-size: 400% 100%;
  animation: skeleton_loading__Sw7EI 8s ease-in-out infinite;
}

@media (prefers-reduced-motion: reduce) {
  .skeleton_skeleton__GX01T {
    animation: none;
    background-position: 200% 0;
  }
}

@keyframes skeleton_loading__Sw7EI {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./node_modules/.pnpm/react-tweet@3.2.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.module.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.tweet-skeleton_root__bCnMI {
  pointer-events: none;
  padding-bottom: 0.25rem;
}

/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./node_modules/.pnpm/react-tweet@3.2.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-tweet/dist/twitter-theme/tweet-not-found.module.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.tweet-not-found_root__sG954 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 0.75rem;
}
.tweet-not-found_root__sG954 > h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./node_modules/.pnpm/react-tweet@3.2.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-tweet/dist/twitter-theme/tweet-header.module.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.tweet-header_header__cM3sM {
  display: flex;
  padding-bottom: 0.75rem;
  line-height: var(--tweet-header-line-height);
  font-size: var(--tweet-header-font-size);
  white-space: nowrap;
  overflow-wrap: break-word;
  overflow: hidden;
}

.tweet-header_avatar__iLL7X {
  position: relative;
  height: 48px;
  width: 48px;
}
.tweet-header_avatarOverflow__gGYpH {
  height: 100%;
  width: 100%;
  position: absolute;
  overflow: hidden;
  border-radius: 9999px;
}
.tweet-header_avatarSquare__93u9t {
  border-radius: 4px;
}
.tweet-header_avatarShadow__A4rX7 {
  height: 100%;
  width: 100%;
  transition-property: background-color;
  transition-duration: 0.2s;
  box-shadow: rgb(0 0 0 / 3%) 0px 0px 2px inset;
}
.tweet-header_avatarShadow__A4rX7:hover {
  background-color: rgba(26, 26, 26, 0.15);
}

.tweet-header_author__az1d4 {
  max-width: calc(100% - 84px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin: 0 0.5rem;
}
.tweet-header_authorLink__vdrUE {
  text-decoration: none;
  color: inherit;
  display: flex;
  align-items: center;
}
.tweet-header_authorLink__vdrUE:hover {
  text-decoration-line: underline;
}
.tweet-header_authorVerified__FNpLy {
  display: inline-flex;
}
.tweet-header_authorLinkText__fLeBw {
  font-weight: 700;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.tweet-header_authorMeta__ZWGmH {
  display: flex;
}
.tweet-header_authorFollow__mCYSP {
  display: flex;
}
.tweet-header_username__WNo_t {
  color: var(--tweet-font-color-secondary);
  text-decoration: none;
  text-overflow: ellipsis;
}
.tweet-header_follow__Z4QqQ {
  color: var(--tweet-color-blue-secondary);
  text-decoration: none;
  font-weight: 700;
}
.tweet-header_follow__Z4QqQ:hover {
  text-decoration-line: underline;
}
.tweet-header_separator__mFLfb {
  padding: 0 0.25rem;
}

.tweet-header_brand__nNYO4 {
  margin-inline-start: auto;
}

.tweet-header_twitterIcon__6Swfc {
  width: 23.75px;
  height: 23.75px;
  color: var(--tweet-twitter-icon-color);
  fill: currentColor;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./node_modules/.pnpm/react-tweet@3.2.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-tweet/dist/twitter-theme/verified-badge.module.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.verified-badge_verifiedOld__ltW5W {
  color: var(--tweet-verified-old-color);
}
.verified-badge_verifiedBlue__cpQwJ {
  color: var(--tweet-verified-blue-color);
}
.verified-badge_verifiedGovernment__K2vM5 {
  /* color: var(--tweet-verified-government-color); */
  color: rgb(130, 154, 171);
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./node_modules/.pnpm/react-tweet@3.2.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.icons_verified__9zpD6 {
  margin-left: 0.125rem;
  max-width: 20px;
  max-height: 20px;
  height: 1.25em;
  fill: currentColor;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  vertical-align: text-bottom;
}

/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./node_modules/.pnpm/react-tweet@3.2.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.tweet-in-reply-to_root__cdgXz {
  text-decoration: none;
  color: var(--tweet-font-color-secondary);
  font-size: 0.9375rem;
  line-height: 1.25rem;
  margin-bottom: 0.25rem;
  overflow-wrap: break-word;
  white-space: pre-wrap;
}
.tweet-in-reply-to_root__cdgXz:hover {
  text-decoration-thickness: 1px;
  text-decoration-line: underline;
}

/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./node_modules/.pnpm/react-tweet@3.2.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-tweet/dist/twitter-theme/tweet-link.module.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.tweet-link_root__UMq8J {
  font-weight: inherit;
  color: var(--tweet-color-blue-secondary);
  text-decoration: none;
  cursor: pointer;
}
.tweet-link_root__UMq8J:hover {
  text-decoration-thickness: 1px;
  text-decoration-line: underline;
}

/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./node_modules/.pnpm/react-tweet@3.2.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-tweet/dist/twitter-theme/tweet-body.module.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.tweet-body_root__PqOVP {
  font-size: var(--tweet-body-font-size);
  font-weight: var(--tweet-body-font-weight);
  line-height: var(--tweet-body-line-height);
  margin: var(--tweet-body-margin);
  overflow-wrap: break-word;
  white-space: pre-wrap;
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./node_modules/.pnpm/react-tweet@3.2.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.tweet-media_root__Ry9Pv {
  margin-top: 0.75rem;
  overflow: hidden;
  position: relative;
}
.tweet-media_rounded__izlat {
  border: var(--tweet-border);
  border-radius: 12px;
}
.tweet-media_mediaWrapper__pYZdd {
  display: grid;
  grid-auto-rows: 1fr;
  gap: 2px;
  height: 100%;
  width: 100%;
}
.tweet-media_grid2Columns__Os_4I {
  grid-template-columns: repeat(2, 1fr);
}
.tweet-media_grid3__n4Csp > a:first-child {
  grid-row: span 2;
}
.tweet-media_grid2x2__HL_Kw {
  grid-template-rows: repeat(2, 1fr);
}
.tweet-media_mediaContainer__tOGaX {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tweet-media_mediaLink__fT2Pb {
  text-decoration: none;
  outline-style: none;
}
.tweet-media_skeleton__9wsr9 {
  padding-bottom: 56.25%;
  width: 100%;
  display: block;
}
.tweet-media_image__59ejp {
  position: absolute;
  top: 0px;
  left: 0px;
  bottom: 0px;
  height: 100%;
  width: 100%;
  margin: 0;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}

/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./node_modules/.pnpm/react-tweet@3.2.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-tweet/dist/twitter-theme/tweet-media-video.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.tweet-media-video_anchor__txZbE {
  display: flex;
  align-items: center;
  color: white;
  padding: 0 1rem;
  border: 1px solid transparent;
  border-radius: 9999px;
  font-weight: 700;
  transition: background-color 0.2s;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  outline-style: none;
  text-decoration: none;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tweet-media-video_videoButton__WZuUh {
  position: relative;
  height: 67px;
  width: 67px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--tweet-color-blue-primary);
  transition-property: background-color;
  transition-duration: 0.2s;
  border: 4px solid #fff;
  border-radius: 9999px;
  cursor: pointer;
}
.tweet-media-video_videoButton__WZuUh:hover,
.tweet-media-video_videoButton__WZuUh:focus-visible {
  background-color: var(--tweet-color-blue-primary-hover);
}
.tweet-media-video_videoButtonIcon__fYRPj {
  margin-left: 3px;
  width: calc(50% + 4px);
  height: calc(50% + 4px);
  max-width: 100%;
  color: #fff;
  fill: currentColor;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.tweet-media-video_watchOnTwitter__IZsLp {
  position: absolute;
  top: 12px;
  right: 8px;
}
.tweet-media-video_watchOnTwitter__IZsLp > a {
  min-width: 2rem;
  min-height: 2rem;
  font-size: 0.875rem;
  line-height: 1rem;
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
  background-color: rgba(15, 20, 25, 0.75);
}
.tweet-media-video_watchOnTwitter__IZsLp > a:hover {
  background-color: rgba(39, 44, 48, 0.75);
}
.tweet-media-video_viewReplies__EgRmH {
  position: relative;
  min-height: 2rem;
  background-color: var(--tweet-color-blue-primary);
  border-color: var(--tweet-color-blue-primary);
  font-size: 0.9375rem;
  line-height: 1.25rem;
}
.tweet-media-video_viewReplies__EgRmH:hover {
  background-color: var(--tweet-color-blue-primary-hover);
}

/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./node_modules/.pnpm/react-tweet@3.2.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.tweet-info-created-at_root__WZLY0 {
  color: inherit;
  text-decoration: none;
  font-size: var(--tweet-info-font-size);
  line-height: var(--tweet-info-line-height);
}
.tweet-info-created-at_root__WZLY0:hover {
  text-decoration-thickness: 1px;
  text-decoration-line: underline;
}

/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./node_modules/.pnpm/react-tweet@3.2.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-tweet/dist/twitter-theme/tweet-info.module.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.tweet-info_info__mohZA {
  display: flex;
  align-items: center;
  color: var(--tweet-font-color-secondary);
  margin-top: 0.125rem;
  overflow-wrap: break-word;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.tweet-info_infoLink__krj6C {
  color: inherit;
  text-decoration: none;
  height: var(--tweet-actions-icon-wrapper-size);
  width: var(--tweet-actions-icon-wrapper-size);
  font: inherit;
  margin-left: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: -4px;
  border-radius: 9999px;
  transition-property: background-color;
  transition-duration: 0.2s;
}
.tweet-info_infoLink__krj6C:hover {
  background-color: var(--tweet-color-blue-secondary-hover);
}
.tweet-info_infoIcon__xlISI {
  color: inherit;
  fill: currentColor;
  height: var(--tweet-actions-icon-size);
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.tweet-info_infoLink__krj6C:hover > .tweet-info_infoIcon__xlISI {
  color: var(--tweet-color-blue-secondary);
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./node_modules/.pnpm/react-tweet@3.2.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.tweet-actions_actions___SVAx {
  display: flex;
  align-items: center;
  color: var(--tweet-font-color-secondary);
  padding-top: 0.25rem;
  margin-top: 0.25rem;
  border-top: var(--tweet-border);
  overflow-wrap: break-word;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.tweet-actions_like__QwVxc,
.tweet-actions_reply__tZAJf,
.tweet-actions_copy__lME6I {
  text-decoration: none;
  color: inherit;
  display: flex;
  align-items: center;
  margin-right: 1.25rem;
}
.tweet-actions_like__QwVxc:hover,
.tweet-actions_reply__tZAJf:hover,
.tweet-actions_copy__lME6I:hover {
  background-color: rgba(0, 0, 0, 0);
}
.tweet-actions_like__QwVxc:hover > .tweet-actions_likeIconWrapper___pmaw {
  background-color: var(--tweet-color-red-primary-hover);
}
.tweet-actions_like__QwVxc:hover > .tweet-actions_likeCount__BZHPw {
  color: var(--tweet-color-red-primary);
  text-decoration-line: underline;
}
.tweet-actions_likeIconWrapper___pmaw,
.tweet-actions_replyIconWrapper__uWuFU,
.tweet-actions_copyIconWrapper__nbuDJ {
  width: var(--tweet-actions-icon-wrapper-size);
  height: var(--tweet-actions-icon-wrapper-size);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: -0.25rem;
  border-radius: 9999px;
}
.tweet-actions_likeIcon__UIMsW,
.tweet-actions_replyIcon__zz7Gz,
.tweet-actions_copyIcon__d3xza {
  height: var(--tweet-actions-icon-size);
  fill: currentColor;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.tweet-actions_likeIcon__UIMsW {
  color: var(--tweet-color-red-primary);
}
.tweet-actions_likeCount__BZHPw,
.tweet-actions_replyText__8GESM,
.tweet-actions_copyText__ExAuR {
  font-size: var(--tweet-actions-font-size);
  font-weight: var(--tweet-actions-font-weight);
  line-height: var(--tweet-actions-line-height);
  margin-left: 0.25rem;
}

.tweet-actions_reply__tZAJf:hover > .tweet-actions_replyIconWrapper__uWuFU {
  background-color: var(--tweet-color-blue-secondary-hover);
}
.tweet-actions_reply__tZAJf:hover > .tweet-actions_replyText__8GESM {
  color: var(--tweet-color-blue-secondary);
  text-decoration-line: underline;
}
.tweet-actions_replyIcon__zz7Gz {
  color: var(--tweet-color-blue-primary);
}

.tweet-actions_copy__lME6I {
  font: inherit;
  background: none;
  border: none;
  cursor: pointer;
}
.tweet-actions_copy__lME6I:hover > .tweet-actions_copyIconWrapper__nbuDJ {
  background-color: var(--tweet-color-green-primary-hover);
}
.tweet-actions_copy__lME6I:hover .tweet-actions_copyIcon__d3xza {
  color: var(--tweet-color-green-primary);
}
.tweet-actions_copy__lME6I:hover > .tweet-actions_copyText__ExAuR {
  color: var(--tweet-color-green-primary);
  text-decoration-line: underline;
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./node_modules/.pnpm/react-tweet@3.2.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-tweet/dist/twitter-theme/tweet-replies.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.tweet-replies_replies__ZEeXL {
  padding: 0.25rem 0;
}
.tweet-replies_link__22_nz {
  text-decoration: none;
  color: var(--tweet-color-blue-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  min-height: 32px;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  outline-style: none;
  transition-property: background-color;
  transition-duration: 0.2s;
  padding: 0 1rem;
  border: var(--tweet-border);
  border-radius: 9999px;
}
.tweet-replies_link__22_nz:hover {
  background-color: var(--tweet-color-blue-secondary-hover);
}
.tweet-replies_text__iHrWm {
  font-weight: var(--tweet-replies-font-weight);
  font-size: var(--tweet-replies-font-size);
  line-height: var(--tweet-replies-line-height);
  overflow-wrap: break-word;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./node_modules/.pnpm/react-tweet@3.2.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.module.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.quoted-tweet-container_root__4AqxN {
  width: 100%;
  overflow: hidden;
  border: var(--tweet-border);
  border-radius: 12px;
  margin: var(--tweet-quoted-container-margin);
  transition-property: background-color, box-shadow;
  transition-duration: 0.2s;
  cursor: pointer;
}

.quoted-tweet-container_root__4AqxN:hover {
  background-color: var(--tweet-quoted-bg-color-hover);
}

.quoted-tweet-container_article__gIu7F {
  position: relative;
  box-sizing: inherit;
}

/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./node_modules/.pnpm/react-tweet@3.2.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.module.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.quoted-tweet-header_header__sYqtO {
  display: flex;
  padding: 0.75rem 0.75rem 0 0.75rem;
  line-height: var(--tweet-header-line-height);
  font-size: var(--tweet-header-font-size);
  white-space: nowrap;
  overflow-wrap: break-word;
  overflow: hidden;
}

.quoted-tweet-header_avatar__oLOkr {
  position: relative;
  height: 20px;
  width: 20px;
}

.quoted-tweet-header_avatarSquare__OW1d_ {
  border-radius: 4px;
}

.quoted-tweet-header_author__tBmK0 {
  display: flex;
  margin: 0 0.5rem;
}

.quoted-tweet-header_authorText__4lPwx {
  font-weight: 700;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.quoted-tweet-header_username__YHgYj {
  color: var(--tweet-font-color-secondary);
  text-decoration: none;
  text-overflow: ellipsis;
  margin-left: 0.125rem;
}

/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./node_modules/.pnpm/react-tweet@3.2.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.module.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.quoted-tweet-body_root__b_MLK {
  font-size: var(--tweet-quoted-body-font-size);
  font-weight: var(--tweet-quoted-body-font-weight);
  line-height: var(--tweet-quoted-body-line-height);
  margin: var(--tweet-quoted-body-margin);
  overflow-wrap: break-word;
  white-space: pre-wrap;
  padding: 0 0.75rem;
}

/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/components/ai-editor/ai-editor.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* AI Editor 样式 */

/* ProseMirror 基础样式 */
.ProseMirror {
  line-height: 1.75;
  outline: none;
}

.ProseMirror .is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

.ProseMirror:not(.dragging) .ProseMirror-selectednode {
  outline: none !important;
  background-color: rgba(59, 130, 246, 0.1);
  transition: background-color 0.2s;
  box-shadow: none;
}

/* 编辑器基础样式 */
.ai-editor-container {
  position: relative;
  width: 100%;
  min-height: 400px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.dark .ai-editor-container {
  border-color: #374151;
  background: #1f2937;
}

/* 任务列表样式 */
.ai-editor-task-list {
  list-style: none;
  padding-left: 8px;
}

.ai-editor-task-item {
  display: flex;
  gap: 8px;
  align-items: flex-start;
  margin: 16px 0;
}

/* 图片样式 */
.ai-editor-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.ai-editor-image-uploading {
  opacity: 0.4;
  border: 1px solid #d1d5db;
}

/* 列表样式 */
.ai-editor-bullet-list {
  list-style-type: disc;
  padding-left: 24px;
}

.ai-editor-ordered-list {
  list-style-type: decimal;
  padding-left: 24px;
  margin-top: -8px;
}

.ai-editor-blockquote {
  border-left: 4px solid #3b82f6;
  padding-left: 16px;
  margin: 16px 0;
  font-style: italic;
}

.ai-editor-code {
  background: #f3f4f6;
  padding: 2px 4px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}

.ai-editor-heading {
  font-weight: 600;
  margin-top: 24px;
  margin-bottom: 16px;
}

/* 链接样式 */
.ai-editor-link {
  color: #3b82f6;
  text-decoration: underline;
  text-underline-offset: 3px;
  transition: color 0.15s;
}

.ai-editor-link:hover {
  color: #1d4ed8;
}

/* 工具栏样式 */
.ai-toolbar-bubble {
  z-index: 50;
}

/* 拖拽手柄样式 */
.drag-handle {
  position: absolute;
  left: -2rem;
  top: 0;
  width: 1.25rem;
  height: 1.25rem;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 0.25rem;
  cursor: grab;
  opacity: 0;
  transition: opacity 0.2s;
}

.drag-handle:hover {
  background: #e5e7eb;
}

.ProseMirror-focused .drag-handle {
  opacity: 1;
}

/* 深色模式支持 */
.dark .ai-editor-blockquote {
  border-left-color: #374151;
  color: #9ca3af;
}

.dark .ai-editor-code {
  background-color: #374151;
  color: #f9fafb;
}

.dark .ai-editor-image {
  border-color: #374151;
}

.dark .ai-editor-image-uploading {
  border-color: #374151;
}

.dark .drag-handle {
  background: #374151;
  border-color: #4b5563;
}

.dark .drag-handle:hover {
  background: #4b5563;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-editor-container .ProseMirror {
    padding: 0.75rem;
  }
}

/* 动画效果 */
.ai-editor-container * {
  transition: all 0.2s ease-in-out;
}

/* 滚动条样式 */
.ai-editor-container .ProseMirror::-webkit-scrollbar {
  width: 8px;
}

.ai-editor-container .ProseMirror::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.ai-editor-container .ProseMirror::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.ai-editor-container .ProseMirror::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

