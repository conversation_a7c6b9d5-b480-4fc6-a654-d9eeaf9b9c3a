"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ai-editor/image-upload.ts":
/*!**************************************************!*\
  !*** ./src/components/ai-editor/image-upload.ts ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   uploadFn: () => (/* binding */ uploadFn)\n/* harmony export */ });\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/novel@1.0.2_@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tipta_3fbtezt2z6la73kpow3ollimny/node_modules/novel/dist/index.js\");\n\n// 模拟图片上传函数\nconst onUpload = (file)=>{\n    return new Promise((resolve, reject)=>{\n        try {\n            // 创建本地 URL 用于预览\n            const url = URL.createObjectURL(file);\n            // 模拟上传延迟\n            setTimeout(()=>{\n                resolve(url);\n            }, 1000);\n        } catch (error) {\n            reject(new Error(\"图片上传失败，请重试\"));\n        }\n    });\n};\n// 创建图片上传配置\nconst uploadFn = (0,novel__WEBPACK_IMPORTED_MODULE_0__.createImageUpload)({\n    onUpload,\n    validateFn: (file)=>{\n        // 检查文件是否存在\n        if (!file) {\n            console.error(\"未选择文件\");\n            return false;\n        }\n        // 验证文件类型\n        if (!file.type || !file.type.includes(\"image/\")) {\n            console.error(\"文件类型不支持，请选择图片文件\");\n            return false;\n        }\n        // 验证文件大小 (最大 20MB)\n        if (file.size / 1024 / 1024 > 20) {\n            console.error(\"文件大小超过限制，最大支持 20MB\");\n            return false;\n        }\n        return true;\n    }\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ai-editor/image-upload.ts\n"));

/***/ })

});