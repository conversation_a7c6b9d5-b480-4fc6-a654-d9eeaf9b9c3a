"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ai_editor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ai-editor */ \"(app-pages-browser)/./src/components/ai-editor/index.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction Home() {\n    _s();\n    const [markdown, setMarkdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"container mx-auto p-8 max-w-6xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold mb-4\",\n                        children: \"AI编辑器演示\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 dark:text-gray-400\",\n                        children: \"基于 Novel + TipTap + ProseMirror 技术栈的独立AI编辑器组件\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-[600px] border rounded-lg overflow-hidden bg-white dark:bg-gray-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_editor__WEBPACK_IMPORTED_MODULE_2__.AIEditor, {\n                    initialContent: {\n                        type: \"doc\",\n                        content: [\n                            {\n                                type: \"heading\",\n                                attrs: {\n                                    level: 1\n                                },\n                                content: [\n                                    {\n                                        type: \"text\",\n                                        text: \"🎉 欢迎使用 AI Editor\"\n                                    }\n                                ]\n                            },\n                            {\n                                type: \"paragraph\",\n                                content: [\n                                    {\n                                        type: \"text\",\n                                        text: \"这是一个功能完整的 AI 编辑器，基于 \"\n                                    },\n                                    {\n                                        type: \"text\",\n                                        text: \"Novel + TipTap + ProseMirror\",\n                                        marks: [\n                                            {\n                                                type: \"bold\"\n                                            }\n                                        ]\n                                    },\n                                    {\n                                        type: \"text\",\n                                        text: \" 技术栈构建。\"\n                                    }\n                                ]\n                            },\n                            {\n                                type: \"heading\",\n                                attrs: {\n                                    level: 2\n                                },\n                                content: [\n                                    {\n                                        type: \"text\",\n                                        text: \"🚀 快速开始\"\n                                    }\n                                ]\n                            },\n                            {\n                                type: \"bulletList\",\n                                content: [\n                                    {\n                                        type: \"listItem\",\n                                        content: [\n                                            {\n                                                type: \"paragraph\",\n                                                content: [\n                                                    {\n                                                        type: \"text\",\n                                                        text: \"选中这段文字，然后点击工具栏中的 \"\n                                                    },\n                                                    {\n                                                        type: \"text\",\n                                                        text: \"AI\",\n                                                        marks: [\n                                                            {\n                                                                type: \"bold\"\n                                                            }\n                                                        ]\n                                                    },\n                                                    {\n                                                        type: \"text\",\n                                                        text: \" 按钮体验 AI 功能\"\n                                                    }\n                                                ]\n                                            }\n                                        ]\n                                    },\n                                    {\n                                        type: \"listItem\",\n                                        content: [\n                                            {\n                                                type: \"paragraph\",\n                                                content: [\n                                                    {\n                                                        type: \"text\",\n                                                        text: \"按 \"\n                                                    },\n                                                    {\n                                                        type: \"text\",\n                                                        text: \"Ctrl+K\",\n                                                        marks: [\n                                                            {\n                                                                type: \"code\"\n                                                            }\n                                                        ]\n                                                    },\n                                                    {\n                                                        type: \"text\",\n                                                        text: \" 快捷键打开 AI 助手\"\n                                                    }\n                                                ]\n                                            }\n                                        ]\n                                    },\n                                    {\n                                        type: \"listItem\",\n                                        content: [\n                                            {\n                                                type: \"paragraph\",\n                                                content: [\n                                                    {\n                                                        type: \"text\",\n                                                        text: \"输入 \"\n                                                    },\n                                                    {\n                                                        type: \"text\",\n                                                        text: \"/\",\n                                                        marks: [\n                                                            {\n                                                                type: \"code\"\n                                                            }\n                                                        ]\n                                                    },\n                                                    {\n                                                        type: \"text\",\n                                                        text: \" 打开 Slash 命令菜单\"\n                                                    }\n                                                ]\n                                            }\n                                        ]\n                                    }\n                                ]\n                            },\n                            {\n                                type: \"heading\",\n                                attrs: {\n                                    level: 2\n                                },\n                                content: [\n                                    {\n                                        type: \"text\",\n                                        text: \"✨ 试试这些功能\"\n                                    }\n                                ]\n                            },\n                            {\n                                type: \"taskList\",\n                                content: [\n                                    {\n                                        type: \"taskItem\",\n                                        attrs: {\n                                            checked: false\n                                        },\n                                        content: [\n                                            {\n                                                type: \"paragraph\",\n                                                content: [\n                                                    {\n                                                        type: \"text\",\n                                                        text: \"拖拽图片到编辑器中\"\n                                                    }\n                                                ]\n                                            }\n                                        ]\n                                    },\n                                    {\n                                        type: \"taskItem\",\n                                        attrs: {\n                                            checked: false\n                                        },\n                                        content: [\n                                            {\n                                                type: \"paragraph\",\n                                                content: [\n                                                    {\n                                                        type: \"text\",\n                                                        text: \"使用 AI 功能改进文字\"\n                                                    }\n                                                ]\n                                            }\n                                        ]\n                                    },\n                                    {\n                                        type: \"taskItem\",\n                                        attrs: {\n                                            checked: true\n                                        },\n                                        content: [\n                                            {\n                                                type: \"paragraph\",\n                                                content: [\n                                                    {\n                                                        type: \"text\",\n                                                        text: \"体验 Slash 命令\"\n                                                    }\n                                                ]\n                                            }\n                                        ]\n                                    }\n                                ]\n                            }\n                        ]\n                    },\n                    placeholder: \"开始写作，体验AI功能...\",\n                    onMarkdownChange: setMarkdown,\n                    showToolbar: true,\n                    defaultMode: \"edit\",\n                    className: \"h-full\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-12 bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg border border-blue-200 dark:border-blue-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4 text-blue-800 dark:text-blue-200\",\n                        children: \"\\uD83D\\uDCA1 快速上手\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-600 dark:text-blue-400\",\n                                        children: \"1️⃣\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"选中文字\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"：选中任意文字，工具栏会自动出现，点击 AI 按钮体验智能功能\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-600 dark:text-blue-400\",\n                                        children: \"2️⃣\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"快捷键\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"：按 \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                className: \"bg-blue-100 dark:bg-blue-800 px-1 rounded\",\n                                                children: \"Ctrl+K\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 38\n                                            }, this),\n                                            \" 打开 AI 助手，输入 \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                className: \"bg-blue-100 dark:bg-blue-800 px-1 rounded\",\n                                                children: \"/\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 124\n                                            }, this),\n                                            \" 打开命令菜单\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-600 dark:text-blue-400\",\n                                        children: \"3️⃣\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"AI 功能\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"：改进文字、扩展内容、总结要点、修正语法等智能写作助手功能\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold\",\n                        children: \"功能特性\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 p-6 rounded-lg border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"\\uD83D\\uDCDD 富文本编辑\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: \"支持标题、列表、引用、代码块等丰富的文本格式\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 p-6 rounded-lg border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"⚡ Slash 命令\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: '输入 \"/\" 快速插入不同类型的内容块'\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 p-6 rounded-lg border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"\\uD83D\\uDDBC️ 图片支持\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: \"支持拖拽和粘贴图片上传\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 p-6 rounded-lg border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"✅ 任务列表\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: \"创建可交互的待办事项列表\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 p-6 rounded-lg border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"\\uD83D\\uDCC4 Markdown 导出\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: \"实时转换为标准 Markdown 格式\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 p-6 rounded-lg border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"\\uD83E\\uDD16 AI 智能助手\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: \"选中文字使用AI工具栏，按Ctrl+K打开AI助手\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 p-6 rounded-lg border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"\\uD83C\\uDFA8 多视图模式\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: \"支持编辑、预览和分割视图模式切换\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"j588dLFwM1JC5gTneX/Rtk4bX2U=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});