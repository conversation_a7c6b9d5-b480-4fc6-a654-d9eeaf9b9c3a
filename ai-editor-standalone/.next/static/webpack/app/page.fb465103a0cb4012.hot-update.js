"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ai-editor/extensions.ts":
/*!************************************************!*\
  !*** ./src/components/ai-editor/extensions.ts ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiEditorExtensions: () => (/* binding */ aiEditorExtensions),\n/* harmony export */   uploadFn: () => (/* reexport safe */ _image_upload__WEBPACK_IMPORTED_MODULE_12__.uploadFn)\n/* harmony export */ });\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+starter-kit@2.26.1/node_modules/@tiptap/starter-kit/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/novel@1.0.2_@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tipta_3fbtezt2z6la73kpow3ollimny/node_modules/novel/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-link@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-link/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-task-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-task-list/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-task-item@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-task-item/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-image@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-image/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/tiptap-extension-global-drag-handle@0.1.18/node_modules/tiptap-extension-global-drag-handle/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-underline@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-underline/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-text-style@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-text-style/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-color@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+extension-text-_ochc23jjcl3wq5k6zoczrgb3ku/node_modules/@tiptap/extension-color/dist/index.js\");\n/* harmony import */ var tiptap_markdown__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! tiptap-markdown */ \"(app-pages-browser)/./node_modules/.pnpm/tiptap-markdown@0.8.10_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/tiptap-markdown/dist/tiptap-markdown.es.js\");\n/* harmony import */ var _tiptap_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/react */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _image_upload__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./image-upload */ \"(app-pages-browser)/./src/components/ai-editor/image-upload.ts\");\n\n\n\n// 自定义 AI 命令扩展\nconst AICommandExtension = _tiptap_react__WEBPACK_IMPORTED_MODULE_0__.Extension.create({\n    name: \"aiCommand\",\n    addKeyboardShortcuts () {\n        return {\n            // Ctrl/Cmd + K 触发 AI 助手\n            \"Mod-k\": ()=>{\n                // 触发 AI 助手的逻辑将在组件中处理\n                const event = new CustomEvent(\"ai-assistant-trigger\");\n                document.dispatchEvent(event);\n                return true;\n            },\n            // Ctrl/Cmd + Shift + A 快速 AI 生成\n            \"Mod-Shift-a\": ()=>{\n                const event = new CustomEvent(\"ai-quick-generate\");\n                document.dispatchEvent(event);\n                return true;\n            }\n        };\n    },\n    addCommands () {\n        return {\n            // 添加 AI 高亮命令\n            setAIHighlight: ()=>(param)=>{\n                    let { commands } = param;\n                    return commands.setHighlight({\n                        color: \"#3b82f6\"\n                    });\n                },\n            // 移除 AI 高亮命令\n            unsetAIHighlight: ()=>(param)=>{\n                    let { commands } = param;\n                    return commands.unsetHighlight();\n                }\n        };\n    }\n});\n// 基础扩展配置\nconst starterKit = novel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].configure({\n    bulletList: {\n        HTMLAttributes: {\n            class: \"ai-editor-bullet-list\"\n        }\n    },\n    orderedList: {\n        HTMLAttributes: {\n            class: \"ai-editor-ordered-list\"\n        }\n    },\n    blockquote: {\n        HTMLAttributes: {\n            class: \"ai-editor-blockquote\"\n        }\n    },\n    code: {\n        HTMLAttributes: {\n            class: \"ai-editor-code\"\n        }\n    },\n    heading: {\n        HTMLAttributes: {\n            class: \"ai-editor-heading\"\n        }\n    }\n});\n// 占位符配置\nconst placeholder = novel__WEBPACK_IMPORTED_MODULE_2__.Placeholder.configure({\n    placeholder: (param)=>{\n        let { node } = param;\n        if (node.type.name === \"heading\") {\n            return \"标题\";\n        }\n        return \"开始写作...\";\n    }\n});\n// 链接配置\nconst link = novel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].configure({\n    HTMLAttributes: {\n        class: \"ai-editor-link\"\n    }\n});\n// 高亮配置\nconst highlight = novel__WEBPACK_IMPORTED_MODULE_2__.HighlightExtension.configure({\n    multicolor: true\n});\n// 任务列表配置\nconst taskList = novel__WEBPACK_IMPORTED_MODULE_4__.TaskList.configure({\n    HTMLAttributes: {\n        class: \"ai-editor-task-list\"\n    }\n});\nconst taskItem = novel__WEBPACK_IMPORTED_MODULE_5__.TaskItem.configure({\n    HTMLAttributes: {\n        class: \"ai-editor-task-item\"\n    }\n});\n// 图片配置\nconst tiptapImage = novel__WEBPACK_IMPORTED_MODULE_6__[\"default\"].extend({\n    addProseMirrorPlugins () {\n        return [\n            (0,novel__WEBPACK_IMPORTED_MODULE_2__.UploadImagesPlugin)({\n                imageClass: \"ai-editor-image-uploading opacity-40 rounded-lg border border-gray-200\"\n            })\n        ];\n    }\n}).configure({\n    allowBase64: true,\n    HTMLAttributes: {\n        class: \"ai-editor-image rounded-lg border border-gray-200 max-w-full h-auto\"\n    }\n});\n// 拖拽手柄配置\nconst globalDragHandle = novel__WEBPACK_IMPORTED_MODULE_7__[\"default\"].configure({\n    dragHandleWidth: 20\n});\n// 配置 Markdown 支持\nconst markdown = tiptap_markdown__WEBPACK_IMPORTED_MODULE_8__.Markdown.configure({\n    html: true,\n    tightLists: true,\n    bulletListMarker: \"-\",\n    linkify: false,\n    breaks: false\n});\n// 导出所有扩展\nconst aiEditorExtensions = [\n    starterKit,\n    placeholder,\n    link,\n    novel__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    highlight,\n    novel__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    novel__WEBPACK_IMPORTED_MODULE_11__.Color,\n    markdown,\n    taskList,\n    taskItem,\n    tiptapImage,\n    globalDragHandle\n];\n// 导出图片上传函数\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2FpLWVkaXRvci9leHRlbnNpb25zLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQWFjO0FBQzRCO0FBQ0Q7QUFJekMsY0FBYztBQUNkLE1BQU1jLHFCQUFxQkQsb0RBQVNBLENBQUNFLE1BQU0sQ0FBQztJQUMxQ0MsTUFBTTtJQUVOQztRQUNFLE9BQU87WUFDTCx3QkFBd0I7WUFDeEIsU0FBUztnQkFDUCxxQkFBcUI7Z0JBQ3JCLE1BQU1DLFFBQVEsSUFBSUMsWUFBWTtnQkFDOUJDLFNBQVNDLGFBQWEsQ0FBQ0g7Z0JBQ3ZCLE9BQU87WUFDVDtZQUNBLGdDQUFnQztZQUNoQyxlQUFlO2dCQUNiLE1BQU1BLFFBQVEsSUFBSUMsWUFBWTtnQkFDOUJDLFNBQVNDLGFBQWEsQ0FBQ0g7Z0JBQ3ZCLE9BQU87WUFDVDtRQUNGO0lBQ0Y7SUFFQUk7UUFDRSxPQUFPO1lBQ0wsYUFBYTtZQUNiQyxnQkFDRSxJQUNBO3dCQUFDLEVBQUVDLFFBQVEsRUFBRTtvQkFDWCxPQUFPQSxTQUFTQyxZQUFZLENBQUM7d0JBQUVDLE9BQU87b0JBQVU7Z0JBQ2xEO1lBQ0YsYUFBYTtZQUNiQyxrQkFDRSxJQUNBO3dCQUFDLEVBQUVILFFBQVEsRUFBRTtvQkFDWCxPQUFPQSxTQUFTSSxjQUFjO2dCQUNoQztRQUNKO0lBQ0Y7QUFDRjtBQUVBLFNBQVM7QUFDVCxNQUFNQyxhQUFhN0IsNkNBQVVBLENBQUM4QixTQUFTLENBQUM7SUFDdENDLFlBQVk7UUFDVkMsZ0JBQWdCO1lBQ2RDLE9BQU87UUFDVDtJQUNGO0lBQ0FDLGFBQWE7UUFDWEYsZ0JBQWdCO1lBQ2RDLE9BQU87UUFDVDtJQUNGO0lBQ0FFLFlBQVk7UUFDVkgsZ0JBQWdCO1lBQ2RDLE9BQU87UUFDVDtJQUNGO0lBQ0FHLE1BQU07UUFDSkosZ0JBQWdCO1lBQ2RDLE9BQU87UUFDVDtJQUNGO0lBQ0FJLFNBQVM7UUFDUEwsZ0JBQWdCO1lBQ2RDLE9BQU87UUFDVDtJQUNGO0FBQ0Y7QUFFQSxRQUFRO0FBQ1IsTUFBTUssY0FBY3JDLDhDQUFXQSxDQUFDNkIsU0FBUyxDQUFDO0lBQ3hDUSxhQUFhO1lBQUMsRUFBRUMsSUFBSSxFQUFFO1FBQ3BCLElBQUlBLEtBQUtDLElBQUksQ0FBQ3hCLElBQUksS0FBSyxXQUFXO1lBQ2hDLE9BQU87UUFDVDtRQUNBLE9BQU87SUFDVDtBQUNGO0FBRUEsT0FBTztBQUNQLE1BQU15QixPQUFPdkMsNkNBQVVBLENBQUM0QixTQUFTLENBQUM7SUFDaENFLGdCQUFnQjtRQUNkQyxPQUFPO0lBQ1Q7QUFDRjtBQUVBLE9BQU87QUFDUCxNQUFNUyxZQUFZdEMscURBQWtCQSxDQUFDMEIsU0FBUyxDQUFDO0lBQzdDYSxZQUFZO0FBQ2Q7QUFFQSxTQUFTO0FBQ1QsTUFBTUMsV0FBV2xDLDJDQUFRQSxDQUFDb0IsU0FBUyxDQUFDO0lBQ2xDRSxnQkFBZ0I7UUFDZEMsT0FBTztJQUNUO0FBQ0Y7QUFFQSxNQUFNWSxXQUFXbEMsMkNBQVFBLENBQUNtQixTQUFTLENBQUM7SUFDbENFLGdCQUFnQjtRQUNkQyxPQUFPO0lBQ1Q7QUFDRjtBQUVBLE9BQU87QUFDUCxNQUFNYSxjQUFjdEMsNkNBQVdBLENBQUN1QyxNQUFNLENBQUM7SUFDckNDO1FBQ0UsT0FBTztZQUNMdkMseURBQWtCQSxDQUFDO2dCQUNqQndDLFlBQVk7WUFDZDtTQUNEO0lBQ0g7QUFDRixHQUFHbkIsU0FBUyxDQUFDO0lBQ1hvQixhQUFhO0lBQ2JsQixnQkFBZ0I7UUFDZEMsT0FBTztJQUNUO0FBQ0Y7QUFFQSxTQUFTO0FBQ1QsTUFBTWtCLG1CQUFtQjVDLDZDQUFnQkEsQ0FBQ3VCLFNBQVMsQ0FBQztJQUNsRHNCLGlCQUFpQjtBQUNuQjtBQUVBLGlCQUFpQjtBQUNqQixNQUFNQyxXQUFXekMscURBQVFBLENBQUNrQixTQUFTLENBQUM7SUFDbEN3QixNQUFNO0lBQ05DLFlBQVk7SUFDWkMsa0JBQWtCO0lBQ2xCQyxTQUFTO0lBQ1RDLFFBQVE7QUFDVjtBQUVBLFNBQVM7QUFDRixNQUFNQyxxQkFBcUI7SUFDaEM5QjtJQUNBUztJQUNBRztJQUNBdEMsNkNBQWVBO0lBQ2Z1QztJQUNBckMsOENBQVNBO0lBQ1RDLHlDQUFLQTtJQUNMK0M7SUFDQVQ7SUFDQUM7SUFDQUM7SUFDQUs7Q0FDRDtBQUVELFdBQVc7QUFDOEIiLCJzb3VyY2VzIjpbIi9ob21lL211emkvZ2l0aHViL2RlZXItZmxvdy9haS1lZGl0b3Itc3RhbmRhbG9uZS9zcmMvY29tcG9uZW50cy9haS1lZGl0b3IvZXh0ZW5zaW9ucy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICBTdGFydGVyS2l0LFxuICBQbGFjZWhvbGRlcixcbiAgVGlwdGFwTGluayxcbiAgVGlwdGFwVW5kZXJsaW5lLFxuICBIaWdobGlnaHRFeHRlbnNpb24sXG4gIFRleHRTdHlsZSxcbiAgQ29sb3IsXG4gIEdsb2JhbERyYWdIYW5kbGUsXG4gIFRpcHRhcEltYWdlLFxuICBVcGxvYWRJbWFnZXNQbHVnaW4sXG4gIFRhc2tMaXN0LFxuICBUYXNrSXRlbSxcbn0gZnJvbSBcIm5vdmVsXCJcbmltcG9ydCB7IE1hcmtkb3duIH0gZnJvbSBcInRpcHRhcC1tYXJrZG93blwiXG5pbXBvcnQgeyBFeHRlbnNpb24gfSBmcm9tIFwiQHRpcHRhcC9yZWFjdFwiXG5pbXBvcnQgeyBzbGFzaENvbW1hbmQgfSBmcm9tIFwiLi9zbGFzaC1jb21tYW5kXCJcbmltcG9ydCB7IHVwbG9hZEZuIH0gZnJvbSBcIi4vaW1hZ2UtdXBsb2FkXCJcblxuLy8g6Ieq5a6a5LmJIEFJIOWRveS7pOaJqeWxlVxuY29uc3QgQUlDb21tYW5kRXh0ZW5zaW9uID0gRXh0ZW5zaW9uLmNyZWF0ZSh7XG4gIG5hbWU6IFwiYWlDb21tYW5kXCIsXG5cbiAgYWRkS2V5Ym9hcmRTaG9ydGN1dHMoKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIC8vIEN0cmwvQ21kICsgSyDop6blj5EgQUkg5Yqp5omLXG4gICAgICBcIk1vZC1rXCI6ICgpID0+IHtcbiAgICAgICAgLy8g6Kem5Y+RIEFJIOWKqeaJi+eahOmAu+i+keWwhuWcqOe7hOS7tuS4reWkhOeQhlxuICAgICAgICBjb25zdCBldmVudCA9IG5ldyBDdXN0b21FdmVudChcImFpLWFzc2lzdGFudC10cmlnZ2VyXCIpXG4gICAgICAgIGRvY3VtZW50LmRpc3BhdGNoRXZlbnQoZXZlbnQpXG4gICAgICAgIHJldHVybiB0cnVlXG4gICAgICB9LFxuICAgICAgLy8gQ3RybC9DbWQgKyBTaGlmdCArIEEg5b+r6YCfIEFJIOeUn+aIkFxuICAgICAgXCJNb2QtU2hpZnQtYVwiOiAoKSA9PiB7XG4gICAgICAgIGNvbnN0IGV2ZW50ID0gbmV3IEN1c3RvbUV2ZW50KFwiYWktcXVpY2stZ2VuZXJhdGVcIilcbiAgICAgICAgZG9jdW1lbnQuZGlzcGF0Y2hFdmVudChldmVudClcbiAgICAgICAgcmV0dXJuIHRydWVcbiAgICAgIH0sXG4gICAgfVxuICB9LFxuXG4gIGFkZENvbW1hbmRzKCkge1xuICAgIHJldHVybiB7XG4gICAgICAvLyDmt7vliqAgQUkg6auY5Lqu5ZG95LukXG4gICAgICBzZXRBSUhpZ2hsaWdodDpcbiAgICAgICAgKCkgPT5cbiAgICAgICAgKHsgY29tbWFuZHMgfSkgPT4ge1xuICAgICAgICAgIHJldHVybiBjb21tYW5kcy5zZXRIaWdobGlnaHQoeyBjb2xvcjogXCIjM2I4MmY2XCIgfSlcbiAgICAgICAgfSxcbiAgICAgIC8vIOenu+mZpCBBSSDpq5jkuq7lkb3ku6RcbiAgICAgIHVuc2V0QUlIaWdobGlnaHQ6XG4gICAgICAgICgpID0+XG4gICAgICAgICh7IGNvbW1hbmRzIH0pID0+IHtcbiAgICAgICAgICByZXR1cm4gY29tbWFuZHMudW5zZXRIaWdobGlnaHQoKVxuICAgICAgICB9LFxuICAgIH1cbiAgfSxcbn0pXG5cbi8vIOWfuuehgOaJqeWxlemFjee9rlxuY29uc3Qgc3RhcnRlcktpdCA9IFN0YXJ0ZXJLaXQuY29uZmlndXJlKHtcbiAgYnVsbGV0TGlzdDoge1xuICAgIEhUTUxBdHRyaWJ1dGVzOiB7XG4gICAgICBjbGFzczogXCJhaS1lZGl0b3ItYnVsbGV0LWxpc3RcIixcbiAgICB9LFxuICB9LFxuICBvcmRlcmVkTGlzdDoge1xuICAgIEhUTUxBdHRyaWJ1dGVzOiB7XG4gICAgICBjbGFzczogXCJhaS1lZGl0b3Itb3JkZXJlZC1saXN0XCIsXG4gICAgfSxcbiAgfSxcbiAgYmxvY2txdW90ZToge1xuICAgIEhUTUxBdHRyaWJ1dGVzOiB7XG4gICAgICBjbGFzczogXCJhaS1lZGl0b3ItYmxvY2txdW90ZVwiLFxuICAgIH0sXG4gIH0sXG4gIGNvZGU6IHtcbiAgICBIVE1MQXR0cmlidXRlczoge1xuICAgICAgY2xhc3M6IFwiYWktZWRpdG9yLWNvZGVcIixcbiAgICB9LFxuICB9LFxuICBoZWFkaW5nOiB7XG4gICAgSFRNTEF0dHJpYnV0ZXM6IHtcbiAgICAgIGNsYXNzOiBcImFpLWVkaXRvci1oZWFkaW5nXCIsXG4gICAgfSxcbiAgfSxcbn0pXG5cbi8vIOWNoOS9jeespumFjee9rlxuY29uc3QgcGxhY2Vob2xkZXIgPSBQbGFjZWhvbGRlci5jb25maWd1cmUoe1xuICBwbGFjZWhvbGRlcjogKHsgbm9kZSB9KSA9PiB7XG4gICAgaWYgKG5vZGUudHlwZS5uYW1lID09PSBcImhlYWRpbmdcIikge1xuICAgICAgcmV0dXJuIFwi5qCH6aKYXCJcbiAgICB9XG4gICAgcmV0dXJuIFwi5byA5aeL5YaZ5L2cLi4uXCJcbiAgfSxcbn0pXG5cbi8vIOmTvuaOpemFjee9rlxuY29uc3QgbGluayA9IFRpcHRhcExpbmsuY29uZmlndXJlKHtcbiAgSFRNTEF0dHJpYnV0ZXM6IHtcbiAgICBjbGFzczogXCJhaS1lZGl0b3ItbGlua1wiLFxuICB9LFxufSlcblxuLy8g6auY5Lqu6YWN572uXG5jb25zdCBoaWdobGlnaHQgPSBIaWdobGlnaHRFeHRlbnNpb24uY29uZmlndXJlKHtcbiAgbXVsdGljb2xvcjogdHJ1ZSxcbn0pXG5cbi8vIOS7u+WKoeWIl+ihqOmFjee9rlxuY29uc3QgdGFza0xpc3QgPSBUYXNrTGlzdC5jb25maWd1cmUoe1xuICBIVE1MQXR0cmlidXRlczoge1xuICAgIGNsYXNzOiBcImFpLWVkaXRvci10YXNrLWxpc3RcIixcbiAgfSxcbn0pXG5cbmNvbnN0IHRhc2tJdGVtID0gVGFza0l0ZW0uY29uZmlndXJlKHtcbiAgSFRNTEF0dHJpYnV0ZXM6IHtcbiAgICBjbGFzczogXCJhaS1lZGl0b3ItdGFzay1pdGVtXCIsXG4gIH0sXG59KVxuXG4vLyDlm77niYfphY3nva5cbmNvbnN0IHRpcHRhcEltYWdlID0gVGlwdGFwSW1hZ2UuZXh0ZW5kKHtcbiAgYWRkUHJvc2VNaXJyb3JQbHVnaW5zKCkge1xuICAgIHJldHVybiBbXG4gICAgICBVcGxvYWRJbWFnZXNQbHVnaW4oe1xuICAgICAgICBpbWFnZUNsYXNzOiBcImFpLWVkaXRvci1pbWFnZS11cGxvYWRpbmcgb3BhY2l0eS00MCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDBcIixcbiAgICAgIH0pLFxuICAgIF1cbiAgfSxcbn0pLmNvbmZpZ3VyZSh7XG4gIGFsbG93QmFzZTY0OiB0cnVlLFxuICBIVE1MQXR0cmlidXRlczoge1xuICAgIGNsYXNzOiBcImFpLWVkaXRvci1pbWFnZSByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDAgbWF4LXctZnVsbCBoLWF1dG9cIixcbiAgfSxcbn0pXG5cbi8vIOaLluaLveaJi+afhOmFjee9rlxuY29uc3QgZ2xvYmFsRHJhZ0hhbmRsZSA9IEdsb2JhbERyYWdIYW5kbGUuY29uZmlndXJlKHtcbiAgZHJhZ0hhbmRsZVdpZHRoOiAyMCxcbn0pXG5cbi8vIOmFjee9riBNYXJrZG93biDmlK/mjIFcbmNvbnN0IG1hcmtkb3duID0gTWFya2Rvd24uY29uZmlndXJlKHtcbiAgaHRtbDogdHJ1ZSxcbiAgdGlnaHRMaXN0czogdHJ1ZSxcbiAgYnVsbGV0TGlzdE1hcmtlcjogXCItXCIsXG4gIGxpbmtpZnk6IGZhbHNlLFxuICBicmVha3M6IGZhbHNlLFxufSlcblxuLy8g5a+85Ye65omA5pyJ5omp5bGVXG5leHBvcnQgY29uc3QgYWlFZGl0b3JFeHRlbnNpb25zID0gW1xuICBzdGFydGVyS2l0LFxuICBwbGFjZWhvbGRlcixcbiAgbGluayxcbiAgVGlwdGFwVW5kZXJsaW5lLFxuICBoaWdobGlnaHQsXG4gIFRleHRTdHlsZSxcbiAgQ29sb3IsXG4gIG1hcmtkb3duLFxuICB0YXNrTGlzdCxcbiAgdGFza0l0ZW0sXG4gIHRpcHRhcEltYWdlLFxuICBnbG9iYWxEcmFnSGFuZGxlLFxuXVxuXG4vLyDlr7zlh7rlm77niYfkuIrkvKDlh73mlbBcbmV4cG9ydCB7IHVwbG9hZEZuIH0gZnJvbSBcIi4vaW1hZ2UtdXBsb2FkXCJcbiJdLCJuYW1lcyI6WyJTdGFydGVyS2l0IiwiUGxhY2Vob2xkZXIiLCJUaXB0YXBMaW5rIiwiVGlwdGFwVW5kZXJsaW5lIiwiSGlnaGxpZ2h0RXh0ZW5zaW9uIiwiVGV4dFN0eWxlIiwiQ29sb3IiLCJHbG9iYWxEcmFnSGFuZGxlIiwiVGlwdGFwSW1hZ2UiLCJVcGxvYWRJbWFnZXNQbHVnaW4iLCJUYXNrTGlzdCIsIlRhc2tJdGVtIiwiTWFya2Rvd24iLCJFeHRlbnNpb24iLCJBSUNvbW1hbmRFeHRlbnNpb24iLCJjcmVhdGUiLCJuYW1lIiwiYWRkS2V5Ym9hcmRTaG9ydGN1dHMiLCJldmVudCIsIkN1c3RvbUV2ZW50IiwiZG9jdW1lbnQiLCJkaXNwYXRjaEV2ZW50IiwiYWRkQ29tbWFuZHMiLCJzZXRBSUhpZ2hsaWdodCIsImNvbW1hbmRzIiwic2V0SGlnaGxpZ2h0IiwiY29sb3IiLCJ1bnNldEFJSGlnaGxpZ2h0IiwidW5zZXRIaWdobGlnaHQiLCJzdGFydGVyS2l0IiwiY29uZmlndXJlIiwiYnVsbGV0TGlzdCIsIkhUTUxBdHRyaWJ1dGVzIiwiY2xhc3MiLCJvcmRlcmVkTGlzdCIsImJsb2NrcXVvdGUiLCJjb2RlIiwiaGVhZGluZyIsInBsYWNlaG9sZGVyIiwibm9kZSIsInR5cGUiLCJsaW5rIiwiaGlnaGxpZ2h0IiwibXVsdGljb2xvciIsInRhc2tMaXN0IiwidGFza0l0ZW0iLCJ0aXB0YXBJbWFnZSIsImV4dGVuZCIsImFkZFByb3NlTWlycm9yUGx1Z2lucyIsImltYWdlQ2xhc3MiLCJhbGxvd0Jhc2U2NCIsImdsb2JhbERyYWdIYW5kbGUiLCJkcmFnSGFuZGxlV2lkdGgiLCJtYXJrZG93biIsImh0bWwiLCJ0aWdodExpc3RzIiwiYnVsbGV0TGlzdE1hcmtlciIsImxpbmtpZnkiLCJicmVha3MiLCJhaUVkaXRvckV4dGVuc2lvbnMiLCJ1cGxvYWRGbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ai-editor/extensions.ts\n"));

/***/ })

});