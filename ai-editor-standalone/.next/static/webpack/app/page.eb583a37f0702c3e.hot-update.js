"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ai-editor/index.tsx":
/*!********************************************!*\
  !*** ./src/components/ai-editor/index.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIEditor: () => (/* binding */ AIEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/novel/dist/index.js\");\n/* harmony import */ var use_debounce__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! use-debounce */ \"(app-pages-browser)/./node_modules/use-debounce/dist/index.module.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var _extensions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./extensions */ \"(app-pages-browser)/./src/components/ai-editor/extensions.ts\");\n/* harmony import */ var _ai_toolbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ai-toolbar */ \"(app-pages-browser)/./src/components/ai-editor/ai-toolbar.tsx\");\n/* harmony import */ var _ai_assistant__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ai-assistant */ \"(app-pages-browser)/./src/components/ai-editor/ai-assistant.tsx\");\n/* harmony import */ var _slash_command__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./slash-command */ \"(app-pages-browser)/./src/components/ai-editor/slash-command.tsx\");\n/* harmony import */ var _ai_editor_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ai-editor.css */ \"(app-pages-browser)/./src/components/ai-editor/ai-editor.css\");\n/* __next_internal_client_entry_do_not_use__ AIEditor auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n// 导入我们的扩展和组件\n\n\n\n\n\n// 内部编辑器组件，不使用SSR\nfunction AIEditorInternal(param) {\n    let { initialContent, placeholder = \"开始写作...\", className = \"\", onContentChange, onMarkdownChange, showToolbar = true, defaultMode = \"edit\" } = param;\n    _s();\n    // 编辑器状态\n    const [editor, setEditor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAIOpen, setIsAIOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedText, setSelectedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [aiSuggestion, setAISuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mode, setMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultMode);\n    const [markdown, setMarkdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 防抖更新函数\n    const debouncedUpdate = (0,use_debounce__WEBPACK_IMPORTED_MODULE_8__.useDebouncedCallback)({\n        \"AIEditorInternal.useDebouncedCallback[debouncedUpdate]\": (editor)=>{\n            const json = editor.getJSON();\n            onContentChange === null || onContentChange === void 0 ? void 0 : onContentChange(json);\n            const markdownContent = editor.storage.markdown.getMarkdown();\n            setMarkdown(markdownContent);\n            if (onMarkdownChange) {\n                onMarkdownChange(markdownContent);\n            }\n        }\n    }[\"AIEditorInternal.useDebouncedCallback[debouncedUpdate]\"], 300);\n    // AI 功能处理函数\n    const handleAIClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[handleAIClick]\": ()=>{\n            if (!editor) return;\n            const { selection } = editor.state;\n            const selectedText = editor.state.doc.textBetween(selection.from, selection.to);\n            setSelectedText(selectedText);\n            setIsAIOpen(true);\n        }\n    }[\"AIEditorInternal.useCallback[handleAIClick]\"], [\n        editor\n    ]);\n    // 模拟 AI 生成文本\n    const generateAIText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[generateAIText]\": async (prompt)=>{\n            setIsLoading(true);\n            setAISuggestion(\"\");\n            try {\n                // 模拟 API 调用延迟\n                await new Promise({\n                    \"AIEditorInternal.useCallback[generateAIText]\": (resolve)=>setTimeout(resolve, 1500)\n                }[\"AIEditorInternal.useCallback[generateAIText]\"]);\n                // 根据提示词生成不同的响应\n                let response = \"\";\n                if (prompt.includes(\"改进\")) {\n                    response = \"这是一个改进后的文本版本，更加清晰和有说服力。\";\n                } else if (prompt.includes(\"扩展\")) {\n                    response = \"经过优化的内容，增加了更多细节和具体例子。\";\n                } else if (prompt.includes(\"总结\")) {\n                    response = \"重新组织的段落结构，逻辑更加清晰。\";\n                } else if (prompt.includes(\"修正\")) {\n                    response = \"修正了语法问题，表达更加准确。\";\n                } else {\n                    // 默认响应\n                    const responses = [\n                        \"这是一个改进后的文本版本，更加清晰和有说服力。\",\n                        \"经过优化的内容，增加了更多细节和具体例子。\",\n                        \"重新组织的段落结构，逻辑更加清晰。\",\n                        \"修正了语法问题，表达更加准确。\"\n                    ];\n                    response = responses[Math.floor(Math.random() * responses.length)];\n                }\n                setAISuggestion(response);\n            } catch (error) {\n                console.error(\"AI 生成失败:\", error);\n                setAISuggestion(\"抱歉，AI 生成失败，请稍后重试。\");\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"AIEditorInternal.useCallback[generateAIText]\"], []);\n    // 插入 AI 生成的文本\n    const insertAIText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[insertAIText]\": (text)=>{\n            if (!editor) return;\n            editor.chain().focus().insertContent(text).run();\n            setIsAIOpen(false);\n            setAISuggestion(\"\");\n        }\n    }[\"AIEditorInternal.useCallback[insertAIText]\"], [\n        editor\n    ]);\n    // 替换选中的文本\n    const replaceSelectedText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[replaceSelectedText]\": (text)=>{\n            if (!editor) return;\n            const { selection } = editor.state;\n            editor.chain().focus().deleteRange({\n                from: selection.from,\n                to: selection.to\n            }).insertContent(text).run();\n            setIsAIOpen(false);\n            setAISuggestion(\"\");\n        }\n    }[\"AIEditorInternal.useCallback[replaceSelectedText]\"], [\n        editor\n    ]);\n    // 导出Markdown文件\n    const exportMarkdown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[exportMarkdown]\": ()=>{\n            if (!markdown) return;\n            const blob = new Blob([\n                markdown\n            ], {\n                type: 'text/markdown'\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"document-\".concat(new Date().toISOString().split('T')[0], \".md\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    }[\"AIEditorInternal.useCallback[exportMarkdown]\"], [\n        markdown\n    ]);\n    // 监听键盘快捷键\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIEditorInternal.useEffect\": ()=>{\n            const handleKeyboardShortcuts = {\n                \"AIEditorInternal.useEffect.handleKeyboardShortcuts\": (event)=>{\n                    if (event.type === \"ai-assistant-trigger\") {\n                        handleAIClick();\n                    }\n                }\n            }[\"AIEditorInternal.useEffect.handleKeyboardShortcuts\"];\n            document.addEventListener(\"ai-assistant-trigger\", handleKeyboardShortcuts);\n            document.addEventListener(\"ai-quick-generate\", handleKeyboardShortcuts);\n            return ({\n                \"AIEditorInternal.useEffect\": ()=>{\n                    document.removeEventListener(\"ai-assistant-trigger\", handleKeyboardShortcuts);\n                    document.removeEventListener(\"ai-quick-generate\", handleKeyboardShortcuts);\n                }\n            })[\"AIEditorInternal.useEffect\"];\n        }\n    }[\"AIEditorInternal.useEffect\"], [\n        handleAIClick\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"ai-editor-container h-full flex flex-col \".concat(className),\n        children: [\n            showToolbar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b bg-gray-50 dark:bg-gray-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                            children: \"AI 编辑器\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: mode === \"edit\" ? \"secondary\" : \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setMode(\"edit\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" 编辑\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: mode === \"preview\" ? \"secondary\" : \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setMode(\"preview\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Markdown输出\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: exportMarkdown,\n                                disabled: !markdown,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" 导出\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 178,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    (mode === \"edit\" || mode === \"split\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(mode === \"split\" ? \"w-1/2\" : \"w-full\", \" flex flex-col\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_12__.EditorRoot, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_12__.EditorContent, {\n                                immediatelyRender: false,\n                                initialContent: initialContent,\n                                extensions: _extensions__WEBPACK_IMPORTED_MODULE_3__.aiEditorExtensions,\n                                className: \"h-full w-full\",\n                                editorProps: {\n                                    handleDOMEvents: {\n                                        keydown: (_view, event)=>(0,novel__WEBPACK_IMPORTED_MODULE_12__.handleCommandNavigation)(event)\n                                    },\n                                    handlePaste: (view, event)=>(0,novel__WEBPACK_IMPORTED_MODULE_12__.handleImagePaste)(view, event, _extensions__WEBPACK_IMPORTED_MODULE_3__.uploadFn),\n                                    handleDrop: (view, event, _slice, moved)=>(0,novel__WEBPACK_IMPORTED_MODULE_12__.handleImageDrop)(view, event, moved, _extensions__WEBPACK_IMPORTED_MODULE_3__.uploadFn),\n                                    attributes: {\n                                        class: \"prose prose-base dark:prose-invert max-w-none p-4 focus:outline-none h-full overflow-auto\",\n                                        \"data-placeholder\": placeholder\n                                    }\n                                },\n                                onCreate: (param)=>{\n                                    let { editor } = param;\n                                    setEditor(editor);\n                                },\n                                onUpdate: (param)=>{\n                                    let { editor } = param;\n                                    debouncedUpdate(editor);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_12__.EditorCommand, {\n                                        className: \"z-50 h-auto max-h-[330px] overflow-y-auto rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-1 py-2 shadow-md transition-all\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_12__.EditorCommandEmpty, {\n                                                className: \"px-2 text-gray-500 dark:text-gray-400\",\n                                                children: \"没有找到结果\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_12__.EditorCommandList, {\n                                                children: _slash_command__WEBPACK_IMPORTED_MODULE_6__.suggestionItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_12__.EditorCommandItem, {\n                                                        value: item.title,\n                                                        onCommand: (val)=>{\n                                                            var _item_command;\n                                                            return (_item_command = item.command) === null || _item_command === void 0 ? void 0 : _item_command.call(item, val);\n                                                        },\n                                                        className: \"flex w-full items-center space-x-2 rounded-md px-2 py-1 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 aria-selected:bg-gray-100 dark:aria-selected:bg-gray-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex h-10 w-10 items-center justify-center rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\",\n                                                                children: item.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: item.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: item.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                                        lineNumber: 259,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, item.title, true, {\n                                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ai_toolbar__WEBPACK_IMPORTED_MODULE_4__.AIToolbar, {\n                                        editor: editor,\n                                        onAIClick: handleAIClick,\n                                        isLoading: isLoading\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_12__.ImageResizer, {}, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this),\n                    (mode === \"preview\" || mode === \"split\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(mode === \"split\" ? \"w-1/2 border-l border-gray-200 dark:border-gray-700\" : \"w-full\", \" p-4 overflow-auto bg-gray-50 dark:bg-gray-800\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"prose prose-sm max-w-none dark:prose-invert\",\n                            children: markdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_13__.Markdown, {\n                                remarkPlugins: [\n                                    remark_gfm__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n                                ],\n                                children: markdown\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-500 dark:text-gray-400 italic\",\n                                children: \"开始编辑以查看预览...\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this),\n            isAIOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ai_assistant__WEBPACK_IMPORTED_MODULE_5__.AIAssistant, {\n                selectedText: selectedText,\n                suggestion: aiSuggestion,\n                isLoading: isLoading,\n                onGenerate: generateAIText,\n                onInsert: insertAIText,\n                onReplace: replaceSelectedText,\n                onClose: ()=>setIsAIOpen(false)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 302,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n        lineNumber: 175,\n        columnNumber: 5\n    }, this);\n}\n_s(AIEditorInternal, \"2H/6ktHM3v44k/4SuMrn7VZPQJw=\", false, function() {\n    return [\n        use_debounce__WEBPACK_IMPORTED_MODULE_8__.useDebouncedCallback\n    ];\n});\n_c = AIEditorInternal;\n// 使用动态导入禁用SSR的主要导出组件\nfunction AIEditor(props) {\n    _s1();\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIEditor.useEffect\": ()=>{\n            setIsMounted(true);\n        }\n    }[\"AIEditor.useEffect\"], []);\n    if (!isMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"ai-editor-container \".concat(props.className || \"\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full min-h-[400px] flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500 dark:text-gray-400\",\n                    children: \"加载编辑器中...\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 327,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n            lineNumber: 326,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIEditorInternal, {\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n        lineNumber: 334,\n        columnNumber: 10\n    }, this);\n}\n_s1(AIEditor, \"h7njlszr1nxUzrk46zHyBTBrvgI=\");\n_c1 = AIEditor;\nvar _c, _c1;\n$RefreshReg$(_c, \"AIEditorInternal\");\n$RefreshReg$(_c1, \"AIEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ai-editor/index.tsx\n"));

/***/ })

});