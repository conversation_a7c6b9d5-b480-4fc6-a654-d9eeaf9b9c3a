"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ai-editor/extensions.ts":
/*!************************************************!*\
  !*** ./src/components/ai-editor/extensions.ts ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiEditorExtensions: () => (/* binding */ aiEditorExtensions),\n/* harmony export */   uploadFn: () => (/* reexport safe */ _image_upload__WEBPACK_IMPORTED_MODULE_12__.uploadFn)\n/* harmony export */ });\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+starter-kit@2.26.1/node_modules/@tiptap/starter-kit/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/novel@1.0.2_@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tipta_3fbtezt2z6la73kpow3ollimny/node_modules/novel/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-link@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-link/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-task-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-task-list/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-task-item@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-task-item/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-image@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-image/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/tiptap-extension-global-drag-handle@0.1.18/node_modules/tiptap-extension-global-drag-handle/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-underline@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-underline/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-text-style@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-text-style/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-color@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+extension-text-_ochc23jjcl3wq5k6zoczrgb3ku/node_modules/@tiptap/extension-color/dist/index.js\");\n/* harmony import */ var tiptap_markdown__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! tiptap-markdown */ \"(app-pages-browser)/./node_modules/.pnpm/tiptap-markdown@0.8.10_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/tiptap-markdown/dist/tiptap-markdown.es.js\");\n/* harmony import */ var _tiptap_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/react */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _image_upload__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./image-upload */ \"(app-pages-browser)/./src/components/ai-editor/image-upload.ts\");\n\n\n\n// 自定义 AI 命令扩展\nconst AICommandExtension = _tiptap_react__WEBPACK_IMPORTED_MODULE_0__.Extension.create({\n    name: \"aiCommand\",\n    addKeyboardShortcuts () {\n        return {\n            // Ctrl/Cmd + K 触发 AI 助手\n            \"Mod-k\": ()=>{\n                // 触发 AI 助手的逻辑将在组件中处理\n                const event = new CustomEvent(\"ai-assistant-trigger\");\n                document.dispatchEvent(event);\n                return true;\n            },\n            // Ctrl/Cmd + Shift + A 快速 AI 生成\n            \"Mod-Shift-a\": ()=>{\n                const event = new CustomEvent(\"ai-quick-generate\");\n                document.dispatchEvent(event);\n                return true;\n            }\n        };\n    },\n    addCommands () {\n        return {\n            // 添加 AI 高亮命令\n            setAIHighlight: ()=>(param)=>{\n                    let { commands } = param;\n                    return commands.setHighlight({\n                        color: \"#3b82f6\"\n                    });\n                },\n            // 移除 AI 高亮命令\n            unsetAIHighlight: ()=>(param)=>{\n                    let { commands } = param;\n                    return commands.unsetHighlight();\n                }\n        };\n    }\n});\n// 基础扩展配置\nconst starterKit = novel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].configure({\n    bulletList: {\n        HTMLAttributes: {\n            class: \"ai-editor-bullet-list\"\n        }\n    },\n    orderedList: {\n        HTMLAttributes: {\n            class: \"ai-editor-ordered-list\"\n        }\n    },\n    blockquote: {\n        HTMLAttributes: {\n            class: \"ai-editor-blockquote\"\n        }\n    },\n    code: {\n        HTMLAttributes: {\n            class: \"ai-editor-code\"\n        }\n    },\n    heading: {\n        HTMLAttributes: {\n            class: \"ai-editor-heading\"\n        }\n    }\n});\n// 占位符配置\nconst placeholder = novel__WEBPACK_IMPORTED_MODULE_2__.Placeholder.configure({\n    placeholder: (param)=>{\n        let { node } = param;\n        if (node.type.name === \"heading\") {\n            return \"标题\";\n        }\n        return \"开始写作...\";\n    }\n});\n// 链接配置\nconst link = novel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].configure({\n    HTMLAttributes: {\n        class: \"ai-editor-link\"\n    },\n    openOnClick: false\n});\n// 高亮配置\nconst highlight = novel__WEBPACK_IMPORTED_MODULE_2__.HighlightExtension.configure({\n    multicolor: true\n});\n// 任务列表配置\nconst taskList = novel__WEBPACK_IMPORTED_MODULE_4__.TaskList.configure({\n    HTMLAttributes: {\n        class: \"ai-editor-task-list\"\n    }\n});\nconst taskItem = novel__WEBPACK_IMPORTED_MODULE_5__.TaskItem.configure({\n    HTMLAttributes: {\n        class: \"ai-editor-task-item\"\n    }\n});\n// 图片配置\nconst tiptapImage = novel__WEBPACK_IMPORTED_MODULE_6__[\"default\"].extend({\n    addProseMirrorPlugins () {\n        return [\n            (0,novel__WEBPACK_IMPORTED_MODULE_2__.UploadImagesPlugin)({\n                imageClass: \"ai-editor-image-uploading opacity-40 rounded-lg border border-gray-200\"\n            })\n        ];\n    }\n}).configure({\n    allowBase64: true,\n    HTMLAttributes: {\n        class: \"ai-editor-image rounded-lg border border-gray-200 max-w-full h-auto\"\n    }\n});\n// 拖拽手柄配置\nconst globalDragHandle = novel__WEBPACK_IMPORTED_MODULE_7__[\"default\"].configure({\n    dragHandleWidth: 20\n});\n// 配置 Markdown 支持\nconst markdown = tiptap_markdown__WEBPACK_IMPORTED_MODULE_8__.Markdown.configure({\n    html: true,\n    tightLists: true,\n    bulletListMarker: \"-\",\n    linkify: false,\n    breaks: false\n});\n// 导出所有扩展\nconst aiEditorExtensions = [\n    starterKit,\n    placeholder,\n    link,\n    novel__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    highlight,\n    novel__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    novel__WEBPACK_IMPORTED_MODULE_11__.Color,\n    markdown,\n    taskList,\n    taskItem,\n    tiptapImage,\n    globalDragHandle\n];\n// 导出图片上传函数\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ai-editor/extensions.ts\n"));

/***/ })

});