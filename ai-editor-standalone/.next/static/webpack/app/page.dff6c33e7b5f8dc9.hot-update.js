"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ai-editor/ai-editor.css":
/*!************************************************!*\
  !*** ./src/components/ai-editor/ai-editor.css ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4fcaf2fcf8b3\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2FpLWVkaXRvci9haS1lZGl0b3IuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksSUFBVSxJQUFJLGlCQUFpQiIsInNvdXJjZXMiOlsiL2hvbWUvbXV6aS9naXRodWIvZGVlci1mbG93L2FpLWVkaXRvci1zdGFuZGFsb25lL3NyYy9jb21wb25lbnRzL2FpLWVkaXRvci9haS1lZGl0b3IuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNGZjYWYyZmNmOGIzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ai-editor/ai-editor.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ai-editor/index.tsx":
/*!********************************************!*\
  !*** ./src/components/ai-editor/index.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIEditor: () => (/* binding */ AIEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/novel@1.0.2_@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tipta_3fbtezt2z6la73kpow3ollimny/node_modules/novel/dist/index.js\");\n/* harmony import */ var use_debounce__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! use-debounce */ \"(app-pages-browser)/./node_modules/.pnpm/use-debounce@10.0.5_react@18.3.1/node_modules/use-debounce/dist/index.module.js\");\n/* harmony import */ var _extensions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./extensions */ \"(app-pages-browser)/./src/components/ai-editor/extensions.ts\");\n/* harmony import */ var _slash_command__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./slash-command */ \"(app-pages-browser)/./src/components/ai-editor/slash-command.tsx\");\n/* harmony import */ var _ai_editor_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ai-editor.css */ \"(app-pages-browser)/./src/components/ai-editor/ai-editor.css\");\n/* __next_internal_client_entry_do_not_use__ AIEditor auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// 导入我们的扩展和组件\n\n\n\n// 内部编辑器组件，不使用SSR\nfunction AIEditorInternal(param) {\n    let { initialContent, placeholder = \"开始写作...\", className = \"\", onContentChange, onMarkdownChange } = param;\n    _s();\n    const [editor, setEditor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 防抖更新函数\n    const debouncedUpdate = (0,use_debounce__WEBPACK_IMPORTED_MODULE_5__.useDebouncedCallback)({\n        \"AIEditorInternal.useDebouncedCallback[debouncedUpdate]\": (editor)=>{\n            const json = editor.getJSON();\n            onContentChange === null || onContentChange === void 0 ? void 0 : onContentChange(json);\n            if (onMarkdownChange) {\n                const markdown = editor.storage.markdown.getMarkdown();\n                onMarkdownChange(markdown);\n            }\n        }\n    }[\"AIEditorInternal.useDebouncedCallback[debouncedUpdate]\"], 300);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"ai-editor-container \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_6__.EditorRoot, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_6__.EditorContent, {\n                immediatelyRender: false,\n                initialContent: initialContent,\n                extensions: _extensions__WEBPACK_IMPORTED_MODULE_2__.aiEditorExtensions,\n                className: \"w-full h-full\",\n                editorProps: {\n                    handleDOMEvents: {\n                        keydown: (_view, event)=>(0,novel__WEBPACK_IMPORTED_MODULE_6__.handleCommandNavigation)(event)\n                    },\n                    handlePaste: (view, event)=>(0,novel__WEBPACK_IMPORTED_MODULE_6__.handleImagePaste)(view, event, _extensions__WEBPACK_IMPORTED_MODULE_2__.uploadFn),\n                    handleDrop: (view, event, _slice, moved)=>(0,novel__WEBPACK_IMPORTED_MODULE_6__.handleImageDrop)(view, event, moved, _extensions__WEBPACK_IMPORTED_MODULE_2__.uploadFn),\n                    attributes: {\n                        class: \"prose prose-base dark:prose-invert max-w-none p-4 focus:outline-none min-h-[400px]\",\n                        \"data-placeholder\": placeholder\n                    }\n                },\n                onCreate: (param)=>{\n                    let { editor } = param;\n                    setEditor(editor);\n                },\n                onUpdate: (param)=>{\n                    let { editor } = param;\n                    debouncedUpdate(editor);\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_6__.EditorCommand, {\n                    className: \"z-50 h-auto max-h-[330px] overflow-y-auto rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-1 py-2 shadow-md transition-all\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_6__.EditorCommandEmpty, {\n                            className: \"px-2 text-gray-500 dark:text-gray-400\",\n                            children: \"没有找到结果\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_6__.EditorCommandList, {\n                            children: _slash_command__WEBPACK_IMPORTED_MODULE_3__.suggestionItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_6__.EditorCommandItem, {\n                                    value: item.title,\n                                    onCommand: (val)=>{\n                                        var _item_command;\n                                        return (_item_command = item.command) === null || _item_command === void 0 ? void 0 : _item_command.call(item, val);\n                                    },\n                                    className: \"flex w-full items-center space-x-2 rounded-md px-2 py-1 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 aria-selected:bg-gray-100 dark:aria-selected:bg-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex h-10 w-10 items-center justify-center rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\",\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: item.title\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: item.description\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.title, true, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n_s(AIEditorInternal, \"njiDum+KQWjEo5/aC20Mls3urdI=\", false, function() {\n    return [\n        use_debounce__WEBPACK_IMPORTED_MODULE_5__.useDebouncedCallback\n    ];\n});\n_c = AIEditorInternal;\n// 使用动态导入禁用SSR的主要导出组件\nfunction AIEditor(props) {\n    _s1();\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIEditor.useEffect\": ()=>{\n            setIsMounted(true);\n        }\n    }[\"AIEditor.useEffect\"], []);\n    if (!isMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"ai-editor-container \".concat(props.className || \"\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full min-h-[400px] flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500 dark:text-gray-400\",\n                    children: \"加载编辑器中...\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIEditorInternal, {\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n        lineNumber: 135,\n        columnNumber: 10\n    }, this);\n}\n_s1(AIEditor, \"h7njlszr1nxUzrk46zHyBTBrvgI=\");\n_c1 = AIEditor;\nvar _c, _c1;\n$RefreshReg$(_c, \"AIEditorInternal\");\n$RefreshReg$(_c1, \"AIEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ai-editor/index.tsx\n"));

/***/ })

});