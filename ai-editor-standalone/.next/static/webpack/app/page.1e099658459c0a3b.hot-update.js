"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ai-editor/index.tsx":
/*!********************************************!*\
  !*** ./src/components/ai-editor/index.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIEditor: () => (/* binding */ AIEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/novel/dist/index.js\");\n/* harmony import */ var use_debounce__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! use-debounce */ \"(app-pages-browser)/./node_modules/use-debounce/dist/index.module.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Code_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Download,Edit,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Download,Edit,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Download,Edit,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Download,Edit,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var _extensions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./extensions */ \"(app-pages-browser)/./src/components/ai-editor/extensions.ts\");\n/* harmony import */ var _ai_toolbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ai-toolbar */ \"(app-pages-browser)/./src/components/ai-editor/ai-toolbar.tsx\");\n/* harmony import */ var _ai_assistant__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ai-assistant */ \"(app-pages-browser)/./src/components/ai-editor/ai-assistant.tsx\");\n/* harmony import */ var _slash_command__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./slash-command */ \"(app-pages-browser)/./src/components/ai-editor/slash-command.tsx\");\n/* harmony import */ var _ai_editor_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ai-editor.css */ \"(app-pages-browser)/./src/components/ai-editor/ai-editor.css\");\n/* __next_internal_client_entry_do_not_use__ AIEditor auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n// 导入我们的扩展和组件\n\n\n\n\n\n// 内部编辑器组件，不使用SSR\nfunction AIEditorInternal(param) {\n    let { initialContent, placeholder = \"开始写作...\", className = \"\", onContentChange, onMarkdownChange, showToolbar = true, defaultMode = \"edit\" } = param;\n    _s();\n    // 编辑器状态\n    const [editor, setEditor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAIOpen, setIsAIOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedText, setSelectedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [aiSuggestion, setAISuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mode, setMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultMode);\n    const [markdown, setMarkdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 防抖更新函数\n    const debouncedUpdate = (0,use_debounce__WEBPACK_IMPORTED_MODULE_8__.useDebouncedCallback)({\n        \"AIEditorInternal.useDebouncedCallback[debouncedUpdate]\": (editor)=>{\n            const json = editor.getJSON();\n            onContentChange === null || onContentChange === void 0 ? void 0 : onContentChange(json);\n            const markdownContent = editor.storage.markdown.getMarkdown();\n            setMarkdown(markdownContent);\n            if (onMarkdownChange) {\n                onMarkdownChange(markdownContent);\n            }\n        }\n    }[\"AIEditorInternal.useDebouncedCallback[debouncedUpdate]\"], 300);\n    // AI 功能处理函数\n    const handleAIClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[handleAIClick]\": ()=>{\n            if (!editor) return;\n            const { selection } = editor.state;\n            const selectedText = editor.state.doc.textBetween(selection.from, selection.to);\n            setSelectedText(selectedText);\n            setIsAIOpen(true);\n        }\n    }[\"AIEditorInternal.useCallback[handleAIClick]\"], [\n        editor\n    ]);\n    // 模拟 AI 生成文本\n    const generateAIText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[generateAIText]\": async (prompt)=>{\n            setIsLoading(true);\n            setAISuggestion(\"\");\n            try {\n                // 模拟 API 调用延迟\n                await new Promise({\n                    \"AIEditorInternal.useCallback[generateAIText]\": (resolve)=>setTimeout(resolve, 1500)\n                }[\"AIEditorInternal.useCallback[generateAIText]\"]);\n                // 根据提示词生成不同的响应\n                let response = \"\";\n                if (prompt.includes(\"改进\")) {\n                    response = \"这是一个改进后的文本版本，更加清晰和有说服力。\";\n                } else if (prompt.includes(\"扩展\")) {\n                    response = \"经过优化的内容，增加了更多细节和具体例子。\";\n                } else if (prompt.includes(\"总结\")) {\n                    response = \"重新组织的段落结构，逻辑更加清晰。\";\n                } else if (prompt.includes(\"修正\")) {\n                    response = \"修正了语法问题，表达更加准确。\";\n                } else {\n                    // 默认响应\n                    const responses = [\n                        \"这是一个改进后的文本版本，更加清晰和有说服力。\",\n                        \"经过优化的内容，增加了更多细节和具体例子。\",\n                        \"重新组织的段落结构，逻辑更加清晰。\",\n                        \"修正了语法问题，表达更加准确。\"\n                    ];\n                    response = responses[Math.floor(Math.random() * responses.length)];\n                }\n                setAISuggestion(response);\n            } catch (error) {\n                console.error(\"AI 生成失败:\", error);\n                setAISuggestion(\"抱歉，AI 生成失败，请稍后重试。\");\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"AIEditorInternal.useCallback[generateAIText]\"], []);\n    // 插入 AI 生成的文本\n    const insertAIText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[insertAIText]\": (text)=>{\n            if (!editor) return;\n            editor.chain().focus().insertContent(text).run();\n            setIsAIOpen(false);\n            setAISuggestion(\"\");\n        }\n    }[\"AIEditorInternal.useCallback[insertAIText]\"], [\n        editor\n    ]);\n    // 替换选中的文本\n    const replaceSelectedText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[replaceSelectedText]\": (text)=>{\n            if (!editor) return;\n            const { selection } = editor.state;\n            editor.chain().focus().deleteRange({\n                from: selection.from,\n                to: selection.to\n            }).insertContent(text).run();\n            setIsAIOpen(false);\n            setAISuggestion(\"\");\n        }\n    }[\"AIEditorInternal.useCallback[replaceSelectedText]\"], [\n        editor\n    ]);\n    // 导出Markdown文件\n    const exportMarkdown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[exportMarkdown]\": ()=>{\n            if (!markdown) return;\n            const blob = new Blob([\n                markdown\n            ], {\n                type: 'text/markdown'\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"document-\".concat(new Date().toISOString().split('T')[0], \".md\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    }[\"AIEditorInternal.useCallback[exportMarkdown]\"], [\n        markdown\n    ]);\n    // 监听键盘快捷键\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIEditorInternal.useEffect\": ()=>{\n            const handleKeyboardShortcuts = {\n                \"AIEditorInternal.useEffect.handleKeyboardShortcuts\": (event)=>{\n                    if (event.type === \"ai-assistant-trigger\") {\n                        handleAIClick();\n                    }\n                }\n            }[\"AIEditorInternal.useEffect.handleKeyboardShortcuts\"];\n            document.addEventListener(\"ai-assistant-trigger\", handleKeyboardShortcuts);\n            document.addEventListener(\"ai-quick-generate\", handleKeyboardShortcuts);\n            return ({\n                \"AIEditorInternal.useEffect\": ()=>{\n                    document.removeEventListener(\"ai-assistant-trigger\", handleKeyboardShortcuts);\n                    document.removeEventListener(\"ai-quick-generate\", handleKeyboardShortcuts);\n                }\n            })[\"AIEditorInternal.useEffect\"];\n        }\n    }[\"AIEditorInternal.useEffect\"], [\n        handleAIClick\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"ai-editor-container h-full flex flex-col \".concat(className),\n        children: [\n            showToolbar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b bg-gray-50 dark:bg-gray-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                            children: \"AI 编辑器\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: mode === \"edit\" ? \"secondary\" : \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setMode(\"edit\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" 编辑\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: mode === \"preview\" ? \"secondary\" : \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setMode(\"preview\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" 预览\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: mode === \"split\" ? \"secondary\" : \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setMode(\"split\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" 分割\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: exportMarkdown,\n                                disabled: !markdown,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" 导出\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 178,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    (mode === \"edit\" || mode === \"split\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(mode === \"split\" ? \"w-1/2\" : \"w-full\", \" flex flex-col\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_13__.EditorRoot, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_13__.EditorContent, {\n                                immediatelyRender: false,\n                                initialContent: initialContent,\n                                extensions: _extensions__WEBPACK_IMPORTED_MODULE_3__.aiEditorExtensions,\n                                className: \"h-full w-full\",\n                                editorProps: {\n                                    handleDOMEvents: {\n                                        keydown: (_view, event)=>(0,novel__WEBPACK_IMPORTED_MODULE_13__.handleCommandNavigation)(event)\n                                    },\n                                    handlePaste: (view, event)=>(0,novel__WEBPACK_IMPORTED_MODULE_13__.handleImagePaste)(view, event, _extensions__WEBPACK_IMPORTED_MODULE_3__.uploadFn),\n                                    handleDrop: (view, event, _slice, moved)=>(0,novel__WEBPACK_IMPORTED_MODULE_13__.handleImageDrop)(view, event, moved, _extensions__WEBPACK_IMPORTED_MODULE_3__.uploadFn),\n                                    attributes: {\n                                        class: \"prose prose-base dark:prose-invert max-w-none p-4 focus:outline-none h-full overflow-auto\",\n                                        \"data-placeholder\": placeholder\n                                    }\n                                },\n                                onCreate: (param)=>{\n                                    let { editor } = param;\n                                    setEditor(editor);\n                                },\n                                onUpdate: (param)=>{\n                                    let { editor } = param;\n                                    debouncedUpdate(editor);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_13__.EditorCommand, {\n                                        className: \"z-50 h-auto max-h-[330px] overflow-y-auto rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-1 py-2 shadow-md transition-all\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_13__.EditorCommandEmpty, {\n                                                className: \"px-2 text-gray-500 dark:text-gray-400\",\n                                                children: \"没有找到结果\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_13__.EditorCommandList, {\n                                                children: _slash_command__WEBPACK_IMPORTED_MODULE_6__.suggestionItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_13__.EditorCommandItem, {\n                                                        value: item.title,\n                                                        onCommand: (val)=>{\n                                                            var _item_command;\n                                                            return (_item_command = item.command) === null || _item_command === void 0 ? void 0 : _item_command.call(item, val);\n                                                        },\n                                                        className: \"flex w-full items-center space-x-2 rounded-md px-2 py-1 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 aria-selected:bg-gray-100 dark:aria-selected:bg-gray-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex h-10 w-10 items-center justify-center rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\",\n                                                                children: item.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: item.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                                        lineNumber: 265,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: item.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                                        lineNumber: 266,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, item.title, true, {\n                                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ai_toolbar__WEBPACK_IMPORTED_MODULE_4__.AIToolbar, {\n                                        editor: editor,\n                                        onAIClick: handleAIClick,\n                                        isLoading: isLoading\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_13__.ImageResizer, {}, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, this),\n                    (mode === \"preview\" || mode === \"split\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(mode === \"split\" ? \"w-1/2 border-l border-gray-200 dark:border-gray-700\" : \"w-full\", \" p-4 overflow-auto bg-gray-50 dark:bg-gray-800\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"prose prose-sm max-w-none dark:prose-invert\",\n                            children: markdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_14__.Markdown, {\n                                remarkPlugins: [\n                                    remark_gfm__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n                                ],\n                                children: markdown\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-500 dark:text-gray-400 italic\",\n                                children: \"开始编辑以查看预览...\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this),\n            isAIOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ai_assistant__WEBPACK_IMPORTED_MODULE_5__.AIAssistant, {\n                selectedText: selectedText,\n                suggestion: aiSuggestion,\n                isLoading: isLoading,\n                onGenerate: generateAIText,\n                onInsert: insertAIText,\n                onReplace: replaceSelectedText,\n                onClose: ()=>setIsAIOpen(false)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 309,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n        lineNumber: 175,\n        columnNumber: 5\n    }, this);\n}\n_s(AIEditorInternal, \"2H/6ktHM3v44k/4SuMrn7VZPQJw=\", false, function() {\n    return [\n        use_debounce__WEBPACK_IMPORTED_MODULE_8__.useDebouncedCallback\n    ];\n});\n_c = AIEditorInternal;\n// 使用动态导入禁用SSR的主要导出组件\nfunction AIEditor(props) {\n    _s1();\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIEditor.useEffect\": ()=>{\n            setIsMounted(true);\n        }\n    }[\"AIEditor.useEffect\"], []);\n    if (!isMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"ai-editor-container \".concat(props.className || \"\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full min-h-[400px] flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500 dark:text-gray-400\",\n                    children: \"加载编辑器中...\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 334,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n            lineNumber: 333,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIEditorInternal, {\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n        lineNumber: 341,\n        columnNumber: 10\n    }, this);\n}\n_s1(AIEditor, \"h7njlszr1nxUzrk46zHyBTBrvgI=\");\n_c1 = AIEditor;\nvar _c, _c1;\n$RefreshReg$(_c, \"AIEditorInternal\");\n$RefreshReg$(_c1, \"AIEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2FpLWVkaXRvci9pbmRleC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFd0Q7QUFjMUM7QUFDcUM7QUFDSjtBQUNTO0FBQ2Q7QUFDUjtBQUVsQyxhQUFhO0FBQzhDO0FBQ25CO0FBQ0k7QUFDSztBQUN6QjtBQVl4QixpQkFBaUI7QUFDakIsU0FBUzBCLGlCQUFpQixLQVFWO1FBUlUsRUFDeEJDLGNBQWMsRUFDZEMsY0FBYyxTQUFTLEVBQ3ZCQyxZQUFZLEVBQUUsRUFDZEMsZUFBZSxFQUNmQyxnQkFBZ0IsRUFDaEJDLGNBQWMsSUFBSSxFQUNsQkMsY0FBYyxNQUFNLEVBQ04sR0FSVTs7SUFTeEIsUUFBUTtJQUNSLE1BQU0sQ0FBQ0MsUUFBUUMsVUFBVSxHQUFHbkMsK0NBQVFBLENBQXdCO0lBQzVELE1BQU0sQ0FBQ29DLFVBQVVDLFlBQVksR0FBR3JDLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ3NDLGNBQWNDLGdCQUFnQixHQUFHdkMsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDd0MsY0FBY0MsZ0JBQWdCLEdBQUd6QywrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUMwQyxXQUFXQyxhQUFhLEdBQUczQywrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUM0QyxNQUFNQyxRQUFRLEdBQUc3QywrQ0FBUUEsQ0FBcUJpQztJQUNyRCxNQUFNLENBQUNhLFVBQVVDLFlBQVksR0FBRy9DLCtDQUFRQSxDQUFDO0lBRXpDLFNBQVM7SUFDVCxNQUFNZ0Qsa0JBQWtCbkMsa0VBQW9CQTtrRUFBQyxDQUFDcUI7WUFDNUMsTUFBTWUsT0FBT2YsT0FBT2dCLE9BQU87WUFDM0JwQiw0QkFBQUEsc0NBQUFBLGdCQUFrQm1CO1lBRWxCLE1BQU1FLGtCQUFrQmpCLE9BQU9rQixPQUFPLENBQUNOLFFBQVEsQ0FBQ08sV0FBVztZQUMzRE4sWUFBWUk7WUFFWixJQUFJcEIsa0JBQWtCO2dCQUNwQkEsaUJBQWlCb0I7WUFDbkI7UUFDRjtpRUFBRztJQUVILFlBQVk7SUFDWixNQUFNRyxnQkFBZ0JyRCxrREFBV0E7dURBQUM7WUFDaEMsSUFBSSxDQUFDaUMsUUFBUTtZQUViLE1BQU0sRUFBRXFCLFNBQVMsRUFBRSxHQUFHckIsT0FBT3NCLEtBQUs7WUFDbEMsTUFBTWxCLGVBQWVKLE9BQU9zQixLQUFLLENBQUNDLEdBQUcsQ0FBQ0MsV0FBVyxDQUFDSCxVQUFVSSxJQUFJLEVBQUVKLFVBQVVLLEVBQUU7WUFFOUVyQixnQkFBZ0JEO1lBQ2hCRCxZQUFZO1FBQ2Q7c0RBQUc7UUFBQ0g7S0FBTztJQUVYLGFBQWE7SUFDYixNQUFNMkIsaUJBQWlCNUQsa0RBQVdBO3dEQUFDLE9BQU82RDtZQUN4Q25CLGFBQWE7WUFDYkYsZ0JBQWdCO1lBRWhCLElBQUk7Z0JBQ0YsY0FBYztnQkFDZCxNQUFNLElBQUlzQjtvRUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUzs7Z0JBRWpELGVBQWU7Z0JBQ2YsSUFBSUUsV0FBVztnQkFDZixJQUFJSixPQUFPSyxRQUFRLENBQUMsT0FBTztvQkFDekJELFdBQVc7Z0JBQ2IsT0FBTyxJQUFJSixPQUFPSyxRQUFRLENBQUMsT0FBTztvQkFDaENELFdBQVc7Z0JBQ2IsT0FBTyxJQUFJSixPQUFPSyxRQUFRLENBQUMsT0FBTztvQkFDaENELFdBQVc7Z0JBQ2IsT0FBTyxJQUFJSixPQUFPSyxRQUFRLENBQUMsT0FBTztvQkFDaENELFdBQVc7Z0JBQ2IsT0FBTztvQkFDTCxPQUFPO29CQUNQLE1BQU1FLFlBQVk7d0JBQ2hCO3dCQUNBO3dCQUNBO3dCQUNBO3FCQUNEO29CQUNERixXQUFXRSxTQUFTLENBQUNDLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLSCxVQUFVSSxNQUFNLEVBQUU7Z0JBQ3BFO2dCQUVBL0IsZ0JBQWdCeUI7WUFDbEIsRUFBRSxPQUFPTyxPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsWUFBWUE7Z0JBQzFCaEMsZ0JBQWdCO1lBQ2xCLFNBQVU7Z0JBQ1JFLGFBQWE7WUFDZjtRQUNGO3VEQUFHLEVBQUU7SUFFTCxjQUFjO0lBQ2QsTUFBTWdDLGVBQWUxRSxrREFBV0E7c0RBQUMsQ0FBQzJFO1lBQ2hDLElBQUksQ0FBQzFDLFFBQVE7WUFFYkEsT0FBTzJDLEtBQUssR0FBR0MsS0FBSyxHQUFHQyxhQUFhLENBQUNILE1BQU1JLEdBQUc7WUFDOUMzQyxZQUFZO1lBQ1pJLGdCQUFnQjtRQUNsQjtxREFBRztRQUFDUDtLQUFPO0lBRVgsVUFBVTtJQUNWLE1BQU0rQyxzQkFBc0JoRixrREFBV0E7NkRBQUMsQ0FBQzJFO1lBQ3ZDLElBQUksQ0FBQzFDLFFBQVE7WUFFYixNQUFNLEVBQUVxQixTQUFTLEVBQUUsR0FBR3JCLE9BQU9zQixLQUFLO1lBQ2xDdEIsT0FBTzJDLEtBQUssR0FBR0MsS0FBSyxHQUFHSSxXQUFXLENBQUM7Z0JBQUV2QixNQUFNSixVQUFVSSxJQUFJO2dCQUFFQyxJQUFJTCxVQUFVSyxFQUFFO1lBQUMsR0FBR21CLGFBQWEsQ0FBQ0gsTUFBTUksR0FBRztZQUN0RzNDLFlBQVk7WUFDWkksZ0JBQWdCO1FBQ2xCOzREQUFHO1FBQUNQO0tBQU87SUFFWCxlQUFlO0lBQ2YsTUFBTWlELGlCQUFpQmxGLGtEQUFXQTt3REFBQztZQUNqQyxJQUFJLENBQUM2QyxVQUFVO1lBRWYsTUFBTXNDLE9BQU8sSUFBSUMsS0FBSztnQkFBQ3ZDO2FBQVMsRUFBRTtnQkFBRXdDLE1BQU07WUFBZ0I7WUFDMUQsTUFBTUMsTUFBTUMsSUFBSUMsZUFBZSxDQUFDTDtZQUNoQyxNQUFNTSxJQUFJQyxTQUFTQyxhQUFhLENBQUM7WUFDakNGLEVBQUVHLElBQUksR0FBR047WUFDVEcsRUFBRUksUUFBUSxHQUFHLFlBQW1ELE9BQXZDLElBQUlDLE9BQU9DLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLEVBQUM7WUFDaEVOLFNBQVNPLElBQUksQ0FBQ0MsV0FBVyxDQUFDVDtZQUMxQkEsRUFBRVUsS0FBSztZQUNQVCxTQUFTTyxJQUFJLENBQUNHLFdBQVcsQ0FBQ1g7WUFDMUJGLElBQUljLGVBQWUsQ0FBQ2Y7UUFDdEI7dURBQUc7UUFBQ3pDO0tBQVM7SUFFYixVQUFVO0lBQ1Y1QyxnREFBU0E7c0NBQUM7WUFDUixNQUFNcUc7c0VBQTBCLENBQUNDO29CQUMvQixJQUFJQSxNQUFNbEIsSUFBSSxLQUFLLHdCQUF3Qjt3QkFDekNoQztvQkFDRjtnQkFDRjs7WUFFQXFDLFNBQVNjLGdCQUFnQixDQUFDLHdCQUF3QkY7WUFDbERaLFNBQVNjLGdCQUFnQixDQUFDLHFCQUFxQkY7WUFFL0M7OENBQU87b0JBQ0xaLFNBQVNlLG1CQUFtQixDQUFDLHdCQUF3Qkg7b0JBQ3JEWixTQUFTZSxtQkFBbUIsQ0FBQyxxQkFBcUJIO2dCQUNwRDs7UUFDRjtxQ0FBRztRQUFDakQ7S0FBYztJQUVsQixxQkFDRSw4REFBQ3FEO1FBQUk5RSxXQUFXLDRDQUFzRCxPQUFWQTs7WUFFekRHLDZCQUNDLDhEQUFDMkU7Z0JBQUk5RSxXQUFVOztrQ0FDYiw4REFBQzhFO3dCQUFJOUUsV0FBVTtrQ0FDYiw0RUFBQytFOzRCQUFHL0UsV0FBVTtzQ0FBc0Q7Ozs7Ozs7Ozs7O2tDQUV0RSw4REFBQzhFO3dCQUFJOUUsV0FBVTs7MENBQ2IsOERBQUNmLHlEQUFNQTtnQ0FDTCtGLFNBQVNqRSxTQUFTLFNBQVMsY0FBYztnQ0FDekNrRSxNQUFLO2dDQUNMQyxTQUFTLElBQU1sRSxRQUFROztrREFFdkIsOERBQUM3QixrR0FBSUE7d0NBQUNhLFdBQVU7Ozs7OztvQ0FBaUI7Ozs7Ozs7MENBRW5DLDhEQUFDZix5REFBTUE7Z0NBQ0wrRixTQUFTakUsU0FBUyxZQUFZLGNBQWM7Z0NBQzVDa0UsTUFBSztnQ0FDTEMsU0FBUyxJQUFNbEUsUUFBUTs7a0RBRXZCLDhEQUFDOUIsbUdBQUdBO3dDQUFDYyxXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7OzBDQUVsQyw4REFBQ2YseURBQU1BO2dDQUNMK0YsU0FBU2pFLFNBQVMsVUFBVSxjQUFjO2dDQUMxQ2tFLE1BQUs7Z0NBQ0xDLFNBQVMsSUFBTWxFLFFBQVE7O2tEQUV2Qiw4REFBQzVCLG1HQUFJQTt3Q0FBQ1ksV0FBVTs7Ozs7O29DQUFpQjs7Ozs7OzswQ0FFbkMsOERBQUM4RTtnQ0FBSTlFLFdBQVU7Ozs7OzswQ0FDZiw4REFBQ2YseURBQU1BO2dDQUNMK0YsU0FBUTtnQ0FDUkMsTUFBSztnQ0FDTEMsU0FBUzVCO2dDQUNUNkIsVUFBVSxDQUFDbEU7O2tEQUVYLDhEQUFDNUIsbUdBQVFBO3dDQUFDVyxXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU83Qyw4REFBQzhFO2dCQUFJOUUsV0FBVTs7b0JBRVhlLENBQUFBLFNBQVMsVUFBVUEsU0FBUyxPQUFNLG1CQUNsQyw4REFBQytEO3dCQUFJOUUsV0FBVyxHQUF5QyxPQUF0Q2UsU0FBUyxVQUFVLFVBQVUsVUFBUztrQ0FDdkQsNEVBQUN6Qyw4Q0FBVUE7c0NBQ1QsNEVBQUNDLGlEQUFhQTtnQ0FDWjZHLG1CQUFtQjtnQ0FDbkJ0RixnQkFBZ0JBO2dDQUNoQnVGLFlBQVk3RiwyREFBa0JBO2dDQUM5QlEsV0FBVTtnQ0FDVnNGLGFBQWE7b0NBQ1hDLGlCQUFpQjt3Q0FDZkMsU0FBUyxDQUFDQyxPQUFPZCxRQUFVOUYsK0RBQXVCQSxDQUFDOEY7b0NBQ3JEO29DQUNBZSxhQUFhLENBQUNDLE1BQU1oQixRQUNsQjVGLHdEQUFnQkEsQ0FBQzRHLE1BQU1oQixPQUFPbEYsaURBQVFBO29DQUN4Q21HLFlBQVksQ0FBQ0QsTUFBTWhCLE9BQU9rQixRQUFRQyxRQUNoQ2hILHVEQUFlQSxDQUFDNkcsTUFBTWhCLE9BQU9tQixPQUFPckcsaURBQVFBO29DQUM5Q3NHLFlBQVk7d0NBQ1ZDLE9BQU87d0NBQ1Asb0JBQW9Cakc7b0NBQ3RCO2dDQUNGO2dDQUNBa0csVUFBVTt3Q0FBQyxFQUFFNUYsTUFBTSxFQUFFO29DQUNuQkMsVUFBVUQ7Z0NBQ1o7Z0NBQ0E2RixVQUFVO3dDQUFDLEVBQUU3RixNQUFNLEVBQUU7b0NBQ25CYyxnQkFBZ0JkO2dDQUNsQjs7a0RBR0EsOERBQUM3QixpREFBYUE7d0NBQUN3QixXQUFVOzswREFDdkIsOERBQUN2QixzREFBa0JBO2dEQUFDdUIsV0FBVTswREFBd0M7Ozs7OzswREFHdEUsOERBQUNyQixxREFBaUJBOzBEQUNmaUIsMkRBQWVBLENBQUN1RyxHQUFHLENBQUMsQ0FBQ0MscUJBQ3BCLDhEQUFDMUgscURBQWlCQTt3REFDaEIySCxPQUFPRCxLQUFLRSxLQUFLO3dEQUNqQkMsV0FBVyxDQUFDQztnRUFBUUo7b0VBQUFBLGdCQUFBQSxLQUFLSyxPQUFPLGNBQVpMLG9DQUFBQSxtQkFBQUEsTUFBZUk7O3dEQUNuQ3hHLFdBQVU7OzBFQUdWLDhEQUFDOEU7Z0VBQUk5RSxXQUFVOzBFQUNab0csS0FBS00sSUFBSTs7Ozs7OzBFQUVaLDhEQUFDNUI7O2tGQUNDLDhEQUFDNkI7d0VBQUUzRyxXQUFVO2tGQUFlb0csS0FBS0UsS0FBSzs7Ozs7O2tGQUN0Qyw4REFBQ0s7d0VBQUUzRyxXQUFVO2tGQUNWb0csS0FBS1EsV0FBVzs7Ozs7Ozs7Ozs7Ozt1REFSaEJSLEtBQUtFLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBaUJ2Qiw4REFBQzVHLGtEQUFTQTt3Q0FDUlcsUUFBUUE7d0NBQ1J3RyxXQUFXcEY7d0NBQ1haLFdBQVdBOzs7Ozs7a0RBSWIsOERBQUNqQyxnREFBWUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkFPbkJtQyxDQUFBQSxTQUFTLGFBQWFBLFNBQVMsT0FBTSxtQkFDckMsOERBQUMrRDt3QkFBSTlFLFdBQVcsR0FBdUYsT0FBcEZlLFNBQVMsVUFBVSx3REFBd0QsVUFBUztrQ0FDckcsNEVBQUMrRDs0QkFBSTlFLFdBQVU7c0NBQ1ppQix5QkFDQyw4REFBQzNCLHFEQUFhQTtnQ0FBQ3dILGVBQWU7b0NBQUN2SCxtREFBU0E7aUNBQUM7MENBQ3RDMEI7Ozs7O3FEQUdILDhEQUFDNkQ7Z0NBQUk5RSxXQUFVOzBDQUEwQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVVsRU8sMEJBQ0MsOERBQUNaLHNEQUFXQTtnQkFDVmMsY0FBY0E7Z0JBQ2RzRyxZQUFZcEc7Z0JBQ1pFLFdBQVdBO2dCQUNYbUcsWUFBWWhGO2dCQUNaaUYsVUFBVW5FO2dCQUNWb0UsV0FBVzlEO2dCQUNYK0QsU0FBUyxJQUFNM0csWUFBWTs7Ozs7Ozs7Ozs7O0FBS3JDO0dBdlJTWDs7UUFtQmlCYiw4REFBb0JBOzs7S0FuQnJDYTtBQXlSVCxxQkFBcUI7QUFDZCxTQUFTdUgsU0FBU0MsS0FBb0I7O0lBQzNDLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHcEosK0NBQVFBLENBQUM7SUFFM0NFLGdEQUFTQTs4QkFBQztZQUNSa0osYUFBYTtRQUNmOzZCQUFHLEVBQUU7SUFFTCxJQUFJLENBQUNELFdBQVc7UUFDZCxxQkFDRSw4REFBQ3hDO1lBQUk5RSxXQUFXLHVCQUE2QyxPQUF0QnFILE1BQU1ySCxTQUFTLElBQUk7c0JBQ3hELDRFQUFDOEU7Z0JBQUk5RSxXQUFVOzBCQUNiLDRFQUFDOEU7b0JBQUk5RSxXQUFVOzhCQUFtQzs7Ozs7Ozs7Ozs7Ozs7OztJQUkxRDtJQUVBLHFCQUFPLDhEQUFDSDtRQUFrQixHQUFHd0gsS0FBSzs7Ozs7O0FBQ3BDO0lBbEJnQkQ7TUFBQUEiLCJzb3VyY2VzIjpbIi9ob21lL211emkvZ2l0aHViL2RlZXItZmxvdy9haS1lZGl0b3Itc3RhbmRhbG9uZS9zcmMvY29tcG9uZW50cy9haS1lZGl0b3IvaW5kZXgudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VDYWxsYmFjaywgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7XG4gIEVkaXRvclJvb3QsXG4gIEVkaXRvckNvbnRlbnQsXG4gIEVkaXRvckNvbW1hbmQsXG4gIEVkaXRvckNvbW1hbmRFbXB0eSxcbiAgRWRpdG9yQ29tbWFuZEl0ZW0sXG4gIEVkaXRvckNvbW1hbmRMaXN0LFxuICBJbWFnZVJlc2l6ZXIsXG4gIHR5cGUgRWRpdG9ySW5zdGFuY2UsXG4gIHR5cGUgSlNPTkNvbnRlbnQsXG4gIGhhbmRsZUNvbW1hbmROYXZpZ2F0aW9uLFxuICBoYW5kbGVJbWFnZURyb3AsXG4gIGhhbmRsZUltYWdlUGFzdGUsXG59IGZyb20gXCJub3ZlbFwiXG5pbXBvcnQgeyB1c2VEZWJvdW5jZWRDYWxsYmFjayB9IGZyb20gXCJ1c2UtZGVib3VuY2VcIlxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIlxuaW1wb3J0IHsgRXllLCBFZGl0LCBDb2RlLCBEb3dubG9hZCB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxuaW1wb3J0IFJlYWN0TWFya2Rvd24gZnJvbSBcInJlYWN0LW1hcmtkb3duXCJcbmltcG9ydCByZW1hcmtHZm0gZnJvbSBcInJlbWFyay1nZm1cIlxuXG4vLyDlr7zlhaXmiJHku6znmoTmianlsZXlkoznu4Tku7ZcbmltcG9ydCB7IGFpRWRpdG9yRXh0ZW5zaW9ucywgdXBsb2FkRm4gfSBmcm9tIFwiLi9leHRlbnNpb25zXCJcbmltcG9ydCB7IEFJVG9vbGJhciB9IGZyb20gXCIuL2FpLXRvb2xiYXJcIlxuaW1wb3J0IHsgQUlBc3Npc3RhbnQgfSBmcm9tIFwiLi9haS1hc3Npc3RhbnRcIlxuaW1wb3J0IHsgc3VnZ2VzdGlvbkl0ZW1zIH0gZnJvbSBcIi4vc2xhc2gtY29tbWFuZFwiXG5pbXBvcnQgXCIuL2FpLWVkaXRvci5jc3NcIlxuXG5pbnRlcmZhY2UgQUlFZGl0b3JQcm9wcyB7XG4gIGluaXRpYWxDb250ZW50PzogSlNPTkNvbnRlbnRcbiAgcGxhY2Vob2xkZXI/OiBzdHJpbmdcbiAgY2xhc3NOYW1lPzogc3RyaW5nXG4gIG9uQ29udGVudENoYW5nZT86IChjb250ZW50OiBKU09OQ29udGVudCkgPT4gdm9pZFxuICBvbk1hcmtkb3duQ2hhbmdlPzogKG1hcmtkb3duOiBzdHJpbmcpID0+IHZvaWRcbiAgc2hvd1Rvb2xiYXI/OiBib29sZWFuXG4gIGRlZmF1bHRNb2RlPzogXCJlZGl0XCIgfCBcInByZXZpZXdcIlxufVxuXG4vLyDlhoXpg6jnvJbovpHlmajnu4Tku7bvvIzkuI3kvb/nlKhTU1JcbmZ1bmN0aW9uIEFJRWRpdG9ySW50ZXJuYWwoe1xuICBpbml0aWFsQ29udGVudCxcbiAgcGxhY2Vob2xkZXIgPSBcIuW8gOWni+WGmeS9nC4uLlwiLFxuICBjbGFzc05hbWUgPSBcIlwiLFxuICBvbkNvbnRlbnRDaGFuZ2UsXG4gIG9uTWFya2Rvd25DaGFuZ2UsXG4gIHNob3dUb29sYmFyID0gdHJ1ZSxcbiAgZGVmYXVsdE1vZGUgPSBcImVkaXRcIixcbn06IEFJRWRpdG9yUHJvcHMpIHtcbiAgLy8g57yW6L6R5Zmo54q25oCBXG4gIGNvbnN0IFtlZGl0b3IsIHNldEVkaXRvcl0gPSB1c2VTdGF0ZTxFZGl0b3JJbnN0YW5jZSB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtpc0FJT3Blbiwgc2V0SXNBSU9wZW5dID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtzZWxlY3RlZFRleHQsIHNldFNlbGVjdGVkVGV4dF0gPSB1c2VTdGF0ZShcIlwiKVxuICBjb25zdCBbYWlTdWdnZXN0aW9uLCBzZXRBSVN1Z2dlc3Rpb25dID0gdXNlU3RhdGUoXCJcIilcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbbW9kZSwgc2V0TW9kZV0gPSB1c2VTdGF0ZTxcImVkaXRcIiB8IFwicHJldmlld1wiPihkZWZhdWx0TW9kZSlcbiAgY29uc3QgW21hcmtkb3duLCBzZXRNYXJrZG93bl0gPSB1c2VTdGF0ZShcIlwiKVxuXG4gIC8vIOmYsuaKluabtOaWsOWHveaVsFxuICBjb25zdCBkZWJvdW5jZWRVcGRhdGUgPSB1c2VEZWJvdW5jZWRDYWxsYmFjaygoZWRpdG9yOiBFZGl0b3JJbnN0YW5jZSkgPT4ge1xuICAgIGNvbnN0IGpzb24gPSBlZGl0b3IuZ2V0SlNPTigpXG4gICAgb25Db250ZW50Q2hhbmdlPy4oanNvbilcblxuICAgIGNvbnN0IG1hcmtkb3duQ29udGVudCA9IGVkaXRvci5zdG9yYWdlLm1hcmtkb3duLmdldE1hcmtkb3duKClcbiAgICBzZXRNYXJrZG93bihtYXJrZG93bkNvbnRlbnQpXG5cbiAgICBpZiAob25NYXJrZG93bkNoYW5nZSkge1xuICAgICAgb25NYXJrZG93bkNoYW5nZShtYXJrZG93bkNvbnRlbnQpXG4gICAgfVxuICB9LCAzMDApXG5cbiAgLy8gQUkg5Yqf6IO95aSE55CG5Ye95pWwXG4gIGNvbnN0IGhhbmRsZUFJQ2xpY2sgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKCFlZGl0b3IpIHJldHVyblxuXG4gICAgY29uc3QgeyBzZWxlY3Rpb24gfSA9IGVkaXRvci5zdGF0ZVxuICAgIGNvbnN0IHNlbGVjdGVkVGV4dCA9IGVkaXRvci5zdGF0ZS5kb2MudGV4dEJldHdlZW4oc2VsZWN0aW9uLmZyb20sIHNlbGVjdGlvbi50bylcblxuICAgIHNldFNlbGVjdGVkVGV4dChzZWxlY3RlZFRleHQpXG4gICAgc2V0SXNBSU9wZW4odHJ1ZSlcbiAgfSwgW2VkaXRvcl0pXG5cbiAgLy8g5qih5oufIEFJIOeUn+aIkOaWh+acrFxuICBjb25zdCBnZW5lcmF0ZUFJVGV4dCA9IHVzZUNhbGxiYWNrKGFzeW5jIChwcm9tcHQ6IHN0cmluZykgPT4ge1xuICAgIHNldElzTG9hZGluZyh0cnVlKVxuICAgIHNldEFJU3VnZ2VzdGlvbihcIlwiKVxuXG4gICAgdHJ5IHtcbiAgICAgIC8vIOaooeaLnyBBUEkg6LCD55So5bu26L+fXG4gICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTUwMCkpXG5cbiAgICAgIC8vIOagueaNruaPkOekuuivjeeUn+aIkOS4jeWQjOeahOWTjeW6lFxuICAgICAgbGV0IHJlc3BvbnNlID0gXCJcIlxuICAgICAgaWYgKHByb21wdC5pbmNsdWRlcyhcIuaUuei/m1wiKSkge1xuICAgICAgICByZXNwb25zZSA9IFwi6L+Z5piv5LiA5Liq5pS56L+b5ZCO55qE5paH5pys54mI5pys77yM5pu05Yqg5riF5pmw5ZKM5pyJ6K+05pyN5Yqb44CCXCJcbiAgICAgIH0gZWxzZSBpZiAocHJvbXB0LmluY2x1ZGVzKFwi5omp5bGVXCIpKSB7XG4gICAgICAgIHJlc3BvbnNlID0gXCLnu4/ov4fkvJjljJbnmoTlhoXlrrnvvIzlop7liqDkuobmm7TlpJrnu4boioLlkozlhbfkvZPkvovlrZDjgIJcIlxuICAgICAgfSBlbHNlIGlmIChwcm9tcHQuaW5jbHVkZXMoXCLmgLvnu5NcIikpIHtcbiAgICAgICAgcmVzcG9uc2UgPSBcIumHjeaWsOe7hOe7h+eahOauteiQvee7k+aehO+8jOmAu+i+keabtOWKoOa4heaZsOOAglwiXG4gICAgICB9IGVsc2UgaWYgKHByb21wdC5pbmNsdWRlcyhcIuS/ruato1wiKSkge1xuICAgICAgICByZXNwb25zZSA9IFwi5L+u5q2j5LqG6K+t5rOV6Zeu6aKY77yM6KGo6L6+5pu05Yqg5YeG56Gu44CCXCJcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIOm7mOiupOWTjeW6lFxuICAgICAgICBjb25zdCByZXNwb25zZXMgPSBbXG4gICAgICAgICAgXCLov5nmmK/kuIDkuKrmlLnov5vlkI7nmoTmlofmnKzniYjmnKzvvIzmm7TliqDmuIXmmbDlkozmnInor7TmnI3lipvjgIJcIixcbiAgICAgICAgICBcIue7j+i/h+S8mOWMlueahOWGheWuue+8jOWinuWKoOS6huabtOWkmue7huiKguWSjOWFt+S9k+S+i+WtkOOAglwiLFxuICAgICAgICAgIFwi6YeN5paw57uE57uH55qE5q616JC957uT5p6E77yM6YC76L6R5pu05Yqg5riF5pmw44CCXCIsXG4gICAgICAgICAgXCLkv67mraPkuobor63ms5Xpl67popjvvIzooajovr7mm7TliqDlh4bnoa7jgIJcIlxuICAgICAgICBdXG4gICAgICAgIHJlc3BvbnNlID0gcmVzcG9uc2VzW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIHJlc3BvbnNlcy5sZW5ndGgpXVxuICAgICAgfVxuXG4gICAgICBzZXRBSVN1Z2dlc3Rpb24ocmVzcG9uc2UpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJBSSDnlJ/miJDlpLHotKU6XCIsIGVycm9yKVxuICAgICAgc2V0QUlTdWdnZXN0aW9uKFwi5oqx5q2J77yMQUkg55Sf5oiQ5aSx6LSl77yM6K+356iN5ZCO6YeN6K+V44CCXCIpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH0sIFtdKVxuXG4gIC8vIOaPkuWFpSBBSSDnlJ/miJDnmoTmlofmnKxcbiAgY29uc3QgaW5zZXJ0QUlUZXh0ID0gdXNlQ2FsbGJhY2soKHRleHQ6IHN0cmluZykgPT4ge1xuICAgIGlmICghZWRpdG9yKSByZXR1cm5cblxuICAgIGVkaXRvci5jaGFpbigpLmZvY3VzKCkuaW5zZXJ0Q29udGVudCh0ZXh0KS5ydW4oKVxuICAgIHNldElzQUlPcGVuKGZhbHNlKVxuICAgIHNldEFJU3VnZ2VzdGlvbihcIlwiKVxuICB9LCBbZWRpdG9yXSlcblxuICAvLyDmm7/mjaLpgInkuK3nmoTmlofmnKxcbiAgY29uc3QgcmVwbGFjZVNlbGVjdGVkVGV4dCA9IHVzZUNhbGxiYWNrKCh0ZXh0OiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIWVkaXRvcikgcmV0dXJuXG5cbiAgICBjb25zdCB7IHNlbGVjdGlvbiB9ID0gZWRpdG9yLnN0YXRlXG4gICAgZWRpdG9yLmNoYWluKCkuZm9jdXMoKS5kZWxldGVSYW5nZSh7IGZyb206IHNlbGVjdGlvbi5mcm9tLCB0bzogc2VsZWN0aW9uLnRvIH0pLmluc2VydENvbnRlbnQodGV4dCkucnVuKClcbiAgICBzZXRJc0FJT3BlbihmYWxzZSlcbiAgICBzZXRBSVN1Z2dlc3Rpb24oXCJcIilcbiAgfSwgW2VkaXRvcl0pXG5cbiAgLy8g5a+85Ye6TWFya2Rvd27mlofku7ZcbiAgY29uc3QgZXhwb3J0TWFya2Rvd24gPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKCFtYXJrZG93bikgcmV0dXJuXG5cbiAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW21hcmtkb3duXSwgeyB0eXBlOiAndGV4dC9tYXJrZG93bicgfSlcbiAgICBjb25zdCB1cmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpXG4gICAgY29uc3QgYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKVxuICAgIGEuaHJlZiA9IHVybFxuICAgIGEuZG93bmxvYWQgPSBgZG9jdW1lbnQtJHtuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXX0ubWRgXG4gICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChhKVxuICAgIGEuY2xpY2soKVxuICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQoYSlcbiAgICBVUkwucmV2b2tlT2JqZWN0VVJMKHVybClcbiAgfSwgW21hcmtkb3duXSlcblxuICAvLyDnm5HlkKzplK7nm5jlv6vmjbfplK5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBoYW5kbGVLZXlib2FyZFNob3J0Y3V0cyA9IChldmVudDogQ3VzdG9tRXZlbnQpID0+IHtcbiAgICAgIGlmIChldmVudC50eXBlID09PSBcImFpLWFzc2lzdGFudC10cmlnZ2VyXCIpIHtcbiAgICAgICAgaGFuZGxlQUlDbGljaygpXG4gICAgICB9XG4gICAgfVxuXG4gICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcihcImFpLWFzc2lzdGFudC10cmlnZ2VyXCIsIGhhbmRsZUtleWJvYXJkU2hvcnRjdXRzIGFzIEV2ZW50TGlzdGVuZXIpXG4gICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcihcImFpLXF1aWNrLWdlbmVyYXRlXCIsIGhhbmRsZUtleWJvYXJkU2hvcnRjdXRzIGFzIEV2ZW50TGlzdGVuZXIpXG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcihcImFpLWFzc2lzdGFudC10cmlnZ2VyXCIsIGhhbmRsZUtleWJvYXJkU2hvcnRjdXRzIGFzIEV2ZW50TGlzdGVuZXIpXG4gICAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKFwiYWktcXVpY2stZ2VuZXJhdGVcIiwgaGFuZGxlS2V5Ym9hcmRTaG9ydGN1dHMgYXMgRXZlbnRMaXN0ZW5lcilcbiAgICB9XG4gIH0sIFtoYW5kbGVBSUNsaWNrXSlcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgYWktZWRpdG9yLWNvbnRhaW5lciBoLWZ1bGwgZmxleCBmbGV4LWNvbCAke2NsYXNzTmFtZX1gfT5cbiAgICAgIHsvKiDlt6XlhbfmoI8gKi99XG4gICAgICB7c2hvd1Rvb2xiYXIgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTQgYm9yZGVyLWIgYmctZ3JheS01MCBkYXJrOmJnLWdyYXktODAwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5BSSDnvJbovpHlmag8L2gyPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xXCI+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHZhcmlhbnQ9e21vZGUgPT09IFwiZWRpdFwiID8gXCJzZWNvbmRhcnlcIiA6IFwiZ2hvc3RcIn1cbiAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0TW9kZShcImVkaXRcIil9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxFZGl0IGNsYXNzTmFtZT1cInctNCBoLTQgbXItMVwiIC8+IOe8lui+kVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHZhcmlhbnQ9e21vZGUgPT09IFwicHJldmlld1wiID8gXCJzZWNvbmRhcnlcIiA6IFwiZ2hvc3RcIn1cbiAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0TW9kZShcInByZXZpZXdcIil9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxFeWUgY2xhc3NOYW1lPVwidy00IGgtNCBtci0xXCIgLz4g6aKE6KeIXG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgdmFyaWFudD17bW9kZSA9PT0gXCJzcGxpdFwiID8gXCJzZWNvbmRhcnlcIiA6IFwiZ2hvc3RcIn1cbiAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0TW9kZShcInNwbGl0XCIpfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8Q29kZSBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTFcIiAvPiDliIblibJcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LXB4IGgtNiBiZy1ncmF5LTMwMCBkYXJrOmJnLWdyYXktNjAwIG14LTJcIiAvPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICBvbkNsaWNrPXtleHBvcnRNYXJrZG93bn1cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFtYXJrZG93bn1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPERvd25sb2FkIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMVwiIC8+IOWvvOWHulxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIOe8lui+keWZqOWSjOmihOiniOWMuuWfnyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgIHsvKiDnvJbovpHlmajljLrln58gKi99XG4gICAgICAgIHsobW9kZSA9PT0gXCJlZGl0XCIgfHwgbW9kZSA9PT0gXCJzcGxpdFwiKSAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2Ake21vZGUgPT09IFwic3BsaXRcIiA/IFwidy0xLzJcIiA6IFwidy1mdWxsXCJ9IGZsZXggZmxleC1jb2xgfT5cbiAgICAgICAgICAgIDxFZGl0b3JSb290PlxuICAgICAgICAgICAgICA8RWRpdG9yQ29udGVudFxuICAgICAgICAgICAgICAgIGltbWVkaWF0ZWx5UmVuZGVyPXtmYWxzZX1cbiAgICAgICAgICAgICAgICBpbml0aWFsQ29udGVudD17aW5pdGlhbENvbnRlbnR9XG4gICAgICAgICAgICAgICAgZXh0ZW5zaW9ucz17YWlFZGl0b3JFeHRlbnNpb25zfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtZnVsbCB3LWZ1bGxcIlxuICAgICAgICAgICAgICAgIGVkaXRvclByb3BzPXt7XG4gICAgICAgICAgICAgICAgICBoYW5kbGVET01FdmVudHM6IHtcbiAgICAgICAgICAgICAgICAgICAga2V5ZG93bjogKF92aWV3LCBldmVudCkgPT4gaGFuZGxlQ29tbWFuZE5hdmlnYXRpb24oZXZlbnQpLFxuICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgIGhhbmRsZVBhc3RlOiAodmlldywgZXZlbnQpID0+XG4gICAgICAgICAgICAgICAgICAgIGhhbmRsZUltYWdlUGFzdGUodmlldywgZXZlbnQsIHVwbG9hZEZuKSxcbiAgICAgICAgICAgICAgICAgIGhhbmRsZURyb3A6ICh2aWV3LCBldmVudCwgX3NsaWNlLCBtb3ZlZCkgPT5cbiAgICAgICAgICAgICAgICAgICAgaGFuZGxlSW1hZ2VEcm9wKHZpZXcsIGV2ZW50LCBtb3ZlZCwgdXBsb2FkRm4pLFxuICAgICAgICAgICAgICAgICAgYXR0cmlidXRlczoge1xuICAgICAgICAgICAgICAgICAgICBjbGFzczogXCJwcm9zZSBwcm9zZS1iYXNlIGRhcms6cHJvc2UtaW52ZXJ0IG1heC13LW5vbmUgcC00IGZvY3VzOm91dGxpbmUtbm9uZSBoLWZ1bGwgb3ZlcmZsb3ctYXV0b1wiLFxuICAgICAgICAgICAgICAgICAgICBcImRhdGEtcGxhY2Vob2xkZXJcIjogcGxhY2Vob2xkZXIsXG4gICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgb25DcmVhdGU9eyh7IGVkaXRvciB9KSA9PiB7XG4gICAgICAgICAgICAgICAgICBzZXRFZGl0b3IoZWRpdG9yKVxuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgb25VcGRhdGU9eyh7IGVkaXRvciB9KSA9PiB7XG4gICAgICAgICAgICAgICAgICBkZWJvdW5jZWRVcGRhdGUoZWRpdG9yKVxuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7LyogU2xhc2gg5ZG95Luk6I+c5Y2VICovfVxuICAgICAgICAgICAgICAgIDxFZGl0b3JDb21tYW5kIGNsYXNzTmFtZT1cInotNTAgaC1hdXRvIG1heC1oLVszMzBweF0gb3ZlcmZsb3cteS1hdXRvIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBkYXJrOmJvcmRlci1ncmF5LTcwMCBiZy13aGl0ZSBkYXJrOmJnLWdyYXktODAwIHB4LTEgcHktMiBzaGFkb3ctbWQgdHJhbnNpdGlvbi1hbGxcIj5cbiAgICAgICAgICAgICAgICAgIDxFZGl0b3JDb21tYW5kRW1wdHkgY2xhc3NOYW1lPVwicHgtMiB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICDmsqHmnInmib7liLDnu5PmnpxcbiAgICAgICAgICAgICAgICAgIDwvRWRpdG9yQ29tbWFuZEVtcHR5PlxuICAgICAgICAgICAgICAgICAgPEVkaXRvckNvbW1hbmRMaXN0PlxuICAgICAgICAgICAgICAgICAgICB7c3VnZ2VzdGlvbkl0ZW1zLm1hcCgoaXRlbSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxFZGl0b3JDb21tYW5kSXRlbVxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2l0ZW0udGl0bGV9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNvbW1hbmQ9eyh2YWwpID0+IGl0ZW0uY29tbWFuZD8uKHZhbCl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IHctZnVsbCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHJvdW5kZWQtbWQgcHgtMiBweS0xIHRleHQtbGVmdCB0ZXh0LXNtIGhvdmVyOmJnLWdyYXktMTAwIGRhcms6aG92ZXI6YmctZ3JheS03MDAgYXJpYS1zZWxlY3RlZDpiZy1ncmF5LTEwMCBkYXJrOmFyaWEtc2VsZWN0ZWQ6YmctZ3JheS03MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtpdGVtLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLTEwIHctMTAgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBkYXJrOmJvcmRlci1ncmF5LTcwMCBiZy13aGl0ZSBkYXJrOmJnLWdyYXktODAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLmljb259XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e2l0ZW0udGl0bGV9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0uZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvRWRpdG9yQ29tbWFuZEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC9FZGl0b3JDb21tYW5kTGlzdD5cbiAgICAgICAgICAgICAgICA8L0VkaXRvckNvbW1hbmQ+XG5cbiAgICAgICAgICAgICAgICB7LyogQUkg5bel5YW35qCPICovfVxuICAgICAgICAgICAgICAgIDxBSVRvb2xiYXJcbiAgICAgICAgICAgICAgICAgIGVkaXRvcj17ZWRpdG9yfVxuICAgICAgICAgICAgICAgICAgb25BSUNsaWNrPXtoYW5kbGVBSUNsaWNrfVxuICAgICAgICAgICAgICAgICAgaXNMb2FkaW5nPXtpc0xvYWRpbmd9XG4gICAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICAgIHsvKiDlm77niYfosIPmlbTlmaggKi99XG4gICAgICAgICAgICAgICAgPEltYWdlUmVzaXplciAvPlxuICAgICAgICAgICAgICA8L0VkaXRvckNvbnRlbnQ+XG4gICAgICAgICAgICA8L0VkaXRvclJvb3Q+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIOmihOiniOWMuuWfnyAqL31cbiAgICAgICAgeyhtb2RlID09PSBcInByZXZpZXdcIiB8fCBtb2RlID09PSBcInNwbGl0XCIpICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YCR7bW9kZSA9PT0gXCJzcGxpdFwiID8gXCJ3LTEvMiBib3JkZXItbCBib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS03MDBcIiA6IFwidy1mdWxsXCJ9IHAtNCBvdmVyZmxvdy1hdXRvIGJnLWdyYXktNTAgZGFyazpiZy1ncmF5LTgwMGB9PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwcm9zZSBwcm9zZS1zbSBtYXgtdy1ub25lIGRhcms6cHJvc2UtaW52ZXJ0XCI+XG4gICAgICAgICAgICAgIHttYXJrZG93biA/IChcbiAgICAgICAgICAgICAgICA8UmVhY3RNYXJrZG93biByZW1hcmtQbHVnaW5zPXtbcmVtYXJrR2ZtXX0+XG4gICAgICAgICAgICAgICAgICB7bWFya2Rvd259XG4gICAgICAgICAgICAgICAgPC9SZWFjdE1hcmtkb3duPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDAgaXRhbGljXCI+XG4gICAgICAgICAgICAgICAgICDlvIDlp4vnvJbovpHku6Xmn6XnnIvpooTop4guLi5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBBSSDliqnmiYvpnaLmnb8gKi99XG4gICAgICB7aXNBSU9wZW4gJiYgKFxuICAgICAgICA8QUlBc3Npc3RhbnRcbiAgICAgICAgICBzZWxlY3RlZFRleHQ9e3NlbGVjdGVkVGV4dH1cbiAgICAgICAgICBzdWdnZXN0aW9uPXthaVN1Z2dlc3Rpb259XG4gICAgICAgICAgaXNMb2FkaW5nPXtpc0xvYWRpbmd9XG4gICAgICAgICAgb25HZW5lcmF0ZT17Z2VuZXJhdGVBSVRleHR9XG4gICAgICAgICAgb25JbnNlcnQ9e2luc2VydEFJVGV4dH1cbiAgICAgICAgICBvblJlcGxhY2U9e3JlcGxhY2VTZWxlY3RlZFRleHR9XG4gICAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0SXNBSU9wZW4oZmFsc2UpfVxuICAgICAgICAvPlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKVxufVxuXG4vLyDkvb/nlKjliqjmgIHlr7zlhaXnpoHnlKhTU1LnmoTkuLvopoHlr7zlh7rnu4Tku7ZcbmV4cG9ydCBmdW5jdGlvbiBBSUVkaXRvcihwcm9wczogQUlFZGl0b3JQcm9wcykge1xuICBjb25zdCBbaXNNb3VudGVkLCBzZXRJc01vdW50ZWRdID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzZXRJc01vdW50ZWQodHJ1ZSlcbiAgfSwgW10pXG5cbiAgaWYgKCFpc01vdW50ZWQpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9e2BhaS1lZGl0b3ItY29udGFpbmVyICR7cHJvcHMuY2xhc3NOYW1lIHx8IFwiXCJ9YH0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBtaW4taC1bNDAwcHhdIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJvcmRlciBib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS03MDAgcm91bmRlZC1sZyBiZy1ncmF5LTUwIGRhcms6YmctZ3JheS04MDBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+5Yqg6L2957yW6L6R5Zmo5LitLi4uPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIDxBSUVkaXRvckludGVybmFsIHsuLi5wcm9wc30gLz5cbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUNhbGxiYWNrIiwidXNlRWZmZWN0IiwiRWRpdG9yUm9vdCIsIkVkaXRvckNvbnRlbnQiLCJFZGl0b3JDb21tYW5kIiwiRWRpdG9yQ29tbWFuZEVtcHR5IiwiRWRpdG9yQ29tbWFuZEl0ZW0iLCJFZGl0b3JDb21tYW5kTGlzdCIsIkltYWdlUmVzaXplciIsImhhbmRsZUNvbW1hbmROYXZpZ2F0aW9uIiwiaGFuZGxlSW1hZ2VEcm9wIiwiaGFuZGxlSW1hZ2VQYXN0ZSIsInVzZURlYm91bmNlZENhbGxiYWNrIiwiQnV0dG9uIiwiRXllIiwiRWRpdCIsIkNvZGUiLCJEb3dubG9hZCIsIlJlYWN0TWFya2Rvd24iLCJyZW1hcmtHZm0iLCJhaUVkaXRvckV4dGVuc2lvbnMiLCJ1cGxvYWRGbiIsIkFJVG9vbGJhciIsIkFJQXNzaXN0YW50Iiwic3VnZ2VzdGlvbkl0ZW1zIiwiQUlFZGl0b3JJbnRlcm5hbCIsImluaXRpYWxDb250ZW50IiwicGxhY2Vob2xkZXIiLCJjbGFzc05hbWUiLCJvbkNvbnRlbnRDaGFuZ2UiLCJvbk1hcmtkb3duQ2hhbmdlIiwic2hvd1Rvb2xiYXIiLCJkZWZhdWx0TW9kZSIsImVkaXRvciIsInNldEVkaXRvciIsImlzQUlPcGVuIiwic2V0SXNBSU9wZW4iLCJzZWxlY3RlZFRleHQiLCJzZXRTZWxlY3RlZFRleHQiLCJhaVN1Z2dlc3Rpb24iLCJzZXRBSVN1Z2dlc3Rpb24iLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJtb2RlIiwic2V0TW9kZSIsIm1hcmtkb3duIiwic2V0TWFya2Rvd24iLCJkZWJvdW5jZWRVcGRhdGUiLCJqc29uIiwiZ2V0SlNPTiIsIm1hcmtkb3duQ29udGVudCIsInN0b3JhZ2UiLCJnZXRNYXJrZG93biIsImhhbmRsZUFJQ2xpY2siLCJzZWxlY3Rpb24iLCJzdGF0ZSIsImRvYyIsInRleHRCZXR3ZWVuIiwiZnJvbSIsInRvIiwiZ2VuZXJhdGVBSVRleHQiLCJwcm9tcHQiLCJQcm9taXNlIiwicmVzb2x2ZSIsInNldFRpbWVvdXQiLCJyZXNwb25zZSIsImluY2x1ZGVzIiwicmVzcG9uc2VzIiwiTWF0aCIsImZsb29yIiwicmFuZG9tIiwibGVuZ3RoIiwiZXJyb3IiLCJjb25zb2xlIiwiaW5zZXJ0QUlUZXh0IiwidGV4dCIsImNoYWluIiwiZm9jdXMiLCJpbnNlcnRDb250ZW50IiwicnVuIiwicmVwbGFjZVNlbGVjdGVkVGV4dCIsImRlbGV0ZVJhbmdlIiwiZXhwb3J0TWFya2Rvd24iLCJibG9iIiwiQmxvYiIsInR5cGUiLCJ1cmwiLCJVUkwiLCJjcmVhdGVPYmplY3RVUkwiLCJhIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwiaHJlZiIsImRvd25sb2FkIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwic3BsaXQiLCJib2R5IiwiYXBwZW5kQ2hpbGQiLCJjbGljayIsInJlbW92ZUNoaWxkIiwicmV2b2tlT2JqZWN0VVJMIiwiaGFuZGxlS2V5Ym9hcmRTaG9ydGN1dHMiLCJldmVudCIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiZGl2IiwiaDIiLCJ2YXJpYW50Iiwic2l6ZSIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsImltbWVkaWF0ZWx5UmVuZGVyIiwiZXh0ZW5zaW9ucyIsImVkaXRvclByb3BzIiwiaGFuZGxlRE9NRXZlbnRzIiwia2V5ZG93biIsIl92aWV3IiwiaGFuZGxlUGFzdGUiLCJ2aWV3IiwiaGFuZGxlRHJvcCIsIl9zbGljZSIsIm1vdmVkIiwiYXR0cmlidXRlcyIsImNsYXNzIiwib25DcmVhdGUiLCJvblVwZGF0ZSIsIm1hcCIsIml0ZW0iLCJ2YWx1ZSIsInRpdGxlIiwib25Db21tYW5kIiwidmFsIiwiY29tbWFuZCIsImljb24iLCJwIiwiZGVzY3JpcHRpb24iLCJvbkFJQ2xpY2siLCJyZW1hcmtQbHVnaW5zIiwic3VnZ2VzdGlvbiIsIm9uR2VuZXJhdGUiLCJvbkluc2VydCIsIm9uUmVwbGFjZSIsIm9uQ2xvc2UiLCJBSUVkaXRvciIsInByb3BzIiwiaXNNb3VudGVkIiwic2V0SXNNb3VudGVkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ai-editor/index.tsx\n"));

/***/ })

});