"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bold.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bold.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Bold)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.487.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M6 12h9a4 4 0 0 1 0 8H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h7a4 4 0 0 1 0 8\",\n            key: \"mg9rjx\"\n        }\n    ]\n];\nconst Bold = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"bold\", __iconNode);\n //# sourceMappingURL=bold.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bold.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Check)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.487.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M20 6 9 17l-5-5\",\n            key: \"1gmf2c\"\n        }\n    ]\n];\nconst Check = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"check\", __iconNode);\n //# sourceMappingURL=check.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9sdWNpZGUtcmVhY3RAMC40ODcuMF9yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9jaGVjay5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHYSxpQkFBdUI7SUFBQztRQUFDLE1BQVE7UUFBQSxDQUFFO1lBQUEsRUFBRyxrQkFBbUI7WUFBQSxLQUFLLENBQVM7UUFBQSxDQUFDO0tBQUM7Q0FBQTtBQWFoRixZQUFRLGtFQUFpQixVQUFTLENBQVUiLCJzb3VyY2VzIjpbIi9ob21lL211emkvc3JjL2ljb25zL2NoZWNrLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuaW1wb3J0IHsgSWNvbk5vZGUgfSBmcm9tICcuLi90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBfX2ljb25Ob2RlOiBJY29uTm9kZSA9IFtbJ3BhdGgnLCB7IGQ6ICdNMjAgNiA5IDE3bC01LTUnLCBrZXk6ICcxZ21mMmMnIH1dXTtcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIENoZWNrXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSk5NakFnTmlBNUlERTNiQzAxTFRVaUlDOCtDand2YzNablBnbz0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2NoZWNrXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQ2hlY2sgPSBjcmVhdGVMdWNpZGVJY29uKCdjaGVjaycsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgZGVmYXVsdCBDaGVjaztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/copy.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/copy.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Copy)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.487.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            width: \"14\",\n            height: \"14\",\n            x: \"8\",\n            y: \"8\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"17jyea\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2\",\n            key: \"zix9uf\"\n        }\n    ]\n];\nconst Copy = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"copy\", __iconNode);\n //# sourceMappingURL=copy.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/copy.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/italic.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/italic.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Italic)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.487.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"line\",\n        {\n            x1: \"19\",\n            x2: \"10\",\n            y1: \"4\",\n            y2: \"4\",\n            key: \"15jd3p\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"14\",\n            x2: \"5\",\n            y1: \"20\",\n            y2: \"20\",\n            key: \"bu0au3\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"15\",\n            x2: \"9\",\n            y1: \"4\",\n            y2: \"20\",\n            key: \"uljnxc\"\n        }\n    ]\n];\nconst Italic = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"italic\", __iconNode);\n //# sourceMappingURL=italic.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/italic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ LoaderCircle)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.487.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21 12a9 9 0 1 1-6.219-8.56\",\n            key: \"13zald\"\n        }\n    ]\n];\nconst LoaderCircle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"loader-circle\", __iconNode);\n //# sourceMappingURL=loader-circle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/pen-line.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/pen-line.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ PenLine)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.487.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12 20h9\",\n            key: \"t2du7b\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z\",\n            key: \"1ykcvy\"\n        }\n    ]\n];\nconst PenLine = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"pen-line\", __iconNode);\n //# sourceMappingURL=pen-line.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/pen-line.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Plus)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.487.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M5 12h14\",\n            key: \"1ays0h\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 5v14\",\n            key: \"s699le\"\n        }\n    ]\n];\nconst Plus = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"plus\", __iconNode);\n //# sourceMappingURL=plus.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/refresh-cw.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/refresh-cw.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ RefreshCw)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.487.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8\",\n            key: \"v9h5vc\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 3v5h-5\",\n            key: \"1q7to0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16\",\n            key: \"3uifl3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 16H3v5\",\n            key: \"1cv678\"\n        }\n    ]\n];\nconst RefreshCw = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"refresh-cw\", __iconNode);\n //# sourceMappingURL=refresh-cw.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/sparkles.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/sparkles.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Sparkles)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.487.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z\",\n            key: \"4pj2yx\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M20 3v4\",\n            key: \"1olli1\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M22 5h-4\",\n            key: \"1gvqau\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M4 17v2\",\n            key: \"vumght\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M5 18H3\",\n            key: \"zchphs\"\n        }\n    ]\n];\nconst Sparkles = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"sparkles\", __iconNode);\n //# sourceMappingURL=sparkles.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/sparkles.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/strikethrough.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/strikethrough.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Strikethrough)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.487.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M16 4H9a3 3 0 0 0-2.83 4\",\n            key: \"43sutm\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 12a4 4 0 0 1 0 8H6\",\n            key: \"nlfj13\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"4\",\n            x2: \"20\",\n            y1: \"12\",\n            y2: \"12\",\n            key: \"1e0a9i\"\n        }\n    ]\n];\nconst Strikethrough = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"strikethrough\", __iconNode);\n //# sourceMappingURL=strikethrough.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/strikethrough.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/underline.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/underline.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Underline)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.487.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M6 4v6a6 6 0 0 0 12 0V4\",\n            key: \"9kb039\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"4\",\n            x2: \"20\",\n            y1: \"20\",\n            y2: \"20\",\n            key: \"nun2al\"\n        }\n    ]\n];\nconst Underline = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"underline\", __iconNode);\n //# sourceMappingURL=underline.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/underline.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wand-sparkles.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wand-sparkles.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ WandSparkles)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.487.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72\",\n            key: \"ul74o6\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m14 7 3 3\",\n            key: \"1r5n42\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M5 6v4\",\n            key: \"ilb8ba\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M19 14v4\",\n            key: \"blhpug\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 2v2\",\n            key: \"7u0qdc\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M7 8H3\",\n            key: \"zfb6yr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 16h-4\",\n            key: \"1cnmox\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M11 3H9\",\n            key: \"1obp7u\"\n        }\n    ]\n];\nconst WandSparkles = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"wand-sparkles\", __iconNode);\n //# sourceMappingURL=wand-sparkles.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ X)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.487.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M18 6 6 18\",\n            key: \"1bl5f8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m6 6 12 12\",\n            key: \"d8bk6v\"\n        }\n    ]\n];\nconst X = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"x\", __iconNode);\n //# sourceMappingURL=x.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ai-editor/ai-assistant.tsx":
/*!***************************************************!*\
  !*** ./src/components/ai-editor/ai-assistant.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIAssistant: () => (/* binding */ AIAssistant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Edit3,Loader2,Plus,RefreshCw,Sparkles,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Edit3,Loader2,Plus,RefreshCw,Sparkles,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Edit3,Loader2,Plus,RefreshCw,Sparkles,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Edit3,Loader2,Plus,RefreshCw,Sparkles,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Edit3,Loader2,Plus,RefreshCw,Sparkles,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Edit3,Loader2,Plus,RefreshCw,Sparkles,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Edit3,Loader2,Plus,RefreshCw,Sparkles,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Edit3,Loader2,Plus,RefreshCw,Sparkles,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Edit3,Loader2,Plus,RefreshCw,Sparkles,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* __next_internal_client_entry_do_not_use__ AIAssistant auto */ \nvar _s = $RefreshSig$();\n\n\nconst AI_PROMPTS = [\n    {\n        label: \"改进文字\",\n        prompt: \"请帮我改进这段文字，让它更清晰、更有说服力\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n            lineNumber: 27,\n            columnNumber: 59\n        }, undefined)\n    },\n    {\n        label: \"扩展内容\",\n        prompt: \"请帮我扩展这段内容，添加更多细节和例子\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n            lineNumber: 28,\n            columnNumber: 57\n        }, undefined)\n    },\n    {\n        label: \"总结要点\",\n        prompt: \"请帮我总结这段文字的核心要点\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n            lineNumber: 29,\n            columnNumber: 52\n        }, undefined)\n    },\n    {\n        label: \"修正语法\",\n        prompt: \"请帮我检查并修正这段文字的语法和表达\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n            lineNumber: 30,\n            columnNumber: 56\n        }, undefined)\n    }\n];\nfunction AIAssistant(param) {\n    let { selectedText, suggestion, isLoading, onGenerate, onInsert, onReplace, onClose } = param;\n    _s();\n    const [customPrompt, setCustomPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showCustomInput, setShowCustomInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleQuickPrompt = async (prompt)=>{\n        if (selectedText) {\n            await onGenerate(\"\".concat(prompt, \"：\").concat(selectedText));\n        }\n    };\n    const handleCustomPrompt = async ()=>{\n        if (customPrompt.trim()) {\n            const fullPrompt = selectedText ? \"\".concat(customPrompt, \"：\").concat(selectedText) : customPrompt;\n            await onGenerate(fullPrompt);\n            setCustomPrompt(\"\");\n            setShowCustomInput(false);\n        }\n    };\n    const copyToClipboard = async (text)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n        } catch (error) {\n            console.error(\"复制失败:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/20 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-2xl max-h-[80vh] overflow-hidden bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-row items-center justify-between space-y-0 p-6 pb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"flex items-center space-x-2 text-lg font-semibold\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"AI 写作助手\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                    children: selectedText ? \"为选中的文字提供 AI 建议\" : \"使用 AI 生成内容\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 pb-6 space-y-4\",\n                    children: [\n                        selectedText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded\",\n                                            children: \"选中的文字\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>copyToClipboard(selectedText),\n                                            className: \"p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                    children: selectedText\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium\",\n                                    children: \"快速操作\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-2\",\n                                    children: AI_PROMPTS.map((prompt, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleQuickPrompt(prompt.prompt),\n                                            disabled: isLoading || !selectedText,\n                                            className: \"flex items-center justify-start p-2 text-sm border border-gray-200 dark:border-gray-700 rounded hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: [\n                                                prompt.icon,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2\",\n                                                    children: prompt.label\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"自定义指令\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCustomInput(!showCustomInput),\n                                            className: \"text-sm text-blue-600 dark:text-blue-400 hover:underline\",\n                                            children: showCustomInput ? \"收起\" : \"展开\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                showCustomInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            placeholder: \"输入你的自定义指令...\",\n                                            value: customPrompt,\n                                            onChange: (e)=>setCustomPrompt(e.target.value),\n                                            className: \"w-full min-h-[80px] p-2 border border-gray-200 dark:border-gray-700 rounded resize-none bg-white dark:bg-gray-800 text-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCustomPrompt,\n                                            disabled: isLoading || !customPrompt.trim(),\n                                            className: \"w-full flex items-center justify-center p-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: [\n                                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 animate-spin mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"生成内容\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this),\n                        (suggestion || isLoading) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1 text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"AI 建议\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, this),\n                                        suggestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>copyToClipboard(suggestion),\n                                            className: \"p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-blue-600 dark:text-blue-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: \"AI 正在思考中...\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-800 dark:text-blue-200\",\n                                        children: suggestion\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this),\n                                suggestion && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>selectedText ? onReplace(suggestion) : onInsert(suggestion),\n                                            className: \"flex-1 flex items-center justify-center p-2 bg-blue-600 text-white rounded hover:bg-blue-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 21\n                                                }, this),\n                                                selectedText ? \"替换文字\" : \"插入内容\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 19\n                                        }, this),\n                                        !selectedText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>onInsert(suggestion),\n                                            className: \"flex-1 flex items-center justify-center p-2 border border-gray-200 dark:border-gray-700 rounded hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"插入内容\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleQuickPrompt(\"请重新生成一个不同的版本\"),\n                                            className: \"p-2 border border-gray-200 dark:border-gray-700 rounded hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_s(AIAssistant, \"OM9s6K3/FJ23uiNTCCk7LtjQHZw=\");\n_c = AIAssistant;\nvar _c;\n$RefreshReg$(_c, \"AIAssistant\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ai-editor/ai-assistant.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ai-editor/ai-toolbar.tsx":
/*!*************************************************!*\
  !*** ./src/components/ai-editor/ai-toolbar.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIToolbar: () => (/* binding */ AIToolbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Heading1,Heading2,Italic,Loader2,Sparkles,Strikethrough,Underline!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bold.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Heading1,Heading2,Italic,Loader2,Sparkles,Strikethrough,Underline!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/italic.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Heading1,Heading2,Italic,Loader2,Sparkles,Strikethrough,Underline!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/underline.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Heading1,Heading2,Italic,Loader2,Sparkles,Strikethrough,Underline!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/strikethrough.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Heading1,Heading2,Italic,Loader2,Sparkles,Strikethrough,Underline!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/heading-1.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Heading1,Heading2,Italic,Loader2,Sparkles,Strikethrough,Underline!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/heading-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Heading1,Heading2,Italic,Loader2,Sparkles,Strikethrough,Underline!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Heading1,Heading2,Italic,Loader2,Sparkles,Strikethrough,Underline!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/novel@1.0.2_@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tipta_3fbtezt2z6la73kpow3ollimny/node_modules/novel/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ AIToolbar auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AIToolbar(param) {\n    let { editor, onAIClick, isLoading } = param;\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIToolbar.useEffect\": ()=>{\n            if (!editor) return;\n            const updateVisibility = {\n                \"AIToolbar.useEffect.updateVisibility\": ()=>{\n                    const { selection } = editor.state;\n                    const hasSelection = !selection.empty;\n                    setIsVisible(hasSelection);\n                }\n            }[\"AIToolbar.useEffect.updateVisibility\"];\n            // 监听选择变化\n            editor.on(\"selectionUpdate\", updateVisibility);\n            editor.on(\"transaction\", updateVisibility);\n            return ({\n                \"AIToolbar.useEffect\": ()=>{\n                    editor.off(\"selectionUpdate\", updateVisibility);\n                    editor.off(\"transaction\", updateVisibility);\n                }\n            })[\"AIToolbar.useEffect\"];\n        }\n    }[\"AIToolbar.useEffect\"], [\n        editor\n    ]);\n    if (!editor || !isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_2__.EditorBubble, {\n        tippyOptions: {\n            placement: \"top\",\n            duration: 100\n        },\n        className: \"ai-toolbar-bubble\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_2__.EditorBubbleItem, {\n                    onSelect: (editor)=>{\n                        editor.chain().focus().toggleBold().run();\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"px-2 py-1 text-sm rounded-none hover:bg-gray-100 dark:hover:bg-gray-700 \".concat(editor.isActive(\"bold\") ? \"bg-gray-100 dark:bg-gray-700\" : \"\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_2__.EditorBubbleItem, {\n                    onSelect: (editor)=>{\n                        editor.chain().focus().toggleItalic().run();\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"px-2 py-1 text-sm rounded-none hover:bg-gray-100 dark:hover:bg-gray-700 \".concat(editor.isActive(\"italic\") ? \"bg-gray-100 dark:bg-gray-700\" : \"\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_2__.EditorBubbleItem, {\n                    onSelect: (editor)=>{\n                        editor.chain().focus().toggleUnderline().run();\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"px-2 py-1 text-sm rounded-none hover:bg-gray-100 dark:hover:bg-gray-700 \".concat(editor.isActive(\"underline\") ? \"bg-gray-100 dark:bg-gray-700\" : \"\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_2__.EditorBubbleItem, {\n                    onSelect: (editor)=>{\n                        editor.chain().focus().toggleStrike().run();\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"px-2 py-1 text-sm rounded-none hover:bg-gray-100 dark:hover:bg-gray-700 \".concat(editor.isActive(\"strike\") ? \"bg-gray-100 dark:bg-gray-700\" : \"\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-px h-6 bg-gray-200 dark:bg-gray-700 mx-1\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_2__.EditorBubbleItem, {\n                    onSelect: (editor)=>{\n                        editor.chain().focus().toggleHeading({\n                            level: 1\n                        }).run();\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"px-2 py-1 text-sm rounded-none hover:bg-gray-100 dark:hover:bg-gray-700 \".concat(editor.isActive(\"heading\", {\n                            level: 1\n                        }) ? \"bg-gray-100 dark:bg-gray-700\" : \"\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_2__.EditorBubbleItem, {\n                    onSelect: (editor)=>{\n                        editor.chain().focus().toggleHeading({\n                            level: 2\n                        }).run();\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"px-2 py-1 text-sm rounded-none hover:bg-gray-100 dark:hover:bg-gray-700 \".concat(editor.isActive(\"heading\", {\n                            level: 2\n                        }) ? \"bg-gray-100 dark:bg-gray-700\" : \"\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-px h-6 bg-gray-200 dark:bg-gray-700 mx-1\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onAIClick,\n                    disabled: isLoading,\n                    className: \"px-2 py-1 text-sm rounded-none text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/20 disabled:opacity-50\",\n                    children: [\n                        isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-4 w-4 animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-1 text-xs\",\n                            children: \"AI\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_s(AIToolbar, \"J3yJOyGdBT4L7hs1p1XQYVGMdrY=\");\n_c = AIToolbar;\nvar _c;\n$RefreshReg$(_c, \"AIToolbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ai-editor/ai-toolbar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ai-editor/index.tsx":
/*!********************************************!*\
  !*** ./src/components/ai-editor/index.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIEditor: () => (/* binding */ AIEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/novel@1.0.2_@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tipta_3fbtezt2z6la73kpow3ollimny/node_modules/novel/dist/index.js\");\n/* harmony import */ var use_debounce__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! use-debounce */ \"(app-pages-browser)/./node_modules/.pnpm/use-debounce@10.0.5_react@18.3.1/node_modules/use-debounce/dist/index.module.js\");\n/* harmony import */ var _extensions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./extensions */ \"(app-pages-browser)/./src/components/ai-editor/extensions.ts\");\n/* harmony import */ var _ai_toolbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ai-toolbar */ \"(app-pages-browser)/./src/components/ai-editor/ai-toolbar.tsx\");\n/* harmony import */ var _ai_assistant__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ai-assistant */ \"(app-pages-browser)/./src/components/ai-editor/ai-assistant.tsx\");\n/* harmony import */ var _slash_command__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./slash-command */ \"(app-pages-browser)/./src/components/ai-editor/slash-command.tsx\");\n/* harmony import */ var _ai_editor_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ai-editor.css */ \"(app-pages-browser)/./src/components/ai-editor/ai-editor.css\");\n/* __next_internal_client_entry_do_not_use__ AIEditor auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// 导入我们的扩展和组件\n\n\n\n\n\n// 内部编辑器组件，不使用SSR\nfunction AIEditorInternal(param) {\n    let { initialContent, placeholder = \"开始写作...\", className = \"\", onContentChange, onMarkdownChange } = param;\n    _s();\n    // 编辑器状态\n    const [editor, setEditor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAIOpen, setIsAIOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedText, setSelectedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [aiSuggestion, setAISuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 防抖更新函数\n    const debouncedUpdate = (0,use_debounce__WEBPACK_IMPORTED_MODULE_7__.useDebouncedCallback)({\n        \"AIEditorInternal.useDebouncedCallback[debouncedUpdate]\": (editor)=>{\n            const json = editor.getJSON();\n            onContentChange === null || onContentChange === void 0 ? void 0 : onContentChange(json);\n            if (onMarkdownChange) {\n                const markdown = editor.storage.markdown.getMarkdown();\n                onMarkdownChange(markdown);\n            }\n        }\n    }[\"AIEditorInternal.useDebouncedCallback[debouncedUpdate]\"], 300);\n    // AI 功能处理函数\n    const handleAIClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[handleAIClick]\": ()=>{\n            if (!editor) return;\n            const { selection } = editor.state;\n            const selectedText = editor.state.doc.textBetween(selection.from, selection.to);\n            setSelectedText(selectedText);\n            setIsAIOpen(true);\n        }\n    }[\"AIEditorInternal.useCallback[handleAIClick]\"], [\n        editor\n    ]);\n    // 模拟 AI 生成文本\n    const generateAIText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[generateAIText]\": async (prompt)=>{\n            setIsLoading(true);\n            setAISuggestion(\"\");\n            try {\n                // 模拟 API 调用延迟\n                await new Promise({\n                    \"AIEditorInternal.useCallback[generateAIText]\": (resolve)=>setTimeout(resolve, 1500)\n                }[\"AIEditorInternal.useCallback[generateAIText]\"]);\n                // 模拟 AI 响应\n                const responses = [\n                    \"这是一个改进后的文本版本，更加清晰和有说服力。\",\n                    \"经过优化的内容，增加了更多细节和具体例子。\",\n                    \"重新组织的段落结构，逻辑更加清晰。\",\n                    \"修正了语法问题，表达更加准确。\"\n                ];\n                const randomResponse = responses[Math.floor(Math.random() * responses.length)];\n                setAISuggestion(randomResponse);\n            } catch (error) {\n                console.error(\"AI 生成失败:\", error);\n                setAISuggestion(\"抱歉，AI 生成失败，请稍后重试。\");\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"AIEditorInternal.useCallback[generateAIText]\"], []);\n    // 插入 AI 生成的文本\n    const insertAIText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[insertAIText]\": (text)=>{\n            if (!editor) return;\n            editor.chain().focus().insertContent(text).run();\n            setIsAIOpen(false);\n            setAISuggestion(\"\");\n        }\n    }[\"AIEditorInternal.useCallback[insertAIText]\"], [\n        editor\n    ]);\n    // 替换选中的文本\n    const replaceSelectedText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[replaceSelectedText]\": (text)=>{\n            if (!editor) return;\n            const { selection } = editor.state;\n            editor.chain().focus().deleteRange({\n                from: selection.from,\n                to: selection.to\n            }).insertContent(text).run();\n            setIsAIOpen(false);\n            setAISuggestion(\"\");\n        }\n    }[\"AIEditorInternal.useCallback[replaceSelectedText]\"], [\n        editor\n    ]);\n    // 监听键盘快捷键\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIEditorInternal.useEffect\": ()=>{\n            const handleKeyboardShortcuts = {\n                \"AIEditorInternal.useEffect.handleKeyboardShortcuts\": (event)=>{\n                    if (event.type === \"ai-assistant-trigger\") {\n                        handleAIClick();\n                    }\n                }\n            }[\"AIEditorInternal.useEffect.handleKeyboardShortcuts\"];\n            document.addEventListener(\"ai-assistant-trigger\", handleKeyboardShortcuts);\n            document.addEventListener(\"ai-quick-generate\", handleKeyboardShortcuts);\n            return ({\n                \"AIEditorInternal.useEffect\": ()=>{\n                    document.removeEventListener(\"ai-assistant-trigger\", handleKeyboardShortcuts);\n                    document.removeEventListener(\"ai-quick-generate\", handleKeyboardShortcuts);\n                }\n            })[\"AIEditorInternal.useEffect\"];\n        }\n    }[\"AIEditorInternal.useEffect\"], [\n        handleAIClick\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"ai-editor-container \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_8__.EditorRoot, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_8__.EditorContent, {\n                    immediatelyRender: false,\n                    initialContent: initialContent,\n                    extensions: _extensions__WEBPACK_IMPORTED_MODULE_2__.aiEditorExtensions,\n                    className: \"w-full h-full\",\n                    editorProps: {\n                        handleDOMEvents: {\n                            keydown: (_view, event)=>(0,novel__WEBPACK_IMPORTED_MODULE_8__.handleCommandNavigation)(event)\n                        },\n                        handlePaste: (view, event)=>(0,novel__WEBPACK_IMPORTED_MODULE_8__.handleImagePaste)(view, event, _extensions__WEBPACK_IMPORTED_MODULE_2__.uploadFn),\n                        handleDrop: (view, event, _slice, moved)=>(0,novel__WEBPACK_IMPORTED_MODULE_8__.handleImageDrop)(view, event, moved, _extensions__WEBPACK_IMPORTED_MODULE_2__.uploadFn),\n                        attributes: {\n                            class: \"prose prose-base dark:prose-invert max-w-none p-4 focus:outline-none min-h-[400px]\",\n                            \"data-placeholder\": placeholder\n                        }\n                    },\n                    onCreate: (param)=>{\n                        let { editor } = param;\n                        setEditor(editor);\n                    },\n                    onUpdate: (param)=>{\n                        let { editor } = param;\n                        debouncedUpdate(editor);\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_8__.EditorCommand, {\n                            className: \"z-50 h-auto max-h-[330px] overflow-y-auto rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-1 py-2 shadow-md transition-all\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_8__.EditorCommandEmpty, {\n                                    className: \"px-2 text-gray-500 dark:text-gray-400\",\n                                    children: \"没有找到结果\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_8__.EditorCommandList, {\n                                    children: _slash_command__WEBPACK_IMPORTED_MODULE_5__.suggestionItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_8__.EditorCommandItem, {\n                                            value: item.title,\n                                            onCommand: (val)=>{\n                                                var _item_command;\n                                                return (_item_command = item.command) === null || _item_command === void 0 ? void 0 : _item_command.call(item, val);\n                                            },\n                                            className: \"flex w-full items-center space-x-2 rounded-md px-2 py-1 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 aria-selected:bg-gray-100 dark:aria-selected:bg-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex h-10 w-10 items-center justify-center rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\",\n                                                    children: item.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                            children: item.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, item.title, true, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ai_toolbar__WEBPACK_IMPORTED_MODULE_3__.AIToolbar, {\n                            editor: editor,\n                            onAIClick: handleAIClick,\n                            isLoading: isLoading\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            isAIOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ai_assistant__WEBPACK_IMPORTED_MODULE_4__.AIAssistant, {\n                selectedText: selectedText,\n                suggestion: aiSuggestion,\n                isLoading: isLoading,\n                onGenerate: generateAIText,\n                onInsert: insertAIText,\n                onReplace: replaceSelectedText,\n                onClose: ()=>setIsAIOpen(false)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 202,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, this);\n}\n_s(AIEditorInternal, \"SgzzB+Q3hYK1Mio7EKg9DCinmxs=\", false, function() {\n    return [\n        use_debounce__WEBPACK_IMPORTED_MODULE_7__.useDebouncedCallback\n    ];\n});\n_c = AIEditorInternal;\n// 使用动态导入禁用SSR的主要导出组件\nfunction AIEditor(props) {\n    _s1();\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIEditor.useEffect\": ()=>{\n            setIsMounted(true);\n        }\n    }[\"AIEditor.useEffect\"], []);\n    if (!isMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"ai-editor-container \".concat(props.className || \"\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full min-h-[400px] flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500 dark:text-gray-400\",\n                    children: \"加载编辑器中...\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 227,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n            lineNumber: 226,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIEditorInternal, {\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n        lineNumber: 234,\n        columnNumber: 10\n    }, this);\n}\n_s1(AIEditor, \"h7njlszr1nxUzrk46zHyBTBrvgI=\");\n_c1 = AIEditor;\nvar _c, _c1;\n$RefreshReg$(_c, \"AIEditorInternal\");\n$RefreshReg$(_c1, \"AIEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ai-editor/index.tsx\n"));

/***/ })

});