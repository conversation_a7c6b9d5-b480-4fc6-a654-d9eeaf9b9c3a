"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ai-editor/slash-command.tsx":
/*!****************************************************!*\
  !*** ./src/components/ai-editor/slash-command.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   slashCommand: () => (/* binding */ slashCommand),\n/* harmony export */   suggestionItems: () => (/* binding */ suggestionItems)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Code,Heading1,Heading2,Heading3,List,ListOrdered,Quote,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Code,Heading1,Heading2,Heading3,List,ListOrdered,Quote,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/heading-1.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Code,Heading1,Heading2,Heading3,List,ListOrdered,Quote,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/heading-2.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Code,Heading1,Heading2,Heading3,List,ListOrdered,Quote,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/heading-3.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Code,Heading1,Heading2,Heading3,List,ListOrdered,Quote,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Code,Heading1,Heading2,Heading3,List,ListOrdered,Quote,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/list-ordered.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Code,Heading1,Heading2,Heading3,List,ListOrdered,Quote,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Code,Heading1,Heading2,Heading3,List,ListOrdered,Quote,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Code,Heading1,Heading2,Heading3,List,ListOrdered,Quote,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.487.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/novel@1.0.2_@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tipta_3fbtezt2z6la73kpow3ollimny/node_modules/novel/dist/index.js\");\n\n\n\n\nconst suggestionItems = (0,novel__WEBPACK_IMPORTED_MODULE_2__.createSuggestionItems)([\n    {\n        title: \"文本\",\n        description: \"开始写作普通文本\",\n        searchTerms: [\n            \"p\",\n            \"paragraph\",\n            \"text\",\n            \"文本\"\n        ],\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            size: 18\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/slash-command.tsx\",\n            lineNumber: 20,\n            columnNumber: 11\n        }, undefined),\n        command: (param)=>{\n            let { editor, range } = param;\n            editor.chain().focus().deleteRange(range).toggleNode(\"paragraph\", \"paragraph\").run();\n        }\n    },\n    {\n        title: \"一级标题\",\n        description: \"大标题\",\n        searchTerms: [\n            \"title\",\n            \"big\",\n            \"large\",\n            \"h1\",\n            \"标题\",\n            \"大标题\"\n        ],\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            size: 18\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/slash-command.tsx\",\n            lineNumber: 34,\n            columnNumber: 11\n        }, undefined),\n        command: (param)=>{\n            let { editor, range } = param;\n            editor.chain().focus().deleteRange(range).setNode(\"heading\", {\n                level: 1\n            }).run();\n        }\n    },\n    {\n        title: \"二级标题\",\n        description: \"中等标题\",\n        searchTerms: [\n            \"subtitle\",\n            \"medium\",\n            \"h2\",\n            \"副标题\",\n            \"中标题\"\n        ],\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            size: 18\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/slash-command.tsx\",\n            lineNumber: 48,\n            columnNumber: 11\n        }, undefined),\n        command: (param)=>{\n            let { editor, range } = param;\n            editor.chain().focus().deleteRange(range).setNode(\"heading\", {\n                level: 2\n            }).run();\n        }\n    },\n    {\n        title: \"三级标题\",\n        description: \"小标题\",\n        searchTerms: [\n            \"small\",\n            \"h3\",\n            \"小标题\"\n        ],\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            size: 18\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/slash-command.tsx\",\n            lineNumber: 62,\n            columnNumber: 11\n        }, undefined),\n        command: (param)=>{\n            let { editor, range } = param;\n            editor.chain().focus().deleteRange(range).setNode(\"heading\", {\n                level: 3\n            }).run();\n        }\n    },\n    {\n        title: \"无序列表\",\n        description: \"创建一个简单的无序列表\",\n        searchTerms: [\n            \"unordered\",\n            \"point\",\n            \"list\",\n            \"无序\",\n            \"列表\",\n            \"项目\"\n        ],\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            size: 18\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/slash-command.tsx\",\n            lineNumber: 76,\n            columnNumber: 11\n        }, undefined),\n        command: (param)=>{\n            let { editor, range } = param;\n            editor.chain().focus().deleteRange(range).toggleBulletList().run();\n        }\n    },\n    {\n        title: \"有序列表\",\n        description: \"创建一个带数字的有序列表\",\n        searchTerms: [\n            \"ordered\",\n            \"numbered\",\n            \"list\",\n            \"有序\",\n            \"数字\",\n            \"编号\"\n        ],\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            size: 18\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/slash-command.tsx\",\n            lineNumber: 85,\n            columnNumber: 11\n        }, undefined),\n        command: (param)=>{\n            let { editor, range } = param;\n            editor.chain().focus().deleteRange(range).toggleOrderedList().run();\n        }\n    },\n    {\n        title: \"引用\",\n        description: \"引用一段文字\",\n        searchTerms: [\n            \"blockquote\",\n            \"quote\",\n            \"引用\",\n            \"引述\"\n        ],\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            size: 18\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/slash-command.tsx\",\n            lineNumber: 94,\n            columnNumber: 11\n        }, undefined),\n        command: (param)=>{\n            let { editor, range } = param;\n            return editor.chain().focus().deleteRange(range).toggleNode(\"paragraph\", \"paragraph\").toggleBlockquote().run();\n        }\n    },\n    {\n        title: \"代码块\",\n        description: \"插入代码块\",\n        searchTerms: [\n            \"codeblock\",\n            \"code\",\n            \"代码\",\n            \"代码块\"\n        ],\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            size: 18\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/slash-command.tsx\",\n            lineNumber: 108,\n            columnNumber: 11\n        }, undefined),\n        command: (param)=>{\n            let { editor, range } = param;\n            return editor.chain().focus().deleteRange(range).toggleCodeBlock().run();\n        }\n    },\n    {\n        title: \"待办事项\",\n        description: \"创建待办事项列表\",\n        searchTerms: [\n            \"todo\",\n            \"task\",\n            \"list\",\n            \"check\",\n            \"checkbox\",\n            \"待办\",\n            \"任务\"\n        ],\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            size: 18\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/slash-command.tsx\",\n            lineNumber: 116,\n            columnNumber: 11\n        }, undefined),\n        command: (param)=>{\n            let { editor, range } = param;\n            editor.chain().focus().deleteRange(range).toggleTaskList().run();\n        }\n    }\n]);\nconst slashCommand = novel__WEBPACK_IMPORTED_MODULE_2__.Command.configure({\n    suggestion: {\n        items: ()=>suggestionItems,\n        render: novel__WEBPACK_IMPORTED_MODULE_2__.renderItems\n    }\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ai-editor/slash-command.tsx\n"));

/***/ })

});