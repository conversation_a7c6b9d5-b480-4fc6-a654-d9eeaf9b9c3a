"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ai-editor/index.tsx":
/*!********************************************!*\
  !*** ./src/components/ai-editor/index.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIEditor: () => (/* binding */ AIEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/novel/dist/index.js\");\n/* harmony import */ var use_debounce__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! use-debounce */ \"(app-pages-browser)/./node_modules/use-debounce/dist/index.module.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _extensions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./extensions */ \"(app-pages-browser)/./src/components/ai-editor/extensions.ts\");\n/* harmony import */ var _ai_toolbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ai-toolbar */ \"(app-pages-browser)/./src/components/ai-editor/ai-toolbar.tsx\");\n/* harmony import */ var _ai_assistant__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ai-assistant */ \"(app-pages-browser)/./src/components/ai-editor/ai-assistant.tsx\");\n/* harmony import */ var _slash_command__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./slash-command */ \"(app-pages-browser)/./src/components/ai-editor/slash-command.tsx\");\n/* harmony import */ var _ai_editor_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ai-editor.css */ \"(app-pages-browser)/./src/components/ai-editor/ai-editor.css\");\n/* __next_internal_client_entry_do_not_use__ AIEditor auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n// 导入我们的扩展和组件\n\n\n\n\n\n// 内部编辑器组件，不使用SSR\nfunction AIEditorInternal(param) {\n    let { initialContent, placeholder = \"开始写作...\", className = \"\", onContentChange, onMarkdownChange, showToolbar = true, defaultMode = \"edit\" } = param;\n    _s();\n    // 编辑器状态\n    const [editor, setEditor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAIOpen, setIsAIOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedText, setSelectedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [aiSuggestion, setAISuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mode, setMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultMode);\n    const [markdown, setMarkdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 防抖更新函数\n    const debouncedUpdate = (0,use_debounce__WEBPACK_IMPORTED_MODULE_8__.useDebouncedCallback)({\n        \"AIEditorInternal.useDebouncedCallback[debouncedUpdate]\": (editor)=>{\n            const json = editor.getJSON();\n            onContentChange === null || onContentChange === void 0 ? void 0 : onContentChange(json);\n            const markdownContent = editor.storage.markdown.getMarkdown();\n            setMarkdown(markdownContent);\n            if (onMarkdownChange) {\n                onMarkdownChange(markdownContent);\n            }\n        }\n    }[\"AIEditorInternal.useDebouncedCallback[debouncedUpdate]\"], 300);\n    // AI 功能处理函数\n    const handleAIClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[handleAIClick]\": ()=>{\n            if (!editor) return;\n            const { selection } = editor.state;\n            const selectedText = editor.state.doc.textBetween(selection.from, selection.to);\n            setSelectedText(selectedText);\n            setIsAIOpen(true);\n        }\n    }[\"AIEditorInternal.useCallback[handleAIClick]\"], [\n        editor\n    ]);\n    // 模拟 AI 生成文本\n    const generateAIText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[generateAIText]\": async (prompt)=>{\n            setIsLoading(true);\n            setAISuggestion(\"\");\n            try {\n                // 模拟 API 调用延迟\n                await new Promise({\n                    \"AIEditorInternal.useCallback[generateAIText]\": (resolve)=>setTimeout(resolve, 1500)\n                }[\"AIEditorInternal.useCallback[generateAIText]\"]);\n                // 根据提示词生成不同的响应\n                let response = \"\";\n                if (prompt.includes(\"改进\")) {\n                    response = \"这是一个改进后的文本版本，更加清晰和有说服力。\";\n                } else if (prompt.includes(\"扩展\")) {\n                    response = \"经过优化的内容，增加了更多细节和具体例子。\";\n                } else if (prompt.includes(\"总结\")) {\n                    response = \"重新组织的段落结构，逻辑更加清晰。\";\n                } else if (prompt.includes(\"修正\")) {\n                    response = \"修正了语法问题，表达更加准确。\";\n                } else {\n                    // 默认响应\n                    const responses = [\n                        \"这是一个改进后的文本版本，更加清晰和有说服力。\",\n                        \"经过优化的内容，增加了更多细节和具体例子。\",\n                        \"重新组织的段落结构，逻辑更加清晰。\",\n                        \"修正了语法问题，表达更加准确。\"\n                    ];\n                    response = responses[Math.floor(Math.random() * responses.length)];\n                }\n                setAISuggestion(response);\n            } catch (error) {\n                console.error(\"AI 生成失败:\", error);\n                setAISuggestion(\"抱歉，AI 生成失败，请稍后重试。\");\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"AIEditorInternal.useCallback[generateAIText]\"], []);\n    // 插入 AI 生成的文本\n    const insertAIText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[insertAIText]\": (text)=>{\n            if (!editor) return;\n            editor.chain().focus().insertContent(text).run();\n            setIsAIOpen(false);\n            setAISuggestion(\"\");\n        }\n    }[\"AIEditorInternal.useCallback[insertAIText]\"], [\n        editor\n    ]);\n    // 替换选中的文本\n    const replaceSelectedText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[replaceSelectedText]\": (text)=>{\n            if (!editor) return;\n            const { selection } = editor.state;\n            editor.chain().focus().deleteRange({\n                from: selection.from,\n                to: selection.to\n            }).insertContent(text).run();\n            setIsAIOpen(false);\n            setAISuggestion(\"\");\n        }\n    }[\"AIEditorInternal.useCallback[replaceSelectedText]\"], [\n        editor\n    ]);\n    // 导出Markdown文件\n    const exportMarkdown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[exportMarkdown]\": ()=>{\n            if (!markdown) return;\n            const blob = new Blob([\n                markdown\n            ], {\n                type: 'text/markdown'\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"document-\".concat(new Date().toISOString().split('T')[0], \".md\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    }[\"AIEditorInternal.useCallback[exportMarkdown]\"], [\n        markdown\n    ]);\n    // 监听键盘快捷键\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIEditorInternal.useEffect\": ()=>{\n            const handleKeyboardShortcuts = {\n                \"AIEditorInternal.useEffect.handleKeyboardShortcuts\": (event)=>{\n                    if (event.type === \"ai-assistant-trigger\") {\n                        handleAIClick();\n                    }\n                }\n            }[\"AIEditorInternal.useEffect.handleKeyboardShortcuts\"];\n            document.addEventListener(\"ai-assistant-trigger\", handleKeyboardShortcuts);\n            document.addEventListener(\"ai-quick-generate\", handleKeyboardShortcuts);\n            return ({\n                \"AIEditorInternal.useEffect\": ()=>{\n                    document.removeEventListener(\"ai-assistant-trigger\", handleKeyboardShortcuts);\n                    document.removeEventListener(\"ai-quick-generate\", handleKeyboardShortcuts);\n                }\n            })[\"AIEditorInternal.useEffect\"];\n        }\n    }[\"AIEditorInternal.useEffect\"], [\n        handleAIClick\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"ai-editor-container h-full flex flex-col \".concat(className),\n        children: [\n            showToolbar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b bg-gray-50 dark:bg-gray-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                            children: \"AI 编辑器\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: mode === \"edit\" ? \"secondary\" : \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setMode(\"edit\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" 编辑\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: mode === \"preview\" ? \"secondary\" : \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setMode(\"preview\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Markdown输出\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: exportMarkdown,\n                                disabled: !markdown,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" 导出\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 176,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: [\n                    mode === \"edit\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_12__.EditorRoot, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_12__.EditorContent, {\n                                immediatelyRender: false,\n                                initialContent: initialContent,\n                                extensions: _extensions__WEBPACK_IMPORTED_MODULE_3__.aiEditorExtensions,\n                                className: \"h-full w-full\",\n                                editorProps: {\n                                    handleDOMEvents: {\n                                        keydown: (_view, event)=>(0,novel__WEBPACK_IMPORTED_MODULE_12__.handleCommandNavigation)(event)\n                                    },\n                                    handlePaste: (view, event)=>(0,novel__WEBPACK_IMPORTED_MODULE_12__.handleImagePaste)(view, event, _extensions__WEBPACK_IMPORTED_MODULE_3__.uploadFn),\n                                    handleDrop: (view, event, _slice, moved)=>(0,novel__WEBPACK_IMPORTED_MODULE_12__.handleImageDrop)(view, event, moved, _extensions__WEBPACK_IMPORTED_MODULE_3__.uploadFn),\n                                    attributes: {\n                                        class: \"prose prose-base dark:prose-invert max-w-none p-4 focus:outline-none h-full overflow-auto scroll-smooth\",\n                                        \"data-placeholder\": placeholder,\n                                        style: \"min-height: 100%; overflow-y: auto; overflow-x: hidden;\"\n                                    }\n                                },\n                                onCreate: (param)=>{\n                                    let { editor } = param;\n                                    setEditor(editor);\n                                },\n                                onUpdate: (param)=>{\n                                    let { editor } = param;\n                                    debouncedUpdate(editor);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_12__.EditorCommand, {\n                                        className: \"z-50 h-auto max-h-[330px] overflow-y-auto rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-1 py-2 shadow-md transition-all\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_12__.EditorCommandEmpty, {\n                                                className: \"px-2 text-gray-500 dark:text-gray-400\",\n                                                children: \"没有找到结果\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_12__.EditorCommandList, {\n                                                children: _slash_command__WEBPACK_IMPORTED_MODULE_6__.suggestionItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_12__.EditorCommandItem, {\n                                                        value: item.title,\n                                                        onCommand: (val)=>{\n                                                            var _item_command;\n                                                            return (_item_command = item.command) === null || _item_command === void 0 ? void 0 : _item_command.call(item, val);\n                                                        },\n                                                        className: \"flex w-full items-center space-x-2 rounded-md px-2 py-1 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 aria-selected:bg-gray-100 dark:aria-selected:bg-gray-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex h-10 w-10 items-center justify-center rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\",\n                                                                children: item.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: item.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: item.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, item.title, true, {\n                                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ai_toolbar__WEBPACK_IMPORTED_MODULE_4__.AIToolbar, {\n                                        editor: editor,\n                                        onAIClick: handleAIClick,\n                                        isLoading: isLoading\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_12__.ImageResizer, {}, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this),\n                    mode === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full p-4 overflow-auto bg-gray-50 dark:bg-gray-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 p-4 h-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4 text-gray-900 dark:text-white\",\n                                    children: \"Markdown 输出\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"text-sm whitespace-pre-wrap font-mono text-gray-800 dark:text-gray-200 overflow-auto h-full\",\n                                    children: markdown || '开始编辑以查看Markdown输出...'\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this),\n            isAIOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ai_assistant__WEBPACK_IMPORTED_MODULE_5__.AIAssistant, {\n                selectedText: selectedText,\n                suggestion: aiSuggestion,\n                isLoading: isLoading,\n                onGenerate: generateAIText,\n                onInsert: insertAIText,\n                onReplace: replaceSelectedText,\n                onClose: ()=>setIsAIOpen(false)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 296,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, this);\n}\n_s(AIEditorInternal, \"2H/6ktHM3v44k/4SuMrn7VZPQJw=\", false, function() {\n    return [\n        use_debounce__WEBPACK_IMPORTED_MODULE_8__.useDebouncedCallback\n    ];\n});\n_c = AIEditorInternal;\n// 使用动态导入禁用SSR的主要导出组件\nfunction AIEditor(props) {\n    _s1();\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIEditor.useEffect\": ()=>{\n            setIsMounted(true);\n        }\n    }[\"AIEditor.useEffect\"], []);\n    if (!isMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"ai-editor-container \".concat(props.className || \"\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full min-h-[400px] flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500 dark:text-gray-400\",\n                    children: \"加载编辑器中...\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 321,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n            lineNumber: 320,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIEditorInternal, {\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n        lineNumber: 328,\n        columnNumber: 10\n    }, this);\n}\n_s1(AIEditor, \"h7njlszr1nxUzrk46zHyBTBrvgI=\");\n_c1 = AIEditor;\nvar _c, _c1;\n$RefreshReg$(_c, \"AIEditorInternal\");\n$RefreshReg$(_c1, \"AIEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ai-editor/index.tsx\n"));

/***/ })

});