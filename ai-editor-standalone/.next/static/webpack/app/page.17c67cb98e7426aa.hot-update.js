"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ai-editor/extensions.ts":
/*!************************************************!*\
  !*** ./src/components/ai-editor/extensions.ts ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiEditorExtensions: () => (/* binding */ aiEditorExtensions),\n/* harmony export */   uploadFn: () => (/* reexport safe */ _image_upload__WEBPACK_IMPORTED_MODULE_0__.uploadFn)\n/* harmony export */ });\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+starter-kit@2.26.1/node_modules/@tiptap/starter-kit/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/novel@1.0.2_@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tipta_3fbtezt2z6la73kpow3ollimny/node_modules/novel/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-link@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-link/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-task-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-task-list/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-task-item@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-task-item/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-image@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-image/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/tiptap-extension-global-drag-handle@0.1.18/node_modules/tiptap-extension-global-drag-handle/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-underline@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-underline/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-text-style@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-text-style/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-color@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+extension-text-_ochc23jjcl3wq5k6zoczrgb3ku/node_modules/@tiptap/extension-color/dist/index.js\");\n/* harmony import */ var tiptap_markdown__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! tiptap-markdown */ \"(app-pages-browser)/./node_modules/.pnpm/tiptap-markdown@0.8.10_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/tiptap-markdown/dist/tiptap-markdown.es.js\");\n/* harmony import */ var _image_upload__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./image-upload */ \"(app-pages-browser)/./src/components/ai-editor/image-upload.ts\");\n\n\n\n// 基础扩展配置\nconst starterKit = novel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].configure({\n    bulletList: {\n        HTMLAttributes: {\n            class: \"ai-editor-bullet-list\"\n        }\n    },\n    orderedList: {\n        HTMLAttributes: {\n            class: \"ai-editor-ordered-list\"\n        }\n    },\n    blockquote: {\n        HTMLAttributes: {\n            class: \"ai-editor-blockquote\"\n        }\n    },\n    code: {\n        HTMLAttributes: {\n            class: \"ai-editor-code\"\n        }\n    },\n    heading: {\n        HTMLAttributes: {\n            class: \"ai-editor-heading\"\n        }\n    }\n});\n// 占位符配置\nconst placeholder = novel__WEBPACK_IMPORTED_MODULE_2__.Placeholder.configure({\n    placeholder: (param)=>{\n        let { node } = param;\n        if (node.type.name === \"heading\") {\n            return \"标题\";\n        }\n        return \"开始写作...\";\n    }\n});\n// 链接配置\nconst link = novel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].configure({\n    HTMLAttributes: {\n        class: \"ai-editor-link\"\n    }\n});\n// 高亮配置\nconst highlight = novel__WEBPACK_IMPORTED_MODULE_2__.HighlightExtension.configure({\n    multicolor: true\n});\n// 任务列表配置\nconst taskList = novel__WEBPACK_IMPORTED_MODULE_4__.TaskList.configure({\n    HTMLAttributes: {\n        class: \"ai-editor-task-list\"\n    }\n});\nconst taskItem = novel__WEBPACK_IMPORTED_MODULE_5__.TaskItem.configure({\n    HTMLAttributes: {\n        class: \"ai-editor-task-item\"\n    }\n});\n// 图片配置\nconst tiptapImage = novel__WEBPACK_IMPORTED_MODULE_6__[\"default\"].extend({\n    addProseMirrorPlugins () {\n        return [\n            (0,_image_upload__WEBPACK_IMPORTED_MODULE_0__.uploadFn)()\n        ];\n    }\n}).configure({\n    allowBase64: true,\n    HTMLAttributes: {\n        class: \"ai-editor-image\"\n    }\n});\n// 拖拽手柄配置\nconst globalDragHandle = novel__WEBPACK_IMPORTED_MODULE_7__[\"default\"].configure({\n    dragHandleWidth: 20\n});\n// 配置 Markdown 支持\nconst markdown = tiptap_markdown__WEBPACK_IMPORTED_MODULE_8__.Markdown.configure({\n    html: true,\n    tightLists: true,\n    bulletListMarker: \"-\",\n    linkify: false,\n    breaks: false\n});\n// 导出所有扩展\nconst aiEditorExtensions = [\n    starterKit,\n    placeholder,\n    link,\n    novel__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    highlight,\n    novel__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    novel__WEBPACK_IMPORTED_MODULE_11__.Color,\n    markdown,\n    taskList,\n    taskItem,\n    tiptapImage,\n    globalDragHandle\n];\n// 导出图片上传函数\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ai-editor/extensions.ts\n"));

/***/ })

});