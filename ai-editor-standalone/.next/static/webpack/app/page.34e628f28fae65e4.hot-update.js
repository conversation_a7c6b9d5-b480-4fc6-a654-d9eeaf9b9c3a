"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ai-editor/index.tsx":
/*!********************************************!*\
  !*** ./src/components/ai-editor/index.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIEditor: () => (/* binding */ AIEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/novel@1.0.2_@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tipta_3fbtezt2z6la73kpow3ollimny/node_modules/novel/dist/index.js\");\n/* harmony import */ var use_debounce__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! use-debounce */ \"(app-pages-browser)/./node_modules/.pnpm/use-debounce@10.0.5_react@18.3.1/node_modules/use-debounce/dist/index.module.js\");\n/* harmony import */ var _extensions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./extensions */ \"(app-pages-browser)/./src/components/ai-editor/extensions.ts\");\n/* harmony import */ var _slash_command__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./slash-command */ \"(app-pages-browser)/./src/components/ai-editor/slash-command.tsx\");\n/* harmony import */ var _ai_editor_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ai-editor.css */ \"(app-pages-browser)/./src/components/ai-editor/ai-editor.css\");\n/* __next_internal_client_entry_do_not_use__ AIEditor auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// 导入我们的扩展和组件\n\n\n\n// 内部编辑器组件，不使用SSR\nfunction AIEditorInternal(param) {\n    let { initialContent, placeholder = \"开始写作...\", className = \"\", onContentChange, onMarkdownChange } = param;\n    _s();\n    // 编辑器状态\n    const [editor, setEditor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAIOpen, setIsAIOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedText, setSelectedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [aiSuggestion, setAISuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 防抖更新函数\n    const debouncedUpdate = (0,use_debounce__WEBPACK_IMPORTED_MODULE_5__.useDebouncedCallback)({\n        \"AIEditorInternal.useDebouncedCallback[debouncedUpdate]\": (editor)=>{\n            const json = editor.getJSON();\n            onContentChange === null || onContentChange === void 0 ? void 0 : onContentChange(json);\n            if (onMarkdownChange) {\n                const markdown = editor.storage.markdown.getMarkdown();\n                onMarkdownChange(markdown);\n            }\n        }\n    }[\"AIEditorInternal.useDebouncedCallback[debouncedUpdate]\"], 300);\n    // AI 功能处理函数\n    const handleAIClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[handleAIClick]\": ()=>{\n            if (!editor) return;\n            const { selection } = editor.state;\n            const selectedText = editor.state.doc.textBetween(selection.from, selection.to);\n            setSelectedText(selectedText);\n            setIsAIOpen(true);\n        }\n    }[\"AIEditorInternal.useCallback[handleAIClick]\"], [\n        editor\n    ]);\n    // 模拟 AI 生成文本\n    const generateAIText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[generateAIText]\": async (prompt)=>{\n            setIsLoading(true);\n            setAISuggestion(\"\");\n            try {\n                // 模拟 API 调用延迟\n                await new Promise({\n                    \"AIEditorInternal.useCallback[generateAIText]\": (resolve)=>setTimeout(resolve, 1500)\n                }[\"AIEditorInternal.useCallback[generateAIText]\"]);\n                // 模拟 AI 响应\n                const responses = [\n                    \"这是一个改进后的文本版本，更加清晰和有说服力。\",\n                    \"经过优化的内容，增加了更多细节和具体例子。\",\n                    \"重新组织的段落结构，逻辑更加清晰。\",\n                    \"修正了语法问题，表达更加准确。\"\n                ];\n                const randomResponse = responses[Math.floor(Math.random() * responses.length)];\n                setAISuggestion(randomResponse);\n            } catch (error) {\n                console.error(\"AI 生成失败:\", error);\n                setAISuggestion(\"抱歉，AI 生成失败，请稍后重试。\");\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"AIEditorInternal.useCallback[generateAIText]\"], []);\n    // 插入 AI 生成的文本\n    const insertAIText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[insertAIText]\": (text)=>{\n            if (!editor) return;\n            editor.chain().focus().insertContent(text).run();\n            setIsAIOpen(false);\n            setAISuggestion(\"\");\n        }\n    }[\"AIEditorInternal.useCallback[insertAIText]\"], [\n        editor\n    ]);\n    // 替换选中的文本\n    const replaceSelectedText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[replaceSelectedText]\": (text)=>{\n            if (!editor) return;\n            const { selection } = editor.state;\n            editor.chain().focus().deleteRange({\n                from: selection.from,\n                to: selection.to\n            }).insertContent(text).run();\n            setIsAIOpen(false);\n            setAISuggestion(\"\");\n        }\n    }[\"AIEditorInternal.useCallback[replaceSelectedText]\"], [\n        editor\n    ]);\n    // 监听键盘快捷键\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIEditorInternal.useEffect\": ()=>{\n            const handleKeyboardShortcuts = {\n                \"AIEditorInternal.useEffect.handleKeyboardShortcuts\": (event)=>{\n                    if (event.type === \"ai-assistant-trigger\") {\n                        handleAIClick();\n                    }\n                }\n            }[\"AIEditorInternal.useEffect.handleKeyboardShortcuts\"];\n            document.addEventListener(\"ai-assistant-trigger\", handleKeyboardShortcuts);\n            document.addEventListener(\"ai-quick-generate\", handleKeyboardShortcuts);\n            return ({\n                \"AIEditorInternal.useEffect\": ()=>{\n                    document.removeEventListener(\"ai-assistant-trigger\", handleKeyboardShortcuts);\n                    document.removeEventListener(\"ai-quick-generate\", handleKeyboardShortcuts);\n                }\n            })[\"AIEditorInternal.useEffect\"];\n        }\n    }[\"AIEditorInternal.useEffect\"], [\n        handleAIClick\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"ai-editor-container \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_6__.EditorRoot, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_6__.EditorContent, {\n                immediatelyRender: false,\n                initialContent: initialContent,\n                extensions: _extensions__WEBPACK_IMPORTED_MODULE_2__.aiEditorExtensions,\n                className: \"w-full h-full\",\n                editorProps: {\n                    handleDOMEvents: {\n                        keydown: (_view, event)=>(0,novel__WEBPACK_IMPORTED_MODULE_6__.handleCommandNavigation)(event)\n                    },\n                    handlePaste: (view, event)=>(0,novel__WEBPACK_IMPORTED_MODULE_6__.handleImagePaste)(view, event, _extensions__WEBPACK_IMPORTED_MODULE_2__.uploadFn),\n                    handleDrop: (view, event, _slice, moved)=>(0,novel__WEBPACK_IMPORTED_MODULE_6__.handleImageDrop)(view, event, moved, _extensions__WEBPACK_IMPORTED_MODULE_2__.uploadFn),\n                    attributes: {\n                        class: \"prose prose-base dark:prose-invert max-w-none p-4 focus:outline-none min-h-[400px]\",\n                        \"data-placeholder\": placeholder\n                    }\n                },\n                onCreate: (param)=>{\n                    let { editor } = param;\n                    setEditor(editor);\n                },\n                onUpdate: (param)=>{\n                    let { editor } = param;\n                    debouncedUpdate(editor);\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_6__.EditorCommand, {\n                    className: \"z-50 h-auto max-h-[330px] overflow-y-auto rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-1 py-2 shadow-md transition-all\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_6__.EditorCommandEmpty, {\n                            className: \"px-2 text-gray-500 dark:text-gray-400\",\n                            children: \"没有找到结果\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_6__.EditorCommandList, {\n                            children: _slash_command__WEBPACK_IMPORTED_MODULE_3__.suggestionItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_6__.EditorCommandItem, {\n                                    value: item.title,\n                                    onCommand: (val)=>{\n                                        var _item_command;\n                                        return (_item_command = item.command) === null || _item_command === void 0 ? void 0 : _item_command.call(item, val);\n                                    },\n                                    className: \"flex w-full items-center space-x-2 rounded-md px-2 py-1 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 aria-selected:bg-gray-100 dark:aria-selected:bg-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex h-10 w-10 items-center justify-center rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\",\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: item.title\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: item.description\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.title, true, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, this);\n}\n_s(AIEditorInternal, \"SgzzB+Q3hYK1Mio7EKg9DCinmxs=\", false, function() {\n    return [\n        use_debounce__WEBPACK_IMPORTED_MODULE_5__.useDebouncedCallback\n    ];\n});\n_c = AIEditorInternal;\n// 使用动态导入禁用SSR的主要导出组件\nfunction AIEditor(props) {\n    _s1();\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIEditor.useEffect\": ()=>{\n            setIsMounted(true);\n        }\n    }[\"AIEditor.useEffect\"], []);\n    if (!isMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"ai-editor-container \".concat(props.className || \"\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full min-h-[400px] flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500 dark:text-gray-400\",\n                    children: \"加载编辑器中...\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n            lineNumber: 206,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIEditorInternal, {\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n        lineNumber: 214,\n        columnNumber: 10\n    }, this);\n}\n_s1(AIEditor, \"h7njlszr1nxUzrk46zHyBTBrvgI=\");\n_c1 = AIEditor;\nvar _c, _c1;\n$RefreshReg$(_c, \"AIEditorInternal\");\n$RefreshReg$(_c1, \"AIEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ai-editor/index.tsx\n"));

/***/ })

});