"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ai-editor/index.tsx":
/*!********************************************!*\
  !*** ./src/components/ai-editor/index.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIEditor: () => (/* binding */ AIEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/novel@1.0.2_@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tipta_3fbtezt2z6la73kpow3ollimny/node_modules/novel/dist/index.js\");\n/* harmony import */ var use_debounce__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! use-debounce */ \"(app-pages-browser)/./node_modules/.pnpm/use-debounce@10.0.5_react@18.3.1/node_modules/use-debounce/dist/index.module.js\");\n/* harmony import */ var _extensions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./extensions */ \"(app-pages-browser)/./src/components/ai-editor/extensions.ts\");\n/* harmony import */ var _ai_toolbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ai-toolbar */ \"(app-pages-browser)/./src/components/ai-editor/ai-toolbar.tsx\");\n/* harmony import */ var _ai_assistant__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ai-assistant */ \"(app-pages-browser)/./src/components/ai-editor/ai-assistant.tsx\");\n/* harmony import */ var _slash_command__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./slash-command */ \"(app-pages-browser)/./src/components/ai-editor/slash-command.tsx\");\n/* harmony import */ var _ai_editor_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ai-editor.css */ \"(app-pages-browser)/./src/components/ai-editor/ai-editor.css\");\n/* __next_internal_client_entry_do_not_use__ AIEditor auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// 导入我们的扩展和组件\n\n\n\n\n\n// 内部编辑器组件，不使用SSR\nfunction AIEditorInternal(param) {\n    let { initialContent, placeholder = \"开始写作...\", className = \"\", onContentChange, onMarkdownChange } = param;\n    _s();\n    // 编辑器状态\n    const [editor, setEditor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAIOpen, setIsAIOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedText, setSelectedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [aiSuggestion, setAISuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 防抖更新函数\n    const debouncedUpdate = (0,use_debounce__WEBPACK_IMPORTED_MODULE_7__.useDebouncedCallback)({\n        \"AIEditorInternal.useDebouncedCallback[debouncedUpdate]\": (editor)=>{\n            const json = editor.getJSON();\n            onContentChange === null || onContentChange === void 0 ? void 0 : onContentChange(json);\n            if (onMarkdownChange) {\n                const markdown = editor.storage.markdown.getMarkdown();\n                onMarkdownChange(markdown);\n            }\n        }\n    }[\"AIEditorInternal.useDebouncedCallback[debouncedUpdate]\"], 300);\n    // AI 功能处理函数\n    const handleAIClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[handleAIClick]\": ()=>{\n            if (!editor) return;\n            const { selection } = editor.state;\n            const selectedText = editor.state.doc.textBetween(selection.from, selection.to);\n            setSelectedText(selectedText);\n            setIsAIOpen(true);\n        }\n    }[\"AIEditorInternal.useCallback[handleAIClick]\"], [\n        editor\n    ]);\n    // 模拟 AI 生成文本\n    const generateAIText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[generateAIText]\": async (prompt)=>{\n            setIsLoading(true);\n            setAISuggestion(\"\");\n            try {\n                // 模拟 API 调用延迟\n                await new Promise({\n                    \"AIEditorInternal.useCallback[generateAIText]\": (resolve)=>setTimeout(resolve, 1500)\n                }[\"AIEditorInternal.useCallback[generateAIText]\"]);\n                // 模拟 AI 响应\n                const responses = [\n                    \"这是一个改进后的文本版本，更加清晰和有说服力。\",\n                    \"经过优化的内容，增加了更多细节和具体例子。\",\n                    \"重新组织的段落结构，逻辑更加清晰。\",\n                    \"修正了语法问题，表达更加准确。\"\n                ];\n                const randomResponse = responses[Math.floor(Math.random() * responses.length)];\n                setAISuggestion(randomResponse);\n            } catch (error) {\n                console.error(\"AI 生成失败:\", error);\n                setAISuggestion(\"抱歉，AI 生成失败，请稍后重试。\");\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"AIEditorInternal.useCallback[generateAIText]\"], []);\n    // 插入 AI 生成的文本\n    const insertAIText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[insertAIText]\": (text)=>{\n            if (!editor) return;\n            editor.chain().focus().insertContent(text).run();\n            setIsAIOpen(false);\n            setAISuggestion(\"\");\n        }\n    }[\"AIEditorInternal.useCallback[insertAIText]\"], [\n        editor\n    ]);\n    // 替换选中的文本\n    const replaceSelectedText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[replaceSelectedText]\": (text)=>{\n            if (!editor) return;\n            const { selection } = editor.state;\n            editor.chain().focus().deleteRange({\n                from: selection.from,\n                to: selection.to\n            }).insertContent(text).run();\n            setIsAIOpen(false);\n            setAISuggestion(\"\");\n        }\n    }[\"AIEditorInternal.useCallback[replaceSelectedText]\"], [\n        editor\n    ]);\n    // 监听键盘快捷键\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIEditorInternal.useEffect\": ()=>{\n            const handleKeyboardShortcuts = {\n                \"AIEditorInternal.useEffect.handleKeyboardShortcuts\": (event)=>{\n                    if (event.type === \"ai-assistant-trigger\") {\n                        handleAIClick();\n                    }\n                }\n            }[\"AIEditorInternal.useEffect.handleKeyboardShortcuts\"];\n            document.addEventListener(\"ai-assistant-trigger\", handleKeyboardShortcuts);\n            document.addEventListener(\"ai-quick-generate\", handleKeyboardShortcuts);\n            return ({\n                \"AIEditorInternal.useEffect\": ()=>{\n                    document.removeEventListener(\"ai-assistant-trigger\", handleKeyboardShortcuts);\n                    document.removeEventListener(\"ai-quick-generate\", handleKeyboardShortcuts);\n                }\n            })[\"AIEditorInternal.useEffect\"];\n        }\n    }[\"AIEditorInternal.useEffect\"], [\n        handleAIClick\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"ai-editor-container \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_8__.EditorRoot, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_8__.EditorContent, {\n                    immediatelyRender: false,\n                    initialContent: initialContent,\n                    extensions: _extensions__WEBPACK_IMPORTED_MODULE_2__.aiEditorExtensions,\n                    className: \"w-full h-full\",\n                    editorProps: {\n                        handleDOMEvents: {\n                            keydown: (_view, event)=>(0,novel__WEBPACK_IMPORTED_MODULE_8__.handleCommandNavigation)(event)\n                        },\n                        handlePaste: (view, event)=>(0,novel__WEBPACK_IMPORTED_MODULE_8__.handleImagePaste)(view, event, _extensions__WEBPACK_IMPORTED_MODULE_2__.uploadFn),\n                        handleDrop: (view, event, _slice, moved)=>(0,novel__WEBPACK_IMPORTED_MODULE_8__.handleImageDrop)(view, event, moved, _extensions__WEBPACK_IMPORTED_MODULE_2__.uploadFn),\n                        attributes: {\n                            class: \"prose prose-base dark:prose-invert max-w-none p-4 focus:outline-none min-h-[400px]\",\n                            \"data-placeholder\": placeholder\n                        }\n                    },\n                    onCreate: (param)=>{\n                        let { editor } = param;\n                        setEditor(editor);\n                    },\n                    onUpdate: (param)=>{\n                        let { editor } = param;\n                        debouncedUpdate(editor);\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_8__.EditorCommand, {\n                            className: \"z-50 h-auto max-h-[330px] overflow-y-auto rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-1 py-2 shadow-md transition-all\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_8__.EditorCommandEmpty, {\n                                    className: \"px-2 text-gray-500 dark:text-gray-400\",\n                                    children: \"没有找到结果\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_8__.EditorCommandList, {\n                                    children: _slash_command__WEBPACK_IMPORTED_MODULE_5__.suggestionItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_8__.EditorCommandItem, {\n                                            value: item.title,\n                                            onCommand: (val)=>{\n                                                var _item_command;\n                                                return (_item_command = item.command) === null || _item_command === void 0 ? void 0 : _item_command.call(item, val);\n                                            },\n                                            className: \"flex w-full items-center space-x-2 rounded-md px-2 py-1 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 aria-selected:bg-gray-100 dark:aria-selected:bg-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex h-10 w-10 items-center justify-center rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\",\n                                                    children: item.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                            children: item.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, item.title, true, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ai_toolbar__WEBPACK_IMPORTED_MODULE_3__.AIToolbar, {\n                            editor: editor,\n                            onAIClick: handleAIClick,\n                            isLoading: isLoading\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_8__.ImageResizer, {}, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            isAIOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ai_assistant__WEBPACK_IMPORTED_MODULE_4__.AIAssistant, {\n                selectedText: selectedText,\n                suggestion: aiSuggestion,\n                isLoading: isLoading,\n                onGenerate: generateAIText,\n                onInsert: insertAIText,\n                onReplace: replaceSelectedText,\n                onClose: ()=>setIsAIOpen(false)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 205,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, this);\n}\n_s(AIEditorInternal, \"SgzzB+Q3hYK1Mio7EKg9DCinmxs=\", false, function() {\n    return [\n        use_debounce__WEBPACK_IMPORTED_MODULE_7__.useDebouncedCallback\n    ];\n});\n_c = AIEditorInternal;\n// 使用动态导入禁用SSR的主要导出组件\nfunction AIEditor(props) {\n    _s1();\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIEditor.useEffect\": ()=>{\n            setIsMounted(true);\n        }\n    }[\"AIEditor.useEffect\"], []);\n    if (!isMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"ai-editor-container \".concat(props.className || \"\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full min-h-[400px] flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500 dark:text-gray-400\",\n                    children: \"加载编辑器中...\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 230,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n            lineNumber: 229,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIEditorInternal, {\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n        lineNumber: 237,\n        columnNumber: 10\n    }, this);\n}\n_s1(AIEditor, \"h7njlszr1nxUzrk46zHyBTBrvgI=\");\n_c1 = AIEditor;\nvar _c, _c1;\n$RefreshReg$(_c, \"AIEditorInternal\");\n$RefreshReg$(_c1, \"AIEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ai-editor/index.tsx\n"));

/***/ })

});