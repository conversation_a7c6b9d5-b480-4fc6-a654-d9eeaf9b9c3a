"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ai-editor/extensions.ts":
/*!************************************************!*\
  !*** ./src/components/ai-editor/extensions.ts ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiEditorExtensions: () => (/* binding */ aiEditorExtensions),\n/* harmony export */   uploadFn: () => (/* reexport safe */ _image_upload__WEBPACK_IMPORTED_MODULE_11__.uploadFn)\n/* harmony export */ });\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+starter-kit@2.26.1/node_modules/@tiptap/starter-kit/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/novel@1.0.2_@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tipta_3fbtezt2z6la73kpow3ollimny/node_modules/novel/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-link@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-link/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-task-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-task-list/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-task-item@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-task-item/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-image@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-image/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/tiptap-extension-global-drag-handle@0.1.18/node_modules/tiptap-extension-global-drag-handle/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-underline@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-underline/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-text-style@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-text-style/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-color@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+extension-text-_ochc23jjcl3wq5k6zoczrgb3ku/node_modules/@tiptap/extension-color/dist/index.js\");\n/* harmony import */ var tiptap_markdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! tiptap-markdown */ \"(app-pages-browser)/./node_modules/.pnpm/tiptap-markdown@0.8.10_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/tiptap-markdown/dist/tiptap-markdown.es.js\");\n/* harmony import */ var _image_upload__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./image-upload */ \"(app-pages-browser)/./src/components/ai-editor/image-upload.ts\");\n\n\n// 基础扩展配置\nconst starterKit = novel__WEBPACK_IMPORTED_MODULE_0__[\"default\"].configure({\n    bulletList: {\n        HTMLAttributes: {\n            class: \"ai-editor-bullet-list\"\n        }\n    },\n    orderedList: {\n        HTMLAttributes: {\n            class: \"ai-editor-ordered-list\"\n        }\n    },\n    blockquote: {\n        HTMLAttributes: {\n            class: \"ai-editor-blockquote\"\n        }\n    },\n    code: {\n        HTMLAttributes: {\n            class: \"ai-editor-code\"\n        }\n    },\n    heading: {\n        HTMLAttributes: {\n            class: \"ai-editor-heading\"\n        }\n    }\n});\n// 占位符配置\nconst placeholder = novel__WEBPACK_IMPORTED_MODULE_1__.Placeholder.configure({\n    placeholder: (param)=>{\n        let { node } = param;\n        if (node.type.name === \"heading\") {\n            return \"标题\";\n        }\n        return \"开始写作...\";\n    }\n});\n// 链接配置\nconst link = novel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].configure({\n    HTMLAttributes: {\n        class: \"ai-editor-link\"\n    }\n});\n// 高亮配置\nconst highlight = novel__WEBPACK_IMPORTED_MODULE_1__.HighlightExtension.configure({\n    multicolor: true\n});\n// 任务列表配置\nconst taskList = novel__WEBPACK_IMPORTED_MODULE_3__.TaskList.configure({\n    HTMLAttributes: {\n        class: \"ai-editor-task-list\"\n    }\n});\nconst taskItem = novel__WEBPACK_IMPORTED_MODULE_4__.TaskItem.configure({\n    HTMLAttributes: {\n        class: \"ai-editor-task-item\"\n    }\n});\n// 图片配置\nconst tiptapImage = novel__WEBPACK_IMPORTED_MODULE_5__[\"default\"].extend({\n    addProseMirrorPlugins () {\n        return [\n            (0,novel__WEBPACK_IMPORTED_MODULE_1__.UploadImagesPlugin)({\n                imageClass: \"ai-editor-image-uploading opacity-40 rounded-lg border border-gray-200\"\n            })\n        ];\n    }\n}).configure({\n    allowBase64: true,\n    HTMLAttributes: {\n        class: \"ai-editor-image rounded-lg border border-gray-200 max-w-full h-auto\"\n    }\n});\n// 拖拽手柄配置\nconst globalDragHandle = novel__WEBPACK_IMPORTED_MODULE_6__[\"default\"].configure({\n    dragHandleWidth: 20\n});\n// 配置 Markdown 支持\nconst markdown = tiptap_markdown__WEBPACK_IMPORTED_MODULE_7__.Markdown.configure({\n    html: true,\n    tightLists: true,\n    bulletListMarker: \"-\",\n    linkify: false,\n    breaks: false\n});\n// 导出所有扩展\nconst aiEditorExtensions = [\n    starterKit,\n    placeholder,\n    link,\n    novel__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    highlight,\n    novel__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    novel__WEBPACK_IMPORTED_MODULE_10__.Color,\n    markdown,\n    taskList,\n    taskItem,\n    tiptapImage,\n    globalDragHandle\n];\n// 导出图片上传函数\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ai-editor/extensions.ts\n"));

/***/ })

});