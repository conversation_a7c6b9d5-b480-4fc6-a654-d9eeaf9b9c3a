"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ai-editor/extensions.ts":
/*!************************************************!*\
  !*** ./src/components/ai-editor/extensions.ts ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiEditorExtensions: () => (/* binding */ aiEditorExtensions),\n/* harmony export */   uploadFn: () => (/* reexport safe */ _image_upload__WEBPACK_IMPORTED_MODULE_12__.uploadFn)\n/* harmony export */ });\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+starter-kit@2.26.1/node_modules/@tiptap/starter-kit/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/novel@1.0.2_@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tipta_3fbtezt2z6la73kpow3ollimny/node_modules/novel/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-link@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-link/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-task-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-task-list/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-task-item@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-task-item/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-image@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-image/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/tiptap-extension-global-drag-handle@0.1.18/node_modules/tiptap-extension-global-drag-handle/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-underline@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-underline/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-text-style@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-text-style/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+extension-color@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+extension-text-_ochc23jjcl3wq5k6zoczrgb3ku/node_modules/@tiptap/extension-color/dist/index.js\");\n/* harmony import */ var tiptap_markdown__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! tiptap-markdown */ \"(app-pages-browser)/./node_modules/.pnpm/tiptap-markdown@0.8.10_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/tiptap-markdown/dist/tiptap-markdown.es.js\");\n/* harmony import */ var _tiptap_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/react */ \"(app-pages-browser)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _image_upload__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./image-upload */ \"(app-pages-browser)/./src/components/ai-editor/image-upload.ts\");\n\n\n\n// 自定义 AI 命令扩展\nconst AICommandExtension = _tiptap_react__WEBPACK_IMPORTED_MODULE_0__.Extension.create({\n    name: \"aiCommand\",\n    addKeyboardShortcuts () {\n        return {\n            // Ctrl/Cmd + K 触发 AI 助手\n            \"Mod-k\": ()=>{\n                // 触发 AI 助手的逻辑将在组件中处理\n                const event = new CustomEvent(\"ai-assistant-trigger\");\n                document.dispatchEvent(event);\n                return true;\n            },\n            // Ctrl/Cmd + Shift + A 快速 AI 生成\n            \"Mod-Shift-a\": ()=>{\n                const event = new CustomEvent(\"ai-quick-generate\");\n                document.dispatchEvent(event);\n                return true;\n            }\n        };\n    },\n    addCommands () {\n        return {\n            // 添加 AI 高亮命令\n            setAIHighlight: ()=>(param)=>{\n                    let { commands } = param;\n                    return commands.setHighlight({\n                        color: \"#3b82f6\"\n                    });\n                },\n            // 移除 AI 高亮命令\n            unsetAIHighlight: ()=>(param)=>{\n                    let { commands } = param;\n                    return commands.unsetHighlight();\n                }\n        };\n    }\n});\n// 基础扩展配置\nconst starterKit = novel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].configure({\n    bulletList: {\n        HTMLAttributes: {\n            class: \"ai-editor-bullet-list\"\n        }\n    },\n    orderedList: {\n        HTMLAttributes: {\n            class: \"ai-editor-ordered-list\"\n        }\n    },\n    blockquote: {\n        HTMLAttributes: {\n            class: \"ai-editor-blockquote\"\n        }\n    },\n    code: {\n        HTMLAttributes: {\n            class: \"ai-editor-code\"\n        }\n    },\n    heading: {\n        HTMLAttributes: {\n            class: \"ai-editor-heading\"\n        }\n    }\n});\n// 占位符配置\nconst placeholder = novel__WEBPACK_IMPORTED_MODULE_2__.Placeholder.configure({\n    placeholder: (param)=>{\n        let { node } = param;\n        if (node.type.name === \"heading\") {\n            return \"标题\";\n        }\n        return \"开始写作...\";\n    }\n});\n// 链接配置\nconst link = novel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].configure({\n    HTMLAttributes: {\n        class: \"ai-editor-link\"\n    },\n    openOnClick: false\n});\n// 高亮配置\nconst highlight = novel__WEBPACK_IMPORTED_MODULE_2__.HighlightExtension.configure({\n    multicolor: true\n});\n// 任务列表配置\nconst taskList = novel__WEBPACK_IMPORTED_MODULE_4__.TaskList.configure({\n    HTMLAttributes: {\n        class: \"ai-editor-task-list\"\n    }\n});\nconst taskItem = novel__WEBPACK_IMPORTED_MODULE_5__.TaskItem.configure({\n    HTMLAttributes: {\n        class: \"ai-editor-task-item\"\n    },\n    nested: true\n});\n// 图片配置\nconst tiptapImage = novel__WEBPACK_IMPORTED_MODULE_6__[\"default\"].extend({\n    addProseMirrorPlugins () {\n        return [\n            (0,novel__WEBPACK_IMPORTED_MODULE_2__.UploadImagesPlugin)({\n                imageClass: \"ai-editor-image-uploading opacity-40 rounded-lg border border-gray-200\"\n            })\n        ];\n    }\n}).configure({\n    allowBase64: true,\n    HTMLAttributes: {\n        class: \"ai-editor-image rounded-lg border border-gray-200 max-w-full h-auto\"\n    }\n});\n// 拖拽手柄配置\nconst globalDragHandle = novel__WEBPACK_IMPORTED_MODULE_7__[\"default\"].configure({\n    dragHandleWidth: 20\n});\n// 配置 Markdown 支持\nconst markdown = tiptap_markdown__WEBPACK_IMPORTED_MODULE_8__.Markdown.configure({\n    html: true,\n    tightLists: true,\n    bulletListMarker: \"-\",\n    linkify: false,\n    breaks: false\n});\n// 导出所有扩展\nconst aiEditorExtensions = [\n    starterKit,\n    placeholder,\n    link,\n    novel__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    highlight,\n    novel__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    novel__WEBPACK_IMPORTED_MODULE_11__.Color,\n    markdown,\n    taskList,\n    taskItem,\n    tiptapImage,\n    globalDragHandle\n];\n// 导出图片上传函数\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ai-editor/extensions.ts\n"));

/***/ })

});