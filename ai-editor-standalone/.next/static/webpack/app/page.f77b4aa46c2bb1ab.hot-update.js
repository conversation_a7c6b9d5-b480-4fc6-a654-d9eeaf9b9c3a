"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ai-editor/index.tsx":
/*!********************************************!*\
  !*** ./src/components/ai-editor/index.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIEditor: () => (/* binding */ AIEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! novel */ \"(app-pages-browser)/./node_modules/.pnpm/novel@1.0.2_@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tipta_3fbtezt2z6la73kpow3ollimny/node_modules/novel/dist/index.js\");\n/* harmony import */ var use_debounce__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! use-debounce */ \"(app-pages-browser)/./node_modules/.pnpm/use-debounce@10.0.5_react@18.3.1/node_modules/use-debounce/dist/index.module.js\");\n/* harmony import */ var _extensions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./extensions */ \"(app-pages-browser)/./src/components/ai-editor/extensions.ts\");\n/* harmony import */ var _slash_command__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./slash-command */ \"(app-pages-browser)/./src/components/ai-editor/slash-command.tsx\");\n/* harmony import */ var _ai_editor_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ai-editor.css */ \"(app-pages-browser)/./src/components/ai-editor/ai-editor.css\");\n/* __next_internal_client_entry_do_not_use__ AIEditor auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// 导入我们的扩展和组件\n\n\n\n// 内部编辑器组件，不使用SSR\nfunction AIEditorInternal(param) {\n    let { initialContent, placeholder = \"开始写作...\", className = \"\", onContentChange, onMarkdownChange } = param;\n    _s();\n    // 编辑器状态\n    const [editor, setEditor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAIOpen, setIsAIOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedText, setSelectedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [aiSuggestion, setAISuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 防抖更新函数\n    const debouncedUpdate = (0,use_debounce__WEBPACK_IMPORTED_MODULE_5__.useDebouncedCallback)({\n        \"AIEditorInternal.useDebouncedCallback[debouncedUpdate]\": (editor)=>{\n            const json = editor.getJSON();\n            onContentChange === null || onContentChange === void 0 ? void 0 : onContentChange(json);\n            if (onMarkdownChange) {\n                const markdown = editor.storage.markdown.getMarkdown();\n                onMarkdownChange(markdown);\n            }\n        }\n    }[\"AIEditorInternal.useDebouncedCallback[debouncedUpdate]\"], 300);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"ai-editor-container \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_6__.EditorRoot, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_6__.EditorContent, {\n                immediatelyRender: false,\n                initialContent: initialContent,\n                extensions: _extensions__WEBPACK_IMPORTED_MODULE_2__.aiEditorExtensions,\n                className: \"w-full h-full\",\n                editorProps: {\n                    handleDOMEvents: {\n                        keydown: (_view, event)=>(0,novel__WEBPACK_IMPORTED_MODULE_6__.handleCommandNavigation)(event)\n                    },\n                    handlePaste: (view, event)=>(0,novel__WEBPACK_IMPORTED_MODULE_6__.handleImagePaste)(view, event, _extensions__WEBPACK_IMPORTED_MODULE_2__.uploadFn),\n                    handleDrop: (view, event, _slice, moved)=>(0,novel__WEBPACK_IMPORTED_MODULE_6__.handleImageDrop)(view, event, moved, _extensions__WEBPACK_IMPORTED_MODULE_2__.uploadFn),\n                    attributes: {\n                        class: \"prose prose-base dark:prose-invert max-w-none p-4 focus:outline-none min-h-[400px]\",\n                        \"data-placeholder\": placeholder\n                    }\n                },\n                onCreate: (param)=>{\n                    let { editor } = param;\n                    setEditor(editor);\n                },\n                onUpdate: (param)=>{\n                    let { editor } = param;\n                    debouncedUpdate(editor);\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_6__.EditorCommand, {\n                    className: \"z-50 h-auto max-h-[330px] overflow-y-auto rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-1 py-2 shadow-md transition-all\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_6__.EditorCommandEmpty, {\n                            className: \"px-2 text-gray-500 dark:text-gray-400\",\n                            children: \"没有找到结果\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_6__.EditorCommandList, {\n                            children: _slash_command__WEBPACK_IMPORTED_MODULE_3__.suggestionItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_6__.EditorCommandItem, {\n                                    value: item.title,\n                                    onCommand: (val)=>{\n                                        var _item_command;\n                                        return (_item_command = item.command) === null || _item_command === void 0 ? void 0 : _item_command.call(item, val);\n                                    },\n                                    className: \"flex w-full items-center space-x-2 rounded-md px-2 py-1 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 aria-selected:bg-gray-100 dark:aria-selected:bg-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex h-10 w-10 items-center justify-center rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\",\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: item.title\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: item.description\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.title, true, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n_s(AIEditorInternal, \"nS+7ASJGGWenSfMwpTkhVMLT/G0=\", false, function() {\n    return [\n        use_debounce__WEBPACK_IMPORTED_MODULE_5__.useDebouncedCallback\n    ];\n});\n_c = AIEditorInternal;\n// 使用动态导入禁用SSR的主要导出组件\nfunction AIEditor(props) {\n    _s1();\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIEditor.useEffect\": ()=>{\n            setIsMounted(true);\n        }\n    }[\"AIEditor.useEffect\"], []);\n    if (!isMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"ai-editor-container \".concat(props.className || \"\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full min-h-[400px] flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500 dark:text-gray-400\",\n                    children: \"加载编辑器中...\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIEditorInternal, {\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n        lineNumber: 140,\n        columnNumber: 10\n    }, this);\n}\n_s1(AIEditor, \"h7njlszr1nxUzrk46zHyBTBrvgI=\");\n_c1 = AIEditor;\nvar _c, _c1;\n$RefreshReg$(_c, \"AIEditorInternal\");\n$RefreshReg$(_c1, \"AIEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ai-editor/index.tsx\n"));

/***/ })

});