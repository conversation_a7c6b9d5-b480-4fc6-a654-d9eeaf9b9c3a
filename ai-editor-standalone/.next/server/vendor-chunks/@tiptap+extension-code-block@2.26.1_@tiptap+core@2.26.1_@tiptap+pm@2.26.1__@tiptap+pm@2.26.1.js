"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-code-block/dist/index.js":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-code-block/dist/index.js ***!
  \*********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CodeBlock: () => (/* binding */ CodeBlock),\n/* harmony export */   backtickInputRegex: () => (/* binding */ backtickInputRegex),\n/* harmony export */   \"default\": () => (/* binding */ CodeBlock),\n/* harmony export */   tildeInputRegex: () => (/* binding */ tildeInputRegex)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/state/dist/index.js\");\n\n\n\n/**\n * Matches a code block with backticks.\n */\nconst backtickInputRegex = /^```([a-z]+)?[\\s\\n]$/;\n/**\n * Matches a code block with tildes.\n */\nconst tildeInputRegex = /^~~~([a-z]+)?[\\s\\n]$/;\n/**\n * This extension allows you to create code blocks.\n * @see https://tiptap.dev/api/nodes/code-block\n */\nconst CodeBlock = _tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Node.create({\n    name: 'codeBlock',\n    addOptions() {\n        return {\n            languageClassPrefix: 'language-',\n            exitOnTripleEnter: true,\n            exitOnArrowDown: true,\n            defaultLanguage: null,\n            HTMLAttributes: {},\n        };\n    },\n    content: 'text*',\n    marks: '',\n    group: 'block',\n    code: true,\n    defining: true,\n    addAttributes() {\n        return {\n            language: {\n                default: this.options.defaultLanguage,\n                parseHTML: element => {\n                    var _a;\n                    const { languageClassPrefix } = this.options;\n                    const classNames = [...(((_a = element.firstElementChild) === null || _a === void 0 ? void 0 : _a.classList) || [])];\n                    const languages = classNames\n                        .filter(className => className.startsWith(languageClassPrefix))\n                        .map(className => className.replace(languageClassPrefix, ''));\n                    const language = languages[0];\n                    if (!language) {\n                        return null;\n                    }\n                    return language;\n                },\n                rendered: false,\n            },\n        };\n    },\n    parseHTML() {\n        return [\n            {\n                tag: 'pre',\n                preserveWhitespace: 'full',\n            },\n        ];\n    },\n    renderHTML({ node, HTMLAttributes }) {\n        return [\n            'pre',\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes),\n            [\n                'code',\n                {\n                    class: node.attrs.language\n                        ? this.options.languageClassPrefix + node.attrs.language\n                        : null,\n                },\n                0,\n            ],\n        ];\n    },\n    addCommands() {\n        return {\n            setCodeBlock: attributes => ({ commands }) => {\n                return commands.setNode(this.name, attributes);\n            },\n            toggleCodeBlock: attributes => ({ commands }) => {\n                return commands.toggleNode(this.name, 'paragraph', attributes);\n            },\n        };\n    },\n    addKeyboardShortcuts() {\n        return {\n            'Mod-Alt-c': () => this.editor.commands.toggleCodeBlock(),\n            // remove code block when at start of document or code block is empty\n            Backspace: () => {\n                const { empty, $anchor } = this.editor.state.selection;\n                const isAtStart = $anchor.pos === 1;\n                if (!empty || $anchor.parent.type.name !== this.name) {\n                    return false;\n                }\n                if (isAtStart || !$anchor.parent.textContent.length) {\n                    return this.editor.commands.clearNodes();\n                }\n                return false;\n            },\n            // exit node on triple enter\n            Enter: ({ editor }) => {\n                if (!this.options.exitOnTripleEnter) {\n                    return false;\n                }\n                const { state } = editor;\n                const { selection } = state;\n                const { $from, empty } = selection;\n                if (!empty || $from.parent.type !== this.type) {\n                    return false;\n                }\n                const isAtEnd = $from.parentOffset === $from.parent.nodeSize - 2;\n                const endsWithDoubleNewline = $from.parent.textContent.endsWith('\\n\\n');\n                if (!isAtEnd || !endsWithDoubleNewline) {\n                    return false;\n                }\n                return editor\n                    .chain()\n                    .command(({ tr }) => {\n                    tr.delete($from.pos - 2, $from.pos);\n                    return true;\n                })\n                    .exitCode()\n                    .run();\n            },\n            // exit node on arrow down\n            ArrowDown: ({ editor }) => {\n                if (!this.options.exitOnArrowDown) {\n                    return false;\n                }\n                const { state } = editor;\n                const { selection, doc } = state;\n                const { $from, empty } = selection;\n                if (!empty || $from.parent.type !== this.type) {\n                    return false;\n                }\n                const isAtEnd = $from.parentOffset === $from.parent.nodeSize - 2;\n                if (!isAtEnd) {\n                    return false;\n                }\n                const after = $from.after();\n                if (after === undefined) {\n                    return false;\n                }\n                const nodeAfter = doc.nodeAt(after);\n                if (nodeAfter) {\n                    return editor.commands.command(({ tr }) => {\n                        tr.setSelection(_tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near(doc.resolve(after)));\n                        return true;\n                    });\n                }\n                return editor.commands.exitCode();\n            },\n        };\n    },\n    addInputRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.textblockTypeInputRule)({\n                find: backtickInputRegex,\n                type: this.type,\n                getAttributes: match => ({\n                    language: match[1],\n                }),\n            }),\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.textblockTypeInputRule)({\n                find: tildeInputRegex,\n                type: this.type,\n                getAttributes: match => ({\n                    language: match[1],\n                }),\n            }),\n        ];\n    },\n    addProseMirrorPlugins() {\n        return [\n            // this plugin creates a code block for pasted content from VS Code\n            // we can also detect the copied code language\n            new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n                key: new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey('codeBlockVSCodeHandler'),\n                props: {\n                    handlePaste: (view, event) => {\n                        if (!event.clipboardData) {\n                            return false;\n                        }\n                        // don’t create a new code block within code blocks\n                        if (this.editor.isActive(this.type.name)) {\n                            return false;\n                        }\n                        const text = event.clipboardData.getData('text/plain');\n                        const vscode = event.clipboardData.getData('vscode-editor-data');\n                        const vscodeData = vscode ? JSON.parse(vscode) : undefined;\n                        const language = vscodeData === null || vscodeData === void 0 ? void 0 : vscodeData.mode;\n                        if (!text || !language) {\n                            return false;\n                        }\n                        const { tr, schema } = view.state;\n                        // prepare a text node\n                        // strip carriage return chars from text pasted as code\n                        // see: https://github.com/ProseMirror/prosemirror-view/commit/a50a6bcceb4ce52ac8fcc6162488d8875613aacd\n                        const textNode = schema.text(text.replace(/\\r\\n?/g, '\\n'));\n                        // create a code block with the text node\n                        // replace selection with the code block\n                        tr.replaceSelectionWith(this.type.create({ language }, textNode));\n                        if (tr.selection.$from.parent.type !== this.type) {\n                            // put cursor inside the newly created code block\n                            tr.setSelection(_tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.near(tr.doc.resolve(Math.max(0, tr.selection.from - 2))));\n                        }\n                        // store meta information\n                        // this is useful for other plugins that depends on the paste event\n                        // like the paste rule plugin\n                        tr.setMeta('paste', true);\n                        view.dispatch(tr);\n                        return true;\n                    },\n                },\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-code-block/dist/index.js\n");

/***/ })

};
;