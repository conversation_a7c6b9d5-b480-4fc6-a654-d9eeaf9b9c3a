"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-model";
exports.ids = ["vendor-chunks/prosemirror-model"];
exports.modules = {

/***/ "(ssr)/./node_modules/prosemirror-model/dist/index.js":
/*!******************************************************!*\
  !*** ./node_modules/prosemirror-model/dist/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContentMatch: () => (/* binding */ ContentMatch),\n/* harmony export */   DOMParser: () => (/* binding */ DOMParser),\n/* harmony export */   DOMSerializer: () => (/* binding */ DOMSerializer),\n/* harmony export */   Fragment: () => (/* binding */ Fragment),\n/* harmony export */   Mark: () => (/* binding */ Mark),\n/* harmony export */   MarkType: () => (/* binding */ MarkType),\n/* harmony export */   Node: () => (/* binding */ Node),\n/* harmony export */   NodeRange: () => (/* binding */ NodeRange),\n/* harmony export */   NodeType: () => (/* binding */ NodeType),\n/* harmony export */   ReplaceError: () => (/* binding */ ReplaceError),\n/* harmony export */   ResolvedPos: () => (/* binding */ ResolvedPos),\n/* harmony export */   Schema: () => (/* binding */ Schema),\n/* harmony export */   Slice: () => (/* binding */ Slice)\n/* harmony export */ });\n/* harmony import */ var orderedmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! orderedmap */ \"(ssr)/./node_modules/orderedmap/dist/index.js\");\n\n\nfunction findDiffStart(a, b, pos) {\n    for (let i = 0;; i++) {\n        if (i == a.childCount || i == b.childCount)\n            return a.childCount == b.childCount ? null : pos;\n        let childA = a.child(i), childB = b.child(i);\n        if (childA == childB) {\n            pos += childA.nodeSize;\n            continue;\n        }\n        if (!childA.sameMarkup(childB))\n            return pos;\n        if (childA.isText && childA.text != childB.text) {\n            for (let j = 0; childA.text[j] == childB.text[j]; j++)\n                pos++;\n            return pos;\n        }\n        if (childA.content.size || childB.content.size) {\n            let inner = findDiffStart(childA.content, childB.content, pos + 1);\n            if (inner != null)\n                return inner;\n        }\n        pos += childA.nodeSize;\n    }\n}\nfunction findDiffEnd(a, b, posA, posB) {\n    for (let iA = a.childCount, iB = b.childCount;;) {\n        if (iA == 0 || iB == 0)\n            return iA == iB ? null : { a: posA, b: posB };\n        let childA = a.child(--iA), childB = b.child(--iB), size = childA.nodeSize;\n        if (childA == childB) {\n            posA -= size;\n            posB -= size;\n            continue;\n        }\n        if (!childA.sameMarkup(childB))\n            return { a: posA, b: posB };\n        if (childA.isText && childA.text != childB.text) {\n            let same = 0, minSize = Math.min(childA.text.length, childB.text.length);\n            while (same < minSize && childA.text[childA.text.length - same - 1] == childB.text[childB.text.length - same - 1]) {\n                same++;\n                posA--;\n                posB--;\n            }\n            return { a: posA, b: posB };\n        }\n        if (childA.content.size || childB.content.size) {\n            let inner = findDiffEnd(childA.content, childB.content, posA - 1, posB - 1);\n            if (inner)\n                return inner;\n        }\n        posA -= size;\n        posB -= size;\n    }\n}\n\n/**\nA fragment represents a node's collection of child nodes.\n\nLike nodes, fragments are persistent data structures, and you\nshould not mutate them or their content. Rather, you create new\ninstances whenever needed. The API tries to make this easy.\n*/\nclass Fragment {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The child nodes in this fragment.\n    */\n    content, size) {\n        this.content = content;\n        this.size = size || 0;\n        if (size == null)\n            for (let i = 0; i < content.length; i++)\n                this.size += content[i].nodeSize;\n    }\n    /**\n    Invoke a callback for all descendant nodes between the given two\n    positions (relative to start of this fragment). Doesn't descend\n    into a node when the callback returns `false`.\n    */\n    nodesBetween(from, to, f, nodeStart = 0, parent) {\n        for (let i = 0, pos = 0; pos < to; i++) {\n            let child = this.content[i], end = pos + child.nodeSize;\n            if (end > from && f(child, nodeStart + pos, parent || null, i) !== false && child.content.size) {\n                let start = pos + 1;\n                child.nodesBetween(Math.max(0, from - start), Math.min(child.content.size, to - start), f, nodeStart + start);\n            }\n            pos = end;\n        }\n    }\n    /**\n    Call the given callback for every descendant node. `pos` will be\n    relative to the start of the fragment. The callback may return\n    `false` to prevent traversal of a given node's children.\n    */\n    descendants(f) {\n        this.nodesBetween(0, this.size, f);\n    }\n    /**\n    Extract the text between `from` and `to`. See the same method on\n    [`Node`](https://prosemirror.net/docs/ref/#model.Node.textBetween).\n    */\n    textBetween(from, to, blockSeparator, leafText) {\n        let text = \"\", first = true;\n        this.nodesBetween(from, to, (node, pos) => {\n            let nodeText = node.isText ? node.text.slice(Math.max(from, pos) - pos, to - pos)\n                : !node.isLeaf ? \"\"\n                    : leafText ? (typeof leafText === \"function\" ? leafText(node) : leafText)\n                        : node.type.spec.leafText ? node.type.spec.leafText(node)\n                            : \"\";\n            if (node.isBlock && (node.isLeaf && nodeText || node.isTextblock) && blockSeparator) {\n                if (first)\n                    first = false;\n                else\n                    text += blockSeparator;\n            }\n            text += nodeText;\n        }, 0);\n        return text;\n    }\n    /**\n    Create a new fragment containing the combined content of this\n    fragment and the other.\n    */\n    append(other) {\n        if (!other.size)\n            return this;\n        if (!this.size)\n            return other;\n        let last = this.lastChild, first = other.firstChild, content = this.content.slice(), i = 0;\n        if (last.isText && last.sameMarkup(first)) {\n            content[content.length - 1] = last.withText(last.text + first.text);\n            i = 1;\n        }\n        for (; i < other.content.length; i++)\n            content.push(other.content[i]);\n        return new Fragment(content, this.size + other.size);\n    }\n    /**\n    Cut out the sub-fragment between the two given positions.\n    */\n    cut(from, to = this.size) {\n        if (from == 0 && to == this.size)\n            return this;\n        let result = [], size = 0;\n        if (to > from)\n            for (let i = 0, pos = 0; pos < to; i++) {\n                let child = this.content[i], end = pos + child.nodeSize;\n                if (end > from) {\n                    if (pos < from || end > to) {\n                        if (child.isText)\n                            child = child.cut(Math.max(0, from - pos), Math.min(child.text.length, to - pos));\n                        else\n                            child = child.cut(Math.max(0, from - pos - 1), Math.min(child.content.size, to - pos - 1));\n                    }\n                    result.push(child);\n                    size += child.nodeSize;\n                }\n                pos = end;\n            }\n        return new Fragment(result, size);\n    }\n    /**\n    @internal\n    */\n    cutByIndex(from, to) {\n        if (from == to)\n            return Fragment.empty;\n        if (from == 0 && to == this.content.length)\n            return this;\n        return new Fragment(this.content.slice(from, to));\n    }\n    /**\n    Create a new fragment in which the node at the given index is\n    replaced by the given node.\n    */\n    replaceChild(index, node) {\n        let current = this.content[index];\n        if (current == node)\n            return this;\n        let copy = this.content.slice();\n        let size = this.size + node.nodeSize - current.nodeSize;\n        copy[index] = node;\n        return new Fragment(copy, size);\n    }\n    /**\n    Create a new fragment by prepending the given node to this\n    fragment.\n    */\n    addToStart(node) {\n        return new Fragment([node].concat(this.content), this.size + node.nodeSize);\n    }\n    /**\n    Create a new fragment by appending the given node to this\n    fragment.\n    */\n    addToEnd(node) {\n        return new Fragment(this.content.concat(node), this.size + node.nodeSize);\n    }\n    /**\n    Compare this fragment to another one.\n    */\n    eq(other) {\n        if (this.content.length != other.content.length)\n            return false;\n        for (let i = 0; i < this.content.length; i++)\n            if (!this.content[i].eq(other.content[i]))\n                return false;\n        return true;\n    }\n    /**\n    The first child of the fragment, or `null` if it is empty.\n    */\n    get firstChild() { return this.content.length ? this.content[0] : null; }\n    /**\n    The last child of the fragment, or `null` if it is empty.\n    */\n    get lastChild() { return this.content.length ? this.content[this.content.length - 1] : null; }\n    /**\n    The number of child nodes in this fragment.\n    */\n    get childCount() { return this.content.length; }\n    /**\n    Get the child node at the given index. Raise an error when the\n    index is out of range.\n    */\n    child(index) {\n        let found = this.content[index];\n        if (!found)\n            throw new RangeError(\"Index \" + index + \" out of range for \" + this);\n        return found;\n    }\n    /**\n    Get the child node at the given index, if it exists.\n    */\n    maybeChild(index) {\n        return this.content[index] || null;\n    }\n    /**\n    Call `f` for every child node, passing the node, its offset\n    into this parent node, and its index.\n    */\n    forEach(f) {\n        for (let i = 0, p = 0; i < this.content.length; i++) {\n            let child = this.content[i];\n            f(child, p, i);\n            p += child.nodeSize;\n        }\n    }\n    /**\n    Find the first position at which this fragment and another\n    fragment differ, or `null` if they are the same.\n    */\n    findDiffStart(other, pos = 0) {\n        return findDiffStart(this, other, pos);\n    }\n    /**\n    Find the first position, searching from the end, at which this\n    fragment and the given fragment differ, or `null` if they are\n    the same. Since this position will not be the same in both\n    nodes, an object with two separate positions is returned.\n    */\n    findDiffEnd(other, pos = this.size, otherPos = other.size) {\n        return findDiffEnd(this, other, pos, otherPos);\n    }\n    /**\n    Find the index and inner offset corresponding to a given relative\n    position in this fragment. The result object will be reused\n    (overwritten) the next time the function is called. @internal\n    */\n    findIndex(pos) {\n        if (pos == 0)\n            return retIndex(0, pos);\n        if (pos == this.size)\n            return retIndex(this.content.length, pos);\n        if (pos > this.size || pos < 0)\n            throw new RangeError(`Position ${pos} outside of fragment (${this})`);\n        for (let i = 0, curPos = 0;; i++) {\n            let cur = this.child(i), end = curPos + cur.nodeSize;\n            if (end >= pos) {\n                if (end == pos)\n                    return retIndex(i + 1, end);\n                return retIndex(i, curPos);\n            }\n            curPos = end;\n        }\n    }\n    /**\n    Return a debugging string that describes this fragment.\n    */\n    toString() { return \"<\" + this.toStringInner() + \">\"; }\n    /**\n    @internal\n    */\n    toStringInner() { return this.content.join(\", \"); }\n    /**\n    Create a JSON-serializeable representation of this fragment.\n    */\n    toJSON() {\n        return this.content.length ? this.content.map(n => n.toJSON()) : null;\n    }\n    /**\n    Deserialize a fragment from its JSON representation.\n    */\n    static fromJSON(schema, value) {\n        if (!value)\n            return Fragment.empty;\n        if (!Array.isArray(value))\n            throw new RangeError(\"Invalid input for Fragment.fromJSON\");\n        return new Fragment(value.map(schema.nodeFromJSON));\n    }\n    /**\n    Build a fragment from an array of nodes. Ensures that adjacent\n    text nodes with the same marks are joined together.\n    */\n    static fromArray(array) {\n        if (!array.length)\n            return Fragment.empty;\n        let joined, size = 0;\n        for (let i = 0; i < array.length; i++) {\n            let node = array[i];\n            size += node.nodeSize;\n            if (i && node.isText && array[i - 1].sameMarkup(node)) {\n                if (!joined)\n                    joined = array.slice(0, i);\n                joined[joined.length - 1] = node\n                    .withText(joined[joined.length - 1].text + node.text);\n            }\n            else if (joined) {\n                joined.push(node);\n            }\n        }\n        return new Fragment(joined || array, size);\n    }\n    /**\n    Create a fragment from something that can be interpreted as a\n    set of nodes. For `null`, it returns the empty fragment. For a\n    fragment, the fragment itself. For a node or array of nodes, a\n    fragment containing those nodes.\n    */\n    static from(nodes) {\n        if (!nodes)\n            return Fragment.empty;\n        if (nodes instanceof Fragment)\n            return nodes;\n        if (Array.isArray(nodes))\n            return this.fromArray(nodes);\n        if (nodes.attrs)\n            return new Fragment([nodes], nodes.nodeSize);\n        throw new RangeError(\"Can not convert \" + nodes + \" to a Fragment\" +\n            (nodes.nodesBetween ? \" (looks like multiple versions of prosemirror-model were loaded)\" : \"\"));\n    }\n}\n/**\nAn empty fragment. Intended to be reused whenever a node doesn't\ncontain anything (rather than allocating a new empty fragment for\neach leaf node).\n*/\nFragment.empty = new Fragment([], 0);\nconst found = { index: 0, offset: 0 };\nfunction retIndex(index, offset) {\n    found.index = index;\n    found.offset = offset;\n    return found;\n}\n\nfunction compareDeep(a, b) {\n    if (a === b)\n        return true;\n    if (!(a && typeof a == \"object\") ||\n        !(b && typeof b == \"object\"))\n        return false;\n    let array = Array.isArray(a);\n    if (Array.isArray(b) != array)\n        return false;\n    if (array) {\n        if (a.length != b.length)\n            return false;\n        for (let i = 0; i < a.length; i++)\n            if (!compareDeep(a[i], b[i]))\n                return false;\n    }\n    else {\n        for (let p in a)\n            if (!(p in b) || !compareDeep(a[p], b[p]))\n                return false;\n        for (let p in b)\n            if (!(p in a))\n                return false;\n    }\n    return true;\n}\n\n/**\nA mark is a piece of information that can be attached to a node,\nsuch as it being emphasized, in code font, or a link. It has a\ntype and optionally a set of attributes that provide further\ninformation (such as the target of the link). Marks are created\nthrough a `Schema`, which controls which types exist and which\nattributes they have.\n*/\nclass Mark {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The type of this mark.\n    */\n    type, \n    /**\n    The attributes associated with this mark.\n    */\n    attrs) {\n        this.type = type;\n        this.attrs = attrs;\n    }\n    /**\n    Given a set of marks, create a new set which contains this one as\n    well, in the right position. If this mark is already in the set,\n    the set itself is returned. If any marks that are set to be\n    [exclusive](https://prosemirror.net/docs/ref/#model.MarkSpec.excludes) with this mark are present,\n    those are replaced by this one.\n    */\n    addToSet(set) {\n        let copy, placed = false;\n        for (let i = 0; i < set.length; i++) {\n            let other = set[i];\n            if (this.eq(other))\n                return set;\n            if (this.type.excludes(other.type)) {\n                if (!copy)\n                    copy = set.slice(0, i);\n            }\n            else if (other.type.excludes(this.type)) {\n                return set;\n            }\n            else {\n                if (!placed && other.type.rank > this.type.rank) {\n                    if (!copy)\n                        copy = set.slice(0, i);\n                    copy.push(this);\n                    placed = true;\n                }\n                if (copy)\n                    copy.push(other);\n            }\n        }\n        if (!copy)\n            copy = set.slice();\n        if (!placed)\n            copy.push(this);\n        return copy;\n    }\n    /**\n    Remove this mark from the given set, returning a new set. If this\n    mark is not in the set, the set itself is returned.\n    */\n    removeFromSet(set) {\n        for (let i = 0; i < set.length; i++)\n            if (this.eq(set[i]))\n                return set.slice(0, i).concat(set.slice(i + 1));\n        return set;\n    }\n    /**\n    Test whether this mark is in the given set of marks.\n    */\n    isInSet(set) {\n        for (let i = 0; i < set.length; i++)\n            if (this.eq(set[i]))\n                return true;\n        return false;\n    }\n    /**\n    Test whether this mark has the same type and attributes as\n    another mark.\n    */\n    eq(other) {\n        return this == other ||\n            (this.type == other.type && compareDeep(this.attrs, other.attrs));\n    }\n    /**\n    Convert this mark to a JSON-serializeable representation.\n    */\n    toJSON() {\n        let obj = { type: this.type.name };\n        for (let _ in this.attrs) {\n            obj.attrs = this.attrs;\n            break;\n        }\n        return obj;\n    }\n    /**\n    Deserialize a mark from JSON.\n    */\n    static fromJSON(schema, json) {\n        if (!json)\n            throw new RangeError(\"Invalid input for Mark.fromJSON\");\n        let type = schema.marks[json.type];\n        if (!type)\n            throw new RangeError(`There is no mark type ${json.type} in this schema`);\n        let mark = type.create(json.attrs);\n        type.checkAttrs(mark.attrs);\n        return mark;\n    }\n    /**\n    Test whether two sets of marks are identical.\n    */\n    static sameSet(a, b) {\n        if (a == b)\n            return true;\n        if (a.length != b.length)\n            return false;\n        for (let i = 0; i < a.length; i++)\n            if (!a[i].eq(b[i]))\n                return false;\n        return true;\n    }\n    /**\n    Create a properly sorted mark set from null, a single mark, or an\n    unsorted array of marks.\n    */\n    static setFrom(marks) {\n        if (!marks || Array.isArray(marks) && marks.length == 0)\n            return Mark.none;\n        if (marks instanceof Mark)\n            return [marks];\n        let copy = marks.slice();\n        copy.sort((a, b) => a.type.rank - b.type.rank);\n        return copy;\n    }\n}\n/**\nThe empty set of marks.\n*/\nMark.none = [];\n\n/**\nError type raised by [`Node.replace`](https://prosemirror.net/docs/ref/#model.Node.replace) when\ngiven an invalid replacement.\n*/\nclass ReplaceError extends Error {\n}\n/*\nReplaceError = function(this: any, message: string) {\n  let err = Error.call(this, message)\n  ;(err as any).__proto__ = ReplaceError.prototype\n  return err\n} as any\n\nReplaceError.prototype = Object.create(Error.prototype)\nReplaceError.prototype.constructor = ReplaceError\nReplaceError.prototype.name = \"ReplaceError\"\n*/\n/**\nA slice represents a piece cut out of a larger document. It\nstores not only a fragment, but also the depth up to which nodes on\nboth side are ‘open’ (cut through).\n*/\nclass Slice {\n    /**\n    Create a slice. When specifying a non-zero open depth, you must\n    make sure that there are nodes of at least that depth at the\n    appropriate side of the fragment—i.e. if the fragment is an\n    empty paragraph node, `openStart` and `openEnd` can't be greater\n    than 1.\n    \n    It is not necessary for the content of open nodes to conform to\n    the schema's content constraints, though it should be a valid\n    start/end/middle for such a node, depending on which sides are\n    open.\n    */\n    constructor(\n    /**\n    The slice's content.\n    */\n    content, \n    /**\n    The open depth at the start of the fragment.\n    */\n    openStart, \n    /**\n    The open depth at the end.\n    */\n    openEnd) {\n        this.content = content;\n        this.openStart = openStart;\n        this.openEnd = openEnd;\n    }\n    /**\n    The size this slice would add when inserted into a document.\n    */\n    get size() {\n        return this.content.size - this.openStart - this.openEnd;\n    }\n    /**\n    @internal\n    */\n    insertAt(pos, fragment) {\n        let content = insertInto(this.content, pos + this.openStart, fragment);\n        return content && new Slice(content, this.openStart, this.openEnd);\n    }\n    /**\n    @internal\n    */\n    removeBetween(from, to) {\n        return new Slice(removeRange(this.content, from + this.openStart, to + this.openStart), this.openStart, this.openEnd);\n    }\n    /**\n    Tests whether this slice is equal to another slice.\n    */\n    eq(other) {\n        return this.content.eq(other.content) && this.openStart == other.openStart && this.openEnd == other.openEnd;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return this.content + \"(\" + this.openStart + \",\" + this.openEnd + \")\";\n    }\n    /**\n    Convert a slice to a JSON-serializable representation.\n    */\n    toJSON() {\n        if (!this.content.size)\n            return null;\n        let json = { content: this.content.toJSON() };\n        if (this.openStart > 0)\n            json.openStart = this.openStart;\n        if (this.openEnd > 0)\n            json.openEnd = this.openEnd;\n        return json;\n    }\n    /**\n    Deserialize a slice from its JSON representation.\n    */\n    static fromJSON(schema, json) {\n        if (!json)\n            return Slice.empty;\n        let openStart = json.openStart || 0, openEnd = json.openEnd || 0;\n        if (typeof openStart != \"number\" || typeof openEnd != \"number\")\n            throw new RangeError(\"Invalid input for Slice.fromJSON\");\n        return new Slice(Fragment.fromJSON(schema, json.content), openStart, openEnd);\n    }\n    /**\n    Create a slice from a fragment by taking the maximum possible\n    open value on both side of the fragment.\n    */\n    static maxOpen(fragment, openIsolating = true) {\n        let openStart = 0, openEnd = 0;\n        for (let n = fragment.firstChild; n && !n.isLeaf && (openIsolating || !n.type.spec.isolating); n = n.firstChild)\n            openStart++;\n        for (let n = fragment.lastChild; n && !n.isLeaf && (openIsolating || !n.type.spec.isolating); n = n.lastChild)\n            openEnd++;\n        return new Slice(fragment, openStart, openEnd);\n    }\n}\n/**\nThe empty slice.\n*/\nSlice.empty = new Slice(Fragment.empty, 0, 0);\nfunction removeRange(content, from, to) {\n    let { index, offset } = content.findIndex(from), child = content.maybeChild(index);\n    let { index: indexTo, offset: offsetTo } = content.findIndex(to);\n    if (offset == from || child.isText) {\n        if (offsetTo != to && !content.child(indexTo).isText)\n            throw new RangeError(\"Removing non-flat range\");\n        return content.cut(0, from).append(content.cut(to));\n    }\n    if (index != indexTo)\n        throw new RangeError(\"Removing non-flat range\");\n    return content.replaceChild(index, child.copy(removeRange(child.content, from - offset - 1, to - offset - 1)));\n}\nfunction insertInto(content, dist, insert, parent) {\n    let { index, offset } = content.findIndex(dist), child = content.maybeChild(index);\n    if (offset == dist || child.isText) {\n        if (parent && !parent.canReplace(index, index, insert))\n            return null;\n        return content.cut(0, dist).append(insert).append(content.cut(dist));\n    }\n    let inner = insertInto(child.content, dist - offset - 1, insert);\n    return inner && content.replaceChild(index, child.copy(inner));\n}\nfunction replace($from, $to, slice) {\n    if (slice.openStart > $from.depth)\n        throw new ReplaceError(\"Inserted content deeper than insertion position\");\n    if ($from.depth - slice.openStart != $to.depth - slice.openEnd)\n        throw new ReplaceError(\"Inconsistent open depths\");\n    return replaceOuter($from, $to, slice, 0);\n}\nfunction replaceOuter($from, $to, slice, depth) {\n    let index = $from.index(depth), node = $from.node(depth);\n    if (index == $to.index(depth) && depth < $from.depth - slice.openStart) {\n        let inner = replaceOuter($from, $to, slice, depth + 1);\n        return node.copy(node.content.replaceChild(index, inner));\n    }\n    else if (!slice.content.size) {\n        return close(node, replaceTwoWay($from, $to, depth));\n    }\n    else if (!slice.openStart && !slice.openEnd && $from.depth == depth && $to.depth == depth) { // Simple, flat case\n        let parent = $from.parent, content = parent.content;\n        return close(parent, content.cut(0, $from.parentOffset).append(slice.content).append(content.cut($to.parentOffset)));\n    }\n    else {\n        let { start, end } = prepareSliceForReplace(slice, $from);\n        return close(node, replaceThreeWay($from, start, end, $to, depth));\n    }\n}\nfunction checkJoin(main, sub) {\n    if (!sub.type.compatibleContent(main.type))\n        throw new ReplaceError(\"Cannot join \" + sub.type.name + \" onto \" + main.type.name);\n}\nfunction joinable($before, $after, depth) {\n    let node = $before.node(depth);\n    checkJoin(node, $after.node(depth));\n    return node;\n}\nfunction addNode(child, target) {\n    let last = target.length - 1;\n    if (last >= 0 && child.isText && child.sameMarkup(target[last]))\n        target[last] = child.withText(target[last].text + child.text);\n    else\n        target.push(child);\n}\nfunction addRange($start, $end, depth, target) {\n    let node = ($end || $start).node(depth);\n    let startIndex = 0, endIndex = $end ? $end.index(depth) : node.childCount;\n    if ($start) {\n        startIndex = $start.index(depth);\n        if ($start.depth > depth) {\n            startIndex++;\n        }\n        else if ($start.textOffset) {\n            addNode($start.nodeAfter, target);\n            startIndex++;\n        }\n    }\n    for (let i = startIndex; i < endIndex; i++)\n        addNode(node.child(i), target);\n    if ($end && $end.depth == depth && $end.textOffset)\n        addNode($end.nodeBefore, target);\n}\nfunction close(node, content) {\n    node.type.checkContent(content);\n    return node.copy(content);\n}\nfunction replaceThreeWay($from, $start, $end, $to, depth) {\n    let openStart = $from.depth > depth && joinable($from, $start, depth + 1);\n    let openEnd = $to.depth > depth && joinable($end, $to, depth + 1);\n    let content = [];\n    addRange(null, $from, depth, content);\n    if (openStart && openEnd && $start.index(depth) == $end.index(depth)) {\n        checkJoin(openStart, openEnd);\n        addNode(close(openStart, replaceThreeWay($from, $start, $end, $to, depth + 1)), content);\n    }\n    else {\n        if (openStart)\n            addNode(close(openStart, replaceTwoWay($from, $start, depth + 1)), content);\n        addRange($start, $end, depth, content);\n        if (openEnd)\n            addNode(close(openEnd, replaceTwoWay($end, $to, depth + 1)), content);\n    }\n    addRange($to, null, depth, content);\n    return new Fragment(content);\n}\nfunction replaceTwoWay($from, $to, depth) {\n    let content = [];\n    addRange(null, $from, depth, content);\n    if ($from.depth > depth) {\n        let type = joinable($from, $to, depth + 1);\n        addNode(close(type, replaceTwoWay($from, $to, depth + 1)), content);\n    }\n    addRange($to, null, depth, content);\n    return new Fragment(content);\n}\nfunction prepareSliceForReplace(slice, $along) {\n    let extra = $along.depth - slice.openStart, parent = $along.node(extra);\n    let node = parent.copy(slice.content);\n    for (let i = extra - 1; i >= 0; i--)\n        node = $along.node(i).copy(Fragment.from(node));\n    return { start: node.resolveNoCache(slice.openStart + extra),\n        end: node.resolveNoCache(node.content.size - slice.openEnd - extra) };\n}\n\n/**\nYou can [_resolve_](https://prosemirror.net/docs/ref/#model.Node.resolve) a position to get more\ninformation about it. Objects of this class represent such a\nresolved position, providing various pieces of context\ninformation, and some helper methods.\n\nThroughout this interface, methods that take an optional `depth`\nparameter will interpret undefined as `this.depth` and negative\nnumbers as `this.depth + value`.\n*/\nclass ResolvedPos {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The position that was resolved.\n    */\n    pos, \n    /**\n    @internal\n    */\n    path, \n    /**\n    The offset this position has into its parent node.\n    */\n    parentOffset) {\n        this.pos = pos;\n        this.path = path;\n        this.parentOffset = parentOffset;\n        this.depth = path.length / 3 - 1;\n    }\n    /**\n    @internal\n    */\n    resolveDepth(val) {\n        if (val == null)\n            return this.depth;\n        if (val < 0)\n            return this.depth + val;\n        return val;\n    }\n    /**\n    The parent node that the position points into. Note that even if\n    a position points into a text node, that node is not considered\n    the parent—text nodes are ‘flat’ in this model, and have no content.\n    */\n    get parent() { return this.node(this.depth); }\n    /**\n    The root node in which the position was resolved.\n    */\n    get doc() { return this.node(0); }\n    /**\n    The ancestor node at the given level. `p.node(p.depth)` is the\n    same as `p.parent`.\n    */\n    node(depth) { return this.path[this.resolveDepth(depth) * 3]; }\n    /**\n    The index into the ancestor at the given level. If this points\n    at the 3rd node in the 2nd paragraph on the top level, for\n    example, `p.index(0)` is 1 and `p.index(1)` is 2.\n    */\n    index(depth) { return this.path[this.resolveDepth(depth) * 3 + 1]; }\n    /**\n    The index pointing after this position into the ancestor at the\n    given level.\n    */\n    indexAfter(depth) {\n        depth = this.resolveDepth(depth);\n        return this.index(depth) + (depth == this.depth && !this.textOffset ? 0 : 1);\n    }\n    /**\n    The (absolute) position at the start of the node at the given\n    level.\n    */\n    start(depth) {\n        depth = this.resolveDepth(depth);\n        return depth == 0 ? 0 : this.path[depth * 3 - 1] + 1;\n    }\n    /**\n    The (absolute) position at the end of the node at the given\n    level.\n    */\n    end(depth) {\n        depth = this.resolveDepth(depth);\n        return this.start(depth) + this.node(depth).content.size;\n    }\n    /**\n    The (absolute) position directly before the wrapping node at the\n    given level, or, when `depth` is `this.depth + 1`, the original\n    position.\n    */\n    before(depth) {\n        depth = this.resolveDepth(depth);\n        if (!depth)\n            throw new RangeError(\"There is no position before the top-level node\");\n        return depth == this.depth + 1 ? this.pos : this.path[depth * 3 - 1];\n    }\n    /**\n    The (absolute) position directly after the wrapping node at the\n    given level, or the original position when `depth` is `this.depth + 1`.\n    */\n    after(depth) {\n        depth = this.resolveDepth(depth);\n        if (!depth)\n            throw new RangeError(\"There is no position after the top-level node\");\n        return depth == this.depth + 1 ? this.pos : this.path[depth * 3 - 1] + this.path[depth * 3].nodeSize;\n    }\n    /**\n    When this position points into a text node, this returns the\n    distance between the position and the start of the text node.\n    Will be zero for positions that point between nodes.\n    */\n    get textOffset() { return this.pos - this.path[this.path.length - 1]; }\n    /**\n    Get the node directly after the position, if any. If the position\n    points into a text node, only the part of that node after the\n    position is returned.\n    */\n    get nodeAfter() {\n        let parent = this.parent, index = this.index(this.depth);\n        if (index == parent.childCount)\n            return null;\n        let dOff = this.pos - this.path[this.path.length - 1], child = parent.child(index);\n        return dOff ? parent.child(index).cut(dOff) : child;\n    }\n    /**\n    Get the node directly before the position, if any. If the\n    position points into a text node, only the part of that node\n    before the position is returned.\n    */\n    get nodeBefore() {\n        let index = this.index(this.depth);\n        let dOff = this.pos - this.path[this.path.length - 1];\n        if (dOff)\n            return this.parent.child(index).cut(0, dOff);\n        return index == 0 ? null : this.parent.child(index - 1);\n    }\n    /**\n    Get the position at the given index in the parent node at the\n    given depth (which defaults to `this.depth`).\n    */\n    posAtIndex(index, depth) {\n        depth = this.resolveDepth(depth);\n        let node = this.path[depth * 3], pos = depth == 0 ? 0 : this.path[depth * 3 - 1] + 1;\n        for (let i = 0; i < index; i++)\n            pos += node.child(i).nodeSize;\n        return pos;\n    }\n    /**\n    Get the marks at this position, factoring in the surrounding\n    marks' [`inclusive`](https://prosemirror.net/docs/ref/#model.MarkSpec.inclusive) property. If the\n    position is at the start of a non-empty node, the marks of the\n    node after it (if any) are returned.\n    */\n    marks() {\n        let parent = this.parent, index = this.index();\n        // In an empty parent, return the empty array\n        if (parent.content.size == 0)\n            return Mark.none;\n        // When inside a text node, just return the text node's marks\n        if (this.textOffset)\n            return parent.child(index).marks;\n        let main = parent.maybeChild(index - 1), other = parent.maybeChild(index);\n        // If the `after` flag is true of there is no node before, make\n        // the node after this position the main reference.\n        if (!main) {\n            let tmp = main;\n            main = other;\n            other = tmp;\n        }\n        // Use all marks in the main node, except those that have\n        // `inclusive` set to false and are not present in the other node.\n        let marks = main.marks;\n        for (var i = 0; i < marks.length; i++)\n            if (marks[i].type.spec.inclusive === false && (!other || !marks[i].isInSet(other.marks)))\n                marks = marks[i--].removeFromSet(marks);\n        return marks;\n    }\n    /**\n    Get the marks after the current position, if any, except those\n    that are non-inclusive and not present at position `$end`. This\n    is mostly useful for getting the set of marks to preserve after a\n    deletion. Will return `null` if this position is at the end of\n    its parent node or its parent node isn't a textblock (in which\n    case no marks should be preserved).\n    */\n    marksAcross($end) {\n        let after = this.parent.maybeChild(this.index());\n        if (!after || !after.isInline)\n            return null;\n        let marks = after.marks, next = $end.parent.maybeChild($end.index());\n        for (var i = 0; i < marks.length; i++)\n            if (marks[i].type.spec.inclusive === false && (!next || !marks[i].isInSet(next.marks)))\n                marks = marks[i--].removeFromSet(marks);\n        return marks;\n    }\n    /**\n    The depth up to which this position and the given (non-resolved)\n    position share the same parent nodes.\n    */\n    sharedDepth(pos) {\n        for (let depth = this.depth; depth > 0; depth--)\n            if (this.start(depth) <= pos && this.end(depth) >= pos)\n                return depth;\n        return 0;\n    }\n    /**\n    Returns a range based on the place where this position and the\n    given position diverge around block content. If both point into\n    the same textblock, for example, a range around that textblock\n    will be returned. If they point into different blocks, the range\n    around those blocks in their shared ancestor is returned. You can\n    pass in an optional predicate that will be called with a parent\n    node to see if a range into that parent is acceptable.\n    */\n    blockRange(other = this, pred) {\n        if (other.pos < this.pos)\n            return other.blockRange(this);\n        for (let d = this.depth - (this.parent.inlineContent || this.pos == other.pos ? 1 : 0); d >= 0; d--)\n            if (other.pos <= this.end(d) && (!pred || pred(this.node(d))))\n                return new NodeRange(this, other, d);\n        return null;\n    }\n    /**\n    Query whether the given position shares the same parent node.\n    */\n    sameParent(other) {\n        return this.pos - this.parentOffset == other.pos - other.parentOffset;\n    }\n    /**\n    Return the greater of this and the given position.\n    */\n    max(other) {\n        return other.pos > this.pos ? other : this;\n    }\n    /**\n    Return the smaller of this and the given position.\n    */\n    min(other) {\n        return other.pos < this.pos ? other : this;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let str = \"\";\n        for (let i = 1; i <= this.depth; i++)\n            str += (str ? \"/\" : \"\") + this.node(i).type.name + \"_\" + this.index(i - 1);\n        return str + \":\" + this.parentOffset;\n    }\n    /**\n    @internal\n    */\n    static resolve(doc, pos) {\n        if (!(pos >= 0 && pos <= doc.content.size))\n            throw new RangeError(\"Position \" + pos + \" out of range\");\n        let path = [];\n        let start = 0, parentOffset = pos;\n        for (let node = doc;;) {\n            let { index, offset } = node.content.findIndex(parentOffset);\n            let rem = parentOffset - offset;\n            path.push(node, index, start + offset);\n            if (!rem)\n                break;\n            node = node.child(index);\n            if (node.isText)\n                break;\n            parentOffset = rem - 1;\n            start += offset + 1;\n        }\n        return new ResolvedPos(pos, path, parentOffset);\n    }\n    /**\n    @internal\n    */\n    static resolveCached(doc, pos) {\n        let cache = resolveCache.get(doc);\n        if (cache) {\n            for (let i = 0; i < cache.elts.length; i++) {\n                let elt = cache.elts[i];\n                if (elt.pos == pos)\n                    return elt;\n            }\n        }\n        else {\n            resolveCache.set(doc, cache = new ResolveCache);\n        }\n        let result = cache.elts[cache.i] = ResolvedPos.resolve(doc, pos);\n        cache.i = (cache.i + 1) % resolveCacheSize;\n        return result;\n    }\n}\nclass ResolveCache {\n    constructor() {\n        this.elts = [];\n        this.i = 0;\n    }\n}\nconst resolveCacheSize = 12, resolveCache = new WeakMap();\n/**\nRepresents a flat range of content, i.e. one that starts and\nends in the same node.\n*/\nclass NodeRange {\n    /**\n    Construct a node range. `$from` and `$to` should point into the\n    same node until at least the given `depth`, since a node range\n    denotes an adjacent set of nodes in a single parent node.\n    */\n    constructor(\n    /**\n    A resolved position along the start of the content. May have a\n    `depth` greater than this object's `depth` property, since\n    these are the positions that were used to compute the range,\n    not re-resolved positions directly at its boundaries.\n    */\n    $from, \n    /**\n    A position along the end of the content. See\n    caveat for [`$from`](https://prosemirror.net/docs/ref/#model.NodeRange.$from).\n    */\n    $to, \n    /**\n    The depth of the node that this range points into.\n    */\n    depth) {\n        this.$from = $from;\n        this.$to = $to;\n        this.depth = depth;\n    }\n    /**\n    The position at the start of the range.\n    */\n    get start() { return this.$from.before(this.depth + 1); }\n    /**\n    The position at the end of the range.\n    */\n    get end() { return this.$to.after(this.depth + 1); }\n    /**\n    The parent node that the range points into.\n    */\n    get parent() { return this.$from.node(this.depth); }\n    /**\n    The start index of the range in the parent node.\n    */\n    get startIndex() { return this.$from.index(this.depth); }\n    /**\n    The end index of the range in the parent node.\n    */\n    get endIndex() { return this.$to.indexAfter(this.depth); }\n}\n\nconst emptyAttrs = Object.create(null);\n/**\nThis class represents a node in the tree that makes up a\nProseMirror document. So a document is an instance of `Node`, with\nchildren that are also instances of `Node`.\n\nNodes are persistent data structures. Instead of changing them, you\ncreate new ones with the content you want. Old ones keep pointing\nat the old document shape. This is made cheaper by sharing\nstructure between the old and new data as much as possible, which a\ntree shape like this (without back pointers) makes easy.\n\n**Do not** directly mutate the properties of a `Node` object. See\n[the guide](https://prosemirror.net/docs/guide/#doc) for more information.\n*/\nclass Node {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The type of node that this is.\n    */\n    type, \n    /**\n    An object mapping attribute names to values. The kind of\n    attributes allowed and required are\n    [determined](https://prosemirror.net/docs/ref/#model.NodeSpec.attrs) by the node type.\n    */\n    attrs, \n    // A fragment holding the node's children.\n    content, \n    /**\n    The marks (things like whether it is emphasized or part of a\n    link) applied to this node.\n    */\n    marks = Mark.none) {\n        this.type = type;\n        this.attrs = attrs;\n        this.marks = marks;\n        this.content = content || Fragment.empty;\n    }\n    /**\n    The array of this node's child nodes.\n    */\n    get children() { return this.content.content; }\n    /**\n    The size of this node, as defined by the integer-based [indexing\n    scheme](https://prosemirror.net/docs/guide/#doc.indexing). For text nodes, this is the\n    amount of characters. For other leaf nodes, it is one. For\n    non-leaf nodes, it is the size of the content plus two (the\n    start and end token).\n    */\n    get nodeSize() { return this.isLeaf ? 1 : 2 + this.content.size; }\n    /**\n    The number of children that the node has.\n    */\n    get childCount() { return this.content.childCount; }\n    /**\n    Get the child node at the given index. Raises an error when the\n    index is out of range.\n    */\n    child(index) { return this.content.child(index); }\n    /**\n    Get the child node at the given index, if it exists.\n    */\n    maybeChild(index) { return this.content.maybeChild(index); }\n    /**\n    Call `f` for every child node, passing the node, its offset\n    into this parent node, and its index.\n    */\n    forEach(f) { this.content.forEach(f); }\n    /**\n    Invoke a callback for all descendant nodes recursively between\n    the given two positions that are relative to start of this\n    node's content. The callback is invoked with the node, its\n    position relative to the original node (method receiver),\n    its parent node, and its child index. When the callback returns\n    false for a given node, that node's children will not be\n    recursed over. The last parameter can be used to specify a\n    starting position to count from.\n    */\n    nodesBetween(from, to, f, startPos = 0) {\n        this.content.nodesBetween(from, to, f, startPos, this);\n    }\n    /**\n    Call the given callback for every descendant node. Doesn't\n    descend into a node when the callback returns `false`.\n    */\n    descendants(f) {\n        this.nodesBetween(0, this.content.size, f);\n    }\n    /**\n    Concatenates all the text nodes found in this fragment and its\n    children.\n    */\n    get textContent() {\n        return (this.isLeaf && this.type.spec.leafText)\n            ? this.type.spec.leafText(this)\n            : this.textBetween(0, this.content.size, \"\");\n    }\n    /**\n    Get all text between positions `from` and `to`. When\n    `blockSeparator` is given, it will be inserted to separate text\n    from different block nodes. If `leafText` is given, it'll be\n    inserted for every non-text leaf node encountered, otherwise\n    [`leafText`](https://prosemirror.net/docs/ref/#model.NodeSpec.leafText) will be used.\n    */\n    textBetween(from, to, blockSeparator, leafText) {\n        return this.content.textBetween(from, to, blockSeparator, leafText);\n    }\n    /**\n    Returns this node's first child, or `null` if there are no\n    children.\n    */\n    get firstChild() { return this.content.firstChild; }\n    /**\n    Returns this node's last child, or `null` if there are no\n    children.\n    */\n    get lastChild() { return this.content.lastChild; }\n    /**\n    Test whether two nodes represent the same piece of document.\n    */\n    eq(other) {\n        return this == other || (this.sameMarkup(other) && this.content.eq(other.content));\n    }\n    /**\n    Compare the markup (type, attributes, and marks) of this node to\n    those of another. Returns `true` if both have the same markup.\n    */\n    sameMarkup(other) {\n        return this.hasMarkup(other.type, other.attrs, other.marks);\n    }\n    /**\n    Check whether this node's markup correspond to the given type,\n    attributes, and marks.\n    */\n    hasMarkup(type, attrs, marks) {\n        return this.type == type &&\n            compareDeep(this.attrs, attrs || type.defaultAttrs || emptyAttrs) &&\n            Mark.sameSet(this.marks, marks || Mark.none);\n    }\n    /**\n    Create a new node with the same markup as this node, containing\n    the given content (or empty, if no content is given).\n    */\n    copy(content = null) {\n        if (content == this.content)\n            return this;\n        return new Node(this.type, this.attrs, content, this.marks);\n    }\n    /**\n    Create a copy of this node, with the given set of marks instead\n    of the node's own marks.\n    */\n    mark(marks) {\n        return marks == this.marks ? this : new Node(this.type, this.attrs, this.content, marks);\n    }\n    /**\n    Create a copy of this node with only the content between the\n    given positions. If `to` is not given, it defaults to the end of\n    the node.\n    */\n    cut(from, to = this.content.size) {\n        if (from == 0 && to == this.content.size)\n            return this;\n        return this.copy(this.content.cut(from, to));\n    }\n    /**\n    Cut out the part of the document between the given positions, and\n    return it as a `Slice` object.\n    */\n    slice(from, to = this.content.size, includeParents = false) {\n        if (from == to)\n            return Slice.empty;\n        let $from = this.resolve(from), $to = this.resolve(to);\n        let depth = includeParents ? 0 : $from.sharedDepth(to);\n        let start = $from.start(depth), node = $from.node(depth);\n        let content = node.content.cut($from.pos - start, $to.pos - start);\n        return new Slice(content, $from.depth - depth, $to.depth - depth);\n    }\n    /**\n    Replace the part of the document between the given positions with\n    the given slice. The slice must 'fit', meaning its open sides\n    must be able to connect to the surrounding content, and its\n    content nodes must be valid children for the node they are placed\n    into. If any of this is violated, an error of type\n    [`ReplaceError`](https://prosemirror.net/docs/ref/#model.ReplaceError) is thrown.\n    */\n    replace(from, to, slice) {\n        return replace(this.resolve(from), this.resolve(to), slice);\n    }\n    /**\n    Find the node directly after the given position.\n    */\n    nodeAt(pos) {\n        for (let node = this;;) {\n            let { index, offset } = node.content.findIndex(pos);\n            node = node.maybeChild(index);\n            if (!node)\n                return null;\n            if (offset == pos || node.isText)\n                return node;\n            pos -= offset + 1;\n        }\n    }\n    /**\n    Find the (direct) child node after the given offset, if any,\n    and return it along with its index and offset relative to this\n    node.\n    */\n    childAfter(pos) {\n        let { index, offset } = this.content.findIndex(pos);\n        return { node: this.content.maybeChild(index), index, offset };\n    }\n    /**\n    Find the (direct) child node before the given offset, if any,\n    and return it along with its index and offset relative to this\n    node.\n    */\n    childBefore(pos) {\n        if (pos == 0)\n            return { node: null, index: 0, offset: 0 };\n        let { index, offset } = this.content.findIndex(pos);\n        if (offset < pos)\n            return { node: this.content.child(index), index, offset };\n        let node = this.content.child(index - 1);\n        return { node, index: index - 1, offset: offset - node.nodeSize };\n    }\n    /**\n    Resolve the given position in the document, returning an\n    [object](https://prosemirror.net/docs/ref/#model.ResolvedPos) with information about its context.\n    */\n    resolve(pos) { return ResolvedPos.resolveCached(this, pos); }\n    /**\n    @internal\n    */\n    resolveNoCache(pos) { return ResolvedPos.resolve(this, pos); }\n    /**\n    Test whether a given mark or mark type occurs in this document\n    between the two given positions.\n    */\n    rangeHasMark(from, to, type) {\n        let found = false;\n        if (to > from)\n            this.nodesBetween(from, to, node => {\n                if (type.isInSet(node.marks))\n                    found = true;\n                return !found;\n            });\n        return found;\n    }\n    /**\n    True when this is a block (non-inline node)\n    */\n    get isBlock() { return this.type.isBlock; }\n    /**\n    True when this is a textblock node, a block node with inline\n    content.\n    */\n    get isTextblock() { return this.type.isTextblock; }\n    /**\n    True when this node allows inline content.\n    */\n    get inlineContent() { return this.type.inlineContent; }\n    /**\n    True when this is an inline node (a text node or a node that can\n    appear among text).\n    */\n    get isInline() { return this.type.isInline; }\n    /**\n    True when this is a text node.\n    */\n    get isText() { return this.type.isText; }\n    /**\n    True when this is a leaf node.\n    */\n    get isLeaf() { return this.type.isLeaf; }\n    /**\n    True when this is an atom, i.e. when it does not have directly\n    editable content. This is usually the same as `isLeaf`, but can\n    be configured with the [`atom` property](https://prosemirror.net/docs/ref/#model.NodeSpec.atom)\n    on a node's spec (typically used when the node is displayed as\n    an uneditable [node view](https://prosemirror.net/docs/ref/#view.NodeView)).\n    */\n    get isAtom() { return this.type.isAtom; }\n    /**\n    Return a string representation of this node for debugging\n    purposes.\n    */\n    toString() {\n        if (this.type.spec.toDebugString)\n            return this.type.spec.toDebugString(this);\n        let name = this.type.name;\n        if (this.content.size)\n            name += \"(\" + this.content.toStringInner() + \")\";\n        return wrapMarks(this.marks, name);\n    }\n    /**\n    Get the content match in this node at the given index.\n    */\n    contentMatchAt(index) {\n        let match = this.type.contentMatch.matchFragment(this.content, 0, index);\n        if (!match)\n            throw new Error(\"Called contentMatchAt on a node with invalid content\");\n        return match;\n    }\n    /**\n    Test whether replacing the range between `from` and `to` (by\n    child index) with the given replacement fragment (which defaults\n    to the empty fragment) would leave the node's content valid. You\n    can optionally pass `start` and `end` indices into the\n    replacement fragment.\n    */\n    canReplace(from, to, replacement = Fragment.empty, start = 0, end = replacement.childCount) {\n        let one = this.contentMatchAt(from).matchFragment(replacement, start, end);\n        let two = one && one.matchFragment(this.content, to);\n        if (!two || !two.validEnd)\n            return false;\n        for (let i = start; i < end; i++)\n            if (!this.type.allowsMarks(replacement.child(i).marks))\n                return false;\n        return true;\n    }\n    /**\n    Test whether replacing the range `from` to `to` (by index) with\n    a node of the given type would leave the node's content valid.\n    */\n    canReplaceWith(from, to, type, marks) {\n        if (marks && !this.type.allowsMarks(marks))\n            return false;\n        let start = this.contentMatchAt(from).matchType(type);\n        let end = start && start.matchFragment(this.content, to);\n        return end ? end.validEnd : false;\n    }\n    /**\n    Test whether the given node's content could be appended to this\n    node. If that node is empty, this will only return true if there\n    is at least one node type that can appear in both nodes (to avoid\n    merging completely incompatible nodes).\n    */\n    canAppend(other) {\n        if (other.content.size)\n            return this.canReplace(this.childCount, this.childCount, other.content);\n        else\n            return this.type.compatibleContent(other.type);\n    }\n    /**\n    Check whether this node and its descendants conform to the\n    schema, and raise an exception when they do not.\n    */\n    check() {\n        this.type.checkContent(this.content);\n        this.type.checkAttrs(this.attrs);\n        let copy = Mark.none;\n        for (let i = 0; i < this.marks.length; i++) {\n            let mark = this.marks[i];\n            mark.type.checkAttrs(mark.attrs);\n            copy = mark.addToSet(copy);\n        }\n        if (!Mark.sameSet(copy, this.marks))\n            throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(m => m.type.name)}`);\n        this.content.forEach(node => node.check());\n    }\n    /**\n    Return a JSON-serializeable representation of this node.\n    */\n    toJSON() {\n        let obj = { type: this.type.name };\n        for (let _ in this.attrs) {\n            obj.attrs = this.attrs;\n            break;\n        }\n        if (this.content.size)\n            obj.content = this.content.toJSON();\n        if (this.marks.length)\n            obj.marks = this.marks.map(n => n.toJSON());\n        return obj;\n    }\n    /**\n    Deserialize a node from its JSON representation.\n    */\n    static fromJSON(schema, json) {\n        if (!json)\n            throw new RangeError(\"Invalid input for Node.fromJSON\");\n        let marks = undefined;\n        if (json.marks) {\n            if (!Array.isArray(json.marks))\n                throw new RangeError(\"Invalid mark data for Node.fromJSON\");\n            marks = json.marks.map(schema.markFromJSON);\n        }\n        if (json.type == \"text\") {\n            if (typeof json.text != \"string\")\n                throw new RangeError(\"Invalid text node in JSON\");\n            return schema.text(json.text, marks);\n        }\n        let content = Fragment.fromJSON(schema, json.content);\n        let node = schema.nodeType(json.type).create(json.attrs, content, marks);\n        node.type.checkAttrs(node.attrs);\n        return node;\n    }\n}\nNode.prototype.text = undefined;\nclass TextNode extends Node {\n    /**\n    @internal\n    */\n    constructor(type, attrs, content, marks) {\n        super(type, attrs, null, marks);\n        if (!content)\n            throw new RangeError(\"Empty text nodes are not allowed\");\n        this.text = content;\n    }\n    toString() {\n        if (this.type.spec.toDebugString)\n            return this.type.spec.toDebugString(this);\n        return wrapMarks(this.marks, JSON.stringify(this.text));\n    }\n    get textContent() { return this.text; }\n    textBetween(from, to) { return this.text.slice(from, to); }\n    get nodeSize() { return this.text.length; }\n    mark(marks) {\n        return marks == this.marks ? this : new TextNode(this.type, this.attrs, this.text, marks);\n    }\n    withText(text) {\n        if (text == this.text)\n            return this;\n        return new TextNode(this.type, this.attrs, text, this.marks);\n    }\n    cut(from = 0, to = this.text.length) {\n        if (from == 0 && to == this.text.length)\n            return this;\n        return this.withText(this.text.slice(from, to));\n    }\n    eq(other) {\n        return this.sameMarkup(other) && this.text == other.text;\n    }\n    toJSON() {\n        let base = super.toJSON();\n        base.text = this.text;\n        return base;\n    }\n}\nfunction wrapMarks(marks, str) {\n    for (let i = marks.length - 1; i >= 0; i--)\n        str = marks[i].type.name + \"(\" + str + \")\";\n    return str;\n}\n\n/**\nInstances of this class represent a match state of a node type's\n[content expression](https://prosemirror.net/docs/ref/#model.NodeSpec.content), and can be used to\nfind out whether further content matches here, and whether a given\nposition is a valid end of the node.\n*/\nclass ContentMatch {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    True when this match state represents a valid end of the node.\n    */\n    validEnd) {\n        this.validEnd = validEnd;\n        /**\n        @internal\n        */\n        this.next = [];\n        /**\n        @internal\n        */\n        this.wrapCache = [];\n    }\n    /**\n    @internal\n    */\n    static parse(string, nodeTypes) {\n        let stream = new TokenStream(string, nodeTypes);\n        if (stream.next == null)\n            return ContentMatch.empty;\n        let expr = parseExpr(stream);\n        if (stream.next)\n            stream.err(\"Unexpected trailing text\");\n        let match = dfa(nfa(expr));\n        checkForDeadEnds(match, stream);\n        return match;\n    }\n    /**\n    Match a node type, returning a match after that node if\n    successful.\n    */\n    matchType(type) {\n        for (let i = 0; i < this.next.length; i++)\n            if (this.next[i].type == type)\n                return this.next[i].next;\n        return null;\n    }\n    /**\n    Try to match a fragment. Returns the resulting match when\n    successful.\n    */\n    matchFragment(frag, start = 0, end = frag.childCount) {\n        let cur = this;\n        for (let i = start; cur && i < end; i++)\n            cur = cur.matchType(frag.child(i).type);\n        return cur;\n    }\n    /**\n    @internal\n    */\n    get inlineContent() {\n        return this.next.length != 0 && this.next[0].type.isInline;\n    }\n    /**\n    Get the first matching node type at this match position that can\n    be generated.\n    */\n    get defaultType() {\n        for (let i = 0; i < this.next.length; i++) {\n            let { type } = this.next[i];\n            if (!(type.isText || type.hasRequiredAttrs()))\n                return type;\n        }\n        return null;\n    }\n    /**\n    @internal\n    */\n    compatible(other) {\n        for (let i = 0; i < this.next.length; i++)\n            for (let j = 0; j < other.next.length; j++)\n                if (this.next[i].type == other.next[j].type)\n                    return true;\n        return false;\n    }\n    /**\n    Try to match the given fragment, and if that fails, see if it can\n    be made to match by inserting nodes in front of it. When\n    successful, return a fragment of inserted nodes (which may be\n    empty if nothing had to be inserted). When `toEnd` is true, only\n    return a fragment if the resulting match goes to the end of the\n    content expression.\n    */\n    fillBefore(after, toEnd = false, startIndex = 0) {\n        let seen = [this];\n        function search(match, types) {\n            let finished = match.matchFragment(after, startIndex);\n            if (finished && (!toEnd || finished.validEnd))\n                return Fragment.from(types.map(tp => tp.createAndFill()));\n            for (let i = 0; i < match.next.length; i++) {\n                let { type, next } = match.next[i];\n                if (!(type.isText || type.hasRequiredAttrs()) && seen.indexOf(next) == -1) {\n                    seen.push(next);\n                    let found = search(next, types.concat(type));\n                    if (found)\n                        return found;\n                }\n            }\n            return null;\n        }\n        return search(this, []);\n    }\n    /**\n    Find a set of wrapping node types that would allow a node of the\n    given type to appear at this position. The result may be empty\n    (when it fits directly) and will be null when no such wrapping\n    exists.\n    */\n    findWrapping(target) {\n        for (let i = 0; i < this.wrapCache.length; i += 2)\n            if (this.wrapCache[i] == target)\n                return this.wrapCache[i + 1];\n        let computed = this.computeWrapping(target);\n        this.wrapCache.push(target, computed);\n        return computed;\n    }\n    /**\n    @internal\n    */\n    computeWrapping(target) {\n        let seen = Object.create(null), active = [{ match: this, type: null, via: null }];\n        while (active.length) {\n            let current = active.shift(), match = current.match;\n            if (match.matchType(target)) {\n                let result = [];\n                for (let obj = current; obj.type; obj = obj.via)\n                    result.push(obj.type);\n                return result.reverse();\n            }\n            for (let i = 0; i < match.next.length; i++) {\n                let { type, next } = match.next[i];\n                if (!type.isLeaf && !type.hasRequiredAttrs() && !(type.name in seen) && (!current.type || next.validEnd)) {\n                    active.push({ match: type.contentMatch, type, via: current });\n                    seen[type.name] = true;\n                }\n            }\n        }\n        return null;\n    }\n    /**\n    The number of outgoing edges this node has in the finite\n    automaton that describes the content expression.\n    */\n    get edgeCount() {\n        return this.next.length;\n    }\n    /**\n    Get the _n_​th outgoing edge from this node in the finite\n    automaton that describes the content expression.\n    */\n    edge(n) {\n        if (n >= this.next.length)\n            throw new RangeError(`There's no ${n}th edge in this content match`);\n        return this.next[n];\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let seen = [];\n        function scan(m) {\n            seen.push(m);\n            for (let i = 0; i < m.next.length; i++)\n                if (seen.indexOf(m.next[i].next) == -1)\n                    scan(m.next[i].next);\n        }\n        scan(this);\n        return seen.map((m, i) => {\n            let out = i + (m.validEnd ? \"*\" : \" \") + \" \";\n            for (let i = 0; i < m.next.length; i++)\n                out += (i ? \", \" : \"\") + m.next[i].type.name + \"->\" + seen.indexOf(m.next[i].next);\n            return out;\n        }).join(\"\\n\");\n    }\n}\n/**\n@internal\n*/\nContentMatch.empty = new ContentMatch(true);\nclass TokenStream {\n    constructor(string, nodeTypes) {\n        this.string = string;\n        this.nodeTypes = nodeTypes;\n        this.inline = null;\n        this.pos = 0;\n        this.tokens = string.split(/\\s*(?=\\b|\\W|$)/);\n        if (this.tokens[this.tokens.length - 1] == \"\")\n            this.tokens.pop();\n        if (this.tokens[0] == \"\")\n            this.tokens.shift();\n    }\n    get next() { return this.tokens[this.pos]; }\n    eat(tok) { return this.next == tok && (this.pos++ || true); }\n    err(str) { throw new SyntaxError(str + \" (in content expression '\" + this.string + \"')\"); }\n}\nfunction parseExpr(stream) {\n    let exprs = [];\n    do {\n        exprs.push(parseExprSeq(stream));\n    } while (stream.eat(\"|\"));\n    return exprs.length == 1 ? exprs[0] : { type: \"choice\", exprs };\n}\nfunction parseExprSeq(stream) {\n    let exprs = [];\n    do {\n        exprs.push(parseExprSubscript(stream));\n    } while (stream.next && stream.next != \")\" && stream.next != \"|\");\n    return exprs.length == 1 ? exprs[0] : { type: \"seq\", exprs };\n}\nfunction parseExprSubscript(stream) {\n    let expr = parseExprAtom(stream);\n    for (;;) {\n        if (stream.eat(\"+\"))\n            expr = { type: \"plus\", expr };\n        else if (stream.eat(\"*\"))\n            expr = { type: \"star\", expr };\n        else if (stream.eat(\"?\"))\n            expr = { type: \"opt\", expr };\n        else if (stream.eat(\"{\"))\n            expr = parseExprRange(stream, expr);\n        else\n            break;\n    }\n    return expr;\n}\nfunction parseNum(stream) {\n    if (/\\D/.test(stream.next))\n        stream.err(\"Expected number, got '\" + stream.next + \"'\");\n    let result = Number(stream.next);\n    stream.pos++;\n    return result;\n}\nfunction parseExprRange(stream, expr) {\n    let min = parseNum(stream), max = min;\n    if (stream.eat(\",\")) {\n        if (stream.next != \"}\")\n            max = parseNum(stream);\n        else\n            max = -1;\n    }\n    if (!stream.eat(\"}\"))\n        stream.err(\"Unclosed braced range\");\n    return { type: \"range\", min, max, expr };\n}\nfunction resolveName(stream, name) {\n    let types = stream.nodeTypes, type = types[name];\n    if (type)\n        return [type];\n    let result = [];\n    for (let typeName in types) {\n        let type = types[typeName];\n        if (type.isInGroup(name))\n            result.push(type);\n    }\n    if (result.length == 0)\n        stream.err(\"No node type or group '\" + name + \"' found\");\n    return result;\n}\nfunction parseExprAtom(stream) {\n    if (stream.eat(\"(\")) {\n        let expr = parseExpr(stream);\n        if (!stream.eat(\")\"))\n            stream.err(\"Missing closing paren\");\n        return expr;\n    }\n    else if (!/\\W/.test(stream.next)) {\n        let exprs = resolveName(stream, stream.next).map(type => {\n            if (stream.inline == null)\n                stream.inline = type.isInline;\n            else if (stream.inline != type.isInline)\n                stream.err(\"Mixing inline and block content\");\n            return { type: \"name\", value: type };\n        });\n        stream.pos++;\n        return exprs.length == 1 ? exprs[0] : { type: \"choice\", exprs };\n    }\n    else {\n        stream.err(\"Unexpected token '\" + stream.next + \"'\");\n    }\n}\n// Construct an NFA from an expression as returned by the parser. The\n// NFA is represented as an array of states, which are themselves\n// arrays of edges, which are `{term, to}` objects. The first state is\n// the entry state and the last node is the success state.\n//\n// Note that unlike typical NFAs, the edge ordering in this one is\n// significant, in that it is used to contruct filler content when\n// necessary.\nfunction nfa(expr) {\n    let nfa = [[]];\n    connect(compile(expr, 0), node());\n    return nfa;\n    function node() { return nfa.push([]) - 1; }\n    function edge(from, to, term) {\n        let edge = { term, to };\n        nfa[from].push(edge);\n        return edge;\n    }\n    function connect(edges, to) {\n        edges.forEach(edge => edge.to = to);\n    }\n    function compile(expr, from) {\n        if (expr.type == \"choice\") {\n            return expr.exprs.reduce((out, expr) => out.concat(compile(expr, from)), []);\n        }\n        else if (expr.type == \"seq\") {\n            for (let i = 0;; i++) {\n                let next = compile(expr.exprs[i], from);\n                if (i == expr.exprs.length - 1)\n                    return next;\n                connect(next, from = node());\n            }\n        }\n        else if (expr.type == \"star\") {\n            let loop = node();\n            edge(from, loop);\n            connect(compile(expr.expr, loop), loop);\n            return [edge(loop)];\n        }\n        else if (expr.type == \"plus\") {\n            let loop = node();\n            connect(compile(expr.expr, from), loop);\n            connect(compile(expr.expr, loop), loop);\n            return [edge(loop)];\n        }\n        else if (expr.type == \"opt\") {\n            return [edge(from)].concat(compile(expr.expr, from));\n        }\n        else if (expr.type == \"range\") {\n            let cur = from;\n            for (let i = 0; i < expr.min; i++) {\n                let next = node();\n                connect(compile(expr.expr, cur), next);\n                cur = next;\n            }\n            if (expr.max == -1) {\n                connect(compile(expr.expr, cur), cur);\n            }\n            else {\n                for (let i = expr.min; i < expr.max; i++) {\n                    let next = node();\n                    edge(cur, next);\n                    connect(compile(expr.expr, cur), next);\n                    cur = next;\n                }\n            }\n            return [edge(cur)];\n        }\n        else if (expr.type == \"name\") {\n            return [edge(from, undefined, expr.value)];\n        }\n        else {\n            throw new Error(\"Unknown expr type\");\n        }\n    }\n}\nfunction cmp(a, b) { return b - a; }\n// Get the set of nodes reachable by null edges from `node`. Omit\n// nodes with only a single null-out-edge, since they may lead to\n// needless duplicated nodes.\nfunction nullFrom(nfa, node) {\n    let result = [];\n    scan(node);\n    return result.sort(cmp);\n    function scan(node) {\n        let edges = nfa[node];\n        if (edges.length == 1 && !edges[0].term)\n            return scan(edges[0].to);\n        result.push(node);\n        for (let i = 0; i < edges.length; i++) {\n            let { term, to } = edges[i];\n            if (!term && result.indexOf(to) == -1)\n                scan(to);\n        }\n    }\n}\n// Compiles an NFA as produced by `nfa` into a DFA, modeled as a set\n// of state objects (`ContentMatch` instances) with transitions\n// between them.\nfunction dfa(nfa) {\n    let labeled = Object.create(null);\n    return explore(nullFrom(nfa, 0));\n    function explore(states) {\n        let out = [];\n        states.forEach(node => {\n            nfa[node].forEach(({ term, to }) => {\n                if (!term)\n                    return;\n                let set;\n                for (let i = 0; i < out.length; i++)\n                    if (out[i][0] == term)\n                        set = out[i][1];\n                nullFrom(nfa, to).forEach(node => {\n                    if (!set)\n                        out.push([term, set = []]);\n                    if (set.indexOf(node) == -1)\n                        set.push(node);\n                });\n            });\n        });\n        let state = labeled[states.join(\",\")] = new ContentMatch(states.indexOf(nfa.length - 1) > -1);\n        for (let i = 0; i < out.length; i++) {\n            let states = out[i][1].sort(cmp);\n            state.next.push({ type: out[i][0], next: labeled[states.join(\",\")] || explore(states) });\n        }\n        return state;\n    }\n}\nfunction checkForDeadEnds(match, stream) {\n    for (let i = 0, work = [match]; i < work.length; i++) {\n        let state = work[i], dead = !state.validEnd, nodes = [];\n        for (let j = 0; j < state.next.length; j++) {\n            let { type, next } = state.next[j];\n            nodes.push(type.name);\n            if (dead && !(type.isText || type.hasRequiredAttrs()))\n                dead = false;\n            if (work.indexOf(next) == -1)\n                work.push(next);\n        }\n        if (dead)\n            stream.err(\"Only non-generatable nodes (\" + nodes.join(\", \") + \") in a required position (see https://prosemirror.net/docs/guide/#generatable)\");\n    }\n}\n\n// For node types where all attrs have a default value (or which don't\n// have any attributes), build up a single reusable default attribute\n// object, and use it for all nodes that don't specify specific\n// attributes.\nfunction defaultAttrs(attrs) {\n    let defaults = Object.create(null);\n    for (let attrName in attrs) {\n        let attr = attrs[attrName];\n        if (!attr.hasDefault)\n            return null;\n        defaults[attrName] = attr.default;\n    }\n    return defaults;\n}\nfunction computeAttrs(attrs, value) {\n    let built = Object.create(null);\n    for (let name in attrs) {\n        let given = value && value[name];\n        if (given === undefined) {\n            let attr = attrs[name];\n            if (attr.hasDefault)\n                given = attr.default;\n            else\n                throw new RangeError(\"No value supplied for attribute \" + name);\n        }\n        built[name] = given;\n    }\n    return built;\n}\nfunction checkAttrs(attrs, values, type, name) {\n    for (let name in values)\n        if (!(name in attrs))\n            throw new RangeError(`Unsupported attribute ${name} for ${type} of type ${name}`);\n    for (let name in attrs) {\n        let attr = attrs[name];\n        if (attr.validate)\n            attr.validate(values[name]);\n    }\n}\nfunction initAttrs(typeName, attrs) {\n    let result = Object.create(null);\n    if (attrs)\n        for (let name in attrs)\n            result[name] = new Attribute(typeName, name, attrs[name]);\n    return result;\n}\n/**\nNode types are objects allocated once per `Schema` and used to\n[tag](https://prosemirror.net/docs/ref/#model.Node.type) `Node` instances. They contain information\nabout the node type, such as its name and what kind of node it\nrepresents.\n*/\nclass NodeType {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The name the node type has in this schema.\n    */\n    name, \n    /**\n    A link back to the `Schema` the node type belongs to.\n    */\n    schema, \n    /**\n    The spec that this type is based on\n    */\n    spec) {\n        this.name = name;\n        this.schema = schema;\n        this.spec = spec;\n        /**\n        The set of marks allowed in this node. `null` means all marks\n        are allowed.\n        */\n        this.markSet = null;\n        this.groups = spec.group ? spec.group.split(\" \") : [];\n        this.attrs = initAttrs(name, spec.attrs);\n        this.defaultAttrs = defaultAttrs(this.attrs);\n        this.contentMatch = null;\n        this.inlineContent = null;\n        this.isBlock = !(spec.inline || name == \"text\");\n        this.isText = name == \"text\";\n    }\n    /**\n    True if this is an inline type.\n    */\n    get isInline() { return !this.isBlock; }\n    /**\n    True if this is a textblock type, a block that contains inline\n    content.\n    */\n    get isTextblock() { return this.isBlock && this.inlineContent; }\n    /**\n    True for node types that allow no content.\n    */\n    get isLeaf() { return this.contentMatch == ContentMatch.empty; }\n    /**\n    True when this node is an atom, i.e. when it does not have\n    directly editable content.\n    */\n    get isAtom() { return this.isLeaf || !!this.spec.atom; }\n    /**\n    Return true when this node type is part of the given\n    [group](https://prosemirror.net/docs/ref/#model.NodeSpec.group).\n    */\n    isInGroup(group) {\n        return this.groups.indexOf(group) > -1;\n    }\n    /**\n    The node type's [whitespace](https://prosemirror.net/docs/ref/#model.NodeSpec.whitespace) option.\n    */\n    get whitespace() {\n        return this.spec.whitespace || (this.spec.code ? \"pre\" : \"normal\");\n    }\n    /**\n    Tells you whether this node type has any required attributes.\n    */\n    hasRequiredAttrs() {\n        for (let n in this.attrs)\n            if (this.attrs[n].isRequired)\n                return true;\n        return false;\n    }\n    /**\n    Indicates whether this node allows some of the same content as\n    the given node type.\n    */\n    compatibleContent(other) {\n        return this == other || this.contentMatch.compatible(other.contentMatch);\n    }\n    /**\n    @internal\n    */\n    computeAttrs(attrs) {\n        if (!attrs && this.defaultAttrs)\n            return this.defaultAttrs;\n        else\n            return computeAttrs(this.attrs, attrs);\n    }\n    /**\n    Create a `Node` of this type. The given attributes are\n    checked and defaulted (you can pass `null` to use the type's\n    defaults entirely, if no required attributes exist). `content`\n    may be a `Fragment`, a node, an array of nodes, or\n    `null`. Similarly `marks` may be `null` to default to the empty\n    set of marks.\n    */\n    create(attrs = null, content, marks) {\n        if (this.isText)\n            throw new Error(\"NodeType.create can't construct text nodes\");\n        return new Node(this, this.computeAttrs(attrs), Fragment.from(content), Mark.setFrom(marks));\n    }\n    /**\n    Like [`create`](https://prosemirror.net/docs/ref/#model.NodeType.create), but check the given content\n    against the node type's content restrictions, and throw an error\n    if it doesn't match.\n    */\n    createChecked(attrs = null, content, marks) {\n        content = Fragment.from(content);\n        this.checkContent(content);\n        return new Node(this, this.computeAttrs(attrs), content, Mark.setFrom(marks));\n    }\n    /**\n    Like [`create`](https://prosemirror.net/docs/ref/#model.NodeType.create), but see if it is\n    necessary to add nodes to the start or end of the given fragment\n    to make it fit the node. If no fitting wrapping can be found,\n    return null. Note that, due to the fact that required nodes can\n    always be created, this will always succeed if you pass null or\n    `Fragment.empty` as content.\n    */\n    createAndFill(attrs = null, content, marks) {\n        attrs = this.computeAttrs(attrs);\n        content = Fragment.from(content);\n        if (content.size) {\n            let before = this.contentMatch.fillBefore(content);\n            if (!before)\n                return null;\n            content = before.append(content);\n        }\n        let matched = this.contentMatch.matchFragment(content);\n        let after = matched && matched.fillBefore(Fragment.empty, true);\n        if (!after)\n            return null;\n        return new Node(this, attrs, content.append(after), Mark.setFrom(marks));\n    }\n    /**\n    Returns true if the given fragment is valid content for this node\n    type.\n    */\n    validContent(content) {\n        let result = this.contentMatch.matchFragment(content);\n        if (!result || !result.validEnd)\n            return false;\n        for (let i = 0; i < content.childCount; i++)\n            if (!this.allowsMarks(content.child(i).marks))\n                return false;\n        return true;\n    }\n    /**\n    Throws a RangeError if the given fragment is not valid content for this\n    node type.\n    @internal\n    */\n    checkContent(content) {\n        if (!this.validContent(content))\n            throw new RangeError(`Invalid content for node ${this.name}: ${content.toString().slice(0, 50)}`);\n    }\n    /**\n    @internal\n    */\n    checkAttrs(attrs) {\n        checkAttrs(this.attrs, attrs, \"node\", this.name);\n    }\n    /**\n    Check whether the given mark type is allowed in this node.\n    */\n    allowsMarkType(markType) {\n        return this.markSet == null || this.markSet.indexOf(markType) > -1;\n    }\n    /**\n    Test whether the given set of marks are allowed in this node.\n    */\n    allowsMarks(marks) {\n        if (this.markSet == null)\n            return true;\n        for (let i = 0; i < marks.length; i++)\n            if (!this.allowsMarkType(marks[i].type))\n                return false;\n        return true;\n    }\n    /**\n    Removes the marks that are not allowed in this node from the given set.\n    */\n    allowedMarks(marks) {\n        if (this.markSet == null)\n            return marks;\n        let copy;\n        for (let i = 0; i < marks.length; i++) {\n            if (!this.allowsMarkType(marks[i].type)) {\n                if (!copy)\n                    copy = marks.slice(0, i);\n            }\n            else if (copy) {\n                copy.push(marks[i]);\n            }\n        }\n        return !copy ? marks : copy.length ? copy : Mark.none;\n    }\n    /**\n    @internal\n    */\n    static compile(nodes, schema) {\n        let result = Object.create(null);\n        nodes.forEach((name, spec) => result[name] = new NodeType(name, schema, spec));\n        let topType = schema.spec.topNode || \"doc\";\n        if (!result[topType])\n            throw new RangeError(\"Schema is missing its top node type ('\" + topType + \"')\");\n        if (!result.text)\n            throw new RangeError(\"Every schema needs a 'text' type\");\n        for (let _ in result.text.attrs)\n            throw new RangeError(\"The text node type should not have attributes\");\n        return result;\n    }\n}\nfunction validateType(typeName, attrName, type) {\n    let types = type.split(\"|\");\n    return (value) => {\n        let name = value === null ? \"null\" : typeof value;\n        if (types.indexOf(name) < 0)\n            throw new RangeError(`Expected value of type ${types} for attribute ${attrName} on type ${typeName}, got ${name}`);\n    };\n}\n// Attribute descriptors\nclass Attribute {\n    constructor(typeName, attrName, options) {\n        this.hasDefault = Object.prototype.hasOwnProperty.call(options, \"default\");\n        this.default = options.default;\n        this.validate = typeof options.validate == \"string\" ? validateType(typeName, attrName, options.validate) : options.validate;\n    }\n    get isRequired() {\n        return !this.hasDefault;\n    }\n}\n// Marks\n/**\nLike nodes, marks (which are associated with nodes to signify\nthings like emphasis or being part of a link) are\n[tagged](https://prosemirror.net/docs/ref/#model.Mark.type) with type objects, which are\ninstantiated once per `Schema`.\n*/\nclass MarkType {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The name of the mark type.\n    */\n    name, \n    /**\n    @internal\n    */\n    rank, \n    /**\n    The schema that this mark type instance is part of.\n    */\n    schema, \n    /**\n    The spec on which the type is based.\n    */\n    spec) {\n        this.name = name;\n        this.rank = rank;\n        this.schema = schema;\n        this.spec = spec;\n        this.attrs = initAttrs(name, spec.attrs);\n        this.excluded = null;\n        let defaults = defaultAttrs(this.attrs);\n        this.instance = defaults ? new Mark(this, defaults) : null;\n    }\n    /**\n    Create a mark of this type. `attrs` may be `null` or an object\n    containing only some of the mark's attributes. The others, if\n    they have defaults, will be added.\n    */\n    create(attrs = null) {\n        if (!attrs && this.instance)\n            return this.instance;\n        return new Mark(this, computeAttrs(this.attrs, attrs));\n    }\n    /**\n    @internal\n    */\n    static compile(marks, schema) {\n        let result = Object.create(null), rank = 0;\n        marks.forEach((name, spec) => result[name] = new MarkType(name, rank++, schema, spec));\n        return result;\n    }\n    /**\n    When there is a mark of this type in the given set, a new set\n    without it is returned. Otherwise, the input set is returned.\n    */\n    removeFromSet(set) {\n        for (var i = 0; i < set.length; i++)\n            if (set[i].type == this) {\n                set = set.slice(0, i).concat(set.slice(i + 1));\n                i--;\n            }\n        return set;\n    }\n    /**\n    Tests whether there is a mark of this type in the given set.\n    */\n    isInSet(set) {\n        for (let i = 0; i < set.length; i++)\n            if (set[i].type == this)\n                return set[i];\n    }\n    /**\n    @internal\n    */\n    checkAttrs(attrs) {\n        checkAttrs(this.attrs, attrs, \"mark\", this.name);\n    }\n    /**\n    Queries whether a given mark type is\n    [excluded](https://prosemirror.net/docs/ref/#model.MarkSpec.excludes) by this one.\n    */\n    excludes(other) {\n        return this.excluded.indexOf(other) > -1;\n    }\n}\n/**\nA document schema. Holds [node](https://prosemirror.net/docs/ref/#model.NodeType) and [mark\ntype](https://prosemirror.net/docs/ref/#model.MarkType) objects for the nodes and marks that may\noccur in conforming documents, and provides functionality for\ncreating and deserializing such documents.\n\nWhen given, the type parameters provide the names of the nodes and\nmarks in this schema.\n*/\nclass Schema {\n    /**\n    Construct a schema from a schema [specification](https://prosemirror.net/docs/ref/#model.SchemaSpec).\n    */\n    constructor(spec) {\n        /**\n        The [linebreak\n        replacement](https://prosemirror.net/docs/ref/#model.NodeSpec.linebreakReplacement) node defined\n        in this schema, if any.\n        */\n        this.linebreakReplacement = null;\n        /**\n        An object for storing whatever values modules may want to\n        compute and cache per schema. (If you want to store something\n        in it, try to use property names unlikely to clash.)\n        */\n        this.cached = Object.create(null);\n        let instanceSpec = this.spec = {};\n        for (let prop in spec)\n            instanceSpec[prop] = spec[prop];\n        instanceSpec.nodes = orderedmap__WEBPACK_IMPORTED_MODULE_0__[\"default\"].from(spec.nodes),\n            instanceSpec.marks = orderedmap__WEBPACK_IMPORTED_MODULE_0__[\"default\"].from(spec.marks || {}),\n            this.nodes = NodeType.compile(this.spec.nodes, this);\n        this.marks = MarkType.compile(this.spec.marks, this);\n        let contentExprCache = Object.create(null);\n        for (let prop in this.nodes) {\n            if (prop in this.marks)\n                throw new RangeError(prop + \" can not be both a node and a mark\");\n            let type = this.nodes[prop], contentExpr = type.spec.content || \"\", markExpr = type.spec.marks;\n            type.contentMatch = contentExprCache[contentExpr] ||\n                (contentExprCache[contentExpr] = ContentMatch.parse(contentExpr, this.nodes));\n            type.inlineContent = type.contentMatch.inlineContent;\n            if (type.spec.linebreakReplacement) {\n                if (this.linebreakReplacement)\n                    throw new RangeError(\"Multiple linebreak nodes defined\");\n                if (!type.isInline || !type.isLeaf)\n                    throw new RangeError(\"Linebreak replacement nodes must be inline leaf nodes\");\n                this.linebreakReplacement = type;\n            }\n            type.markSet = markExpr == \"_\" ? null :\n                markExpr ? gatherMarks(this, markExpr.split(\" \")) :\n                    markExpr == \"\" || !type.inlineContent ? [] : null;\n        }\n        for (let prop in this.marks) {\n            let type = this.marks[prop], excl = type.spec.excludes;\n            type.excluded = excl == null ? [type] : excl == \"\" ? [] : gatherMarks(this, excl.split(\" \"));\n        }\n        this.nodeFromJSON = json => Node.fromJSON(this, json);\n        this.markFromJSON = json => Mark.fromJSON(this, json);\n        this.topNodeType = this.nodes[this.spec.topNode || \"doc\"];\n        this.cached.wrappings = Object.create(null);\n    }\n    /**\n    Create a node in this schema. The `type` may be a string or a\n    `NodeType` instance. Attributes will be extended with defaults,\n    `content` may be a `Fragment`, `null`, a `Node`, or an array of\n    nodes.\n    */\n    node(type, attrs = null, content, marks) {\n        if (typeof type == \"string\")\n            type = this.nodeType(type);\n        else if (!(type instanceof NodeType))\n            throw new RangeError(\"Invalid node type: \" + type);\n        else if (type.schema != this)\n            throw new RangeError(\"Node type from different schema used (\" + type.name + \")\");\n        return type.createChecked(attrs, content, marks);\n    }\n    /**\n    Create a text node in the schema. Empty text nodes are not\n    allowed.\n    */\n    text(text, marks) {\n        let type = this.nodes.text;\n        return new TextNode(type, type.defaultAttrs, text, Mark.setFrom(marks));\n    }\n    /**\n    Create a mark with the given type and attributes.\n    */\n    mark(type, attrs) {\n        if (typeof type == \"string\")\n            type = this.marks[type];\n        return type.create(attrs);\n    }\n    /**\n    @internal\n    */\n    nodeType(name) {\n        let found = this.nodes[name];\n        if (!found)\n            throw new RangeError(\"Unknown node type: \" + name);\n        return found;\n    }\n}\nfunction gatherMarks(schema, marks) {\n    let found = [];\n    for (let i = 0; i < marks.length; i++) {\n        let name = marks[i], mark = schema.marks[name], ok = mark;\n        if (mark) {\n            found.push(mark);\n        }\n        else {\n            for (let prop in schema.marks) {\n                let mark = schema.marks[prop];\n                if (name == \"_\" || (mark.spec.group && mark.spec.group.split(\" \").indexOf(name) > -1))\n                    found.push(ok = mark);\n            }\n        }\n        if (!ok)\n            throw new SyntaxError(\"Unknown mark type: '\" + marks[i] + \"'\");\n    }\n    return found;\n}\n\nfunction isTagRule(rule) { return rule.tag != null; }\nfunction isStyleRule(rule) { return rule.style != null; }\n/**\nA DOM parser represents a strategy for parsing DOM content into a\nProseMirror document conforming to a given schema. Its behavior is\ndefined by an array of [rules](https://prosemirror.net/docs/ref/#model.ParseRule).\n*/\nclass DOMParser {\n    /**\n    Create a parser that targets the given schema, using the given\n    parsing rules.\n    */\n    constructor(\n    /**\n    The schema into which the parser parses.\n    */\n    schema, \n    /**\n    The set of [parse rules](https://prosemirror.net/docs/ref/#model.ParseRule) that the parser\n    uses, in order of precedence.\n    */\n    rules) {\n        this.schema = schema;\n        this.rules = rules;\n        /**\n        @internal\n        */\n        this.tags = [];\n        /**\n        @internal\n        */\n        this.styles = [];\n        let matchedStyles = this.matchedStyles = [];\n        rules.forEach(rule => {\n            if (isTagRule(rule)) {\n                this.tags.push(rule);\n            }\n            else if (isStyleRule(rule)) {\n                let prop = /[^=]*/.exec(rule.style)[0];\n                if (matchedStyles.indexOf(prop) < 0)\n                    matchedStyles.push(prop);\n                this.styles.push(rule);\n            }\n        });\n        // Only normalize list elements when lists in the schema can't directly contain themselves\n        this.normalizeLists = !this.tags.some(r => {\n            if (!/^(ul|ol)\\b/.test(r.tag) || !r.node)\n                return false;\n            let node = schema.nodes[r.node];\n            return node.contentMatch.matchType(node);\n        });\n    }\n    /**\n    Parse a document from the content of a DOM node.\n    */\n    parse(dom, options = {}) {\n        let context = new ParseContext(this, options, false);\n        context.addAll(dom, Mark.none, options.from, options.to);\n        return context.finish();\n    }\n    /**\n    Parses the content of the given DOM node, like\n    [`parse`](https://prosemirror.net/docs/ref/#model.DOMParser.parse), and takes the same set of\n    options. But unlike that method, which produces a whole node,\n    this one returns a slice that is open at the sides, meaning that\n    the schema constraints aren't applied to the start of nodes to\n    the left of the input and the end of nodes at the end.\n    */\n    parseSlice(dom, options = {}) {\n        let context = new ParseContext(this, options, true);\n        context.addAll(dom, Mark.none, options.from, options.to);\n        return Slice.maxOpen(context.finish());\n    }\n    /**\n    @internal\n    */\n    matchTag(dom, context, after) {\n        for (let i = after ? this.tags.indexOf(after) + 1 : 0; i < this.tags.length; i++) {\n            let rule = this.tags[i];\n            if (matches(dom, rule.tag) &&\n                (rule.namespace === undefined || dom.namespaceURI == rule.namespace) &&\n                (!rule.context || context.matchesContext(rule.context))) {\n                if (rule.getAttrs) {\n                    let result = rule.getAttrs(dom);\n                    if (result === false)\n                        continue;\n                    rule.attrs = result || undefined;\n                }\n                return rule;\n            }\n        }\n    }\n    /**\n    @internal\n    */\n    matchStyle(prop, value, context, after) {\n        for (let i = after ? this.styles.indexOf(after) + 1 : 0; i < this.styles.length; i++) {\n            let rule = this.styles[i], style = rule.style;\n            if (style.indexOf(prop) != 0 ||\n                rule.context && !context.matchesContext(rule.context) ||\n                // Test that the style string either precisely matches the prop,\n                // or has an '=' sign after the prop, followed by the given\n                // value.\n                style.length > prop.length &&\n                    (style.charCodeAt(prop.length) != 61 || style.slice(prop.length + 1) != value))\n                continue;\n            if (rule.getAttrs) {\n                let result = rule.getAttrs(value);\n                if (result === false)\n                    continue;\n                rule.attrs = result || undefined;\n            }\n            return rule;\n        }\n    }\n    /**\n    @internal\n    */\n    static schemaRules(schema) {\n        let result = [];\n        function insert(rule) {\n            let priority = rule.priority == null ? 50 : rule.priority, i = 0;\n            for (; i < result.length; i++) {\n                let next = result[i], nextPriority = next.priority == null ? 50 : next.priority;\n                if (nextPriority < priority)\n                    break;\n            }\n            result.splice(i, 0, rule);\n        }\n        for (let name in schema.marks) {\n            let rules = schema.marks[name].spec.parseDOM;\n            if (rules)\n                rules.forEach(rule => {\n                    insert(rule = copy(rule));\n                    if (!(rule.mark || rule.ignore || rule.clearMark))\n                        rule.mark = name;\n                });\n        }\n        for (let name in schema.nodes) {\n            let rules = schema.nodes[name].spec.parseDOM;\n            if (rules)\n                rules.forEach(rule => {\n                    insert(rule = copy(rule));\n                    if (!(rule.node || rule.ignore || rule.mark))\n                        rule.node = name;\n                });\n        }\n        return result;\n    }\n    /**\n    Construct a DOM parser using the parsing rules listed in a\n    schema's [node specs](https://prosemirror.net/docs/ref/#model.NodeSpec.parseDOM), reordered by\n    [priority](https://prosemirror.net/docs/ref/#model.GenericParseRule.priority).\n    */\n    static fromSchema(schema) {\n        return schema.cached.domParser ||\n            (schema.cached.domParser = new DOMParser(schema, DOMParser.schemaRules(schema)));\n    }\n}\nconst blockTags = {\n    address: true, article: true, aside: true, blockquote: true, canvas: true,\n    dd: true, div: true, dl: true, fieldset: true, figcaption: true, figure: true,\n    footer: true, form: true, h1: true, h2: true, h3: true, h4: true, h5: true,\n    h6: true, header: true, hgroup: true, hr: true, li: true, noscript: true, ol: true,\n    output: true, p: true, pre: true, section: true, table: true, tfoot: true, ul: true\n};\nconst ignoreTags = {\n    head: true, noscript: true, object: true, script: true, style: true, title: true\n};\nconst listTags = { ol: true, ul: true };\n// Using a bitfield for node context options\nconst OPT_PRESERVE_WS = 1, OPT_PRESERVE_WS_FULL = 2, OPT_OPEN_LEFT = 4;\nfunction wsOptionsFor(type, preserveWhitespace, base) {\n    if (preserveWhitespace != null)\n        return (preserveWhitespace ? OPT_PRESERVE_WS : 0) |\n            (preserveWhitespace === \"full\" ? OPT_PRESERVE_WS_FULL : 0);\n    return type && type.whitespace == \"pre\" ? OPT_PRESERVE_WS | OPT_PRESERVE_WS_FULL : base & ~OPT_OPEN_LEFT;\n}\nclass NodeContext {\n    constructor(type, attrs, marks, solid, match, options) {\n        this.type = type;\n        this.attrs = attrs;\n        this.marks = marks;\n        this.solid = solid;\n        this.options = options;\n        this.content = [];\n        // Marks applied to the node's children\n        this.activeMarks = Mark.none;\n        this.match = match || (options & OPT_OPEN_LEFT ? null : type.contentMatch);\n    }\n    findWrapping(node) {\n        if (!this.match) {\n            if (!this.type)\n                return [];\n            let fill = this.type.contentMatch.fillBefore(Fragment.from(node));\n            if (fill) {\n                this.match = this.type.contentMatch.matchFragment(fill);\n            }\n            else {\n                let start = this.type.contentMatch, wrap;\n                if (wrap = start.findWrapping(node.type)) {\n                    this.match = start;\n                    return wrap;\n                }\n                else {\n                    return null;\n                }\n            }\n        }\n        return this.match.findWrapping(node.type);\n    }\n    finish(openEnd) {\n        if (!(this.options & OPT_PRESERVE_WS)) { // Strip trailing whitespace\n            let last = this.content[this.content.length - 1], m;\n            if (last && last.isText && (m = /[ \\t\\r\\n\\u000c]+$/.exec(last.text))) {\n                let text = last;\n                if (last.text.length == m[0].length)\n                    this.content.pop();\n                else\n                    this.content[this.content.length - 1] = text.withText(text.text.slice(0, text.text.length - m[0].length));\n            }\n        }\n        let content = Fragment.from(this.content);\n        if (!openEnd && this.match)\n            content = content.append(this.match.fillBefore(Fragment.empty, true));\n        return this.type ? this.type.create(this.attrs, content, this.marks) : content;\n    }\n    inlineContext(node) {\n        if (this.type)\n            return this.type.inlineContent;\n        if (this.content.length)\n            return this.content[0].isInline;\n        return node.parentNode && !blockTags.hasOwnProperty(node.parentNode.nodeName.toLowerCase());\n    }\n}\nclass ParseContext {\n    constructor(\n    // The parser we are using.\n    parser, \n    // The options passed to this parse.\n    options, isOpen) {\n        this.parser = parser;\n        this.options = options;\n        this.isOpen = isOpen;\n        this.open = 0;\n        this.localPreserveWS = false;\n        let topNode = options.topNode, topContext;\n        let topOptions = wsOptionsFor(null, options.preserveWhitespace, 0) | (isOpen ? OPT_OPEN_LEFT : 0);\n        if (topNode)\n            topContext = new NodeContext(topNode.type, topNode.attrs, Mark.none, true, options.topMatch || topNode.type.contentMatch, topOptions);\n        else if (isOpen)\n            topContext = new NodeContext(null, null, Mark.none, true, null, topOptions);\n        else\n            topContext = new NodeContext(parser.schema.topNodeType, null, Mark.none, true, null, topOptions);\n        this.nodes = [topContext];\n        this.find = options.findPositions;\n        this.needsBlock = false;\n    }\n    get top() {\n        return this.nodes[this.open];\n    }\n    // Add a DOM node to the content. Text is inserted as text node,\n    // otherwise, the node is passed to `addElement` or, if it has a\n    // `style` attribute, `addElementWithStyles`.\n    addDOM(dom, marks) {\n        if (dom.nodeType == 3)\n            this.addTextNode(dom, marks);\n        else if (dom.nodeType == 1)\n            this.addElement(dom, marks);\n    }\n    addTextNode(dom, marks) {\n        let value = dom.nodeValue;\n        let top = this.top, preserveWS = (top.options & OPT_PRESERVE_WS_FULL) ? \"full\"\n            : this.localPreserveWS || (top.options & OPT_PRESERVE_WS) > 0;\n        if (preserveWS === \"full\" ||\n            top.inlineContext(dom) ||\n            /[^ \\t\\r\\n\\u000c]/.test(value)) {\n            if (!preserveWS) {\n                value = value.replace(/[ \\t\\r\\n\\u000c]+/g, \" \");\n                // If this starts with whitespace, and there is no node before it, or\n                // a hard break, or a text node that ends with whitespace, strip the\n                // leading space.\n                if (/^[ \\t\\r\\n\\u000c]/.test(value) && this.open == this.nodes.length - 1) {\n                    let nodeBefore = top.content[top.content.length - 1];\n                    let domNodeBefore = dom.previousSibling;\n                    if (!nodeBefore ||\n                        (domNodeBefore && domNodeBefore.nodeName == 'BR') ||\n                        (nodeBefore.isText && /[ \\t\\r\\n\\u000c]$/.test(nodeBefore.text)))\n                        value = value.slice(1);\n                }\n            }\n            else if (preserveWS !== \"full\") {\n                value = value.replace(/\\r?\\n|\\r/g, \" \");\n            }\n            else {\n                value = value.replace(/\\r\\n?/g, \"\\n\");\n            }\n            if (value)\n                this.insertNode(this.parser.schema.text(value), marks, !/\\S/.test(value));\n            this.findInText(dom);\n        }\n        else {\n            this.findInside(dom);\n        }\n    }\n    // Try to find a handler for the given tag and use that to parse. If\n    // none is found, the element's content nodes are added directly.\n    addElement(dom, marks, matchAfter) {\n        let outerWS = this.localPreserveWS, top = this.top;\n        if (dom.tagName == \"PRE\" || /pre/.test(dom.style && dom.style.whiteSpace))\n            this.localPreserveWS = true;\n        let name = dom.nodeName.toLowerCase(), ruleID;\n        if (listTags.hasOwnProperty(name) && this.parser.normalizeLists)\n            normalizeList(dom);\n        let rule = (this.options.ruleFromNode && this.options.ruleFromNode(dom)) ||\n            (ruleID = this.parser.matchTag(dom, this, matchAfter));\n        out: if (rule ? rule.ignore : ignoreTags.hasOwnProperty(name)) {\n            this.findInside(dom);\n            this.ignoreFallback(dom, marks);\n        }\n        else if (!rule || rule.skip || rule.closeParent) {\n            if (rule && rule.closeParent)\n                this.open = Math.max(0, this.open - 1);\n            else if (rule && rule.skip.nodeType)\n                dom = rule.skip;\n            let sync, oldNeedsBlock = this.needsBlock;\n            if (blockTags.hasOwnProperty(name)) {\n                if (top.content.length && top.content[0].isInline && this.open) {\n                    this.open--;\n                    top = this.top;\n                }\n                sync = true;\n                if (!top.type)\n                    this.needsBlock = true;\n            }\n            else if (!dom.firstChild) {\n                this.leafFallback(dom, marks);\n                break out;\n            }\n            let innerMarks = rule && rule.skip ? marks : this.readStyles(dom, marks);\n            if (innerMarks)\n                this.addAll(dom, innerMarks);\n            if (sync)\n                this.sync(top);\n            this.needsBlock = oldNeedsBlock;\n        }\n        else {\n            let innerMarks = this.readStyles(dom, marks);\n            if (innerMarks)\n                this.addElementByRule(dom, rule, innerMarks, rule.consuming === false ? ruleID : undefined);\n        }\n        this.localPreserveWS = outerWS;\n    }\n    // Called for leaf DOM nodes that would otherwise be ignored\n    leafFallback(dom, marks) {\n        if (dom.nodeName == \"BR\" && this.top.type && this.top.type.inlineContent)\n            this.addTextNode(dom.ownerDocument.createTextNode(\"\\n\"), marks);\n    }\n    // Called for ignored nodes\n    ignoreFallback(dom, marks) {\n        // Ignored BR nodes should at least create an inline context\n        if (dom.nodeName == \"BR\" && (!this.top.type || !this.top.type.inlineContent))\n            this.findPlace(this.parser.schema.text(\"-\"), marks, true);\n    }\n    // Run any style parser associated with the node's styles. Either\n    // return an updated array of marks, or null to indicate some of the\n    // styles had a rule with `ignore` set.\n    readStyles(dom, marks) {\n        let styles = dom.style;\n        // Because many properties will only show up in 'normalized' form\n        // in `style.item` (i.e. text-decoration becomes\n        // text-decoration-line, text-decoration-color, etc), we directly\n        // query the styles mentioned in our rules instead of iterating\n        // over the items.\n        if (styles && styles.length)\n            for (let i = 0; i < this.parser.matchedStyles.length; i++) {\n                let name = this.parser.matchedStyles[i], value = styles.getPropertyValue(name);\n                if (value)\n                    for (let after = undefined;;) {\n                        let rule = this.parser.matchStyle(name, value, this, after);\n                        if (!rule)\n                            break;\n                        if (rule.ignore)\n                            return null;\n                        if (rule.clearMark)\n                            marks = marks.filter(m => !rule.clearMark(m));\n                        else\n                            marks = marks.concat(this.parser.schema.marks[rule.mark].create(rule.attrs));\n                        if (rule.consuming === false)\n                            after = rule;\n                        else\n                            break;\n                    }\n            }\n        return marks;\n    }\n    // Look up a handler for the given node. If none are found, return\n    // false. Otherwise, apply it, use its return value to drive the way\n    // the node's content is wrapped, and return true.\n    addElementByRule(dom, rule, marks, continueAfter) {\n        let sync, nodeType;\n        if (rule.node) {\n            nodeType = this.parser.schema.nodes[rule.node];\n            if (!nodeType.isLeaf) {\n                let inner = this.enter(nodeType, rule.attrs || null, marks, rule.preserveWhitespace);\n                if (inner) {\n                    sync = true;\n                    marks = inner;\n                }\n            }\n            else if (!this.insertNode(nodeType.create(rule.attrs), marks, dom.nodeName == \"BR\")) {\n                this.leafFallback(dom, marks);\n            }\n        }\n        else {\n            let markType = this.parser.schema.marks[rule.mark];\n            marks = marks.concat(markType.create(rule.attrs));\n        }\n        let startIn = this.top;\n        if (nodeType && nodeType.isLeaf) {\n            this.findInside(dom);\n        }\n        else if (continueAfter) {\n            this.addElement(dom, marks, continueAfter);\n        }\n        else if (rule.getContent) {\n            this.findInside(dom);\n            rule.getContent(dom, this.parser.schema).forEach(node => this.insertNode(node, marks, false));\n        }\n        else {\n            let contentDOM = dom;\n            if (typeof rule.contentElement == \"string\")\n                contentDOM = dom.querySelector(rule.contentElement);\n            else if (typeof rule.contentElement == \"function\")\n                contentDOM = rule.contentElement(dom);\n            else if (rule.contentElement)\n                contentDOM = rule.contentElement;\n            this.findAround(dom, contentDOM, true);\n            this.addAll(contentDOM, marks);\n            this.findAround(dom, contentDOM, false);\n        }\n        if (sync && this.sync(startIn))\n            this.open--;\n    }\n    // Add all child nodes between `startIndex` and `endIndex` (or the\n    // whole node, if not given). If `sync` is passed, use it to\n    // synchronize after every block element.\n    addAll(parent, marks, startIndex, endIndex) {\n        let index = startIndex || 0;\n        for (let dom = startIndex ? parent.childNodes[startIndex] : parent.firstChild, end = endIndex == null ? null : parent.childNodes[endIndex]; dom != end; dom = dom.nextSibling, ++index) {\n            this.findAtPoint(parent, index);\n            this.addDOM(dom, marks);\n        }\n        this.findAtPoint(parent, index);\n    }\n    // Try to find a way to fit the given node type into the current\n    // context. May add intermediate wrappers and/or leave non-solid\n    // nodes that we're in.\n    findPlace(node, marks, cautious) {\n        let route, sync;\n        for (let depth = this.open, penalty = 0; depth >= 0; depth--) {\n            let cx = this.nodes[depth];\n            let found = cx.findWrapping(node);\n            if (found && (!route || route.length > found.length + penalty)) {\n                route = found;\n                sync = cx;\n                if (!found.length)\n                    break;\n            }\n            if (cx.solid) {\n                if (cautious)\n                    break;\n                penalty += 2;\n            }\n        }\n        if (!route)\n            return null;\n        this.sync(sync);\n        for (let i = 0; i < route.length; i++)\n            marks = this.enterInner(route[i], null, marks, false);\n        return marks;\n    }\n    // Try to insert the given node, adjusting the context when needed.\n    insertNode(node, marks, cautious) {\n        if (node.isInline && this.needsBlock && !this.top.type) {\n            let block = this.textblockFromContext();\n            if (block)\n                marks = this.enterInner(block, null, marks);\n        }\n        let innerMarks = this.findPlace(node, marks, cautious);\n        if (innerMarks) {\n            this.closeExtra();\n            let top = this.top;\n            if (top.match)\n                top.match = top.match.matchType(node.type);\n            let nodeMarks = Mark.none;\n            for (let m of innerMarks.concat(node.marks))\n                if (top.type ? top.type.allowsMarkType(m.type) : markMayApply(m.type, node.type))\n                    nodeMarks = m.addToSet(nodeMarks);\n            top.content.push(node.mark(nodeMarks));\n            return true;\n        }\n        return false;\n    }\n    // Try to start a node of the given type, adjusting the context when\n    // necessary.\n    enter(type, attrs, marks, preserveWS) {\n        let innerMarks = this.findPlace(type.create(attrs), marks, false);\n        if (innerMarks)\n            innerMarks = this.enterInner(type, attrs, marks, true, preserveWS);\n        return innerMarks;\n    }\n    // Open a node of the given type\n    enterInner(type, attrs, marks, solid = false, preserveWS) {\n        this.closeExtra();\n        let top = this.top;\n        top.match = top.match && top.match.matchType(type);\n        let options = wsOptionsFor(type, preserveWS, top.options);\n        if ((top.options & OPT_OPEN_LEFT) && top.content.length == 0)\n            options |= OPT_OPEN_LEFT;\n        let applyMarks = Mark.none;\n        marks = marks.filter(m => {\n            if (top.type ? top.type.allowsMarkType(m.type) : markMayApply(m.type, type)) {\n                applyMarks = m.addToSet(applyMarks);\n                return false;\n            }\n            return true;\n        });\n        this.nodes.push(new NodeContext(type, attrs, applyMarks, solid, null, options));\n        this.open++;\n        return marks;\n    }\n    // Make sure all nodes above this.open are finished and added to\n    // their parents\n    closeExtra(openEnd = false) {\n        let i = this.nodes.length - 1;\n        if (i > this.open) {\n            for (; i > this.open; i--)\n                this.nodes[i - 1].content.push(this.nodes[i].finish(openEnd));\n            this.nodes.length = this.open + 1;\n        }\n    }\n    finish() {\n        this.open = 0;\n        this.closeExtra(this.isOpen);\n        return this.nodes[0].finish(!!(this.isOpen || this.options.topOpen));\n    }\n    sync(to) {\n        for (let i = this.open; i >= 0; i--) {\n            if (this.nodes[i] == to) {\n                this.open = i;\n                return true;\n            }\n            else if (this.localPreserveWS) {\n                this.nodes[i].options |= OPT_PRESERVE_WS;\n            }\n        }\n        return false;\n    }\n    get currentPos() {\n        this.closeExtra();\n        let pos = 0;\n        for (let i = this.open; i >= 0; i--) {\n            let content = this.nodes[i].content;\n            for (let j = content.length - 1; j >= 0; j--)\n                pos += content[j].nodeSize;\n            if (i)\n                pos++;\n        }\n        return pos;\n    }\n    findAtPoint(parent, offset) {\n        if (this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].node == parent && this.find[i].offset == offset)\n                    this.find[i].pos = this.currentPos;\n            }\n    }\n    findInside(parent) {\n        if (this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].pos == null && parent.nodeType == 1 && parent.contains(this.find[i].node))\n                    this.find[i].pos = this.currentPos;\n            }\n    }\n    findAround(parent, content, before) {\n        if (parent != content && this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].pos == null && parent.nodeType == 1 && parent.contains(this.find[i].node)) {\n                    let pos = content.compareDocumentPosition(this.find[i].node);\n                    if (pos & (before ? 2 : 4))\n                        this.find[i].pos = this.currentPos;\n                }\n            }\n    }\n    findInText(textNode) {\n        if (this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].node == textNode)\n                    this.find[i].pos = this.currentPos - (textNode.nodeValue.length - this.find[i].offset);\n            }\n    }\n    // Determines whether the given context string matches this context.\n    matchesContext(context) {\n        if (context.indexOf(\"|\") > -1)\n            return context.split(/\\s*\\|\\s*/).some(this.matchesContext, this);\n        let parts = context.split(\"/\");\n        let option = this.options.context;\n        let useRoot = !this.isOpen && (!option || option.parent.type == this.nodes[0].type);\n        let minDepth = -(option ? option.depth + 1 : 0) + (useRoot ? 0 : 1);\n        let match = (i, depth) => {\n            for (; i >= 0; i--) {\n                let part = parts[i];\n                if (part == \"\") {\n                    if (i == parts.length - 1 || i == 0)\n                        continue;\n                    for (; depth >= minDepth; depth--)\n                        if (match(i - 1, depth))\n                            return true;\n                    return false;\n                }\n                else {\n                    let next = depth > 0 || (depth == 0 && useRoot) ? this.nodes[depth].type\n                        : option && depth >= minDepth ? option.node(depth - minDepth).type\n                            : null;\n                    if (!next || (next.name != part && !next.isInGroup(part)))\n                        return false;\n                    depth--;\n                }\n            }\n            return true;\n        };\n        return match(parts.length - 1, this.open);\n    }\n    textblockFromContext() {\n        let $context = this.options.context;\n        if ($context)\n            for (let d = $context.depth; d >= 0; d--) {\n                let deflt = $context.node(d).contentMatchAt($context.indexAfter(d)).defaultType;\n                if (deflt && deflt.isTextblock && deflt.defaultAttrs)\n                    return deflt;\n            }\n        for (let name in this.parser.schema.nodes) {\n            let type = this.parser.schema.nodes[name];\n            if (type.isTextblock && type.defaultAttrs)\n                return type;\n        }\n    }\n}\n// Kludge to work around directly nested list nodes produced by some\n// tools and allowed by browsers to mean that the nested list is\n// actually part of the list item above it.\nfunction normalizeList(dom) {\n    for (let child = dom.firstChild, prevItem = null; child; child = child.nextSibling) {\n        let name = child.nodeType == 1 ? child.nodeName.toLowerCase() : null;\n        if (name && listTags.hasOwnProperty(name) && prevItem) {\n            prevItem.appendChild(child);\n            child = prevItem;\n        }\n        else if (name == \"li\") {\n            prevItem = child;\n        }\n        else if (name) {\n            prevItem = null;\n        }\n    }\n}\n// Apply a CSS selector.\nfunction matches(dom, selector) {\n    return (dom.matches || dom.msMatchesSelector || dom.webkitMatchesSelector || dom.mozMatchesSelector).call(dom, selector);\n}\nfunction copy(obj) {\n    let copy = {};\n    for (let prop in obj)\n        copy[prop] = obj[prop];\n    return copy;\n}\n// Used when finding a mark at the top level of a fragment parse.\n// Checks whether it would be reasonable to apply a given mark type to\n// a given node, by looking at the way the mark occurs in the schema.\nfunction markMayApply(markType, nodeType) {\n    let nodes = nodeType.schema.nodes;\n    for (let name in nodes) {\n        let parent = nodes[name];\n        if (!parent.allowsMarkType(markType))\n            continue;\n        let seen = [], scan = (match) => {\n            seen.push(match);\n            for (let i = 0; i < match.edgeCount; i++) {\n                let { type, next } = match.edge(i);\n                if (type == nodeType)\n                    return true;\n                if (seen.indexOf(next) < 0 && scan(next))\n                    return true;\n            }\n        };\n        if (scan(parent.contentMatch))\n            return true;\n    }\n}\n\n/**\nA DOM serializer knows how to convert ProseMirror nodes and\nmarks of various types to DOM nodes.\n*/\nclass DOMSerializer {\n    /**\n    Create a serializer. `nodes` should map node names to functions\n    that take a node and return a description of the corresponding\n    DOM. `marks` does the same for mark names, but also gets an\n    argument that tells it whether the mark's content is block or\n    inline content (for typical use, it'll always be inline). A mark\n    serializer may be `null` to indicate that marks of that type\n    should not be serialized.\n    */\n    constructor(\n    /**\n    The node serialization functions.\n    */\n    nodes, \n    /**\n    The mark serialization functions.\n    */\n    marks) {\n        this.nodes = nodes;\n        this.marks = marks;\n    }\n    /**\n    Serialize the content of this fragment to a DOM fragment. When\n    not in the browser, the `document` option, containing a DOM\n    document, should be passed so that the serializer can create\n    nodes.\n    */\n    serializeFragment(fragment, options = {}, target) {\n        if (!target)\n            target = doc(options).createDocumentFragment();\n        let top = target, active = [];\n        fragment.forEach(node => {\n            if (active.length || node.marks.length) {\n                let keep = 0, rendered = 0;\n                while (keep < active.length && rendered < node.marks.length) {\n                    let next = node.marks[rendered];\n                    if (!this.marks[next.type.name]) {\n                        rendered++;\n                        continue;\n                    }\n                    if (!next.eq(active[keep][0]) || next.type.spec.spanning === false)\n                        break;\n                    keep++;\n                    rendered++;\n                }\n                while (keep < active.length)\n                    top = active.pop()[1];\n                while (rendered < node.marks.length) {\n                    let add = node.marks[rendered++];\n                    let markDOM = this.serializeMark(add, node.isInline, options);\n                    if (markDOM) {\n                        active.push([add, top]);\n                        top.appendChild(markDOM.dom);\n                        top = markDOM.contentDOM || markDOM.dom;\n                    }\n                }\n            }\n            top.appendChild(this.serializeNodeInner(node, options));\n        });\n        return target;\n    }\n    /**\n    @internal\n    */\n    serializeNodeInner(node, options) {\n        let { dom, contentDOM } = renderSpec(doc(options), this.nodes[node.type.name](node), null, node.attrs);\n        if (contentDOM) {\n            if (node.isLeaf)\n                throw new RangeError(\"Content hole not allowed in a leaf node spec\");\n            this.serializeFragment(node.content, options, contentDOM);\n        }\n        return dom;\n    }\n    /**\n    Serialize this node to a DOM node. This can be useful when you\n    need to serialize a part of a document, as opposed to the whole\n    document. To serialize a whole document, use\n    [`serializeFragment`](https://prosemirror.net/docs/ref/#model.DOMSerializer.serializeFragment) on\n    its [content](https://prosemirror.net/docs/ref/#model.Node.content).\n    */\n    serializeNode(node, options = {}) {\n        let dom = this.serializeNodeInner(node, options);\n        for (let i = node.marks.length - 1; i >= 0; i--) {\n            let wrap = this.serializeMark(node.marks[i], node.isInline, options);\n            if (wrap) {\n                (wrap.contentDOM || wrap.dom).appendChild(dom);\n                dom = wrap.dom;\n            }\n        }\n        return dom;\n    }\n    /**\n    @internal\n    */\n    serializeMark(mark, inline, options = {}) {\n        let toDOM = this.marks[mark.type.name];\n        return toDOM && renderSpec(doc(options), toDOM(mark, inline), null, mark.attrs);\n    }\n    static renderSpec(doc, structure, xmlNS = null, blockArraysIn) {\n        return renderSpec(doc, structure, xmlNS, blockArraysIn);\n    }\n    /**\n    Build a serializer using the [`toDOM`](https://prosemirror.net/docs/ref/#model.NodeSpec.toDOM)\n    properties in a schema's node and mark specs.\n    */\n    static fromSchema(schema) {\n        return schema.cached.domSerializer ||\n            (schema.cached.domSerializer = new DOMSerializer(this.nodesFromSchema(schema), this.marksFromSchema(schema)));\n    }\n    /**\n    Gather the serializers in a schema's node specs into an object.\n    This can be useful as a base to build a custom serializer from.\n    */\n    static nodesFromSchema(schema) {\n        let result = gatherToDOM(schema.nodes);\n        if (!result.text)\n            result.text = node => node.text;\n        return result;\n    }\n    /**\n    Gather the serializers in a schema's mark specs into an object.\n    */\n    static marksFromSchema(schema) {\n        return gatherToDOM(schema.marks);\n    }\n}\nfunction gatherToDOM(obj) {\n    let result = {};\n    for (let name in obj) {\n        let toDOM = obj[name].spec.toDOM;\n        if (toDOM)\n            result[name] = toDOM;\n    }\n    return result;\n}\nfunction doc(options) {\n    return options.document || window.document;\n}\nconst suspiciousAttributeCache = new WeakMap();\nfunction suspiciousAttributes(attrs) {\n    let value = suspiciousAttributeCache.get(attrs);\n    if (value === undefined)\n        suspiciousAttributeCache.set(attrs, value = suspiciousAttributesInner(attrs));\n    return value;\n}\nfunction suspiciousAttributesInner(attrs) {\n    let result = null;\n    function scan(value) {\n        if (value && typeof value == \"object\") {\n            if (Array.isArray(value)) {\n                if (typeof value[0] == \"string\") {\n                    if (!result)\n                        result = [];\n                    result.push(value);\n                }\n                else {\n                    for (let i = 0; i < value.length; i++)\n                        scan(value[i]);\n                }\n            }\n            else {\n                for (let prop in value)\n                    scan(value[prop]);\n            }\n        }\n    }\n    scan(attrs);\n    return result;\n}\nfunction renderSpec(doc, structure, xmlNS, blockArraysIn) {\n    if (typeof structure == \"string\")\n        return { dom: doc.createTextNode(structure) };\n    if (structure.nodeType != null)\n        return { dom: structure };\n    if (structure.dom && structure.dom.nodeType != null)\n        return structure;\n    let tagName = structure[0], suspicious;\n    if (typeof tagName != \"string\")\n        throw new RangeError(\"Invalid array passed to renderSpec\");\n    if (blockArraysIn && (suspicious = suspiciousAttributes(blockArraysIn)) &&\n        suspicious.indexOf(structure) > -1)\n        throw new RangeError(\"Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.\");\n    let space = tagName.indexOf(\" \");\n    if (space > 0) {\n        xmlNS = tagName.slice(0, space);\n        tagName = tagName.slice(space + 1);\n    }\n    let contentDOM;\n    let dom = (xmlNS ? doc.createElementNS(xmlNS, tagName) : doc.createElement(tagName));\n    let attrs = structure[1], start = 1;\n    if (attrs && typeof attrs == \"object\" && attrs.nodeType == null && !Array.isArray(attrs)) {\n        start = 2;\n        for (let name in attrs)\n            if (attrs[name] != null) {\n                let space = name.indexOf(\" \");\n                if (space > 0)\n                    dom.setAttributeNS(name.slice(0, space), name.slice(space + 1), attrs[name]);\n                else if (name == \"style\" && dom.style)\n                    dom.style.cssText = attrs[name];\n                else\n                    dom.setAttribute(name, attrs[name]);\n            }\n    }\n    for (let i = start; i < structure.length; i++) {\n        let child = structure[i];\n        if (child === 0) {\n            if (i < structure.length - 1 || i > start)\n                throw new RangeError(\"Content hole must be the only child of its parent node\");\n            return { dom, contentDOM: dom };\n        }\n        else {\n            let { dom: inner, contentDOM: innerContent } = renderSpec(doc, child, xmlNS, blockArraysIn);\n            dom.appendChild(inner);\n            if (innerContent) {\n                if (contentDOM)\n                    throw new RangeError(\"Multiple content holes\");\n                contentDOM = innerContent;\n            }\n        }\n    }\n    return { dom, contentDOM };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-model/dist/index.js\n");

/***/ })

};
;