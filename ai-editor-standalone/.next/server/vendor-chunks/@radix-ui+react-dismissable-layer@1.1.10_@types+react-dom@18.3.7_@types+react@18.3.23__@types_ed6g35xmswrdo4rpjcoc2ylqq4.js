"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-dismissable-layer@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23__@types_ed6g35xmswrdo4rpjcoc2ylqq4";
exports.ids = ["vendor-chunks/@radix-ui+react-dismissable-layer@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23__@types_ed6g35xmswrdo4rpjcoc2ylqq4"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23__@types_ed6g35xmswrdo4rpjcoc2ylqq4/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":
/*!*******************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23__@types_ed6g35xmswrdo4rpjcoc2ylqq4/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs ***!
  \*******************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Branch: () => (/* binding */ Branch),\n/* harmony export */   DismissableLayer: () => (/* binding */ DismissableLayer),\n/* harmony export */   DismissableLayerBranch: () => (/* binding */ DismissableLayerBranch),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.1.3_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@18_ujcax4x2btumxxy334cxx2hjfq/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-escape-keydown */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-escape-keydown@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Branch,DismissableLayer,DismissableLayerBranch,Root auto */ // src/dismissable-layer.tsx\n\n\n\n\n\n\n\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    layers: /* @__PURE__ */ new Set(),\n    layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n    branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disableOutsidePointerEvents = false, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, ...layerProps } = props;\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, {\n        \"DismissableLayer.useComposedRefs[composedRefs]\": (node2)=>setNode(node2)\n    }[\"DismissableLayer.useComposedRefs[composedRefs]\"]);\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [\n        ...context.layersWithOutsidePointerEventsDisabled\n    ].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside({\n        \"DismissableLayer.usePointerDownOutside[pointerDownOutside]\": (event)=>{\n            const target = event.target;\n            const isPointerDownOnBranch = [\n                ...context.branches\n            ].some({\n                \"DismissableLayer.usePointerDownOutside[pointerDownOutside].isPointerDownOnBranch\": (branch)=>branch.contains(target)\n            }[\"DismissableLayer.usePointerDownOutside[pointerDownOutside].isPointerDownOnBranch\"]);\n            if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n            onPointerDownOutside?.(event);\n            onInteractOutside?.(event);\n            if (!event.defaultPrevented) onDismiss?.();\n        }\n    }[\"DismissableLayer.usePointerDownOutside[pointerDownOutside]\"], ownerDocument);\n    const focusOutside = useFocusOutside({\n        \"DismissableLayer.useFocusOutside[focusOutside]\": (event)=>{\n            const target = event.target;\n            const isFocusInBranch = [\n                ...context.branches\n            ].some({\n                \"DismissableLayer.useFocusOutside[focusOutside].isFocusInBranch\": (branch)=>branch.contains(target)\n            }[\"DismissableLayer.useFocusOutside[focusOutside].isFocusInBranch\"]);\n            if (isFocusInBranch) return;\n            onFocusOutside?.(event);\n            onInteractOutside?.(event);\n            if (!event.defaultPrevented) onDismiss?.();\n        }\n    }[\"DismissableLayer.useFocusOutside[focusOutside]\"], ownerDocument);\n    (0,_radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__.useEscapeKeydown)({\n        \"DismissableLayer.useEscapeKeydown\": (event)=>{\n            const isHighestLayer = index === context.layers.size - 1;\n            if (!isHighestLayer) return;\n            onEscapeKeyDown?.(event);\n            if (!event.defaultPrevented && onDismiss) {\n                event.preventDefault();\n                onDismiss();\n            }\n        }\n    }[\"DismissableLayer.useEscapeKeydown\"], ownerDocument);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayer.useEffect\": ()=>{\n            if (!node) return;\n            if (disableOutsidePointerEvents) {\n                if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n                    originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n                    ownerDocument.body.style.pointerEvents = \"none\";\n                }\n                context.layersWithOutsidePointerEventsDisabled.add(node);\n            }\n            context.layers.add(node);\n            dispatchUpdate();\n            return ({\n                \"DismissableLayer.useEffect\": ()=>{\n                    if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n                        ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n                    }\n                }\n            })[\"DismissableLayer.useEffect\"];\n        }\n    }[\"DismissableLayer.useEffect\"], [\n        node,\n        ownerDocument,\n        disableOutsidePointerEvents,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayer.useEffect\": ()=>{\n            return ({\n                \"DismissableLayer.useEffect\": ()=>{\n                    if (!node) return;\n                    context.layers.delete(node);\n                    context.layersWithOutsidePointerEventsDisabled.delete(node);\n                    dispatchUpdate();\n                }\n            })[\"DismissableLayer.useEffect\"];\n        }\n    }[\"DismissableLayer.useEffect\"], [\n        node,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayer.useEffect\": ()=>{\n            const handleUpdate = {\n                \"DismissableLayer.useEffect.handleUpdate\": ()=>force({})\n            }[\"DismissableLayer.useEffect.handleUpdate\"];\n            document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n            return ({\n                \"DismissableLayer.useEffect\": ()=>document.removeEventListener(CONTEXT_UPDATE, handleUpdate)\n            })[\"DismissableLayer.useEffect\"];\n        }\n    }[\"DismissableLayer.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n            ...props.style\n        },\n        onFocusCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)\n    });\n});\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayerBranch.useEffect\": ()=>{\n            const node = ref.current;\n            if (node) {\n                context.branches.add(node);\n                return ({\n                    \"DismissableLayerBranch.useEffect\": ()=>{\n                        context.branches.delete(node);\n                    }\n                })[\"DismissableLayerBranch.useEffect\"];\n            }\n        }\n    }[\"DismissableLayerBranch.useEffect\"], [\n        context.branches\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...props,\n        ref: composedRefs\n    });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n    const handlePointerDownOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onPointerDownOutside);\n    const isPointerInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleClickRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        \"usePointerDownOutside.useRef[handleClickRef]\": ()=>{}\n    }[\"usePointerDownOutside.useRef[handleClickRef]\"]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"usePointerDownOutside.useEffect\": ()=>{\n            const handlePointerDown = {\n                \"usePointerDownOutside.useEffect.handlePointerDown\": (event)=>{\n                    if (event.target && !isPointerInsideReactTreeRef.current) {\n                        let handleAndDispatchPointerDownOutsideEvent2 = {\n                            \"usePointerDownOutside.useEffect.handlePointerDown.handleAndDispatchPointerDownOutsideEvent2\": function() {\n                                handleAndDispatchCustomEvent(POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, {\n                                    discrete: true\n                                });\n                            }\n                        }[\"usePointerDownOutside.useEffect.handlePointerDown.handleAndDispatchPointerDownOutsideEvent2\"];\n                        var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n                        const eventDetail = {\n                            originalEvent: event\n                        };\n                        if (event.pointerType === \"touch\") {\n                            ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                            handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n                            ownerDocument.addEventListener(\"click\", handleClickRef.current, {\n                                once: true\n                            });\n                        } else {\n                            handleAndDispatchPointerDownOutsideEvent2();\n                        }\n                    } else {\n                        ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                    }\n                    isPointerInsideReactTreeRef.current = false;\n                }\n            }[\"usePointerDownOutside.useEffect.handlePointerDown\"];\n            const timerId = window.setTimeout({\n                \"usePointerDownOutside.useEffect.timerId\": ()=>{\n                    ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n                }\n            }[\"usePointerDownOutside.useEffect.timerId\"], 0);\n            return ({\n                \"usePointerDownOutside.useEffect\": ()=>{\n                    window.clearTimeout(timerId);\n                    ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n                    ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                }\n            })[\"usePointerDownOutside.useEffect\"];\n        }\n    }[\"usePointerDownOutside.useEffect\"], [\n        ownerDocument,\n        handlePointerDownOutside\n    ]);\n    return {\n        // ensures we check React component tree (not just DOM tree)\n        onPointerDownCapture: ()=>isPointerInsideReactTreeRef.current = true\n    };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n    const handleFocusOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onFocusOutside);\n    const isFocusInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useFocusOutside.useEffect\": ()=>{\n            const handleFocus = {\n                \"useFocusOutside.useEffect.handleFocus\": (event)=>{\n                    if (event.target && !isFocusInsideReactTreeRef.current) {\n                        const eventDetail = {\n                            originalEvent: event\n                        };\n                        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n                            discrete: false\n                        });\n                    }\n                }\n            }[\"useFocusOutside.useEffect.handleFocus\"];\n            ownerDocument.addEventListener(\"focusin\", handleFocus);\n            return ({\n                \"useFocusOutside.useEffect\": ()=>ownerDocument.removeEventListener(\"focusin\", handleFocus)\n            })[\"useFocusOutside.useEffect\"];\n        }\n    }[\"useFocusOutside.useEffect\"], [\n        ownerDocument,\n        handleFocusOutside\n    ]);\n    return {\n        onFocusCapture: ()=>isFocusInsideReactTreeRef.current = true,\n        onBlurCapture: ()=>isFocusInsideReactTreeRef.current = false\n    };\n}\nfunction dispatchUpdate() {\n    const event = new CustomEvent(CONTEXT_UPDATE);\n    document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const target = detail.originalEvent.target;\n    const event = new CustomEvent(name, {\n        bubbles: false,\n        cancelable: true,\n        detail\n    });\n    if (handler) target.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.dispatchDiscreteCustomEvent)(target, event);\n    } else {\n        target.dispatchEvent(event);\n    }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23__@types_ed6g35xmswrdo4rpjcoc2ylqq4/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\n");

/***/ })

};
;