"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-list-item@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-list-item@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-list-item@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-list-item/dist/index.js":
/*!*************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-list-item@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-list-item/dist/index.js ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ListItem: () => (/* binding */ ListItem),\n/* harmony export */   \"default\": () => (/* binding */ ListItem)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n/**\n * This extension allows you to create list items.\n * @see https://www.tiptap.dev/api/nodes/list-item\n */\nconst ListItem = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Node.create({\n    name: 'listItem',\n    addOptions() {\n        return {\n            HTMLAttributes: {},\n            bulletListTypeName: 'bulletList',\n            orderedListTypeName: 'orderedList',\n        };\n    },\n    content: 'paragraph block*',\n    defining: true,\n    parseHTML() {\n        return [\n            {\n                tag: 'li',\n            },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['li', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n    addKeyboardShortcuts() {\n        return {\n            Enter: () => this.editor.commands.splitListItem(this.name),\n            Tab: () => this.editor.commands.sinkListItem(this.name),\n            'Shift-Tab': () => this.editor.commands.liftListItem(this.name),\n        };\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-list-item@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-list-item/dist/index.js\n");

/***/ })

};
;