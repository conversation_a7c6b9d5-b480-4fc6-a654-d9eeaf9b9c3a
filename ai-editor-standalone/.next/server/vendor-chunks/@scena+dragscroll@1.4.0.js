"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@scena+dragscroll@1.4.0";
exports.ids = ["vendor-chunks/@scena+dragscroll@1.4.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@scena+dragscroll@1.4.0/node_modules/@scena/dragscroll/dist/dragscroll.esm.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@scena+dragscroll@1.4.0/node_modules/@scena/dragscroll/dist/dragscroll.esm.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _scena_event_emitter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @scena/event-emitter */ \"(ssr)/./node_modules/.pnpm/@scena+event-emitter@1.0.5/node_modules/@scena/event-emitter/dist/event-emitter.esm.js\");\n/* harmony import */ var _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @daybrush/utils */ \"(ssr)/./node_modules/.pnpm/@daybrush+utils@1.13.0/node_modules/@daybrush/utils/dist/utils.esm.js\");\n/*\nCopyright (c) 2019 Daybrush\nname: @scena/dragscroll\nlicense: MIT\nauthor: Daybrush\nrepository: git+https://github.com/daybrush/dragscroll.git\nversion: 1.4.0\n*/\n\n\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\n/* global Reflect, Promise */\n\nvar extendStatics = function (d, b) {\n  extendStatics = Object.setPrototypeOf || {\n    __proto__: []\n  } instanceof Array && function (d, b) {\n    d.__proto__ = b;\n  } || function (d, b) {\n    for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n  };\n  return extendStatics(d, b);\n};\nfunction __extends(d, b) {\n  extendStatics(d, b);\n  function __() {\n    this.constructor = d;\n  }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\nvar __assign = function () {\n  __assign = Object.assign || function __assign(t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\n\nfunction getDefaultScrollPosition(e) {\n  var container = e.container;\n  if (container === document.body) {\n    return [container.scrollLeft || document.documentElement.scrollLeft, container.scrollTop || document.documentElement.scrollTop];\n  }\n  return [container.scrollLeft, container.scrollTop];\n}\nfunction checkDefaultScrollEvent(container, callback) {\n  container.addEventListener(\"scroll\", callback);\n  return function () {\n    container.removeEventListener(\"scroll\", callback);\n  };\n}\nfunction getContainerElement(container) {\n  if (!container) {\n    return null;\n  } else if ((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.isString)(container)) {\n    return document.querySelector(container);\n  }\n  if ((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.isFunction)(container)) {\n    return container();\n  } else if (container instanceof Element) {\n    return container;\n  } else if (\"current\" in container) {\n    return container.current;\n  } else if (\"value\" in container) {\n    return container.value;\n  }\n}\n/**\n * @sort 1\n */\nvar DragScroll = /*#__PURE__*/function (_super) {\n  __extends(DragScroll, _super);\n  function DragScroll() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this._startRect = null;\n    _this._startPos = [];\n    _this._prevTime = 0;\n    _this._timer = 0;\n    _this._prevScrollPos = [0, 0];\n    _this._isWait = false;\n    _this._flag = false;\n    _this._currentOptions = null;\n    _this._lock = false;\n    _this._unregister = null;\n    _this._onScroll = function () {\n      var options = _this._currentOptions;\n      if (_this._lock || !options) {\n        return;\n      }\n      _this.emit(\"scrollDrag\", {\n        next: function (inputEvent) {\n          _this.checkScroll({\n            container: options.container,\n            inputEvent: inputEvent\n          });\n        }\n      });\n    };\n    return _this;\n  }\n  /**\n   */\n  var __proto = DragScroll.prototype;\n  __proto.dragStart = function (e, options) {\n    var container = getContainerElement(options.container);\n    if (!container) {\n      this._flag = false;\n      return;\n    }\n    var top = 0;\n    var left = 0;\n    var width = 0;\n    var height = 0;\n    if (container === document.body) {\n      width = window.innerWidth;\n      height = window.innerHeight;\n    } else {\n      var rect = container.getBoundingClientRect();\n      top = rect.top;\n      left = rect.left;\n      width = rect.width;\n      height = rect.height;\n    }\n    this._flag = true;\n    this._startPos = [e.clientX, e.clientY];\n    this._startRect = {\n      top: top,\n      left: left,\n      width: width,\n      height: height\n    };\n    this._prevScrollPos = this._getScrollPosition([0, 0], options);\n    this._currentOptions = options;\n    this._registerScrollEvent(options);\n  };\n  __proto.drag = function (e, options) {\n    clearTimeout(this._timer);\n    if (!this._flag) {\n      return;\n    }\n    var clientX = e.clientX,\n      clientY = e.clientY;\n    var _a = options.threshold,\n      threshold = _a === void 0 ? 0 : _a;\n    var _b = this,\n      _startRect = _b._startRect,\n      _startPos = _b._startPos;\n    this._currentOptions = options;\n    var direction = [0, 0];\n    if (_startRect.top > clientY - threshold) {\n      if (_startPos[1] > _startRect.top || clientY < _startPos[1]) {\n        direction[1] = -1;\n      }\n    } else if (_startRect.top + _startRect.height < clientY + threshold) {\n      if (_startPos[1] < _startRect.top + _startRect.height || clientY > _startPos[1]) {\n        direction[1] = 1;\n      }\n    }\n    if (_startRect.left > clientX - threshold) {\n      if (_startPos[0] > _startRect.left || clientX < _startPos[0]) {\n        direction[0] = -1;\n      }\n    } else if (_startRect.left + _startRect.width < clientX + threshold) {\n      if (_startPos[0] < _startRect.left + _startRect.width || clientX > _startPos[0]) {\n        direction[0] = 1;\n      }\n    }\n    if (!direction[0] && !direction[1]) {\n      return false;\n    }\n    return this._continueDrag(__assign(__assign({}, options), {\n      direction: direction,\n      inputEvent: e,\n      isDrag: true\n    }));\n  };\n  /**\n   */\n  __proto.checkScroll = function (options) {\n    var _this = this;\n    if (this._isWait) {\n      return false;\n    }\n    var _a = options.prevScrollPos,\n      prevScrollPos = _a === void 0 ? this._prevScrollPos : _a,\n      direction = options.direction,\n      _b = options.throttleTime,\n      throttleTime = _b === void 0 ? 0 : _b,\n      inputEvent = options.inputEvent,\n      isDrag = options.isDrag;\n    var nextScrollPos = this._getScrollPosition(direction || [0, 0], options);\n    var offsetX = nextScrollPos[0] - prevScrollPos[0];\n    var offsetY = nextScrollPos[1] - prevScrollPos[1];\n    var nextDirection = direction || [offsetX ? Math.abs(offsetX) / offsetX : 0, offsetY ? Math.abs(offsetY) / offsetY : 0];\n    this._prevScrollPos = nextScrollPos;\n    this._lock = false;\n    if (!offsetX && !offsetY) {\n      return false;\n    }\n    /**\n     * @event DragScroll#move\n     */\n    this.emit(\"move\", {\n      offsetX: nextDirection[0] ? offsetX : 0,\n      offsetY: nextDirection[1] ? offsetY : 0,\n      inputEvent: inputEvent\n    });\n    if (throttleTime && isDrag) {\n      clearTimeout(this._timer);\n      this._timer = window.setTimeout(function () {\n        _this._continueDrag(options);\n      }, throttleTime);\n    }\n    return true;\n  };\n  /**\n   *\n   */\n  __proto.dragEnd = function () {\n    this._flag = false;\n    this._lock = false;\n    clearTimeout(this._timer);\n    this._unregisterScrollEvent();\n  };\n  __proto._getScrollPosition = function (direction, options) {\n    var container = options.container,\n      _a = options.getScrollPosition,\n      getScrollPosition = _a === void 0 ? getDefaultScrollPosition : _a;\n    return getScrollPosition({\n      container: getContainerElement(container),\n      direction: direction\n    });\n  };\n  __proto._continueDrag = function (options) {\n    var _this = this;\n    var _a;\n    var container = options.container,\n      direction = options.direction,\n      throttleTime = options.throttleTime,\n      useScroll = options.useScroll,\n      isDrag = options.isDrag,\n      inputEvent = options.inputEvent;\n    if (!this._flag || isDrag && this._isWait) {\n      return;\n    }\n    var nowTime = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.now)();\n    var distTime = Math.max(throttleTime + this._prevTime - nowTime, 0);\n    if (distTime > 0) {\n      clearTimeout(this._timer);\n      this._timer = window.setTimeout(function () {\n        _this._continueDrag(options);\n      }, distTime);\n      return false;\n    }\n    this._prevTime = nowTime;\n    var prevScrollPos = this._getScrollPosition(direction, options);\n    this._prevScrollPos = prevScrollPos;\n    if (isDrag) {\n      this._isWait = true;\n    }\n    // unregister native scroll event\n    if (!useScroll) {\n      this._lock = true;\n    }\n    var param = {\n      container: getContainerElement(container),\n      direction: direction,\n      inputEvent: inputEvent\n    };\n    (_a = options.requestScroll) === null || _a === void 0 ? void 0 : _a.call(options, param);\n    /**\n     * @event DragScroll#scroll\n     */\n    this.emit(\"scroll\", param);\n    this._isWait = false;\n    return useScroll || this.checkScroll(__assign(__assign({}, options), {\n      prevScrollPos: prevScrollPos,\n      direction: direction,\n      inputEvent: inputEvent\n    }));\n  };\n  __proto._registerScrollEvent = function (options) {\n    this._unregisterScrollEvent();\n    var checkScrollEvent = options.checkScrollEvent;\n    if (!checkScrollEvent) {\n      return;\n    }\n    var callback = checkScrollEvent === true ? checkDefaultScrollEvent : checkScrollEvent;\n    var container = getContainerElement(options.container);\n    if (checkScrollEvent === true && (container === document.body || container === document.documentElement)) {\n      this._unregister = checkDefaultScrollEvent(window, this._onScroll);\n    } else {\n      this._unregister = callback(container, this._onScroll);\n    }\n  };\n  __proto._unregisterScrollEvent = function () {\n    var _a;\n    (_a = this._unregister) === null || _a === void 0 ? void 0 : _a.call(this);\n    this._unregister = null;\n  };\n  return DragScroll;\n}(_scena_event_emitter__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DragScroll);\n//# sourceMappingURL=dragscroll.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@scena+dragscroll@1.4.0/node_modules/@scena/dragscroll/dist/dragscroll.esm.js\n");

/***/ })

};
;