"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@egjs+agent@2.4.4";
exports.ids = ["vendor-chunks/@egjs+agent@2.4.4"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@egjs+agent@2.4.4/node_modules/@egjs/agent/dist/agent.esm.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/@egjs+agent@2.4.4/node_modules/@egjs/agent/dist/agent.esm.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getAccurateAgent: () => (/* binding */ getAccurateAgent),\n/* harmony export */   getLegacyAgent: () => (/* binding */ getLegacyAgent)\n/* harmony export */ });\n/*\nCopyright (c) 2015 NAVER Corp.\nname: @egjs/agent\nlicense: MIT\nauthor: NAVER Corp.\nrepository: git+https://github.com/naver/egjs-agent.git\nversion: 2.4.4\n*/\nfunction some(arr, callback) {\n  var length = arr.length;\n\n  for (var i = 0; i < length; ++i) {\n    if (callback(arr[i], i)) {\n      return true;\n    }\n  }\n\n  return false;\n}\nfunction find(arr, callback) {\n  var length = arr.length;\n\n  for (var i = 0; i < length; ++i) {\n    if (callback(arr[i], i)) {\n      return arr[i];\n    }\n  }\n\n  return null;\n}\nfunction getUserAgentString(agent) {\n  var userAgent = agent;\n\n  if (typeof userAgent === \"undefined\") {\n    if (typeof navigator === \"undefined\" || !navigator) {\n      return \"\";\n    }\n\n    userAgent = navigator.userAgent || \"\";\n  }\n\n  return userAgent.toLowerCase();\n}\nfunction execRegExp(pattern, text) {\n  try {\n    return new RegExp(pattern, \"g\").exec(text);\n  } catch (e) {\n    return null;\n  }\n}\nfunction hasUserAgentData() {\n  if (typeof navigator === \"undefined\" || !navigator || !navigator.userAgentData) {\n    return false;\n  }\n\n  var userAgentData = navigator.userAgentData;\n  var brands = userAgentData.brands || userAgentData.uaList;\n  return !!(brands && brands.length);\n}\nfunction findVersion(versionTest, userAgent) {\n  var result = execRegExp(\"(\" + versionTest + \")((?:\\\\/|\\\\s|:)([0-9|\\\\.|_]+))\", userAgent);\n  return result ? result[3] : \"\";\n}\nfunction convertVersion(text) {\n  return text.replace(/_/g, \".\");\n}\nfunction findPreset(presets, userAgent) {\n  var userPreset = null;\n  var version = \"-1\";\n  some(presets, function (preset) {\n    var result = execRegExp(\"(\" + preset.test + \")((?:\\\\/|\\\\s|:)([0-9|\\\\.|_]+))?\", userAgent);\n\n    if (!result || preset.brand) {\n      return false;\n    }\n\n    userPreset = preset;\n    version = result[3] || \"-1\";\n\n    if (preset.versionAlias) {\n      version = preset.versionAlias;\n    } else if (preset.versionTest) {\n      version = findVersion(preset.versionTest.toLowerCase(), userAgent) || version;\n    }\n\n    version = convertVersion(version);\n    return true;\n  });\n  return {\n    preset: userPreset,\n    version: version\n  };\n}\nfunction findPresetBrand(presets, brands) {\n  var brandInfo = {\n    brand: \"\",\n    version: \"-1\"\n  };\n  some(presets, function (preset) {\n    var result = findBrand(brands, preset);\n\n    if (!result) {\n      return false;\n    }\n\n    brandInfo.brand = preset.id;\n    brandInfo.version = preset.versionAlias || result.version;\n    return brandInfo.version !== \"-1\";\n  });\n  return brandInfo;\n}\nfunction findBrand(brands, preset) {\n  return find(brands, function (_a) {\n    var brand = _a.brand;\n    return execRegExp(\"\" + preset.test, brand.toLowerCase());\n  });\n}\n\nvar BROWSER_PRESETS = [{\n  test: \"phantomjs\",\n  id: \"phantomjs\"\n}, {\n  test: \"whale\",\n  id: \"whale\"\n}, {\n  test: \"edgios|edge|edg\",\n  id: \"edge\"\n}, {\n  test: \"msie|trident|windows phone\",\n  id: \"ie\",\n  versionTest: \"iemobile|msie|rv\"\n}, {\n  test: \"miuibrowser\",\n  id: \"miui browser\"\n}, {\n  test: \"samsungbrowser\",\n  id: \"samsung internet\"\n}, {\n  test: \"samsung\",\n  id: \"samsung internet\",\n  versionTest: \"version\"\n}, {\n  test: \"chrome|crios\",\n  id: \"chrome\"\n}, {\n  test: \"firefox|fxios\",\n  id: \"firefox\"\n}, {\n  test: \"android\",\n  id: \"android browser\",\n  versionTest: \"version\"\n}, {\n  test: \"safari|iphone|ipad|ipod\",\n  id: \"safari\",\n  versionTest: \"version\"\n}]; // chromium's engine(blink) is based on applewebkit 537.36.\n\nvar CHROMIUM_PRESETS = [{\n  test: \"(?=.*applewebkit/(53[0-7]|5[0-2]|[0-4]))(?=.*\\\\schrome)\",\n  id: \"chrome\",\n  versionTest: \"chrome\"\n}, {\n  test: \"chromium\",\n  id: \"chrome\"\n}, {\n  test: \"whale\",\n  id: \"chrome\",\n  versionAlias: \"-1\",\n  brand: true\n}];\nvar WEBKIT_PRESETS = [{\n  test: \"applewebkit\",\n  id: \"webkit\",\n  versionTest: \"applewebkit|safari\"\n}];\nvar WEBVIEW_PRESETS = [{\n  test: \"(?=(iphone|ipad))(?!(.*version))\",\n  id: \"webview\"\n}, {\n  test: \"(?=(android|iphone|ipad))(?=.*(naver|daum|; wv))\",\n  id: \"webview\"\n}, {\n  // test webview\n  test: \"webview\",\n  id: \"webview\"\n}];\nvar OS_PRESETS = [{\n  test: \"windows phone\",\n  id: \"windows phone\"\n}, {\n  test: \"windows 2000\",\n  id: \"window\",\n  versionAlias: \"5.0\"\n}, {\n  test: \"windows nt\",\n  id: \"window\"\n}, {\n  test: \"win32|windows\",\n  id: \"window\"\n}, {\n  test: \"iphone|ipad|ipod\",\n  id: \"ios\",\n  versionTest: \"iphone os|cpu os\"\n}, {\n  test: \"macos|macintel|mac os x\",\n  id: \"mac\"\n}, {\n  test: \"android|linux armv81\",\n  id: \"android\"\n}, {\n  test: \"tizen\",\n  id: \"tizen\"\n}, {\n  test: \"webos|web0s\",\n  id: \"webos\"\n}];\n\nfunction isWebView(userAgent) {\n  return !!findPreset(WEBVIEW_PRESETS, userAgent).preset;\n}\nfunction getLegacyAgent(userAgent) {\n  var nextAgent = getUserAgentString(userAgent);\n  var isMobile = !!/mobi/g.exec(nextAgent);\n  var browser = {\n    name: \"unknown\",\n    version: \"-1\",\n    majorVersion: -1,\n    webview: isWebView(nextAgent),\n    chromium: false,\n    chromiumVersion: \"-1\",\n    webkit: false,\n    webkitVersion: \"-1\"\n  };\n  var os = {\n    name: \"unknown\",\n    version: \"-1\",\n    majorVersion: -1\n  };\n\n  var _a = findPreset(BROWSER_PRESETS, nextAgent),\n      browserPreset = _a.preset,\n      browserVersion = _a.version;\n\n  var _b = findPreset(OS_PRESETS, nextAgent),\n      osPreset = _b.preset,\n      osVersion = _b.version;\n\n  var chromiumPreset = findPreset(CHROMIUM_PRESETS, nextAgent);\n  browser.chromium = !!chromiumPreset.preset;\n  browser.chromiumVersion = chromiumPreset.version;\n\n  if (!browser.chromium) {\n    var webkitPreset = findPreset(WEBKIT_PRESETS, nextAgent);\n    browser.webkit = !!webkitPreset.preset;\n    browser.webkitVersion = webkitPreset.version;\n  }\n\n  if (osPreset) {\n    os.name = osPreset.id;\n    os.version = osVersion;\n    os.majorVersion = parseInt(osVersion, 10);\n  }\n\n  if (browserPreset) {\n    browser.name = browserPreset.id;\n    browser.version = browserVersion; // Early whale bugs\n\n    if (browser.webview && os.name === \"ios\" && browser.name !== \"safari\") {\n      browser.webview = false;\n    }\n  }\n\n  browser.majorVersion = parseInt(browser.version, 10);\n  return {\n    browser: browser,\n    os: os,\n    isMobile: isMobile,\n    isHints: false\n  };\n}\n\nfunction getClientHintsAgent(osData) {\n  var userAgentData = navigator.userAgentData;\n  var brands = (userAgentData.uaList || userAgentData.brands).slice();\n  var fullVersionList = osData && osData.fullVersionList;\n  var isMobile = userAgentData.mobile || false;\n  var firstBrand = brands[0];\n  var platform = (osData && osData.platform || userAgentData.platform || navigator.platform).toLowerCase();\n  var browser = {\n    name: firstBrand.brand,\n    version: firstBrand.version,\n    majorVersion: -1,\n    webkit: false,\n    webkitVersion: \"-1\",\n    chromium: false,\n    chromiumVersion: \"-1\",\n    webview: !!findPresetBrand(WEBVIEW_PRESETS, brands).brand || isWebView(getUserAgentString())\n  };\n  var os = {\n    name: \"unknown\",\n    version: \"-1\",\n    majorVersion: -1\n  };\n  browser.webkit = !browser.chromium && some(WEBKIT_PRESETS, function (preset) {\n    return findBrand(brands, preset);\n  });\n  var chromiumBrand = findPresetBrand(CHROMIUM_PRESETS, brands);\n  browser.chromium = !!chromiumBrand.brand;\n  browser.chromiumVersion = chromiumBrand.version || \"-1\";\n\n  if (!browser.chromium) {\n    var webkitBrand = findPresetBrand(WEBKIT_PRESETS, brands);\n    browser.webkit = !!webkitBrand.brand;\n    browser.webkitVersion = webkitBrand.version || \"-1\";\n  }\n\n  var platfomResult = find(OS_PRESETS, function (preset) {\n    return new RegExp(\"\" + preset.test, \"g\").exec(platform);\n  });\n  os.name = platfomResult ? platfomResult.id : \"\";\n\n  if (osData) {\n    os.version = osData.platformVersion || \"-1\";\n  }\n\n  if (fullVersionList && fullVersionList.length) {\n    var browserBrandByFullVersionList = findPresetBrand(BROWSER_PRESETS, fullVersionList);\n    browser.name = browserBrandByFullVersionList.brand || browser.name;\n    browser.version = browserBrandByFullVersionList.version || browser.version;\n  } else {\n    var browserBrand = findPresetBrand(BROWSER_PRESETS, brands);\n    browser.name = browserBrand.brand || browser.name;\n    browser.version = browserBrand.brand && osData ? osData.uaFullVersion : browserBrand.version;\n  }\n\n  if (browser.webkit) {\n    os.name = isMobile ? \"ios\" : \"mac\";\n  }\n\n  if (os.name === \"ios\" && browser.webview) {\n    browser.version = \"-1\";\n  }\n\n  os.version = convertVersion(os.version);\n  browser.version = convertVersion(browser.version);\n  os.majorVersion = parseInt(os.version, 10);\n  browser.majorVersion = parseInt(browser.version, 10);\n  return {\n    browser: browser,\n    os: os,\n    isMobile: isMobile,\n    isHints: true\n  };\n}\n\n/**\n * @namespace eg.agent\n */\n\n/**\n* Extracts accuate browser and operating system information from the user agent string or client hints.\n* @ko 유저 에이전트 문자열 또는 client hints에서 정확한 브라우저와 운영체제 정보를 추출한다.\n* @function eg.agent#getAccurateAgent\n* @param - Callback function to get the accuate agent <ko>정확한 에이전트를 가져오기 위한 callback 함수</ko>\n* @return - get the accuate agent promise. If Promise are not supported, null is returned. <ko> 정확한 에이전트 promise를 가져온다. Promise를 지원 하지 않는 경우, null을 반환한다. </ko>\n* @example\nimport { getAccurateAgent } from \"@egjs/agent\";\n// eg.agent.getAccurateAgent()\ngetAccurateAgent().then(agent => {\n   const { os, browser, isMobile } = agent;\n});\ngetAccurateAgent(agent => {\n    const { os, browser, isMobile } = agent;\n});\n*/\n\nfunction getAccurateAgent(callback) {\n  if (hasUserAgentData()) {\n    return navigator.userAgentData.getHighEntropyValues([\"architecture\", \"model\", \"platform\", \"platformVersion\", \"uaFullVersion\", \"fullVersionList\"]).then(function (info) {\n      var agentInfo = getClientHintsAgent(info);\n      callback && callback(agentInfo);\n      return agentInfo;\n    });\n  }\n\n  callback && callback(agent());\n\n  if (typeof Promise === \"undefined\" || !Promise) {\n    return null;\n  }\n\n  return Promise.resolve(agent());\n}\n/**\n * Extracts browser and operating system information from the user agent string.\n * @ko 유저 에이전트 문자열에서 브라우저와 운영체제 정보를 추출한다.\n * @function eg.agent#agent\n * @param - user agent string to parse <ko>파싱할 유저에이전트 문자열</ko>\n * @return - agent Info <ko> 에이전트 정보 </ko>\n * @example\nimport agent from \"@egjs/agent\";\n// eg.agent();\nconst { os, browser, isMobile } = agent();\n */\n\nfunction agent(userAgent) {\n  if (typeof userAgent === \"undefined\" && hasUserAgentData()) {\n    return getClientHintsAgent();\n  } else {\n    return getLegacyAgent(userAgent);\n  }\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (agent);\n\n//# sourceMappingURL=agent.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGVnanMrYWdlbnRAMi40LjQvbm9kZV9tb2R1bGVzL0BlZ2pzL2FnZW50L2Rpc3QvYWdlbnQuZXNtLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGtCQUFrQixZQUFZO0FBQzlCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGtCQUFrQixZQUFZO0FBQzlCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLENBQUMsR0FBRzs7QUFFSjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCxxREFBcUQ7QUFDckQ7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxzQ0FBc0M7O0FBRXRDO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTLG1CQUFtQjtBQUM1QjtBQUNBO0FBQ0EsV0FBVyx3QkFBd0I7QUFDbkMsQ0FBQztBQUNEO0FBQ0EsWUFBWSx3QkFBd0I7QUFDcEMsQ0FBQztBQUNEOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsd0JBQXdCO0FBQ2hDOztBQUVBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7O0FBRUEsaUVBQWUsS0FBSyxFQUFDO0FBQ3VCO0FBQzVDIiwic291cmNlcyI6WyIvaG9tZS9tdXppL2dpdGh1Yi9kZWVyLWZsb3cvYWktZWRpdG9yLXN0YW5kYWxvbmUvbm9kZV9tb2R1bGVzLy5wbnBtL0BlZ2pzK2FnZW50QDIuNC40L25vZGVfbW9kdWxlcy9AZWdqcy9hZ2VudC9kaXN0L2FnZW50LmVzbS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuQ29weXJpZ2h0IChjKSAyMDE1IE5BVkVSIENvcnAuXG5uYW1lOiBAZWdqcy9hZ2VudFxubGljZW5zZTogTUlUXG5hdXRob3I6IE5BVkVSIENvcnAuXG5yZXBvc2l0b3J5OiBnaXQraHR0cHM6Ly9naXRodWIuY29tL25hdmVyL2VnanMtYWdlbnQuZ2l0XG52ZXJzaW9uOiAyLjQuNFxuKi9cbmZ1bmN0aW9uIHNvbWUoYXJyLCBjYWxsYmFjaykge1xuICB2YXIgbGVuZ3RoID0gYXJyLmxlbmd0aDtcblxuICBmb3IgKHZhciBpID0gMDsgaSA8IGxlbmd0aDsgKytpKSB7XG4gICAgaWYgKGNhbGxiYWNrKGFycltpXSwgaSkpIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiBmYWxzZTtcbn1cbmZ1bmN0aW9uIGZpbmQoYXJyLCBjYWxsYmFjaykge1xuICB2YXIgbGVuZ3RoID0gYXJyLmxlbmd0aDtcblxuICBmb3IgKHZhciBpID0gMDsgaSA8IGxlbmd0aDsgKytpKSB7XG4gICAgaWYgKGNhbGxiYWNrKGFycltpXSwgaSkpIHtcbiAgICAgIHJldHVybiBhcnJbaV07XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIG51bGw7XG59XG5mdW5jdGlvbiBnZXRVc2VyQWdlbnRTdHJpbmcoYWdlbnQpIHtcbiAgdmFyIHVzZXJBZ2VudCA9IGFnZW50O1xuXG4gIGlmICh0eXBlb2YgdXNlckFnZW50ID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgaWYgKHR5cGVvZiBuYXZpZ2F0b3IgPT09IFwidW5kZWZpbmVkXCIgfHwgIW5hdmlnYXRvcikge1xuICAgICAgcmV0dXJuIFwiXCI7XG4gICAgfVxuXG4gICAgdXNlckFnZW50ID0gbmF2aWdhdG9yLnVzZXJBZ2VudCB8fCBcIlwiO1xuICB9XG5cbiAgcmV0dXJuIHVzZXJBZ2VudC50b0xvd2VyQ2FzZSgpO1xufVxuZnVuY3Rpb24gZXhlY1JlZ0V4cChwYXR0ZXJuLCB0ZXh0KSB7XG4gIHRyeSB7XG4gICAgcmV0dXJuIG5ldyBSZWdFeHAocGF0dGVybiwgXCJnXCIpLmV4ZWModGV4dCk7XG4gIH0gY2F0Y2ggKGUpIHtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxufVxuZnVuY3Rpb24gaGFzVXNlckFnZW50RGF0YSgpIHtcbiAgaWYgKHR5cGVvZiBuYXZpZ2F0b3IgPT09IFwidW5kZWZpbmVkXCIgfHwgIW5hdmlnYXRvciB8fCAhbmF2aWdhdG9yLnVzZXJBZ2VudERhdGEpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cblxuICB2YXIgdXNlckFnZW50RGF0YSA9IG5hdmlnYXRvci51c2VyQWdlbnREYXRhO1xuICB2YXIgYnJhbmRzID0gdXNlckFnZW50RGF0YS5icmFuZHMgfHwgdXNlckFnZW50RGF0YS51YUxpc3Q7XG4gIHJldHVybiAhIShicmFuZHMgJiYgYnJhbmRzLmxlbmd0aCk7XG59XG5mdW5jdGlvbiBmaW5kVmVyc2lvbih2ZXJzaW9uVGVzdCwgdXNlckFnZW50KSB7XG4gIHZhciByZXN1bHQgPSBleGVjUmVnRXhwKFwiKFwiICsgdmVyc2lvblRlc3QgKyBcIikoKD86XFxcXC98XFxcXHN8OikoWzAtOXxcXFxcLnxfXSspKVwiLCB1c2VyQWdlbnQpO1xuICByZXR1cm4gcmVzdWx0ID8gcmVzdWx0WzNdIDogXCJcIjtcbn1cbmZ1bmN0aW9uIGNvbnZlcnRWZXJzaW9uKHRleHQpIHtcbiAgcmV0dXJuIHRleHQucmVwbGFjZSgvXy9nLCBcIi5cIik7XG59XG5mdW5jdGlvbiBmaW5kUHJlc2V0KHByZXNldHMsIHVzZXJBZ2VudCkge1xuICB2YXIgdXNlclByZXNldCA9IG51bGw7XG4gIHZhciB2ZXJzaW9uID0gXCItMVwiO1xuICBzb21lKHByZXNldHMsIGZ1bmN0aW9uIChwcmVzZXQpIHtcbiAgICB2YXIgcmVzdWx0ID0gZXhlY1JlZ0V4cChcIihcIiArIHByZXNldC50ZXN0ICsgXCIpKCg/OlxcXFwvfFxcXFxzfDopKFswLTl8XFxcXC58X10rKSk/XCIsIHVzZXJBZ2VudCk7XG5cbiAgICBpZiAoIXJlc3VsdCB8fCBwcmVzZXQuYnJhbmQpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG5cbiAgICB1c2VyUHJlc2V0ID0gcHJlc2V0O1xuICAgIHZlcnNpb24gPSByZXN1bHRbM10gfHwgXCItMVwiO1xuXG4gICAgaWYgKHByZXNldC52ZXJzaW9uQWxpYXMpIHtcbiAgICAgIHZlcnNpb24gPSBwcmVzZXQudmVyc2lvbkFsaWFzO1xuICAgIH0gZWxzZSBpZiAocHJlc2V0LnZlcnNpb25UZXN0KSB7XG4gICAgICB2ZXJzaW9uID0gZmluZFZlcnNpb24ocHJlc2V0LnZlcnNpb25UZXN0LnRvTG93ZXJDYXNlKCksIHVzZXJBZ2VudCkgfHwgdmVyc2lvbjtcbiAgICB9XG5cbiAgICB2ZXJzaW9uID0gY29udmVydFZlcnNpb24odmVyc2lvbik7XG4gICAgcmV0dXJuIHRydWU7XG4gIH0pO1xuICByZXR1cm4ge1xuICAgIHByZXNldDogdXNlclByZXNldCxcbiAgICB2ZXJzaW9uOiB2ZXJzaW9uXG4gIH07XG59XG5mdW5jdGlvbiBmaW5kUHJlc2V0QnJhbmQocHJlc2V0cywgYnJhbmRzKSB7XG4gIHZhciBicmFuZEluZm8gPSB7XG4gICAgYnJhbmQ6IFwiXCIsXG4gICAgdmVyc2lvbjogXCItMVwiXG4gIH07XG4gIHNvbWUocHJlc2V0cywgZnVuY3Rpb24gKHByZXNldCkge1xuICAgIHZhciByZXN1bHQgPSBmaW5kQnJhbmQoYnJhbmRzLCBwcmVzZXQpO1xuXG4gICAgaWYgKCFyZXN1bHQpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG5cbiAgICBicmFuZEluZm8uYnJhbmQgPSBwcmVzZXQuaWQ7XG4gICAgYnJhbmRJbmZvLnZlcnNpb24gPSBwcmVzZXQudmVyc2lvbkFsaWFzIHx8IHJlc3VsdC52ZXJzaW9uO1xuICAgIHJldHVybiBicmFuZEluZm8udmVyc2lvbiAhPT0gXCItMVwiO1xuICB9KTtcbiAgcmV0dXJuIGJyYW5kSW5mbztcbn1cbmZ1bmN0aW9uIGZpbmRCcmFuZChicmFuZHMsIHByZXNldCkge1xuICByZXR1cm4gZmluZChicmFuZHMsIGZ1bmN0aW9uIChfYSkge1xuICAgIHZhciBicmFuZCA9IF9hLmJyYW5kO1xuICAgIHJldHVybiBleGVjUmVnRXhwKFwiXCIgKyBwcmVzZXQudGVzdCwgYnJhbmQudG9Mb3dlckNhc2UoKSk7XG4gIH0pO1xufVxuXG52YXIgQlJPV1NFUl9QUkVTRVRTID0gW3tcbiAgdGVzdDogXCJwaGFudG9tanNcIixcbiAgaWQ6IFwicGhhbnRvbWpzXCJcbn0sIHtcbiAgdGVzdDogXCJ3aGFsZVwiLFxuICBpZDogXCJ3aGFsZVwiXG59LCB7XG4gIHRlc3Q6IFwiZWRnaW9zfGVkZ2V8ZWRnXCIsXG4gIGlkOiBcImVkZ2VcIlxufSwge1xuICB0ZXN0OiBcIm1zaWV8dHJpZGVudHx3aW5kb3dzIHBob25lXCIsXG4gIGlkOiBcImllXCIsXG4gIHZlcnNpb25UZXN0OiBcImllbW9iaWxlfG1zaWV8cnZcIlxufSwge1xuICB0ZXN0OiBcIm1pdWlicm93c2VyXCIsXG4gIGlkOiBcIm1pdWkgYnJvd3NlclwiXG59LCB7XG4gIHRlc3Q6IFwic2Ftc3VuZ2Jyb3dzZXJcIixcbiAgaWQ6IFwic2Ftc3VuZyBpbnRlcm5ldFwiXG59LCB7XG4gIHRlc3Q6IFwic2Ftc3VuZ1wiLFxuICBpZDogXCJzYW1zdW5nIGludGVybmV0XCIsXG4gIHZlcnNpb25UZXN0OiBcInZlcnNpb25cIlxufSwge1xuICB0ZXN0OiBcImNocm9tZXxjcmlvc1wiLFxuICBpZDogXCJjaHJvbWVcIlxufSwge1xuICB0ZXN0OiBcImZpcmVmb3h8Znhpb3NcIixcbiAgaWQ6IFwiZmlyZWZveFwiXG59LCB7XG4gIHRlc3Q6IFwiYW5kcm9pZFwiLFxuICBpZDogXCJhbmRyb2lkIGJyb3dzZXJcIixcbiAgdmVyc2lvblRlc3Q6IFwidmVyc2lvblwiXG59LCB7XG4gIHRlc3Q6IFwic2FmYXJpfGlwaG9uZXxpcGFkfGlwb2RcIixcbiAgaWQ6IFwic2FmYXJpXCIsXG4gIHZlcnNpb25UZXN0OiBcInZlcnNpb25cIlxufV07IC8vIGNocm9taXVtJ3MgZW5naW5lKGJsaW5rKSBpcyBiYXNlZCBvbiBhcHBsZXdlYmtpdCA1MzcuMzYuXG5cbnZhciBDSFJPTUlVTV9QUkVTRVRTID0gW3tcbiAgdGVzdDogXCIoPz0uKmFwcGxld2Via2l0Lyg1M1swLTddfDVbMC0yXXxbMC00XSkpKD89LipcXFxcc2Nocm9tZSlcIixcbiAgaWQ6IFwiY2hyb21lXCIsXG4gIHZlcnNpb25UZXN0OiBcImNocm9tZVwiXG59LCB7XG4gIHRlc3Q6IFwiY2hyb21pdW1cIixcbiAgaWQ6IFwiY2hyb21lXCJcbn0sIHtcbiAgdGVzdDogXCJ3aGFsZVwiLFxuICBpZDogXCJjaHJvbWVcIixcbiAgdmVyc2lvbkFsaWFzOiBcIi0xXCIsXG4gIGJyYW5kOiB0cnVlXG59XTtcbnZhciBXRUJLSVRfUFJFU0VUUyA9IFt7XG4gIHRlc3Q6IFwiYXBwbGV3ZWJraXRcIixcbiAgaWQ6IFwid2Via2l0XCIsXG4gIHZlcnNpb25UZXN0OiBcImFwcGxld2Via2l0fHNhZmFyaVwiXG59XTtcbnZhciBXRUJWSUVXX1BSRVNFVFMgPSBbe1xuICB0ZXN0OiBcIig/PShpcGhvbmV8aXBhZCkpKD8hKC4qdmVyc2lvbikpXCIsXG4gIGlkOiBcIndlYnZpZXdcIlxufSwge1xuICB0ZXN0OiBcIig/PShhbmRyb2lkfGlwaG9uZXxpcGFkKSkoPz0uKihuYXZlcnxkYXVtfDsgd3YpKVwiLFxuICBpZDogXCJ3ZWJ2aWV3XCJcbn0sIHtcbiAgLy8gdGVzdCB3ZWJ2aWV3XG4gIHRlc3Q6IFwid2Vidmlld1wiLFxuICBpZDogXCJ3ZWJ2aWV3XCJcbn1dO1xudmFyIE9TX1BSRVNFVFMgPSBbe1xuICB0ZXN0OiBcIndpbmRvd3MgcGhvbmVcIixcbiAgaWQ6IFwid2luZG93cyBwaG9uZVwiXG59LCB7XG4gIHRlc3Q6IFwid2luZG93cyAyMDAwXCIsXG4gIGlkOiBcIndpbmRvd1wiLFxuICB2ZXJzaW9uQWxpYXM6IFwiNS4wXCJcbn0sIHtcbiAgdGVzdDogXCJ3aW5kb3dzIG50XCIsXG4gIGlkOiBcIndpbmRvd1wiXG59LCB7XG4gIHRlc3Q6IFwid2luMzJ8d2luZG93c1wiLFxuICBpZDogXCJ3aW5kb3dcIlxufSwge1xuICB0ZXN0OiBcImlwaG9uZXxpcGFkfGlwb2RcIixcbiAgaWQ6IFwiaW9zXCIsXG4gIHZlcnNpb25UZXN0OiBcImlwaG9uZSBvc3xjcHUgb3NcIlxufSwge1xuICB0ZXN0OiBcIm1hY29zfG1hY2ludGVsfG1hYyBvcyB4XCIsXG4gIGlkOiBcIm1hY1wiXG59LCB7XG4gIHRlc3Q6IFwiYW5kcm9pZHxsaW51eCBhcm12ODFcIixcbiAgaWQ6IFwiYW5kcm9pZFwiXG59LCB7XG4gIHRlc3Q6IFwidGl6ZW5cIixcbiAgaWQ6IFwidGl6ZW5cIlxufSwge1xuICB0ZXN0OiBcIndlYm9zfHdlYjBzXCIsXG4gIGlkOiBcIndlYm9zXCJcbn1dO1xuXG5mdW5jdGlvbiBpc1dlYlZpZXcodXNlckFnZW50KSB7XG4gIHJldHVybiAhIWZpbmRQcmVzZXQoV0VCVklFV19QUkVTRVRTLCB1c2VyQWdlbnQpLnByZXNldDtcbn1cbmZ1bmN0aW9uIGdldExlZ2FjeUFnZW50KHVzZXJBZ2VudCkge1xuICB2YXIgbmV4dEFnZW50ID0gZ2V0VXNlckFnZW50U3RyaW5nKHVzZXJBZ2VudCk7XG4gIHZhciBpc01vYmlsZSA9ICEhL21vYmkvZy5leGVjKG5leHRBZ2VudCk7XG4gIHZhciBicm93c2VyID0ge1xuICAgIG5hbWU6IFwidW5rbm93blwiLFxuICAgIHZlcnNpb246IFwiLTFcIixcbiAgICBtYWpvclZlcnNpb246IC0xLFxuICAgIHdlYnZpZXc6IGlzV2ViVmlldyhuZXh0QWdlbnQpLFxuICAgIGNocm9taXVtOiBmYWxzZSxcbiAgICBjaHJvbWl1bVZlcnNpb246IFwiLTFcIixcbiAgICB3ZWJraXQ6IGZhbHNlLFxuICAgIHdlYmtpdFZlcnNpb246IFwiLTFcIlxuICB9O1xuICB2YXIgb3MgPSB7XG4gICAgbmFtZTogXCJ1bmtub3duXCIsXG4gICAgdmVyc2lvbjogXCItMVwiLFxuICAgIG1ham9yVmVyc2lvbjogLTFcbiAgfTtcblxuICB2YXIgX2EgPSBmaW5kUHJlc2V0KEJST1dTRVJfUFJFU0VUUywgbmV4dEFnZW50KSxcbiAgICAgIGJyb3dzZXJQcmVzZXQgPSBfYS5wcmVzZXQsXG4gICAgICBicm93c2VyVmVyc2lvbiA9IF9hLnZlcnNpb247XG5cbiAgdmFyIF9iID0gZmluZFByZXNldChPU19QUkVTRVRTLCBuZXh0QWdlbnQpLFxuICAgICAgb3NQcmVzZXQgPSBfYi5wcmVzZXQsXG4gICAgICBvc1ZlcnNpb24gPSBfYi52ZXJzaW9uO1xuXG4gIHZhciBjaHJvbWl1bVByZXNldCA9IGZpbmRQcmVzZXQoQ0hST01JVU1fUFJFU0VUUywgbmV4dEFnZW50KTtcbiAgYnJvd3Nlci5jaHJvbWl1bSA9ICEhY2hyb21pdW1QcmVzZXQucHJlc2V0O1xuICBicm93c2VyLmNocm9taXVtVmVyc2lvbiA9IGNocm9taXVtUHJlc2V0LnZlcnNpb247XG5cbiAgaWYgKCFicm93c2VyLmNocm9taXVtKSB7XG4gICAgdmFyIHdlYmtpdFByZXNldCA9IGZpbmRQcmVzZXQoV0VCS0lUX1BSRVNFVFMsIG5leHRBZ2VudCk7XG4gICAgYnJvd3Nlci53ZWJraXQgPSAhIXdlYmtpdFByZXNldC5wcmVzZXQ7XG4gICAgYnJvd3Nlci53ZWJraXRWZXJzaW9uID0gd2Via2l0UHJlc2V0LnZlcnNpb247XG4gIH1cblxuICBpZiAob3NQcmVzZXQpIHtcbiAgICBvcy5uYW1lID0gb3NQcmVzZXQuaWQ7XG4gICAgb3MudmVyc2lvbiA9IG9zVmVyc2lvbjtcbiAgICBvcy5tYWpvclZlcnNpb24gPSBwYXJzZUludChvc1ZlcnNpb24sIDEwKTtcbiAgfVxuXG4gIGlmIChicm93c2VyUHJlc2V0KSB7XG4gICAgYnJvd3Nlci5uYW1lID0gYnJvd3NlclByZXNldC5pZDtcbiAgICBicm93c2VyLnZlcnNpb24gPSBicm93c2VyVmVyc2lvbjsgLy8gRWFybHkgd2hhbGUgYnVnc1xuXG4gICAgaWYgKGJyb3dzZXIud2VidmlldyAmJiBvcy5uYW1lID09PSBcImlvc1wiICYmIGJyb3dzZXIubmFtZSAhPT0gXCJzYWZhcmlcIikge1xuICAgICAgYnJvd3Nlci53ZWJ2aWV3ID0gZmFsc2U7XG4gICAgfVxuICB9XG5cbiAgYnJvd3Nlci5tYWpvclZlcnNpb24gPSBwYXJzZUludChicm93c2VyLnZlcnNpb24sIDEwKTtcbiAgcmV0dXJuIHtcbiAgICBicm93c2VyOiBicm93c2VyLFxuICAgIG9zOiBvcyxcbiAgICBpc01vYmlsZTogaXNNb2JpbGUsXG4gICAgaXNIaW50czogZmFsc2VcbiAgfTtcbn1cblxuZnVuY3Rpb24gZ2V0Q2xpZW50SGludHNBZ2VudChvc0RhdGEpIHtcbiAgdmFyIHVzZXJBZ2VudERhdGEgPSBuYXZpZ2F0b3IudXNlckFnZW50RGF0YTtcbiAgdmFyIGJyYW5kcyA9ICh1c2VyQWdlbnREYXRhLnVhTGlzdCB8fCB1c2VyQWdlbnREYXRhLmJyYW5kcykuc2xpY2UoKTtcbiAgdmFyIGZ1bGxWZXJzaW9uTGlzdCA9IG9zRGF0YSAmJiBvc0RhdGEuZnVsbFZlcnNpb25MaXN0O1xuICB2YXIgaXNNb2JpbGUgPSB1c2VyQWdlbnREYXRhLm1vYmlsZSB8fCBmYWxzZTtcbiAgdmFyIGZpcnN0QnJhbmQgPSBicmFuZHNbMF07XG4gIHZhciBwbGF0Zm9ybSA9IChvc0RhdGEgJiYgb3NEYXRhLnBsYXRmb3JtIHx8IHVzZXJBZ2VudERhdGEucGxhdGZvcm0gfHwgbmF2aWdhdG9yLnBsYXRmb3JtKS50b0xvd2VyQ2FzZSgpO1xuICB2YXIgYnJvd3NlciA9IHtcbiAgICBuYW1lOiBmaXJzdEJyYW5kLmJyYW5kLFxuICAgIHZlcnNpb246IGZpcnN0QnJhbmQudmVyc2lvbixcbiAgICBtYWpvclZlcnNpb246IC0xLFxuICAgIHdlYmtpdDogZmFsc2UsXG4gICAgd2Via2l0VmVyc2lvbjogXCItMVwiLFxuICAgIGNocm9taXVtOiBmYWxzZSxcbiAgICBjaHJvbWl1bVZlcnNpb246IFwiLTFcIixcbiAgICB3ZWJ2aWV3OiAhIWZpbmRQcmVzZXRCcmFuZChXRUJWSUVXX1BSRVNFVFMsIGJyYW5kcykuYnJhbmQgfHwgaXNXZWJWaWV3KGdldFVzZXJBZ2VudFN0cmluZygpKVxuICB9O1xuICB2YXIgb3MgPSB7XG4gICAgbmFtZTogXCJ1bmtub3duXCIsXG4gICAgdmVyc2lvbjogXCItMVwiLFxuICAgIG1ham9yVmVyc2lvbjogLTFcbiAgfTtcbiAgYnJvd3Nlci53ZWJraXQgPSAhYnJvd3Nlci5jaHJvbWl1bSAmJiBzb21lKFdFQktJVF9QUkVTRVRTLCBmdW5jdGlvbiAocHJlc2V0KSB7XG4gICAgcmV0dXJuIGZpbmRCcmFuZChicmFuZHMsIHByZXNldCk7XG4gIH0pO1xuICB2YXIgY2hyb21pdW1CcmFuZCA9IGZpbmRQcmVzZXRCcmFuZChDSFJPTUlVTV9QUkVTRVRTLCBicmFuZHMpO1xuICBicm93c2VyLmNocm9taXVtID0gISFjaHJvbWl1bUJyYW5kLmJyYW5kO1xuICBicm93c2VyLmNocm9taXVtVmVyc2lvbiA9IGNocm9taXVtQnJhbmQudmVyc2lvbiB8fCBcIi0xXCI7XG5cbiAgaWYgKCFicm93c2VyLmNocm9taXVtKSB7XG4gICAgdmFyIHdlYmtpdEJyYW5kID0gZmluZFByZXNldEJyYW5kKFdFQktJVF9QUkVTRVRTLCBicmFuZHMpO1xuICAgIGJyb3dzZXIud2Via2l0ID0gISF3ZWJraXRCcmFuZC5icmFuZDtcbiAgICBicm93c2VyLndlYmtpdFZlcnNpb24gPSB3ZWJraXRCcmFuZC52ZXJzaW9uIHx8IFwiLTFcIjtcbiAgfVxuXG4gIHZhciBwbGF0Zm9tUmVzdWx0ID0gZmluZChPU19QUkVTRVRTLCBmdW5jdGlvbiAocHJlc2V0KSB7XG4gICAgcmV0dXJuIG5ldyBSZWdFeHAoXCJcIiArIHByZXNldC50ZXN0LCBcImdcIikuZXhlYyhwbGF0Zm9ybSk7XG4gIH0pO1xuICBvcy5uYW1lID0gcGxhdGZvbVJlc3VsdCA/IHBsYXRmb21SZXN1bHQuaWQgOiBcIlwiO1xuXG4gIGlmIChvc0RhdGEpIHtcbiAgICBvcy52ZXJzaW9uID0gb3NEYXRhLnBsYXRmb3JtVmVyc2lvbiB8fCBcIi0xXCI7XG4gIH1cblxuICBpZiAoZnVsbFZlcnNpb25MaXN0ICYmIGZ1bGxWZXJzaW9uTGlzdC5sZW5ndGgpIHtcbiAgICB2YXIgYnJvd3NlckJyYW5kQnlGdWxsVmVyc2lvbkxpc3QgPSBmaW5kUHJlc2V0QnJhbmQoQlJPV1NFUl9QUkVTRVRTLCBmdWxsVmVyc2lvbkxpc3QpO1xuICAgIGJyb3dzZXIubmFtZSA9IGJyb3dzZXJCcmFuZEJ5RnVsbFZlcnNpb25MaXN0LmJyYW5kIHx8IGJyb3dzZXIubmFtZTtcbiAgICBicm93c2VyLnZlcnNpb24gPSBicm93c2VyQnJhbmRCeUZ1bGxWZXJzaW9uTGlzdC52ZXJzaW9uIHx8IGJyb3dzZXIudmVyc2lvbjtcbiAgfSBlbHNlIHtcbiAgICB2YXIgYnJvd3NlckJyYW5kID0gZmluZFByZXNldEJyYW5kKEJST1dTRVJfUFJFU0VUUywgYnJhbmRzKTtcbiAgICBicm93c2VyLm5hbWUgPSBicm93c2VyQnJhbmQuYnJhbmQgfHwgYnJvd3Nlci5uYW1lO1xuICAgIGJyb3dzZXIudmVyc2lvbiA9IGJyb3dzZXJCcmFuZC5icmFuZCAmJiBvc0RhdGEgPyBvc0RhdGEudWFGdWxsVmVyc2lvbiA6IGJyb3dzZXJCcmFuZC52ZXJzaW9uO1xuICB9XG5cbiAgaWYgKGJyb3dzZXIud2Via2l0KSB7XG4gICAgb3MubmFtZSA9IGlzTW9iaWxlID8gXCJpb3NcIiA6IFwibWFjXCI7XG4gIH1cblxuICBpZiAob3MubmFtZSA9PT0gXCJpb3NcIiAmJiBicm93c2VyLndlYnZpZXcpIHtcbiAgICBicm93c2VyLnZlcnNpb24gPSBcIi0xXCI7XG4gIH1cblxuICBvcy52ZXJzaW9uID0gY29udmVydFZlcnNpb24ob3MudmVyc2lvbik7XG4gIGJyb3dzZXIudmVyc2lvbiA9IGNvbnZlcnRWZXJzaW9uKGJyb3dzZXIudmVyc2lvbik7XG4gIG9zLm1ham9yVmVyc2lvbiA9IHBhcnNlSW50KG9zLnZlcnNpb24sIDEwKTtcbiAgYnJvd3Nlci5tYWpvclZlcnNpb24gPSBwYXJzZUludChicm93c2VyLnZlcnNpb24sIDEwKTtcbiAgcmV0dXJuIHtcbiAgICBicm93c2VyOiBicm93c2VyLFxuICAgIG9zOiBvcyxcbiAgICBpc01vYmlsZTogaXNNb2JpbGUsXG4gICAgaXNIaW50czogdHJ1ZVxuICB9O1xufVxuXG4vKipcbiAqIEBuYW1lc3BhY2UgZWcuYWdlbnRcbiAqL1xuXG4vKipcbiogRXh0cmFjdHMgYWNjdWF0ZSBicm93c2VyIGFuZCBvcGVyYXRpbmcgc3lzdGVtIGluZm9ybWF0aW9uIGZyb20gdGhlIHVzZXIgYWdlbnQgc3RyaW5nIG9yIGNsaWVudCBoaW50cy5cbiogQGtvIOycoOyggCDsl5DsnbTsoITtirgg66y47J6Q7Je0IOuYkOuKlCBjbGllbnQgaGludHPsl5DshJwg7KCV7ZmV7ZWcIOu4jOudvOyasOyggOyZgCDsmrTsmIHssrTsoJwg7KCV67O066W8IOy2lOy2nO2VnOuLpC5cbiogQGZ1bmN0aW9uIGVnLmFnZW50I2dldEFjY3VyYXRlQWdlbnRcbiogQHBhcmFtIC0gQ2FsbGJhY2sgZnVuY3Rpb24gdG8gZ2V0IHRoZSBhY2N1YXRlIGFnZW50IDxrbz7soJXtmZXtlZwg7JeQ7J207KCE7Yq466W8IOqwgOyguOyYpOq4sCDsnITtlZwgY2FsbGJhY2sg7ZWo7IiYPC9rbz5cbiogQHJldHVybiAtIGdldCB0aGUgYWNjdWF0ZSBhZ2VudCBwcm9taXNlLiBJZiBQcm9taXNlIGFyZSBub3Qgc3VwcG9ydGVkLCBudWxsIGlzIHJldHVybmVkLiA8a28+IOygle2Zle2VnCDsl5DsnbTsoITtirggcHJvbWlzZeulvCDqsIDsoLjsmKjri6QuIFByb21pc2Xrpbwg7KeA7JuQIO2VmOyngCDslYrripQg6rK97JqwLCBudWxs7J2EIOuwmO2ZmO2VnOuLpC4gPC9rbz5cbiogQGV4YW1wbGVcbmltcG9ydCB7IGdldEFjY3VyYXRlQWdlbnQgfSBmcm9tIFwiQGVnanMvYWdlbnRcIjtcbi8vIGVnLmFnZW50LmdldEFjY3VyYXRlQWdlbnQoKVxuZ2V0QWNjdXJhdGVBZ2VudCgpLnRoZW4oYWdlbnQgPT4ge1xuICAgY29uc3QgeyBvcywgYnJvd3NlciwgaXNNb2JpbGUgfSA9IGFnZW50O1xufSk7XG5nZXRBY2N1cmF0ZUFnZW50KGFnZW50ID0+IHtcbiAgICBjb25zdCB7IG9zLCBicm93c2VyLCBpc01vYmlsZSB9ID0gYWdlbnQ7XG59KTtcbiovXG5cbmZ1bmN0aW9uIGdldEFjY3VyYXRlQWdlbnQoY2FsbGJhY2spIHtcbiAgaWYgKGhhc1VzZXJBZ2VudERhdGEoKSkge1xuICAgIHJldHVybiBuYXZpZ2F0b3IudXNlckFnZW50RGF0YS5nZXRIaWdoRW50cm9weVZhbHVlcyhbXCJhcmNoaXRlY3R1cmVcIiwgXCJtb2RlbFwiLCBcInBsYXRmb3JtXCIsIFwicGxhdGZvcm1WZXJzaW9uXCIsIFwidWFGdWxsVmVyc2lvblwiLCBcImZ1bGxWZXJzaW9uTGlzdFwiXSkudGhlbihmdW5jdGlvbiAoaW5mbykge1xuICAgICAgdmFyIGFnZW50SW5mbyA9IGdldENsaWVudEhpbnRzQWdlbnQoaW5mbyk7XG4gICAgICBjYWxsYmFjayAmJiBjYWxsYmFjayhhZ2VudEluZm8pO1xuICAgICAgcmV0dXJuIGFnZW50SW5mbztcbiAgICB9KTtcbiAgfVxuXG4gIGNhbGxiYWNrICYmIGNhbGxiYWNrKGFnZW50KCkpO1xuXG4gIGlmICh0eXBlb2YgUHJvbWlzZSA9PT0gXCJ1bmRlZmluZWRcIiB8fCAhUHJvbWlzZSkge1xuICAgIHJldHVybiBudWxsO1xuICB9XG5cbiAgcmV0dXJuIFByb21pc2UucmVzb2x2ZShhZ2VudCgpKTtcbn1cbi8qKlxuICogRXh0cmFjdHMgYnJvd3NlciBhbmQgb3BlcmF0aW5nIHN5c3RlbSBpbmZvcm1hdGlvbiBmcm9tIHRoZSB1c2VyIGFnZW50IHN0cmluZy5cbiAqIEBrbyDsnKDsoIAg7JeQ7J207KCE7Yq4IOusuOyekOyXtOyXkOyEnCDruIzrnbzsmrDsoIDsmYAg7Jq07JiB7LK07KCcIOygleuztOulvCDstpTstpztlZzri6QuXG4gKiBAZnVuY3Rpb24gZWcuYWdlbnQjYWdlbnRcbiAqIEBwYXJhbSAtIHVzZXIgYWdlbnQgc3RyaW5nIHRvIHBhcnNlIDxrbz7tjIzsi7HtlaAg7Jyg7KCA7JeQ7J207KCE7Yq4IOusuOyekOyXtDwva28+XG4gKiBAcmV0dXJuIC0gYWdlbnQgSW5mbyA8a28+IOyXkOydtOyghO2KuCDsoJXrs7QgPC9rbz5cbiAqIEBleGFtcGxlXG5pbXBvcnQgYWdlbnQgZnJvbSBcIkBlZ2pzL2FnZW50XCI7XG4vLyBlZy5hZ2VudCgpO1xuY29uc3QgeyBvcywgYnJvd3NlciwgaXNNb2JpbGUgfSA9IGFnZW50KCk7XG4gKi9cblxuZnVuY3Rpb24gYWdlbnQodXNlckFnZW50KSB7XG4gIGlmICh0eXBlb2YgdXNlckFnZW50ID09PSBcInVuZGVmaW5lZFwiICYmIGhhc1VzZXJBZ2VudERhdGEoKSkge1xuICAgIHJldHVybiBnZXRDbGllbnRIaW50c0FnZW50KCk7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIGdldExlZ2FjeUFnZW50KHVzZXJBZ2VudCk7XG4gIH1cbn1cblxuZXhwb3J0IGRlZmF1bHQgYWdlbnQ7XG5leHBvcnQgeyBnZXRBY2N1cmF0ZUFnZW50LCBnZXRMZWdhY3lBZ2VudCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YWdlbnQuZXNtLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@egjs+agent@2.4.4/node_modules/@egjs/agent/dist/agent.esm.js\n");

/***/ })

};
;