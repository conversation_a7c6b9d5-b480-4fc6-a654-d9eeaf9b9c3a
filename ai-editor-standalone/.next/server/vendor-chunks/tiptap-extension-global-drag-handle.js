"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tiptap-extension-global-drag-handle";
exports.ids = ["vendor-chunks/tiptap-extension-global-drag-handle"];
exports.modules = {

/***/ "(ssr)/./node_modules/tiptap-extension-global-drag-handle/dist/index.js":
/*!************************************************************************!*\
  !*** ./node_modules/tiptap-extension-global-drag-handle/dist/index.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragHandlePlugin: () => (/* binding */ DragHandlePlugin),\n/* harmony export */   \"default\": () => (/* binding */ GlobalDragHandle)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/@tiptap/pm/state/dist/index.js\");\n/* harmony import */ var _tiptap_pm_model__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/pm/model */ \"(ssr)/./node_modules/@tiptap/pm/model/dist/index.js\");\n/* harmony import */ var _tiptap_pm_view__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/pm/view */ \"(ssr)/./node_modules/@tiptap/pm/view/dist/index.js\");\n\n\n\n\n\nfunction getPmView() {\n    try {\n        return _tiptap_pm_view__WEBPACK_IMPORTED_MODULE_2__;\n    }\n    catch (error) {\n        return null;\n    }\n}\nfunction serializeForClipboard(view, slice) {\n    // Newer Tiptap/ProseMirror\n    // @ts-ignore\n    if (view && typeof view.serializeForClipboard === 'function') {\n        return view.serializeForClipboard(slice);\n    }\n    // Older version fallback\n    const proseMirrorView = getPmView();\n    // @ts-ignore\n    if (proseMirrorView && typeof proseMirrorView?.__serializeForClipboard === 'function') {\n        // @ts-ignore\n        return proseMirrorView.__serializeForClipboard(view, slice);\n    }\n    throw new Error('No supported clipboard serialization method found.');\n}\n\nfunction absoluteRect(node) {\n    const data = node.getBoundingClientRect();\n    const modal = node.closest('[role=\"dialog\"]');\n    if (modal && window.getComputedStyle(modal).transform !== 'none') {\n        const modalRect = modal.getBoundingClientRect();\n        return {\n            top: data.top - modalRect.top,\n            left: data.left - modalRect.left,\n            width: data.width,\n        };\n    }\n    return {\n        top: data.top,\n        left: data.left,\n        width: data.width,\n    };\n}\nfunction nodeDOMAtCoords(coords, options) {\n    const selectors = [\n        'li',\n        'p:not(:first-child)',\n        'pre',\n        'blockquote',\n        'h1',\n        'h2',\n        'h3',\n        'h4',\n        'h5',\n        'h6',\n        ...options.customNodes.map((node) => `[data-type=${node}]`),\n    ].join(', ');\n    return document\n        .elementsFromPoint(coords.x, coords.y)\n        .find((elem) => elem.parentElement?.matches?.('.ProseMirror') ||\n        elem.matches(selectors));\n}\nfunction nodePosAtDOM(node, view, options) {\n    const boundingRect = node.getBoundingClientRect();\n    return view.posAtCoords({\n        left: boundingRect.left + 50 + options.dragHandleWidth,\n        top: boundingRect.top + 1,\n    })?.inside;\n}\nfunction calcNodePos(pos, view) {\n    const $pos = view.state.doc.resolve(pos);\n    if ($pos.depth > 1)\n        return $pos.before($pos.depth);\n    return pos;\n}\nfunction DragHandlePlugin(options) {\n    let listType = '';\n    function handleDragStart(event, view) {\n        view.focus();\n        if (!event.dataTransfer)\n            return;\n        const node = nodeDOMAtCoords({\n            x: event.clientX + 50 + options.dragHandleWidth,\n            y: event.clientY,\n        }, options);\n        if (!(node instanceof Element))\n            return;\n        let draggedNodePos = nodePosAtDOM(node, view, options);\n        if (draggedNodePos == null || draggedNodePos < 0)\n            return;\n        draggedNodePos = calcNodePos(draggedNodePos, view);\n        const { from, to } = view.state.selection;\n        const diff = from - to;\n        const fromSelectionPos = calcNodePos(from, view);\n        let differentNodeSelected = false;\n        const nodePos = view.state.doc.resolve(fromSelectionPos);\n        // Check if nodePos points to the top level node\n        if (nodePos.node().type.name === 'doc')\n            differentNodeSelected = true;\n        else {\n            const nodeSelection = _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection.create(view.state.doc, nodePos.before());\n            // Check if the node where the drag event started is part of the current selection\n            differentNodeSelected = !(draggedNodePos + 1 >= nodeSelection.$from.pos &&\n                draggedNodePos <= nodeSelection.$to.pos);\n        }\n        let selection = view.state.selection;\n        if (!differentNodeSelected &&\n            diff !== 0 &&\n            !(view.state.selection instanceof _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection)) {\n            const endSelection = _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection.create(view.state.doc, to - 1);\n            selection = _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.create(view.state.doc, draggedNodePos, endSelection.$to.pos);\n        }\n        else {\n            selection = _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection.create(view.state.doc, draggedNodePos);\n            // if inline node is selected, e.g mention -> go to the parent node to select the whole node\n            // if table row is selected, go to the parent node to select the whole node\n            if (selection.node.type.isInline ||\n                selection.node.type.name === 'tableRow') {\n                let $pos = view.state.doc.resolve(selection.from);\n                selection = _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection.create(view.state.doc, $pos.before());\n            }\n        }\n        view.dispatch(view.state.tr.setSelection(selection));\n        // If the selected node is a list item, we need to save the type of the wrapping list e.g. OL or UL\n        if (view.state.selection instanceof _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection &&\n            view.state.selection.node.type.name === 'listItem') {\n            listType = node.parentElement.tagName;\n        }\n        const slice = view.state.selection.content();\n        const { dom, text } = serializeForClipboard(view, slice);\n        event.dataTransfer.clearData();\n        event.dataTransfer.setData('text/html', dom.innerHTML);\n        event.dataTransfer.setData('text/plain', text);\n        event.dataTransfer.effectAllowed = 'copyMove';\n        event.dataTransfer.setDragImage(node, 0, 0);\n        view.dragging = { slice, move: event.ctrlKey };\n    }\n    let dragHandleElement = null;\n    function hideDragHandle() {\n        if (dragHandleElement) {\n            dragHandleElement.classList.add('hide');\n        }\n    }\n    function showDragHandle() {\n        if (dragHandleElement) {\n            dragHandleElement.classList.remove('hide');\n        }\n    }\n    function hideHandleOnEditorOut(event) {\n        if (event.target instanceof Element) {\n            // Check if the relatedTarget class is still inside the editor\n            const relatedTarget = event.relatedTarget;\n            const isInsideEditor = relatedTarget?.classList.contains('tiptap') ||\n                relatedTarget?.classList.contains('drag-handle');\n            if (isInsideEditor)\n                return;\n        }\n        hideDragHandle();\n    }\n    return new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n        key: new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey(options.pluginKey),\n        view: (view) => {\n            const handleBySelector = options.dragHandleSelector\n                ? document.querySelector(options.dragHandleSelector)\n                : null;\n            dragHandleElement = handleBySelector ?? document.createElement('div');\n            dragHandleElement.draggable = true;\n            dragHandleElement.dataset.dragHandle = '';\n            dragHandleElement.classList.add('drag-handle');\n            function onDragHandleDragStart(e) {\n                handleDragStart(e, view);\n            }\n            dragHandleElement.addEventListener('dragstart', onDragHandleDragStart);\n            function onDragHandleDrag(e) {\n                hideDragHandle();\n                let scrollY = window.scrollY;\n                if (e.clientY < options.scrollTreshold) {\n                    window.scrollTo({ top: scrollY - 30, behavior: 'smooth' });\n                }\n                else if (window.innerHeight - e.clientY < options.scrollTreshold) {\n                    window.scrollTo({ top: scrollY + 30, behavior: 'smooth' });\n                }\n            }\n            dragHandleElement.addEventListener('drag', onDragHandleDrag);\n            hideDragHandle();\n            if (!handleBySelector) {\n                view?.dom?.parentElement?.appendChild(dragHandleElement);\n            }\n            view?.dom?.parentElement?.addEventListener('mouseout', hideHandleOnEditorOut);\n            return {\n                destroy: () => {\n                    if (!handleBySelector) {\n                        dragHandleElement?.remove?.();\n                    }\n                    dragHandleElement?.removeEventListener('drag', onDragHandleDrag);\n                    dragHandleElement?.removeEventListener('dragstart', onDragHandleDragStart);\n                    dragHandleElement = null;\n                    view?.dom?.parentElement?.removeEventListener('mouseout', hideHandleOnEditorOut);\n                },\n            };\n        },\n        props: {\n            handleDOMEvents: {\n                mousemove: (view, event) => {\n                    if (!view.editable) {\n                        return;\n                    }\n                    const node = nodeDOMAtCoords({\n                        x: event.clientX + 50 + options.dragHandleWidth,\n                        y: event.clientY,\n                    }, options);\n                    const notDragging = node?.closest('.not-draggable');\n                    const excludedTagList = options.excludedTags\n                        .concat(['ol', 'ul'])\n                        .join(', ');\n                    if (!(node instanceof Element) ||\n                        node.matches(excludedTagList) ||\n                        notDragging) {\n                        hideDragHandle();\n                        return;\n                    }\n                    const compStyle = window.getComputedStyle(node);\n                    const parsedLineHeight = parseInt(compStyle.lineHeight, 10);\n                    const lineHeight = isNaN(parsedLineHeight)\n                        ? parseInt(compStyle.fontSize) * 1.2\n                        : parsedLineHeight;\n                    const paddingTop = parseInt(compStyle.paddingTop, 10);\n                    const rect = absoluteRect(node);\n                    rect.top += (lineHeight - 24) / 2;\n                    rect.top += paddingTop;\n                    // Li markers\n                    if (node.matches('ul:not([data-type=taskList]) li, ol li')) {\n                        rect.left -= options.dragHandleWidth;\n                    }\n                    rect.width = options.dragHandleWidth;\n                    if (!dragHandleElement)\n                        return;\n                    dragHandleElement.style.left = `${rect.left - rect.width}px`;\n                    dragHandleElement.style.top = `${rect.top}px`;\n                    showDragHandle();\n                },\n                keydown: () => {\n                    hideDragHandle();\n                },\n                mousewheel: () => {\n                    hideDragHandle();\n                },\n                // dragging class is used for CSS\n                dragstart: (view) => {\n                    view.dom.classList.add('dragging');\n                },\n                drop: (view, event) => {\n                    view.dom.classList.remove('dragging');\n                    hideDragHandle();\n                    let droppedNode = null;\n                    const dropPos = view.posAtCoords({\n                        left: event.clientX,\n                        top: event.clientY,\n                    });\n                    if (!dropPos)\n                        return;\n                    if (view.state.selection instanceof _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection) {\n                        droppedNode = view.state.selection.node;\n                    }\n                    if (!droppedNode)\n                        return;\n                    const resolvedPos = view.state.doc.resolve(dropPos.pos);\n                    const isDroppedInsideList = resolvedPos.parent.type.name === 'listItem';\n                    // If the selected node is a list item and is not dropped inside a list, we need to wrap it inside <ol> tag otherwise ol list items will be transformed into ul list item when dropped\n                    if (view.state.selection instanceof _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection &&\n                        view.state.selection.node.type.name === 'listItem' &&\n                        !isDroppedInsideList &&\n                        listType == 'OL') {\n                        const newList = view.state.schema.nodes.orderedList?.createAndFill(null, droppedNode);\n                        const slice = new _tiptap_pm_model__WEBPACK_IMPORTED_MODULE_1__.Slice(_tiptap_pm_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(newList), 0, 0);\n                        view.dragging = { slice, move: event.ctrlKey };\n                    }\n                },\n                dragend: (view) => {\n                    view.dom.classList.remove('dragging');\n                },\n            },\n        },\n    });\n}\nconst GlobalDragHandle = _tiptap_core__WEBPACK_IMPORTED_MODULE_3__.Extension.create({\n    name: 'globalDragHandle',\n    addOptions() {\n        return {\n            dragHandleWidth: 20,\n            scrollTreshold: 100,\n            excludedTags: [],\n            customNodes: [],\n        };\n    },\n    addProseMirrorPlugins() {\n        return [\n            DragHandlePlugin({\n                pluginKey: 'globalDragHandle',\n                dragHandleWidth: this.options.dragHandleWidth,\n                scrollTreshold: this.options.scrollTreshold,\n                dragHandleSelector: this.options.dragHandleSelector,\n                excludedTags: this.options.excludedTags,\n                customNodes: this.options.customNodes,\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tiptap-extension-global-drag-handle/dist/index.js\n");

/***/ })

};
;