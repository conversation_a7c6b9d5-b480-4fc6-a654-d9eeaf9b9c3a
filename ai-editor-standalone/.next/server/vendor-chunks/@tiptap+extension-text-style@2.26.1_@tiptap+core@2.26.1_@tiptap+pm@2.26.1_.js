"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-text-style@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-text-style@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-text-style@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-text-style/dist/index.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-text-style@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-text-style/dist/index.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TextStyle: () => (/* binding */ TextStyle),\n/* harmony export */   \"default\": () => (/* binding */ TextStyle)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\nconst mergeNestedSpanStyles = (element) => {\n    if (!element.children.length) {\n        return;\n    }\n    const childSpans = element.querySelectorAll('span');\n    if (!childSpans) {\n        return;\n    }\n    childSpans.forEach(childSpan => {\n        var _a, _b;\n        const childStyle = childSpan.getAttribute('style');\n        const closestParentSpanStyleOfChild = (_b = (_a = childSpan.parentElement) === null || _a === void 0 ? void 0 : _a.closest('span')) === null || _b === void 0 ? void 0 : _b.getAttribute('style');\n        childSpan.setAttribute('style', `${closestParentSpanStyleOfChild};${childStyle}`);\n    });\n};\n/**\n * This extension allows you to create text styles. It is required by default\n * for the `textColor` and `backgroundColor` extensions.\n * @see https://www.tiptap.dev/api/marks/text-style\n */\nconst TextStyle = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Mark.create({\n    name: 'textStyle',\n    priority: 101,\n    addOptions() {\n        return {\n            HTMLAttributes: {},\n            mergeNestedSpanStyles: false,\n        };\n    },\n    parseHTML() {\n        return [\n            {\n                tag: 'span',\n                getAttrs: element => {\n                    const hasStyles = element.hasAttribute('style');\n                    if (!hasStyles) {\n                        return false;\n                    }\n                    if (this.options.mergeNestedSpanStyles) {\n                        mergeNestedSpanStyles(element);\n                    }\n                    return {};\n                },\n            },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['span', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n    addCommands() {\n        return {\n            removeEmptyTextStyle: () => ({ tr }) => {\n                const { selection } = tr;\n                // Gather all of the nodes within the selection range.\n                // We would need to go through each node individually\n                // to check if it has any inline style attributes.\n                // Otherwise, calling commands.unsetMark(this.name)\n                // removes everything from all the nodes\n                // within the selection range.\n                tr.doc.nodesBetween(selection.from, selection.to, (node, pos) => {\n                    // Check if it's a paragraph element, if so, skip this node as we apply\n                    // the text style to inline text nodes only (span).\n                    if (node.isTextblock) {\n                        return true;\n                    }\n                    // Check if the node has no inline style attributes.\n                    // Filter out non-`textStyle` marks.\n                    if (!node.marks.filter(mark => mark.type === this.type).some(mark => Object.values(mark.attrs).some(value => !!value))) {\n                        // Proceed with the removal of the `textStyle` mark for this node only\n                        tr.removeMark(pos, pos + node.nodeSize, this.type);\n                    }\n                });\n                return true;\n            },\n        };\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHRpcHRhcCtleHRlbnNpb24tdGV4dC1zdHlsZUAyLjI2LjFfQHRpcHRhcCtjb3JlQDIuMjYuMV9AdGlwdGFwK3BtQDIuMjYuMV8vbm9kZV9tb2R1bGVzL0B0aXB0YXAvZXh0ZW5zaW9uLXRleHQtc3R5bGUvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBcUQ7O0FBRXJEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQywrQkFBK0IsRUFBRSxXQUFXO0FBQ3ZGLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsOENBQUk7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEI7QUFDOUI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2I7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLGdCQUFnQjtBQUNqQyx3QkFBd0IsNkRBQWU7QUFDdkMsS0FBSztBQUNMO0FBQ0E7QUFDQSwyQ0FBMkMsSUFBSTtBQUMvQyx3QkFBd0IsWUFBWTtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSxhQUFhO0FBQ2I7QUFDQSxLQUFLO0FBQ0wsQ0FBQzs7QUFFMEM7QUFDM0MiLCJzb3VyY2VzIjpbIi9ob21lL211emkvZ2l0aHViL2RlZXItZmxvdy9haS1lZGl0b3Itc3RhbmRhbG9uZS9ub2RlX21vZHVsZXMvLnBucG0vQHRpcHRhcCtleHRlbnNpb24tdGV4dC1zdHlsZUAyLjI2LjFfQHRpcHRhcCtjb3JlQDIuMjYuMV9AdGlwdGFwK3BtQDIuMjYuMV8vbm9kZV9tb2R1bGVzL0B0aXB0YXAvZXh0ZW5zaW9uLXRleHQtc3R5bGUvZGlzdC9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBNYXJrLCBtZXJnZUF0dHJpYnV0ZXMgfSBmcm9tICdAdGlwdGFwL2NvcmUnO1xuXG5jb25zdCBtZXJnZU5lc3RlZFNwYW5TdHlsZXMgPSAoZWxlbWVudCkgPT4ge1xuICAgIGlmICghZWxlbWVudC5jaGlsZHJlbi5sZW5ndGgpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBjb25zdCBjaGlsZFNwYW5zID0gZWxlbWVudC5xdWVyeVNlbGVjdG9yQWxsKCdzcGFuJyk7XG4gICAgaWYgKCFjaGlsZFNwYW5zKSB7XG4gICAgICAgIHJldHVybjtcbiAgICB9XG4gICAgY2hpbGRTcGFucy5mb3JFYWNoKGNoaWxkU3BhbiA9PiB7XG4gICAgICAgIHZhciBfYSwgX2I7XG4gICAgICAgIGNvbnN0IGNoaWxkU3R5bGUgPSBjaGlsZFNwYW4uZ2V0QXR0cmlidXRlKCdzdHlsZScpO1xuICAgICAgICBjb25zdCBjbG9zZXN0UGFyZW50U3BhblN0eWxlT2ZDaGlsZCA9IChfYiA9IChfYSA9IGNoaWxkU3Bhbi5wYXJlbnRFbGVtZW50KSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuY2xvc2VzdCgnc3BhbicpKSA9PT0gbnVsbCB8fCBfYiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2IuZ2V0QXR0cmlidXRlKCdzdHlsZScpO1xuICAgICAgICBjaGlsZFNwYW4uc2V0QXR0cmlidXRlKCdzdHlsZScsIGAke2Nsb3Nlc3RQYXJlbnRTcGFuU3R5bGVPZkNoaWxkfTske2NoaWxkU3R5bGV9YCk7XG4gICAgfSk7XG59O1xuLyoqXG4gKiBUaGlzIGV4dGVuc2lvbiBhbGxvd3MgeW91IHRvIGNyZWF0ZSB0ZXh0IHN0eWxlcy4gSXQgaXMgcmVxdWlyZWQgYnkgZGVmYXVsdFxuICogZm9yIHRoZSBgdGV4dENvbG9yYCBhbmQgYGJhY2tncm91bmRDb2xvcmAgZXh0ZW5zaW9ucy5cbiAqIEBzZWUgaHR0cHM6Ly93d3cudGlwdGFwLmRldi9hcGkvbWFya3MvdGV4dC1zdHlsZVxuICovXG5jb25zdCBUZXh0U3R5bGUgPSBNYXJrLmNyZWF0ZSh7XG4gICAgbmFtZTogJ3RleHRTdHlsZScsXG4gICAgcHJpb3JpdHk6IDEwMSxcbiAgICBhZGRPcHRpb25zKCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgSFRNTEF0dHJpYnV0ZXM6IHt9LFxuICAgICAgICAgICAgbWVyZ2VOZXN0ZWRTcGFuU3R5bGVzOiBmYWxzZSxcbiAgICAgICAgfTtcbiAgICB9LFxuICAgIHBhcnNlSFRNTCgpIHtcbiAgICAgICAgcmV0dXJuIFtcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICB0YWc6ICdzcGFuJyxcbiAgICAgICAgICAgICAgICBnZXRBdHRyczogZWxlbWVudCA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGhhc1N0eWxlcyA9IGVsZW1lbnQuaGFzQXR0cmlidXRlKCdzdHlsZScpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoIWhhc1N0eWxlcykge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLm9wdGlvbnMubWVyZ2VOZXN0ZWRTcGFuU3R5bGVzKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBtZXJnZU5lc3RlZFNwYW5TdHlsZXMoZWxlbWVudCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHt9O1xuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9LFxuICAgICAgICBdO1xuICAgIH0sXG4gICAgcmVuZGVySFRNTCh7IEhUTUxBdHRyaWJ1dGVzIH0pIHtcbiAgICAgICAgcmV0dXJuIFsnc3BhbicsIG1lcmdlQXR0cmlidXRlcyh0aGlzLm9wdGlvbnMuSFRNTEF0dHJpYnV0ZXMsIEhUTUxBdHRyaWJ1dGVzKSwgMF07XG4gICAgfSxcbiAgICBhZGRDb21tYW5kcygpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHJlbW92ZUVtcHR5VGV4dFN0eWxlOiAoKSA9PiAoeyB0ciB9KSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgeyBzZWxlY3Rpb24gfSA9IHRyO1xuICAgICAgICAgICAgICAgIC8vIEdhdGhlciBhbGwgb2YgdGhlIG5vZGVzIHdpdGhpbiB0aGUgc2VsZWN0aW9uIHJhbmdlLlxuICAgICAgICAgICAgICAgIC8vIFdlIHdvdWxkIG5lZWQgdG8gZ28gdGhyb3VnaCBlYWNoIG5vZGUgaW5kaXZpZHVhbGx5XG4gICAgICAgICAgICAgICAgLy8gdG8gY2hlY2sgaWYgaXQgaGFzIGFueSBpbmxpbmUgc3R5bGUgYXR0cmlidXRlcy5cbiAgICAgICAgICAgICAgICAvLyBPdGhlcndpc2UsIGNhbGxpbmcgY29tbWFuZHMudW5zZXRNYXJrKHRoaXMubmFtZSlcbiAgICAgICAgICAgICAgICAvLyByZW1vdmVzIGV2ZXJ5dGhpbmcgZnJvbSBhbGwgdGhlIG5vZGVzXG4gICAgICAgICAgICAgICAgLy8gd2l0aGluIHRoZSBzZWxlY3Rpb24gcmFuZ2UuXG4gICAgICAgICAgICAgICAgdHIuZG9jLm5vZGVzQmV0d2VlbihzZWxlY3Rpb24uZnJvbSwgc2VsZWN0aW9uLnRvLCAobm9kZSwgcG9zKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIC8vIENoZWNrIGlmIGl0J3MgYSBwYXJhZ3JhcGggZWxlbWVudCwgaWYgc28sIHNraXAgdGhpcyBub2RlIGFzIHdlIGFwcGx5XG4gICAgICAgICAgICAgICAgICAgIC8vIHRoZSB0ZXh0IHN0eWxlIHRvIGlubGluZSB0ZXh0IG5vZGVzIG9ubHkgKHNwYW4pLlxuICAgICAgICAgICAgICAgICAgICBpZiAobm9kZS5pc1RleHRibG9jaykge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgLy8gQ2hlY2sgaWYgdGhlIG5vZGUgaGFzIG5vIGlubGluZSBzdHlsZSBhdHRyaWJ1dGVzLlxuICAgICAgICAgICAgICAgICAgICAvLyBGaWx0ZXIgb3V0IG5vbi1gdGV4dFN0eWxlYCBtYXJrcy5cbiAgICAgICAgICAgICAgICAgICAgaWYgKCFub2RlLm1hcmtzLmZpbHRlcihtYXJrID0+IG1hcmsudHlwZSA9PT0gdGhpcy50eXBlKS5zb21lKG1hcmsgPT4gT2JqZWN0LnZhbHVlcyhtYXJrLmF0dHJzKS5zb21lKHZhbHVlID0+ICEhdmFsdWUpKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gUHJvY2VlZCB3aXRoIHRoZSByZW1vdmFsIG9mIHRoZSBgdGV4dFN0eWxlYCBtYXJrIGZvciB0aGlzIG5vZGUgb25seVxuICAgICAgICAgICAgICAgICAgICAgICAgdHIucmVtb3ZlTWFyayhwb3MsIHBvcyArIG5vZGUubm9kZVNpemUsIHRoaXMudHlwZSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH07XG4gICAgfSxcbn0pO1xuXG5leHBvcnQgeyBUZXh0U3R5bGUsIFRleHRTdHlsZSBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-text-style@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-text-style/dist/index.js\n");

/***/ })

};
;