"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gesto";
exports.ids = ["vendor-chunks/gesto"];
exports.modules = {

/***/ "(ssr)/./node_modules/gesto/dist/gesto.esm.js":
/*!**********************************************!*\
  !*** ./node_modules/gesto/dist/gesto.esm.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Gesto)\n/* harmony export */ });\n/* harmony import */ var _scena_event_emitter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @scena/event-emitter */ \"(ssr)/./node_modules/@scena/event-emitter/dist/event-emitter.esm.js\");\n/* harmony import */ var _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @daybrush/utils */ \"(ssr)/./node_modules/@daybrush/utils/dist/utils.esm.js\");\n/*\nCopyright (c) 2019 Daybrush\nname: gesto\nlicense: MIT\nauthor: Daybrush\nrepository: git+https://github.com/daybrush/gesto.git\nversion: 1.19.4\n*/\n\n\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nfunction __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nfunction getRad(pos1, pos2) {\n    var distX = pos2[0] - pos1[0];\n    var distY = pos2[1] - pos1[1];\n    var rad = Math.atan2(distY, distX);\n    return rad >= 0 ? rad : rad + Math.PI * 2;\n}\nfunction getRotatiion(touches) {\n    return getRad([\n        touches[0].clientX,\n        touches[0].clientY,\n    ], [\n        touches[1].clientX,\n        touches[1].clientY,\n    ]) / Math.PI * 180;\n}\nfunction isMultiTouch(e) {\n    return e.touches && e.touches.length >= 2;\n}\nfunction getEventClients(e) {\n    if (!e) {\n        return [];\n    }\n    if (e.touches) {\n        return getClients(e.touches);\n    }\n    else {\n        return [getClient(e)];\n    }\n}\nfunction isMouseEvent(e) {\n    return e && (e.type.indexOf(\"mouse\") > -1 || \"button\" in e);\n}\nfunction getPosition(clients, prevClients, startClients) {\n    var length = startClients.length;\n    var _a = getAverageClient(clients, length), clientX = _a.clientX, clientY = _a.clientY, originalClientX = _a.originalClientX, originalClientY = _a.originalClientY;\n    var _b = getAverageClient(prevClients, length), prevX = _b.clientX, prevY = _b.clientY;\n    var _c = getAverageClient(startClients, length), startX = _c.clientX, startY = _c.clientY;\n    var deltaX = clientX - prevX;\n    var deltaY = clientY - prevY;\n    var distX = clientX - startX;\n    var distY = clientY - startY;\n    return {\n        clientX: originalClientX,\n        clientY: originalClientY,\n        deltaX: deltaX,\n        deltaY: deltaY,\n        distX: distX,\n        distY: distY,\n    };\n}\nfunction getDist(clients) {\n    return Math.sqrt(Math.pow(clients[0].clientX - clients[1].clientX, 2)\n        + Math.pow(clients[0].clientY - clients[1].clientY, 2));\n}\nfunction getClients(touches) {\n    var length = Math.min(touches.length, 2);\n    var clients = [];\n    for (var i = 0; i < length; ++i) {\n        clients.push(getClient(touches[i]));\n    }\n    return clients;\n}\nfunction getClient(e) {\n    return {\n        clientX: e.clientX,\n        clientY: e.clientY,\n    };\n}\nfunction getAverageClient(clients, length) {\n    if (length === void 0) { length = clients.length; }\n    var sumClient = {\n        clientX: 0,\n        clientY: 0,\n        originalClientX: 0,\n        originalClientY: 0,\n    };\n    var minLength = Math.min(clients.length, length);\n    for (var i = 0; i < minLength; ++i) {\n        var client = clients[i];\n        sumClient.originalClientX += \"originalClientX\" in client ? client.originalClientX : client.clientX;\n        sumClient.originalClientY += \"originalClientY\" in client ? client.originalClientY : client.clientY;\n        sumClient.clientX += client.clientX;\n        sumClient.clientY += client.clientY;\n    }\n    if (!length) {\n        return sumClient;\n    }\n    return {\n        clientX: sumClient.clientX / length,\n        clientY: sumClient.clientY / length,\n        originalClientX: sumClient.originalClientX / length,\n        originalClientY: sumClient.originalClientY / length,\n    };\n}\n\nvar ClientStore = /*#__PURE__*/ (function () {\n    function ClientStore(clients) {\n        this.prevClients = [];\n        this.startClients = [];\n        this.movement = 0;\n        this.length = 0;\n        this.startClients = clients;\n        this.prevClients = clients;\n        this.length = clients.length;\n    }\n    ClientStore.prototype.getAngle = function (clients) {\n        if (clients === void 0) { clients = this.prevClients; }\n        return getRotatiion(clients);\n    };\n    ClientStore.prototype.getRotation = function (clients) {\n        if (clients === void 0) { clients = this.prevClients; }\n        return getRotatiion(clients) - getRotatiion(this.startClients);\n    };\n    ClientStore.prototype.getPosition = function (clients, isAdd) {\n        if (clients === void 0) { clients = this.prevClients; }\n        var position = getPosition(clients || this.prevClients, this.prevClients, this.startClients);\n        var deltaX = position.deltaX, deltaY = position.deltaY;\n        this.movement += Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n        this.prevClients = clients;\n        return position;\n    };\n    ClientStore.prototype.getPositions = function (clients) {\n        if (clients === void 0) { clients = this.prevClients; }\n        var prevClients = this.prevClients;\n        var startClients = this.startClients;\n        var minLength = Math.min(this.length, prevClients.length);\n        var positions = [];\n        for (var i = 0; i < minLength; ++i) {\n            positions[i] = getPosition([clients[i]], [prevClients[i]], [startClients[i]]);\n        }\n        return positions;\n    };\n    ClientStore.prototype.getMovement = function (clients) {\n        var movement = this.movement;\n        if (!clients) {\n            return movement;\n        }\n        var currentClient = getAverageClient(clients, this.length);\n        var prevClient = getAverageClient(this.prevClients, this.length);\n        var deltaX = currentClient.clientX - prevClient.clientX;\n        var deltaY = currentClient.clientY - prevClient.clientY;\n        return Math.sqrt(deltaX * deltaX + deltaY * deltaY) + movement;\n    };\n    ClientStore.prototype.getDistance = function (clients) {\n        if (clients === void 0) { clients = this.prevClients; }\n        return getDist(clients);\n    };\n    ClientStore.prototype.getScale = function (clients) {\n        if (clients === void 0) { clients = this.prevClients; }\n        return getDist(clients) / getDist(this.startClients);\n    };\n    ClientStore.prototype.move = function (deltaX, deltaY) {\n        this.startClients.forEach(function (client) {\n            client.clientX -= deltaX;\n            client.clientY -= deltaY;\n        });\n        this.prevClients.forEach(function (client) {\n            client.clientX -= deltaX;\n            client.clientY -= deltaY;\n        });\n    };\n    return ClientStore;\n}());\n\nvar INPUT_TAGNAMES = [\"textarea\", \"input\"];\n/**\n * You can set up drag, pinch events in any browser.\n */\nvar Gesto = /*#__PURE__*/ (function (_super) {\n    __extends(Gesto, _super);\n    /**\n     *\n     */\n    function Gesto(targets, options) {\n        if (options === void 0) { options = {}; }\n        var _this = _super.call(this) || this;\n        _this.options = {};\n        _this.flag = false;\n        _this.pinchFlag = false;\n        _this.data = {};\n        _this.isDrag = false;\n        _this.isPinch = false;\n        _this.clientStores = [];\n        _this.targets = [];\n        _this.prevTime = 0;\n        _this.doubleFlag = false;\n        _this._useMouse = false;\n        _this._useTouch = false;\n        _this._useDrag = false;\n        _this._dragFlag = false;\n        _this._isTrusted = false;\n        _this._isMouseEvent = false;\n        _this._isSecondaryButton = false;\n        _this._preventMouseEvent = false;\n        _this._prevInputEvent = null;\n        _this._isDragAPI = false;\n        _this._isIdle = true;\n        _this._preventMouseEventId = 0;\n        _this._window = window;\n        _this.onDragStart = function (e, isTrusted) {\n            if (isTrusted === void 0) { isTrusted = true; }\n            if (!_this.flag && e.cancelable === false) {\n                return;\n            }\n            var isDragAPI = e.type.indexOf(\"drag\") >= -1;\n            if (_this.flag && isDragAPI) {\n                return;\n            }\n            _this._isDragAPI = true;\n            var _a = _this.options, container = _a.container, pinchOutside = _a.pinchOutside, preventWheelClick = _a.preventWheelClick, preventRightClick = _a.preventRightClick, preventDefault = _a.preventDefault, checkInput = _a.checkInput, dragFocusedInput = _a.dragFocusedInput, preventClickEventOnDragStart = _a.preventClickEventOnDragStart, preventClickEventOnDrag = _a.preventClickEventOnDrag, preventClickEventByCondition = _a.preventClickEventByCondition;\n            var useTouch = _this._useTouch;\n            var isDragStart = !_this.flag;\n            _this._isSecondaryButton = e.which === 3 || e.button === 2;\n            if ((preventWheelClick && (e.which === 2 || e.button === 1))\n                || (preventRightClick && (e.which === 3 || e.button === 2))) {\n                _this.stop();\n                return false;\n            }\n            if (isDragStart) {\n                var activeElement = _this._window.document.activeElement;\n                var target = e.target;\n                if (target) {\n                    var tagName = target.tagName.toLowerCase();\n                    var hasInput = INPUT_TAGNAMES.indexOf(tagName) > -1;\n                    var hasContentEditable = target.isContentEditable;\n                    if (hasInput || hasContentEditable) {\n                        if (checkInput || (!dragFocusedInput && activeElement === target)) {\n                            // force false or already focused.\n                            return false;\n                        }\n                        // no focus\n                        if (activeElement && (activeElement === target\n                            || (hasContentEditable && activeElement.isContentEditable && activeElement.contains(target)))) {\n                            if (dragFocusedInput) {\n                                target.blur();\n                            }\n                            else {\n                                return false;\n                            }\n                        }\n                    }\n                    else if ((preventDefault || e.type === \"touchstart\") && activeElement) {\n                        var activeTagName = activeElement.tagName.toLowerCase();\n                        if (activeElement.isContentEditable || INPUT_TAGNAMES.indexOf(activeTagName) > -1) {\n                            activeElement.blur();\n                        }\n                    }\n                    if (preventClickEventOnDragStart || preventClickEventOnDrag || preventClickEventByCondition) {\n                        (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(_this._window, \"click\", _this._onClick, true);\n                    }\n                }\n                _this.clientStores = [new ClientStore(getEventClients(e))];\n                _this._isIdle = false;\n                _this.flag = true;\n                _this.isDrag = false;\n                _this._isTrusted = isTrusted;\n                _this._dragFlag = true;\n                _this._prevInputEvent = e;\n                _this.data = {};\n                _this.doubleFlag = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.now)() - _this.prevTime < 200;\n                _this._isMouseEvent = isMouseEvent(e);\n                if (!_this._isMouseEvent && _this._preventMouseEvent) {\n                    _this._allowMouseEvent();\n                }\n                var result = _this._preventMouseEvent || _this.emit(\"dragStart\", __assign(__assign({ data: _this.data, datas: _this.data, inputEvent: e, isMouseEvent: _this._isMouseEvent, isSecondaryButton: _this._isSecondaryButton, isTrusted: isTrusted, isDouble: _this.doubleFlag }, _this.getCurrentStore().getPosition()), { preventDefault: function () {\n                        e.preventDefault();\n                    }, preventDrag: function () {\n                        _this._dragFlag = false;\n                    } }));\n                if (result === false) {\n                    _this.stop();\n                }\n                if (_this._isMouseEvent && _this.flag && preventDefault) {\n                    e.preventDefault();\n                }\n            }\n            if (!_this.flag) {\n                return false;\n            }\n            var timer = 0;\n            if (isDragStart) {\n                _this._attchDragEvent();\n                // wait pinch\n                if (useTouch && pinchOutside) {\n                    timer = setTimeout(function () {\n                        (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(container, \"touchstart\", _this.onDragStart, {\n                            passive: false\n                        });\n                    });\n                }\n            }\n            else if (useTouch && pinchOutside) {\n                // pinch is occured\n                (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(container, \"touchstart\", _this.onDragStart);\n            }\n            if (_this.flag && isMultiTouch(e)) {\n                clearTimeout(timer);\n                if (isDragStart && (e.touches.length !== e.changedTouches.length)) {\n                    return;\n                }\n                if (!_this.pinchFlag) {\n                    _this.onPinchStart(e);\n                }\n            }\n        };\n        _this.onDrag = function (e, isScroll) {\n            if (!_this.flag) {\n                return;\n            }\n            var preventDefault = _this.options.preventDefault;\n            if (!_this._isMouseEvent && preventDefault) {\n                e.preventDefault();\n            }\n            _this._prevInputEvent = e;\n            var clients = getEventClients(e);\n            var result = _this.moveClients(clients, e, false);\n            if (_this._dragFlag) {\n                if (_this.pinchFlag || result.deltaX || result.deltaY) {\n                    var dragResult = _this._preventMouseEvent || _this.emit(\"drag\", __assign(__assign({}, result), { isScroll: !!isScroll, inputEvent: e }));\n                    if (dragResult === false) {\n                        _this.stop();\n                        return;\n                    }\n                }\n                if (_this.pinchFlag) {\n                    _this.onPinch(e, clients);\n                }\n            }\n            _this.getCurrentStore().getPosition(clients, true);\n        };\n        _this.onDragEnd = function (e) {\n            if (!_this.flag) {\n                return;\n            }\n            var _a = _this.options, pinchOutside = _a.pinchOutside, container = _a.container, preventClickEventOnDrag = _a.preventClickEventOnDrag, preventClickEventOnDragStart = _a.preventClickEventOnDragStart, preventClickEventByCondition = _a.preventClickEventByCondition;\n            var isDrag = _this.isDrag;\n            if (preventClickEventOnDrag || preventClickEventOnDragStart || preventClickEventByCondition) {\n                requestAnimationFrame(function () {\n                    _this._allowClickEvent();\n                });\n            }\n            if (!preventClickEventByCondition && !preventClickEventOnDragStart && preventClickEventOnDrag && !isDrag) {\n                _this._allowClickEvent();\n            }\n            if (_this._useTouch && pinchOutside) {\n                (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(container, \"touchstart\", _this.onDragStart);\n            }\n            if (_this.pinchFlag) {\n                _this.onPinchEnd(e);\n            }\n            var clients = (e === null || e === void 0 ? void 0 : e.touches) ? getEventClients(e) : [];\n            var clientsLength = clients.length;\n            if (clientsLength === 0 || !_this.options.keepDragging) {\n                _this.flag = false;\n            }\n            else {\n                _this._addStore(new ClientStore(clients));\n            }\n            var position = _this._getPosition();\n            var currentTime = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.now)();\n            var isDouble = !isDrag && _this.doubleFlag;\n            _this._prevInputEvent = null;\n            _this.prevTime = isDrag || isDouble ? 0 : currentTime;\n            if (!_this.flag) {\n                _this._dettachDragEvent();\n                _this._preventMouseEvent || _this.emit(\"dragEnd\", __assign({ data: _this.data, datas: _this.data, isDouble: isDouble, isDrag: isDrag, isClick: !isDrag, isMouseEvent: _this._isMouseEvent, isSecondaryButton: _this._isSecondaryButton, inputEvent: e, isTrusted: _this._isTrusted }, position));\n                _this.clientStores = [];\n                if (!_this._isMouseEvent) {\n                    _this._preventMouseEvent = true;\n                    // Prevent the problem of touch event and mouse event occurring simultaneously\n                    clearTimeout(_this._preventMouseEventId);\n                    _this._preventMouseEventId = setTimeout(function () {\n                        _this._preventMouseEvent = false;\n                    }, 200);\n                }\n                _this._isIdle = true;\n            }\n        };\n        _this.onBlur = function () {\n            _this.onDragEnd();\n        };\n        _this._allowClickEvent = function () {\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(_this._window, \"click\", _this._onClick, true);\n        };\n        _this._onClick = function (e) {\n            _this._allowClickEvent();\n            _this._allowMouseEvent();\n            var preventClickEventByCondition = _this.options.preventClickEventByCondition;\n            if (preventClickEventByCondition === null || preventClickEventByCondition === void 0 ? void 0 : preventClickEventByCondition(e)) {\n                return;\n            }\n            e.stopPropagation();\n            e.preventDefault();\n        };\n        _this._onContextMenu = function (e) {\n            var options = _this.options;\n            if (!options.preventRightClick) {\n                e.preventDefault();\n            }\n            else {\n                _this.onDragEnd(e);\n            }\n        };\n        _this._passCallback = function () { };\n        var elements = [].concat(targets);\n        var firstTarget = elements[0];\n        _this._window = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.isWindow)(firstTarget) ? firstTarget : (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getWindow)(firstTarget);\n        _this.options = __assign({ checkInput: false, container: firstTarget && !(\"document\" in firstTarget) ? (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getWindow)(firstTarget) : firstTarget, preventRightClick: true, preventWheelClick: true, preventClickEventOnDragStart: false, preventClickEventOnDrag: false, preventClickEventByCondition: null, preventDefault: true, checkWindowBlur: false, keepDragging: false, pinchThreshold: 0, events: [\"touch\", \"mouse\"] }, options);\n        var _a = _this.options, container = _a.container, events = _a.events, checkWindowBlur = _a.checkWindowBlur;\n        _this._useDrag = events.indexOf(\"drag\") > -1;\n        _this._useTouch = events.indexOf(\"touch\") > -1;\n        _this._useMouse = events.indexOf(\"mouse\") > -1;\n        _this.targets = elements;\n        if (_this._useDrag) {\n            elements.forEach(function (el) {\n                (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(el, \"dragstart\", _this.onDragStart);\n            });\n        }\n        if (_this._useMouse) {\n            elements.forEach(function (el) {\n                (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(el, \"mousedown\", _this.onDragStart);\n                (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(el, \"mousemove\", _this._passCallback);\n            });\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(container, \"contextmenu\", _this._onContextMenu);\n        }\n        if (checkWindowBlur) {\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getWindow)(), \"blur\", _this.onBlur);\n        }\n        if (_this._useTouch) {\n            var passive_1 = {\n                passive: false,\n            };\n            elements.forEach(function (el) {\n                (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(el, \"touchstart\", _this.onDragStart, passive_1);\n                (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(el, \"touchmove\", _this._passCallback, passive_1);\n            });\n        }\n        return _this;\n    }\n    /**\n     * Stop Gesto's drag events.\n     */\n    Gesto.prototype.stop = function () {\n        this.isDrag = false;\n        this.data = {};\n        this.clientStores = [];\n        this.pinchFlag = false;\n        this.doubleFlag = false;\n        this.prevTime = 0;\n        this.flag = false;\n        this._isIdle = true;\n        this._allowClickEvent();\n        this._dettachDragEvent();\n        this._isDragAPI = false;\n    };\n    /**\n     * The total moved distance\n     */\n    Gesto.prototype.getMovement = function (clients) {\n        return this.getCurrentStore().getMovement(clients) + this.clientStores.slice(1).reduce(function (prev, cur) {\n            return prev + cur.movement;\n        }, 0);\n    };\n    /**\n     * Whether to drag\n     */\n    Gesto.prototype.isDragging = function () {\n        return this.isDrag;\n    };\n    /**\n     * Whether the operation of gesto is finished and is in idle state\n     */\n    Gesto.prototype.isIdle = function () {\n        return this._isIdle;\n    };\n    /**\n     * Whether to start drag\n     */\n    Gesto.prototype.isFlag = function () {\n        return this.flag;\n    };\n    /**\n     * Whether to start pinch\n     */\n    Gesto.prototype.isPinchFlag = function () {\n        return this.pinchFlag;\n    };\n    /**\n     * Whether to start double click\n     */\n    Gesto.prototype.isDoubleFlag = function () {\n        return this.doubleFlag;\n    };\n    /**\n     * Whether to pinch\n     */\n    Gesto.prototype.isPinching = function () {\n        return this.isPinch;\n    };\n    /**\n     * If a scroll event occurs, it is corrected by the scroll distance.\n     */\n    Gesto.prototype.scrollBy = function (deltaX, deltaY, e, isCallDrag) {\n        if (isCallDrag === void 0) { isCallDrag = true; }\n        if (!this.flag) {\n            return;\n        }\n        this.clientStores[0].move(deltaX, deltaY);\n        isCallDrag && this.onDrag(e, true);\n    };\n    /**\n     * Create a virtual drag event.\n     */\n    Gesto.prototype.move = function (_a, inputEvent) {\n        var deltaX = _a[0], deltaY = _a[1];\n        var store = this.getCurrentStore();\n        var nextClients = store.prevClients;\n        return this.moveClients(nextClients.map(function (_a) {\n            var clientX = _a.clientX, clientY = _a.clientY;\n            return {\n                clientX: clientX + deltaX,\n                clientY: clientY + deltaY,\n                originalClientX: clientX,\n                originalClientY: clientY,\n            };\n        }), inputEvent, true);\n    };\n    /**\n     * The dragStart event is triggered by an external event.\n     */\n    Gesto.prototype.triggerDragStart = function (e) {\n        this.onDragStart(e, false);\n    };\n    /**\n     * Set the event data while dragging.\n     */\n    Gesto.prototype.setEventData = function (data) {\n        var currentData = this.data;\n        for (var name_1 in data) {\n            currentData[name_1] = data[name_1];\n        }\n        return this;\n    };\n    /**\n     * Set the event data while dragging.\n     * Use `setEventData`\n     * @deprecated\n     */\n    Gesto.prototype.setEventDatas = function (data) {\n        return this.setEventData(data);\n    };\n    /**\n     * Get the current event state while dragging.\n     */\n    Gesto.prototype.getCurrentEvent = function (inputEvent) {\n        if (inputEvent === void 0) { inputEvent = this._prevInputEvent; }\n        return __assign(__assign({ data: this.data, datas: this.data }, this._getPosition()), { movement: this.getMovement(), isDrag: this.isDrag, isPinch: this.isPinch, isScroll: false, inputEvent: inputEvent });\n    };\n    /**\n     * Get & Set the event data while dragging.\n     */\n    Gesto.prototype.getEventData = function () {\n        return this.data;\n    };\n    /**\n     * Get & Set the event data while dragging.\n     * Use getEventData method\n     * @depreacated\n     */\n    Gesto.prototype.getEventDatas = function () {\n        return this.data;\n    };\n    /**\n     * Unset Gesto\n     */\n    Gesto.prototype.unset = function () {\n        var _this = this;\n        var targets = this.targets;\n        var container = this.options.container;\n        this.off();\n        (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(this._window, \"blur\", this.onBlur);\n        if (this._useDrag) {\n            targets.forEach(function (el) {\n                (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(el, \"dragstart\", _this.onDragStart);\n            });\n        }\n        if (this._useMouse) {\n            targets.forEach(function (target) {\n                (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(target, \"mousedown\", _this.onDragStart);\n            });\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(container, \"contextmenu\", this._onContextMenu);\n        }\n        if (this._useTouch) {\n            targets.forEach(function (target) {\n                (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(target, \"touchstart\", _this.onDragStart);\n            });\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(container, \"touchstart\", this.onDragStart);\n        }\n        this._prevInputEvent = null;\n        this._allowClickEvent();\n        this._dettachDragEvent();\n    };\n    Gesto.prototype.onPinchStart = function (e) {\n        var _this = this;\n        var pinchThreshold = this.options.pinchThreshold;\n        if (this.isDrag && this.getMovement() > pinchThreshold) {\n            return;\n        }\n        var store = new ClientStore(getEventClients(e));\n        this.pinchFlag = true;\n        this._addStore(store);\n        var result = this.emit(\"pinchStart\", __assign(__assign({ data: this.data, datas: this.data, angle: store.getAngle(), touches: this.getCurrentStore().getPositions() }, store.getPosition()), { inputEvent: e, isTrusted: this._isTrusted, preventDefault: function () {\n                e.preventDefault();\n            }, preventDrag: function () {\n                _this._dragFlag = false;\n            } }));\n        if (result === false) {\n            this.pinchFlag = false;\n        }\n    };\n    Gesto.prototype.onPinch = function (e, clients) {\n        if (!this.flag || !this.pinchFlag || clients.length < 2) {\n            return;\n        }\n        var store = this.getCurrentStore();\n        this.isPinch = true;\n        this.emit(\"pinch\", __assign(__assign({ data: this.data, datas: this.data, movement: this.getMovement(clients), angle: store.getAngle(clients), rotation: store.getRotation(clients), touches: store.getPositions(clients), scale: store.getScale(clients), distance: store.getDistance(clients) }, store.getPosition(clients)), { inputEvent: e, isTrusted: this._isTrusted }));\n    };\n    Gesto.prototype.onPinchEnd = function (e) {\n        if (!this.pinchFlag) {\n            return;\n        }\n        var isPinch = this.isPinch;\n        this.isPinch = false;\n        this.pinchFlag = false;\n        var store = this.getCurrentStore();\n        this.emit(\"pinchEnd\", __assign(__assign({ data: this.data, datas: this.data, isPinch: isPinch, touches: store.getPositions() }, store.getPosition()), { inputEvent: e }));\n    };\n    Gesto.prototype.getCurrentStore = function () {\n        return this.clientStores[0];\n    };\n    Gesto.prototype.moveClients = function (clients, inputEvent, isAdd) {\n        var position = this._getPosition(clients, isAdd);\n        var isPrevDrag = this.isDrag;\n        if (position.deltaX || position.deltaY) {\n            this.isDrag = true;\n        }\n        var isFirstDrag = false;\n        if (!isPrevDrag && this.isDrag) {\n            isFirstDrag = true;\n        }\n        return __assign(__assign({ data: this.data, datas: this.data }, position), { movement: this.getMovement(clients), isDrag: this.isDrag, isPinch: this.isPinch, isScroll: false, isMouseEvent: this._isMouseEvent, isSecondaryButton: this._isSecondaryButton, inputEvent: inputEvent, isTrusted: this._isTrusted, isFirstDrag: isFirstDrag });\n    };\n    Gesto.prototype._addStore = function (store) {\n        this.clientStores.splice(0, 0, store);\n    };\n    Gesto.prototype._getPosition = function (clients, isAdd) {\n        var store = this.getCurrentStore();\n        var position = store.getPosition(clients, isAdd);\n        var _a = this.clientStores.slice(1).reduce(function (prev, cur) {\n            var storePosition = cur.getPosition();\n            prev.distX += storePosition.distX;\n            prev.distY += storePosition.distY;\n            return prev;\n        }, position), distX = _a.distX, distY = _a.distY;\n        return __assign(__assign({}, position), { distX: distX, distY: distY });\n    };\n    Gesto.prototype._attchDragEvent = function () {\n        var win = this._window;\n        var container = this.options.container;\n        var passive = {\n            passive: false\n        };\n        if (this._isDragAPI) {\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(container, \"dragover\", this.onDrag, passive);\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(win, \"dragend\", this.onDragEnd);\n        }\n        if (this._useMouse) {\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(container, \"mousemove\", this.onDrag);\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(win, \"mouseup\", this.onDragEnd);\n        }\n        if (this._useTouch) {\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(container, \"touchmove\", this.onDrag, passive);\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(win, \"touchend\", this.onDragEnd, passive);\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(win, \"touchcancel\", this.onDragEnd, passive);\n        }\n    };\n    Gesto.prototype._dettachDragEvent = function () {\n        var win = this._window;\n        var container = this.options.container;\n        if (this._isDragAPI) {\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(container, \"dragover\", this.onDrag);\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(win, \"dragend\", this.onDragEnd);\n        }\n        if (this._useMouse) {\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(container, \"mousemove\", this.onDrag);\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(win, \"mouseup\", this.onDragEnd);\n        }\n        if (this._useTouch) {\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(container, \"touchstart\", this.onDragStart);\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(container, \"touchmove\", this.onDrag);\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(win, \"touchend\", this.onDragEnd);\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(win, \"touchcancel\", this.onDragEnd);\n        }\n    };\n    Gesto.prototype._allowMouseEvent = function () {\n        this._preventMouseEvent = false;\n        clearTimeout(this._preventMouseEventId);\n    };\n    return Gesto;\n}(_scena_event_emitter__WEBPACK_IMPORTED_MODULE_1__[\"default\"]));\n\n\n//# sourceMappingURL=gesto.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/gesto/dist/gesto.esm.js\n");

/***/ })

};
;