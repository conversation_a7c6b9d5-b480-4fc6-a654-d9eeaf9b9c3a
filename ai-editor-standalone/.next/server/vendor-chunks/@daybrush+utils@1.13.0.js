"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@daybrush+utils@1.13.0";
exports.ids = ["vendor-chunks/@daybrush+utils@1.13.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@daybrush+utils@1.13.0/node_modules/@daybrush/utils/dist/utils.esm.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@daybrush+utils@1.13.0/node_modules/@daybrush/utils/dist/utils.esm.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $: () => (/* binding */ $),\n/* harmony export */   ANIMATION: () => (/* binding */ ANIMATION),\n/* harmony export */   ARRAY: () => (/* binding */ ARRAY),\n/* harmony export */   COLOR_MODELS: () => (/* binding */ COLOR_MODELS),\n/* harmony export */   DEFAULT_UNIT_PRESETS: () => (/* binding */ DEFAULT_UNIT_PRESETS),\n/* harmony export */   FILTER: () => (/* binding */ FILTER),\n/* harmony export */   FUNCTION: () => (/* binding */ FUNCTION),\n/* harmony export */   HSL: () => (/* binding */ HSL),\n/* harmony export */   HSLA: () => (/* binding */ HSLA),\n/* harmony export */   IS_WINDOW: () => (/* binding */ IS_WINDOW),\n/* harmony export */   KEYFRAMES: () => (/* binding */ KEYFRAMES),\n/* harmony export */   NUMBER: () => (/* binding */ NUMBER),\n/* harmony export */   OBJECT: () => (/* binding */ OBJECT),\n/* harmony export */   OPEN_CLOSED_CHARACTERS: () => (/* binding */ OPEN_CLOSED_CHARACTERS),\n/* harmony export */   PROPERTY: () => (/* binding */ PROPERTY),\n/* harmony export */   REVERSE_TINY_NUM: () => (/* binding */ REVERSE_TINY_NUM),\n/* harmony export */   RGB: () => (/* binding */ RGB),\n/* harmony export */   RGBA: () => (/* binding */ RGBA),\n/* harmony export */   STRING: () => (/* binding */ STRING),\n/* harmony export */   TINY_NUM: () => (/* binding */ TINY_NUM),\n/* harmony export */   TRANSFORM: () => (/* binding */ TRANSFORM),\n/* harmony export */   UNDEFINED: () => (/* binding */ UNDEFINED),\n/* harmony export */   addClass: () => (/* binding */ addClass),\n/* harmony export */   addEvent: () => (/* binding */ addEvent),\n/* harmony export */   average: () => (/* binding */ average),\n/* harmony export */   between: () => (/* binding */ between),\n/* harmony export */   calculateBoundSize: () => (/* binding */ calculateBoundSize),\n/* harmony export */   camelize: () => (/* binding */ camelize),\n/* harmony export */   cancelAnimationFrame: () => (/* binding */ cancelAnimationFrame),\n/* harmony export */   checkBoundSize: () => (/* binding */ checkBoundSize),\n/* harmony export */   convertUnitSize: () => (/* binding */ convertUnitSize),\n/* harmony export */   counter: () => (/* binding */ counter),\n/* harmony export */   cutHex: () => (/* binding */ cutHex),\n/* harmony export */   decamelize: () => (/* binding */ decamelize),\n/* harmony export */   deepFlat: () => (/* binding */ deepFlat),\n/* harmony export */   document: () => (/* binding */ doc),\n/* harmony export */   dot: () => (/* binding */ dot),\n/* harmony export */   find: () => (/* binding */ find),\n/* harmony export */   findIndex: () => (/* binding */ findIndex),\n/* harmony export */   findLast: () => (/* binding */ findLast),\n/* harmony export */   findLastIndex: () => (/* binding */ findLastIndex),\n/* harmony export */   flat: () => (/* binding */ flat),\n/* harmony export */   fromCSS: () => (/* binding */ fromCSS),\n/* harmony export */   getCenterPoint: () => (/* binding */ getCenterPoint),\n/* harmony export */   getCrossBrowserProperty: () => (/* binding */ getCrossBrowserProperty),\n/* harmony export */   getDist: () => (/* binding */ getDist),\n/* harmony export */   getDocument: () => (/* binding */ getDocument),\n/* harmony export */   getDocumentBody: () => (/* binding */ getDocumentBody),\n/* harmony export */   getDocumentElement: () => (/* binding */ getDocumentElement),\n/* harmony export */   getEntries: () => (/* binding */ getEntries),\n/* harmony export */   getKeys: () => (/* binding */ getKeys),\n/* harmony export */   getRad: () => (/* binding */ getRad),\n/* harmony export */   getShapeDirection: () => (/* binding */ getShapeDirection),\n/* harmony export */   getValues: () => (/* binding */ getValues),\n/* harmony export */   getWindow: () => (/* binding */ getWindow),\n/* harmony export */   hasClass: () => (/* binding */ hasClass),\n/* harmony export */   hexToRGBA: () => (/* binding */ hexToRGBA),\n/* harmony export */   hslToRGBA: () => (/* binding */ hslToRGBA),\n/* harmony export */   isArray: () => (/* binding */ isArray),\n/* harmony export */   isFunction: () => (/* binding */ isFunction),\n/* harmony export */   isNode: () => (/* binding */ isNode),\n/* harmony export */   isNumber: () => (/* binding */ isNumber),\n/* harmony export */   isObject: () => (/* binding */ isObject),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   isUndefined: () => (/* binding */ isUndefined),\n/* harmony export */   isWindow: () => (/* binding */ isWindow),\n/* harmony export */   now: () => (/* binding */ now),\n/* harmony export */   pushSet: () => (/* binding */ pushSet),\n/* harmony export */   removeClass: () => (/* binding */ removeClass),\n/* harmony export */   removeEvent: () => (/* binding */ removeEvent),\n/* harmony export */   replaceOnce: () => (/* binding */ replaceOnce),\n/* harmony export */   requestAnimationFrame: () => (/* binding */ requestAnimationFrame),\n/* harmony export */   sortOrders: () => (/* binding */ sortOrders),\n/* harmony export */   splitBracket: () => (/* binding */ splitBracket),\n/* harmony export */   splitComma: () => (/* binding */ splitComma),\n/* harmony export */   splitSpace: () => (/* binding */ splitSpace),\n/* harmony export */   splitText: () => (/* binding */ splitText),\n/* harmony export */   splitUnit: () => (/* binding */ splitUnit),\n/* harmony export */   stringToRGBA: () => (/* binding */ stringToRGBA),\n/* harmony export */   sum: () => (/* binding */ sum),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   throttleArray: () => (/* binding */ throttleArray),\n/* harmony export */   toArray: () => (/* binding */ toArray),\n/* harmony export */   toFullHex: () => (/* binding */ toFullHex)\n/* harmony export */ });\n/*\nCopyright (c) 2018 Daybrush\n@name: @daybrush/utils\nlicense: MIT\nauthor: Daybrush\nrepository: https://github.com/daybrush/utils\n@version 1.13.0\n*/\n/**\n* @namespace\n* @name Consts\n*/\n/**\n* get string \"rgb\"\n* @memberof Color\n* @example\nimport {RGB} from \"@daybrush/utils\";\n\nconsole.log(RGB); // \"rgb\"\n*/\nvar RGB = \"rgb\";\n/**\n* get string \"rgba\"\n* @memberof Color\n* @example\nimport {RGBA} from \"@daybrush/utils\";\n\nconsole.log(RGBA); // \"rgba\"\n*/\nvar RGBA = \"rgba\";\n/**\n* get string \"hsl\"\n* @memberof Color\n* @example\nimport {HSL} from \"@daybrush/utils\";\n\nconsole.log(HSL); // \"hsl\"\n*/\nvar HSL = \"hsl\";\n/**\n* get string \"hsla\"\n* @memberof Color\n* @example\nimport {HSLA} from \"@daybrush/utils\";\n\nconsole.log(HSLA); // \"hsla\"\n*/\nvar HSLA = \"hsla\";\n/**\n* gets an array of color models.\n* @memberof Color\n* @example\nimport {COLOR_MODELS} from \"@daybrush/utils\";\n\nconsole.log(COLOR_MODELS); // [\"rgb\", \"rgba\", \"hsl\", \"hsla\"];\n*/\nvar COLOR_MODELS = [RGB, RGBA, HSL, HSLA];\n/**\n* get string \"function\"\n* @memberof Consts\n* @example\nimport {FUNCTION} from \"@daybrush/utils\";\n\nconsole.log(FUNCTION); // \"function\"\n*/\nvar FUNCTION = \"function\";\n/**\n* get string \"property\"\n* @memberof Consts\n* @example\nimport {PROPERTY} from \"@daybrush/utils\";\n\nconsole.log(PROPERTY); // \"property\"\n*/\nvar PROPERTY = \"property\";\n/**\n* get string \"array\"\n* @memberof Consts\n* @example\nimport {ARRAY} from \"@daybrush/utils\";\n\nconsole.log(ARRAY); // \"array\"\n*/\nvar ARRAY = \"array\";\n/**\n* get string \"object\"\n* @memberof Consts\n* @example\nimport {OBJECT} from \"@daybrush/utils\";\n\nconsole.log(OBJECT); // \"object\"\n*/\nvar OBJECT = \"object\";\n/**\n* get string \"string\"\n* @memberof Consts\n* @example\nimport {STRING} from \"@daybrush/utils\";\n\nconsole.log(STRING); // \"string\"\n*/\nvar STRING = \"string\";\n/**\n* get string \"number\"\n* @memberof Consts\n* @example\nimport {NUMBER} from \"@daybrush/utils\";\n\nconsole.log(NUMBER); // \"number\"\n*/\nvar NUMBER = \"number\";\n/**\n* get string \"undefined\"\n* @memberof Consts\n* @example\nimport {UNDEFINED} from \"@daybrush/utils\";\n\nconsole.log(UNDEFINED); // \"undefined\"\n*/\nvar UNDEFINED = \"undefined\";\n/**\n* Check whether the environment is window or node.js.\n* @memberof Consts\n* @example\nimport {IS_WINDOW} from \"@daybrush/utils\";\n\nconsole.log(IS_WINDOW); // false in node.js\nconsole.log(IS_WINDOW); // true in browser\n*/\nvar IS_WINDOW = typeof window !== UNDEFINED;\n/**\n* Check whether the environment is window or node.js.\n* @memberof Consts\n* @name document\n* @example\nimport {IS_WINDOW} from \"@daybrush/utils\";\n\nconsole.log(IS_WINDOW); // false in node.js\nconsole.log(IS_WINDOW); // true in browser\n*/\nvar doc = typeof document !== UNDEFINED && document; // FIXME: this type maybe false\nvar prefixes = [\"webkit\", \"ms\", \"moz\", \"o\"];\n/**\n * @namespace CrossBrowser\n */\n/**\n* Get a CSS property with a vendor prefix that supports cross browser.\n* @function\n* @param {string} property - A CSS property\n* @return {string} CSS property with cross-browser vendor prefix\n* @memberof CrossBrowser\n* @example\nimport {getCrossBrowserProperty} from \"@daybrush/utils\";\n\nconsole.log(getCrossBrowserProperty(\"transform\")); // \"transform\", \"-ms-transform\", \"-webkit-transform\"\nconsole.log(getCrossBrowserProperty(\"filter\")); // \"filter\", \"-webkit-filter\"\n*/\nvar getCrossBrowserProperty = /*#__PURE__*/function (property) {\n  if (!doc) {\n    return \"\";\n  }\n  var styles = (doc.body || doc.documentElement).style;\n  var length = prefixes.length;\n  if (typeof styles[property] !== UNDEFINED) {\n    return property;\n  }\n  for (var i = 0; i < length; ++i) {\n    var name = \"-\" + prefixes[i] + \"-\" + property;\n    if (typeof styles[name] !== UNDEFINED) {\n      return name;\n    }\n  }\n  return \"\";\n};\n/**\n* get string \"transfrom\" with the vendor prefix.\n* @memberof CrossBrowser\n* @example\nimport {TRANSFORM} from \"@daybrush/utils\";\n\nconsole.log(TRANSFORM); // \"transform\", \"-ms-transform\", \"-webkit-transform\"\n*/\nvar TRANSFORM = /*#__PURE__*/getCrossBrowserProperty(\"transform\");\n/**\n* get string \"filter\" with the vendor prefix.\n* @memberof CrossBrowser\n* @example\nimport {FILTER} from \"@daybrush/utils\";\n\nconsole.log(FILTER); // \"filter\", \"-ms-filter\", \"-webkit-filter\"\n*/\nvar FILTER = /*#__PURE__*/getCrossBrowserProperty(\"filter\");\n/**\n* get string \"animation\" with the vendor prefix.\n* @memberof CrossBrowser\n* @example\nimport {ANIMATION} from \"@daybrush/utils\";\n\nconsole.log(ANIMATION); // \"animation\", \"-ms-animation\", \"-webkit-animation\"\n*/\nvar ANIMATION = /*#__PURE__*/getCrossBrowserProperty(\"animation\");\n/**\n* get string \"keyframes\" with the vendor prefix.\n* @memberof CrossBrowser\n* @example\nimport {KEYFRAMES} from \"@daybrush/utils\";\n\nconsole.log(KEYFRAMES); // \"keyframes\", \"-ms-keyframes\", \"-webkit-keyframes\"\n*/\nvar KEYFRAMES = /*#__PURE__*/ANIMATION.replace(\"animation\", \"keyframes\");\nvar OPEN_CLOSED_CHARACTERS = [{\n  open: \"(\",\n  close: \")\"\n}, {\n  open: \"\\\"\",\n  close: \"\\\"\"\n}, {\n  open: \"'\",\n  close: \"'\"\n}, {\n  open: \"\\\\\\\"\",\n  close: \"\\\\\\\"\"\n}, {\n  open: \"\\\\'\",\n  close: \"\\\\'\"\n}];\nvar TINY_NUM = 0.0000001;\nvar REVERSE_TINY_NUM = 1 / TINY_NUM;\nvar DEFAULT_UNIT_PRESETS = {\n  \"cm\": function (pos) {\n    return pos * 96 / 2.54;\n  },\n  \"mm\": function (pos) {\n    return pos * 96 / 254;\n  },\n  \"in\": function (pos) {\n    return pos * 96;\n  },\n  \"pt\": function (pos) {\n    return pos * 96 / 72;\n  },\n  \"pc\": function (pos) {\n    return pos * 96 / 6;\n  },\n  \"%\": function (pos, size) {\n    return pos * size / 100;\n  },\n  \"vw\": function (pos, size) {\n    if (size === void 0) {\n      size = window.innerWidth;\n    }\n    return pos / 100 * size;\n  },\n  \"vh\": function (pos, size) {\n    if (size === void 0) {\n      size = window.innerHeight;\n    }\n    return pos / 100 * size;\n  },\n  \"vmax\": function (pos, size) {\n    if (size === void 0) {\n      size = Math.max(window.innerWidth, window.innerHeight);\n    }\n    return pos / 100 * size;\n  },\n  \"vmin\": function (pos, size) {\n    if (size === void 0) {\n      size = Math.min(window.innerWidth, window.innerHeight);\n    }\n    return pos / 100 * size;\n  }\n};\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\nfunction __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++) for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++) r[k] = a[j];\n  return r;\n}\n\n/**\n* @namespace\n* @name Utils\n*/\n/**\n * Returns the inner product of two numbers(`a1`, `a2`) by two criteria(`b1`, `b2`).\n * @memberof Utils\n * @param - The first number\n * @param - The second number\n * @param - The first number to base on the inner product\n * @param - The second number to base on the inner product\n * @return - Returns the inner product\nimport { dot } from \"@daybrush/utils\";\n\nconsole.log(dot(0, 15, 2, 3)); // 6\nconsole.log(dot(5, 15, 2, 3)); // 9\nconsole.log(dot(5, 15, 1, 1)); // 10\n */\nfunction dot(a1, a2, b1, b2) {\n  return (a1 * b2 + a2 * b1) / (b1 + b2);\n}\n/**\n* Check the type that the value is undefined.\n* @memberof Utils\n* @param {string} value - Value to check the type\n* @return {boolean} true if the type is correct, false otherwise\n* @example\nimport {isUndefined} from \"@daybrush/utils\";\n\nconsole.log(isUndefined(undefined)); // true\nconsole.log(isUndefined(\"\")); // false\nconsole.log(isUndefined(1)); // false\nconsole.log(isUndefined(null)); // false\n*/\nfunction isUndefined(value) {\n  return typeof value === UNDEFINED;\n}\n/**\n* Check the type that the value is object.\n* @memberof Utils\n* @param {string} value - Value to check the type\n* @return {} true if the type is correct, false otherwise\n* @example\nimport {isObject} from \"@daybrush/utils\";\n\nconsole.log(isObject({})); // true\nconsole.log(isObject(undefined)); // false\nconsole.log(isObject(\"\")); // false\nconsole.log(isObject(null)); // false\n*/\nfunction isObject(value) {\n  return value && typeof value === OBJECT;\n}\n/**\n* Check the type that the value is isArray.\n* @memberof Utils\n* @param {string} value - Value to check the type\n* @return {} true if the type is correct, false otherwise\n* @example\nimport {isArray} from \"@daybrush/utils\";\n\nconsole.log(isArray([])); // true\nconsole.log(isArray({})); // false\nconsole.log(isArray(undefined)); // false\nconsole.log(isArray(null)); // false\n*/\nfunction isArray(value) {\n  return Array.isArray(value);\n}\n/**\n* Check the type that the value is string.\n* @memberof Utils\n* @param {string} value - Value to check the type\n* @return {} true if the type is correct, false otherwise\n* @example\nimport {isString} from \"@daybrush/utils\";\n\nconsole.log(isString(\"1234\")); // true\nconsole.log(isString(undefined)); // false\nconsole.log(isString(1)); // false\nconsole.log(isString(null)); // false\n*/\nfunction isString(value) {\n  return typeof value === STRING;\n}\nfunction isNumber(value) {\n  return typeof value === NUMBER;\n}\n/**\n* Check the type that the value is function.\n* @memberof Utils\n* @param {string} value - Value to check the type\n* @return {} true if the type is correct, false otherwise\n* @example\nimport {isFunction} from \"@daybrush/utils\";\n\nconsole.log(isFunction(function a() {})); // true\nconsole.log(isFunction(() => {})); // true\nconsole.log(isFunction(\"1234\")); // false\nconsole.log(isFunction(1)); // false\nconsole.log(isFunction(null)); // false\n*/\nfunction isFunction(value) {\n  return typeof value === FUNCTION;\n}\nfunction isEqualSeparator(character, separator) {\n  var isCharacterSpace = character === \"\" || character == \" \";\n  var isSeparatorSpace = separator === \"\" || separator == \" \";\n  return isSeparatorSpace && isCharacterSpace || character === separator;\n}\nfunction findOpen(openCharacter, texts, index, length, openCloseCharacters) {\n  var isIgnore = findIgnore(openCharacter, texts, index);\n  if (!isIgnore) {\n    return findClose(openCharacter, texts, index + 1, length, openCloseCharacters);\n  }\n  return index;\n}\nfunction findIgnore(character, texts, index) {\n  if (!character.ignore) {\n    return null;\n  }\n  var otherText = texts.slice(Math.max(index - 3, 0), index + 3).join(\"\");\n  return new RegExp(character.ignore).exec(otherText);\n}\nfunction findClose(closeCharacter, texts, index, length, openCloseCharacters) {\n  var _loop_1 = function (i) {\n    var character = texts[i].trim();\n    if (character === closeCharacter.close && !findIgnore(closeCharacter, texts, i)) {\n      return {\n        value: i\n      };\n    }\n    var nextIndex = i;\n    // re open\n    var openCharacter = find(openCloseCharacters, function (_a) {\n      var open = _a.open;\n      return open === character;\n    });\n    if (openCharacter) {\n      nextIndex = findOpen(openCharacter, texts, i, length, openCloseCharacters);\n    }\n    if (nextIndex === -1) {\n      return out_i_1 = i, \"break\";\n    }\n    i = nextIndex;\n    out_i_1 = i;\n  };\n  var out_i_1;\n  for (var i = index; i < length; ++i) {\n    var state_1 = _loop_1(i);\n    i = out_i_1;\n    if (typeof state_1 === \"object\") return state_1.value;\n    if (state_1 === \"break\") break;\n  }\n  return -1;\n}\nfunction splitText(text, splitOptions) {\n  var _a = isString(splitOptions) ? {\n      separator: splitOptions\n    } : splitOptions,\n    _b = _a.separator,\n    separator = _b === void 0 ? \",\" : _b,\n    isSeparateFirst = _a.isSeparateFirst,\n    isSeparateOnlyOpenClose = _a.isSeparateOnlyOpenClose,\n    _c = _a.isSeparateOpenClose,\n    isSeparateOpenClose = _c === void 0 ? isSeparateOnlyOpenClose : _c,\n    _d = _a.openCloseCharacters,\n    openCloseCharacters = _d === void 0 ? OPEN_CLOSED_CHARACTERS : _d;\n  var openClosedText = openCloseCharacters.map(function (_a) {\n    var open = _a.open,\n      close = _a.close;\n    if (open === close) {\n      return open;\n    }\n    return open + \"|\" + close;\n  }).join(\"|\");\n  var regexText = \"(\\\\s*\" + separator + \"\\\\s*|\" + openClosedText + \"|\\\\s+)\";\n  var regex = new RegExp(regexText, \"g\");\n  var texts = text.split(regex).filter(function (chr) {\n    return chr && chr !== \"undefined\";\n  });\n  var length = texts.length;\n  var values = [];\n  var tempValues = [];\n  function resetTemp() {\n    if (tempValues.length) {\n      values.push(tempValues.join(\"\"));\n      tempValues = [];\n      return true;\n    }\n    return false;\n  }\n  var _loop_2 = function (i) {\n    var character = texts[i].trim();\n    var nextIndex = i;\n    var openCharacter = find(openCloseCharacters, function (_a) {\n      var open = _a.open;\n      return open === character;\n    });\n    var closeCharacter = find(openCloseCharacters, function (_a) {\n      var close = _a.close;\n      return close === character;\n    });\n    if (openCharacter) {\n      nextIndex = findOpen(openCharacter, texts, i, length, openCloseCharacters);\n      if (nextIndex !== -1 && isSeparateOpenClose) {\n        if (resetTemp() && isSeparateFirst) {\n          return out_i_2 = i, \"break\";\n        }\n        values.push(texts.slice(i, nextIndex + 1).join(\"\"));\n        i = nextIndex;\n        if (isSeparateFirst) {\n          return out_i_2 = i, \"break\";\n        }\n        return out_i_2 = i, \"continue\";\n      }\n    } else if (closeCharacter && !findIgnore(closeCharacter, texts, i)) {\n      var nextOpenCloseCharacters = __spreadArrays(openCloseCharacters);\n      nextOpenCloseCharacters.splice(openCloseCharacters.indexOf(closeCharacter), 1);\n      return {\n        value: splitText(text, {\n          separator: separator,\n          isSeparateFirst: isSeparateFirst,\n          isSeparateOnlyOpenClose: isSeparateOnlyOpenClose,\n          isSeparateOpenClose: isSeparateOpenClose,\n          openCloseCharacters: nextOpenCloseCharacters\n        })\n      };\n    } else if (isEqualSeparator(character, separator) && !isSeparateOnlyOpenClose) {\n      resetTemp();\n      if (isSeparateFirst) {\n        return out_i_2 = i, \"break\";\n      }\n      return out_i_2 = i, \"continue\";\n    }\n    if (nextIndex === -1) {\n      nextIndex = length - 1;\n    }\n    tempValues.push(texts.slice(i, nextIndex + 1).join(\"\"));\n    i = nextIndex;\n    out_i_2 = i;\n  };\n  var out_i_2;\n  for (var i = 0; i < length; ++i) {\n    var state_2 = _loop_2(i);\n    i = out_i_2;\n    if (typeof state_2 === \"object\") return state_2.value;\n    if (state_2 === \"break\") break;\n  }\n  if (tempValues.length) {\n    values.push(tempValues.join(\"\"));\n  }\n  return values;\n}\n/**\n* divide text by space.\n* @memberof Utils\n* @param {string} text - text to divide\n* @return {Array} divided texts\n* @example\nimport {spliceSpace} from \"@daybrush/utils\";\n\nconsole.log(splitSpace(\"a b c d e f g\"));\n// [\"a\", \"b\", \"c\", \"d\", \"e\", \"f\", \"g\"]\nconsole.log(splitSpace(\"'a,b' c 'd,e' f g\"));\n// [\"'a,b'\", \"c\", \"'d,e'\", \"f\", \"g\"]\n*/\nfunction splitSpace(text) {\n  // divide comma(space)\n  return splitText(text, \"\");\n}\n/**\n* divide text by comma.\n* @memberof Utils\n* @param {string} text - text to divide\n* @return {Array} divided texts\n* @example\nimport {splitComma} from \"@daybrush/utils\";\n\nconsole.log(splitComma(\"a,b,c,d,e,f,g\"));\n// [\"a\", \"b\", \"c\", \"d\", \"e\", \"f\", \"g\"]\nconsole.log(splitComma(\"'a,b',c,'d,e',f,g\"));\n// [\"'a,b'\", \"c\", \"'d,e'\", \"f\", \"g\"]\n*/\nfunction splitComma(text) {\n  // divide comma(,)\n  // \"[^\"]*\"|'[^']*'\n  return splitText(text, \",\");\n}\n/**\n* divide text by bracket \"(\", \")\".\n* @memberof Utils\n* @param {string} text - text to divide\n* @return {object} divided texts\n* @example\nimport {splitBracket} from \"@daybrush/utils\";\n\nconsole.log(splitBracket(\"a(1, 2)\"));\n// {prefix: \"a\", value: \"1, 2\", suffix: \"\"}\nconsole.log(splitBracket(\"a(1, 2)b\"));\n// {prefix: \"a\", value: \"1, 2\", suffix: \"b\"}\n*/\nfunction splitBracket(text) {\n  var matches = /([^(]*)\\(([\\s\\S]*)\\)([\\s\\S]*)/g.exec(text);\n  if (!matches || matches.length < 4) {\n    return {};\n  } else {\n    return {\n      prefix: matches[1],\n      value: matches[2],\n      suffix: matches[3]\n    };\n  }\n}\n/**\n* divide text by number and unit.\n* @memberof Utils\n* @param {string} text - text to divide\n* @return {} divided texts\n* @example\nimport {splitUnit} from \"@daybrush/utils\";\n\nconsole.log(splitUnit(\"10px\"));\n// {prefix: \"\", value: 10, unit: \"px\"}\nconsole.log(splitUnit(\"-10px\"));\n// {prefix: \"\", value: -10, unit: \"px\"}\nconsole.log(splitUnit(\"a10%\"));\n// {prefix: \"a\", value: 10, unit: \"%\"}\n*/\nfunction splitUnit(text) {\n  var matches = /^([^\\d|e|\\-|\\+]*)((?:\\d|\\.|-|e-|e\\+)+)(\\S*)$/g.exec(text);\n  if (!matches) {\n    return {\n      prefix: \"\",\n      unit: \"\",\n      value: NaN\n    };\n  }\n  var prefix = matches[1];\n  var value = matches[2];\n  var unit = matches[3];\n  return {\n    prefix: prefix,\n    unit: unit,\n    value: parseFloat(value)\n  };\n}\n/**\n* transform strings to camel-case\n* @memberof Utils\n* @param {String} text - string\n* @return {String} camel-case string\n* @example\nimport {camelize} from \"@daybrush/utils\";\n\nconsole.log(camelize(\"transform-origin\")); // transformOrigin\nconsole.log(camelize(\"abcd_efg\")); // abcdEfg\nconsole.log(camelize(\"abcd efg\")); // abcdEfg\n*/\nfunction camelize(str) {\n  return str.replace(/[\\s-_]+([^\\s-_])/g, function (all, letter) {\n    return letter.toUpperCase();\n  });\n}\n/**\n* transform a camelized string into a lowercased string.\n* @memberof Utils\n* @param {string} text - a camel-cased string\n* @param {string} [separator=\"-\"] - a separator\n* @return {string}  a lowercased string\n* @example\nimport {decamelize} from \"@daybrush/utils\";\n\nconsole.log(decamelize(\"transformOrigin\")); // transform-origin\nconsole.log(decamelize(\"abcdEfg\", \"_\")); // abcd_efg\n*/\nfunction decamelize(str, separator) {\n  if (separator === void 0) {\n    separator = \"-\";\n  }\n  return str.replace(/([a-z])([A-Z])/g, function (all, letter, letter2) {\n    return \"\" + letter + separator + letter2.toLowerCase();\n  });\n}\n/**\n* transforms something in an array into an array.\n* @memberof Utils\n* @param - Array form\n* @return an array\n* @example\nimport {toArray} from \"@daybrush/utils\";\n\nconst arr1 = toArray(document.querySelectorAll(\".a\")); // Element[]\nconst arr2 = toArray(document.querySelectorAll<HTMLElement>(\".a\")); // HTMLElement[]\n*/\nfunction toArray(value) {\n  return [].slice.call(value);\n}\n/**\n* Date.now() method\n* @memberof CrossBrowser\n* @return {number} milliseconds\n* @example\nimport {now} from \"@daybrush/utils\";\n\nconsole.log(now()); // 12121324241(milliseconds)\n*/\nfunction now() {\n  return Date.now ? Date.now() : new Date().getTime();\n}\n/**\n* Returns the index of the first element in the array that satisfies the provided testing function.\n* @function\n* @memberof CrossBrowser\n* @param - The array `findIndex` was called upon.\n* @param - A function to execute on each value in the array until the function returns true, indicating that the satisfying element was found.\n* @param - Returns defaultIndex if not found by the function.\n* @example\nimport { findIndex } from \"@daybrush/utils\";\n\nfindIndex([{a: 1}, {a: 2}, {a: 3}, {a: 4}], ({ a }) => a === 2); // 1\n*/\nfunction findIndex(arr, callback, defaultIndex) {\n  if (defaultIndex === void 0) {\n    defaultIndex = -1;\n  }\n  var length = arr.length;\n  for (var i = 0; i < length; ++i) {\n    if (callback(arr[i], i, arr)) {\n      return i;\n    }\n  }\n  return defaultIndex;\n}\n/**\n* Returns the reverse direction index of the first element in the array that satisfies the provided testing function.\n* @function\n* @memberof CrossBrowser\n* @param - The array `findLastIndex` was called upon.\n* @param - A function to execute on each value in the array until the function returns true, indicating that the satisfying element was found.\n* @param - Returns defaultIndex if not found by the function.\n* @example\nimport { findLastIndex } from \"@daybrush/utils\";\n\nfindLastIndex([{a: 1}, {a: 2}, {a: 3}, {a: 4}], ({ a }) => a === 2); // 1\n*/\nfunction findLastIndex(arr, callback, defaultIndex) {\n  if (defaultIndex === void 0) {\n    defaultIndex = -1;\n  }\n  var length = arr.length;\n  for (var i = length - 1; i >= 0; --i) {\n    if (callback(arr[i], i, arr)) {\n      return i;\n    }\n  }\n  return defaultIndex;\n}\n/**\n* Returns the value of the reverse direction element in the array that satisfies the provided testing function.\n* @function\n* @memberof CrossBrowser\n* @param - The array `findLast` was called upon.\n* @param - A function to execute on each value in the array,\n* @param - Returns defalutValue if not found by the function.\n* @example\nimport { find } from \"@daybrush/utils\";\n\nfind([{a: 1}, {a: 2}, {a: 3}, {a: 4}], ({ a }) => a === 2); // {a: 2}\n*/\nfunction findLast(arr, callback, defalutValue) {\n  var index = findLastIndex(arr, callback);\n  return index > -1 ? arr[index] : defalutValue;\n}\n/**\n* Returns the value of the first element in the array that satisfies the provided testing function.\n* @function\n* @memberof CrossBrowser\n* @param - The array `find` was called upon.\n* @param - A function to execute on each value in the array,\n* @param - Returns defalutValue if not found by the function.\n* @example\nimport { find } from \"@daybrush/utils\";\n\nfind([{a: 1}, {a: 2}, {a: 3}, {a: 4}], ({ a }) => a === 2); // {a: 2}\n*/\nfunction find(arr, callback, defalutValue) {\n  var index = findIndex(arr, callback);\n  return index > -1 ? arr[index] : defalutValue;\n}\n/**\n* window.requestAnimationFrame() method with cross browser.\n* @function\n* @memberof CrossBrowser\n* @param {FrameRequestCallback} callback - The function to call when it's time to update your animation for the next repaint.\n* @return {number} id\n* @example\nimport {requestAnimationFrame} from \"@daybrush/utils\";\n\nrequestAnimationFrame((timestamp) => {\n  console.log(timestamp);\n});\n*/\nvar requestAnimationFrame = /*#__PURE__*/function () {\n  var firstTime = now();\n  var raf = IS_WINDOW && (window.requestAnimationFrame || window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || window.msRequestAnimationFrame);\n  return raf ? raf.bind(window) : function (callback) {\n    var currTime = now();\n    var id = setTimeout(function () {\n      callback(currTime - firstTime);\n    }, 1000 / 60);\n    return id;\n  };\n}();\n/**\n* window.cancelAnimationFrame() method with cross browser.\n* @function\n* @memberof CrossBrowser\n* @param {number} handle - the id obtained through requestAnimationFrame method\n* @return {void}\n* @example\nimport { requestAnimationFrame, cancelAnimationFrame } from \"@daybrush/utils\";\n\nconst id = requestAnimationFrame((timestamp) => {\n  console.log(timestamp);\n});\n\ncancelAnimationFrame(id);\n*/\nvar cancelAnimationFrame = /*#__PURE__*/function () {\n  var caf = IS_WINDOW && (window.cancelAnimationFrame || window.webkitCancelAnimationFrame || window.mozCancelAnimationFrame || window.msCancelAnimationFrame);\n  return caf ? caf.bind(window) : function (handle) {\n    clearTimeout(handle);\n  };\n}();\n/**\n* @function\n* @memberof Utils\n*/\nfunction getKeys(obj) {\n  return Object.keys(obj);\n}\n/**\n* @function\n* @memberof Utils\n*/\nfunction getValues(obj) {\n  var keys = getKeys(obj);\n  return keys.map(function (key) {\n    return obj[key];\n  });\n}\n/**\n* @function\n* @memberof Utils\n*/\nfunction getEntries(obj) {\n  var keys = getKeys(obj);\n  return keys.map(function (key) {\n    return [key, obj[key]];\n  });\n}\n/**\n* @function\n* @memberof Utils\n*/\nfunction sortOrders(keys, orders) {\n  if (orders === void 0) {\n    orders = [];\n  }\n  keys.sort(function (a, b) {\n    var index1 = orders.indexOf(a);\n    var index2 = orders.indexOf(b);\n    if (index2 === -1 && index1 === -1) {\n      return 0;\n    }\n    if (index1 === -1) {\n      return 1;\n    }\n    if (index2 === -1) {\n      return -1;\n    }\n    return index1 - index2;\n  });\n}\n/**\n* convert unit size to px size\n* @function\n* @memberof Utils\n*/\nfunction convertUnitSize(pos, size) {\n  var _a = splitUnit(pos),\n    value = _a.value,\n    unit = _a.unit;\n  if (isObject(size)) {\n    var sizeFunction = size[unit];\n    if (sizeFunction) {\n      if (isFunction(sizeFunction)) {\n        return sizeFunction(value);\n      } else if (DEFAULT_UNIT_PRESETS[unit]) {\n        return DEFAULT_UNIT_PRESETS[unit](value, sizeFunction);\n      }\n    }\n  } else if (unit === \"%\") {\n    return value * size / 100;\n  }\n  if (DEFAULT_UNIT_PRESETS[unit]) {\n    return DEFAULT_UNIT_PRESETS[unit](value);\n  }\n  return value;\n}\n/**\n* calculate between min, max\n* @function\n* @memberof Utils\n*/\nfunction between(value, min, max) {\n  return Math.max(min, Math.min(value, max));\n}\nfunction checkBoundSize(targetSize, compareSize, isMax, ratio) {\n  if (ratio === void 0) {\n    ratio = targetSize[0] / targetSize[1];\n  }\n  return [[throttle(compareSize[0], TINY_NUM), throttle(compareSize[0] / ratio, TINY_NUM)], [throttle(compareSize[1] * ratio, TINY_NUM), throttle(compareSize[1], TINY_NUM)]].filter(function (size) {\n    return size.every(function (value, i) {\n      var defaultSize = compareSize[i];\n      var throttledSize = throttle(defaultSize, TINY_NUM);\n      return isMax ? value <= defaultSize || value <= throttledSize : value >= defaultSize || value >= throttledSize;\n    });\n  })[0] || targetSize;\n}\n/**\n* calculate bound size\n* @function\n* @memberof Utils\n*/\nfunction calculateBoundSize(size, minSize, maxSize, keepRatio) {\n  if (!keepRatio) {\n    return size.map(function (value, i) {\n      return between(value, minSize[i], maxSize[i]);\n    });\n  }\n  var width = size[0],\n    height = size[1];\n  var ratio = keepRatio === true ? width / height : keepRatio;\n  // width : height = minWidth : minHeight;\n  var _a = checkBoundSize(size, minSize, false, ratio),\n    minWidth = _a[0],\n    minHeight = _a[1];\n  var _b = checkBoundSize(size, maxSize, true, ratio),\n    maxWidth = _b[0],\n    maxHeight = _b[1];\n  if (width < minWidth || height < minHeight) {\n    width = minWidth;\n    height = minHeight;\n  } else if (width > maxWidth || height > maxHeight) {\n    width = maxWidth;\n    height = maxHeight;\n  }\n  return [width, height];\n}\n/**\n* Add all the numbers.\n* @function\n* @memberof Utils\n*/\nfunction sum(nums) {\n  var length = nums.length;\n  var total = 0;\n  for (var i = length - 1; i >= 0; --i) {\n    total += nums[i];\n  }\n  return total;\n}\n/**\n* Average all numbers.\n* @function\n* @memberof Utils\n*/\nfunction average(nums) {\n  var length = nums.length;\n  var total = 0;\n  for (var i = length - 1; i >= 0; --i) {\n    total += nums[i];\n  }\n  return length ? total / length : 0;\n}\n/**\n* Get the angle of two points. (0 <= rad < 359)\n* @function\n* @memberof Utils\n*/\nfunction getRad(pos1, pos2) {\n  var distX = pos2[0] - pos1[0];\n  var distY = pos2[1] - pos1[1];\n  var rad = Math.atan2(distY, distX);\n  return rad >= 0 ? rad : rad + Math.PI * 2;\n}\n/**\n* Get the average point of all points.\n* @function\n* @memberof Utils\n*/\nfunction getCenterPoint(points) {\n  return [0, 1].map(function (i) {\n    return average(points.map(function (pos) {\n      return pos[i];\n    }));\n  });\n}\n/**\n* Gets the direction of the shape.\n* @function\n* @memberof Utils\n*/\nfunction getShapeDirection(points) {\n  var center = getCenterPoint(points);\n  var pos1Rad = getRad(center, points[0]);\n  var pos2Rad = getRad(center, points[1]);\n  return pos1Rad < pos2Rad && pos2Rad - pos1Rad < Math.PI || pos1Rad > pos2Rad && pos2Rad - pos1Rad < -Math.PI ? 1 : -1;\n}\n/**\n* Get the distance between two points.\n* @function\n* @memberof Utils\n*/\nfunction getDist(a, b) {\n  return Math.sqrt(Math.pow((b ? b[0] : 0) - a[0], 2) + Math.pow((b ? b[1] : 0) - a[1], 2));\n}\n/**\n* throttle number depending on the unit.\n* @function\n* @memberof Utils\n*/\nfunction throttle(num, unit) {\n  if (!unit) {\n    return num;\n  }\n  var reverseUnit = 1 / unit;\n  return Math.round(num / unit) / reverseUnit;\n}\n/**\n* throttle number array depending on the unit.\n* @function\n* @memberof Utils\n*/\nfunction throttleArray(nums, unit) {\n  nums.forEach(function (_, i) {\n    nums[i] = throttle(nums[i], unit);\n  });\n  return nums;\n}\n/**\n* @function\n* @memberof Utils\n*/\nfunction counter(num) {\n  var nums = [];\n  for (var i = 0; i < num; ++i) {\n    nums.push(i);\n  }\n  return nums;\n}\n/**\n* @function\n* @memberof Utils\n*/\nfunction replaceOnce(text, fromText, toText) {\n  var isOnce = false;\n  return text.replace(fromText, function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    if (isOnce) {\n      return args[0];\n    }\n    isOnce = true;\n    return isString(toText) ? toText : toText.apply(void 0, args);\n  });\n}\n/**\n* @function\n* @memberof Utils\n*/\nfunction flat(arr) {\n  return arr.reduce(function (prev, cur) {\n    return prev.concat(cur);\n  }, []);\n}\n/**\n* @function\n* @memberof Utils\n*/\nfunction deepFlat(arr) {\n  return arr.reduce(function (prev, cur) {\n    if (isArray(cur)) {\n      prev.push.apply(prev, deepFlat(cur));\n    } else {\n      prev.push(cur);\n    }\n    return prev;\n  }, []);\n}\n/**\n * @function\n * @memberof Utils\n */\nfunction pushSet(elements, element) {\n  if (elements.indexOf(element) === -1) {\n    elements.push(element);\n  }\n}\n\n/**\n* @namespace\n* @name Color\n*/\n/**\n* Remove the # from the hex color.\n* @memberof Color\n* @param {} hex - hex color\n* @return {} hex color\n* @example\nimport {cutHex} from \"@daybrush/utils\";\n\nconsole.log(cutHex(\"#000000\")) // \"000000\"\n*/\nfunction cutHex(hex) {\n  return hex.replace(\"#\", \"\");\n}\n/**\n* convert hex color to rgb color.\n* @memberof Color\n* @param {} hex - hex color\n* @return {} rgb color\n* @example\nimport {hexToRGBA} from \"@daybrush/utils\";\n\nconsole.log(hexToRGBA(\"#00000005\"));\n// [0, 0, 0, 1]\nconsole.log(hexToRGBA(\"#201045\"));\n// [32, 16, 69, 1]\n*/\nfunction hexToRGBA(hex) {\n  var h = cutHex(hex);\n  var r = parseInt(h.substring(0, 2), 16);\n  var g = parseInt(h.substring(2, 4), 16);\n  var b = parseInt(h.substring(4, 6), 16);\n  var a = parseInt(h.substring(6, 8), 16) / 255;\n  if (isNaN(a)) {\n    a = 1;\n  }\n  return [r, g, b, a];\n}\n/**\n* convert 3(or 4)-digit hex color to 6(or 8)-digit hex color.\n* @memberof Color\n* @param {} hex - 3(or 4)-digit hex color\n* @return {} 6(or 8)-digit hex color\n* @example\nimport {toFullHex} from \"@daybrush/utils\";\n\nconsole.log(toFullHex(\"#123\")); // \"#112233\"\nconsole.log(toFullHex(\"#123a\")); // \"#112233aa\"\n*/\nfunction toFullHex(h) {\n  var r = h.charAt(1);\n  var g = h.charAt(2);\n  var b = h.charAt(3);\n  var a = h.charAt(4);\n  var arr = [\"#\", r, r, g, g, b, b, a, a];\n  return arr.join(\"\");\n}\n/**\n* convert hsl color to rgba color.\n* @memberof Color\n* @param {} hsl - hsl color(hue: 0 ~ 360, saturation: 0 ~ 1, lightness: 0 ~ 1, alpha: 0 ~ 1)\n* @return {} rgba color\n* @example\nimport {hslToRGBA} from \"@daybrush/utils\";\n\nconsole.log(hslToRGBA([150, 0.5, 0.4]));\n// [51, 153, 102, 1]\n*/\nfunction hslToRGBA(hsl) {\n  var _a;\n  var h = hsl[0];\n  var s = hsl[1];\n  var l = hsl[2];\n  if (h < 0) {\n    h += Math.floor((Math.abs(h) + 360) / 360) * 360;\n  }\n  h %= 360;\n  var c = (1 - Math.abs(2 * l - 1)) * s;\n  var x = c * (1 - Math.abs(h / 60 % 2 - 1));\n  var m = l - c / 2;\n  var rgb;\n  if (h < 60) {\n    rgb = [c, x, 0];\n  } else if (h < 120) {\n    rgb = [x, c, 0];\n  } else if (h < 180) {\n    rgb = [0, c, x];\n  } else if (h < 240) {\n    rgb = [0, x, c];\n  } else if (h < 300) {\n    rgb = [x, 0, c];\n  } else if (h < 360) {\n    rgb = [c, 0, x];\n  } else {\n    rgb = [0, 0, 0];\n  }\n  return [Math.round((rgb[0] + m) * 255), Math.round((rgb[1] + m) * 255), Math.round((rgb[2] + m) * 255), (_a = hsl[3]) !== null && _a !== void 0 ? _a : 1];\n}\n/**\n* convert string to rgba color.\n* @memberof Color\n* @param {} - 3-hex(#000), 4-hex(#0000) 6-hex(#000000), 8-hex(#00000000) or RGB(A), or HSL(A)\n* @return {} rgba color\n* @example\nimport {stringToRGBA} from \"@daybrush/utils\";\n\nconsole.log(stringToRGBA(\"#000000\")); // [0, 0, 0, 1]\nconsole.log(stringToRGBA(\"rgb(100, 100, 100)\")); // [100, 100, 100, 1]\nconsole.log(stringToRGBA(\"hsl(150, 0.5, 0.4)\")); // [51, 153, 102, 1]\n*/\nfunction stringToRGBA(color) {\n  if (color.charAt(0) === \"#\") {\n    if (color.length === 4 || color.length === 5) {\n      return hexToRGBA(toFullHex(color));\n    } else {\n      return hexToRGBA(color);\n    }\n  } else if (color.indexOf(\"(\") !== -1) {\n    // in bracket.\n    var _a = splitBracket(color),\n      prefix = _a.prefix,\n      value = _a.value;\n    if (!prefix || !value) {\n      return undefined;\n    }\n    var arr = splitComma(value);\n    var colorArr = [0, 0, 0, 1];\n    var length = arr.length;\n    switch (prefix) {\n      case RGB:\n      case RGBA:\n        for (var i = 0; i < length; ++i) {\n          colorArr[i] = parseFloat(arr[i]);\n        }\n        return colorArr;\n      case HSL:\n      case HSLA:\n        for (var i = 0; i < length; ++i) {\n          if (arr[i].indexOf(\"%\") !== -1) {\n            colorArr[i] = parseFloat(arr[i]) / 100;\n          } else {\n            colorArr[i] = parseFloat(arr[i]);\n          }\n        }\n        // hsl, hsla to rgba\n        return hslToRGBA(colorArr);\n    }\n  }\n  return undefined;\n}\n\n/**\n * Returns all element descendants of node that\n * match selectors.\n */\n/**\n * Checks if the specified class value exists in the element's class attribute.\n * @memberof DOM\n * @param - A DOMString containing one or more selectors to match\n * @param - If multi is true, a DOMString containing one or more selectors to match against.\n * @example\nimport {$} from \"@daybrush/utils\";\n\nconsole.log($(\"div\")); // div element\nconsole.log($(\"div\", true)); // [div, div] elements\n*/\nfunction $(selectors, multi) {\n  if (!doc) {\n    return multi ? [] : null;\n  }\n  return multi ? doc.querySelectorAll(selectors) : doc.querySelector(selectors);\n}\n/**\n* Checks if the specified class value exists in the element's class attribute.\n* @memberof DOM\n* @param element - target\n* @param className - the class name to search\n* @return {boolean} return false if the class is not found.\n* @example\nimport {hasClass} from \"@daybrush/utils\";\n\nconsole.log(hasClass(element, \"start\")); // true or false\n*/\nfunction hasClass(element, className) {\n  if (element.classList) {\n    return element.classList.contains(className);\n  }\n  return !!element.className.match(new RegExp(\"(\\\\s|^)\" + className + \"(\\\\s|$)\"));\n}\n/**\n* Add the specified class value. If these classe already exist in the element's class attribute they are ignored.\n* @memberof DOM\n* @param element - target\n* @param className - the class name to add\n* @example\nimport {addClass} from \"@daybrush/utils\";\n\naddClass(element, \"start\");\n*/\nfunction addClass(element, className) {\n  if (element.classList) {\n    element.classList.add(className);\n  } else {\n    element.className += \" \" + className;\n  }\n}\n/**\n* Removes the specified class value.\n* @memberof DOM\n* @param element - target\n* @param className - the class name to remove\n* @example\nimport {removeClass} from \"@daybrush/utils\";\n\nremoveClass(element, \"start\");\n*/\nfunction removeClass(element, className) {\n  if (element.classList) {\n    element.classList.remove(className);\n  } else {\n    var reg = new RegExp(\"(\\\\s|^)\" + className + \"(\\\\s|$)\");\n    element.className = element.className.replace(reg, \" \");\n  }\n}\n/**\n* Gets the CSS properties from the element.\n* @memberof DOM\n* @param elements - elements\n* @param properites - the CSS properties\n* @return returns CSS properties and values.\n* @example\nimport {fromCSS} from \"@daybrush/utils\";\n\nconsole.log(fromCSS(element, [\"left\", \"opacity\", \"top\"])); // {\"left\": \"10px\", \"opacity\": 1, \"top\": \"10px\"}\n*/\nfunction fromCSS(elements, properties) {\n  if (!elements || !properties || !properties.length) {\n    return {};\n  }\n  var element;\n  if (elements instanceof Element) {\n    element = elements;\n  } else if (elements.length) {\n    element = elements[0];\n  } else {\n    return {};\n  }\n  var cssObject = {};\n  var styles = getWindow(element).getComputedStyle(element);\n  var length = properties.length;\n  for (var i = 0; i < length; ++i) {\n    cssObject[properties[i]] = styles[properties[i]];\n  }\n  return cssObject;\n}\n/**\n* Sets up a function that will be called whenever the specified event is delivered to the target\n* @memberof DOM\n* @param - event target\n* @param - A case-sensitive string representing the event type to listen for.\n* @param - The object which receives a notification (an object that implements the Event interface) when an event of the specified type occurs\n* @param - An options object that specifies characteristics about the event listener.\n* @example\nimport {addEvent} from \"@daybrush/utils\";\n\naddEvent(el, \"click\", e => {\n  console.log(e);\n});\n*/\nfunction addEvent(el, type, listener, options) {\n  el.addEventListener(type, listener, options);\n}\n/**\n* removes from the EventTarget an event listener previously registered with EventTarget.addEventListener()\n* @memberof DOM\n* @param - event target\n* @param - A case-sensitive string representing the event type to listen for.\n* @param - The EventListener function of the event handler to remove from the event target.\n* @param - An options object that specifies characteristics about the event listener.\n* @example\nimport {addEvent, removeEvent} from \"@daybrush/utils\";\nconst listener = e => {\n  console.log(e);\n};\naddEvent(el, \"click\", listener);\nremoveEvent(el, \"click\", listener);\n*/\nfunction removeEvent(el, type, listener, options) {\n  el.removeEventListener(type, listener, options);\n}\nfunction getDocument(el) {\n  return (el === null || el === void 0 ? void 0 : el.ownerDocument) || doc;\n}\nfunction getDocumentElement(el) {\n  return getDocument(el).documentElement;\n}\nfunction getDocumentBody(el) {\n  return getDocument(el).body;\n}\nfunction getWindow(el) {\n  var _a;\n  return ((_a = el === null || el === void 0 ? void 0 : el.ownerDocument) === null || _a === void 0 ? void 0 : _a.defaultView) || window;\n}\nfunction isWindow(val) {\n  return val && \"postMessage\" in val && \"blur\" in val && \"self\" in val;\n}\nfunction isNode(el) {\n  return isObject(el) && el.nodeName && el.nodeType && \"ownerDocument\" in el;\n}\n\n\n//# sourceMappingURL=utils.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@daybrush+utils@1.13.0/node_modules/@daybrush/utils/dist/utils.esm.js\n");

/***/ })

};
;