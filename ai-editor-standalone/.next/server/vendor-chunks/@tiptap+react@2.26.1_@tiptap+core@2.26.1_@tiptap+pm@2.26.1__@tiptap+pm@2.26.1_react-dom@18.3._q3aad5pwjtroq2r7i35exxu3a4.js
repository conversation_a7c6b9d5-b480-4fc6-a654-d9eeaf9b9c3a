"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+react@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1_react-dom@18.3._q3aad5pwjtroq2r7i35exxu3a4";
exports.ids = ["vendor-chunks/@tiptap+react@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1_react-dom@18.3._q3aad5pwjtroq2r7i35exxu3a4"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+react@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1_react-dom@18.3._q3aad5pwjtroq2r7i35exxu3a4/node_modules/@tiptap/react/dist/index.js":
/*!**********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+react@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1_react-dom@18.3._q3aad5pwjtroq2r7i35exxu3a4/node_modules/@tiptap/react/dist/index.js ***!
  \**********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BubbleMenu: () => (/* binding */ BubbleMenu),\n/* harmony export */   CommandManager: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.CommandManager),\n/* harmony export */   Editor: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.Editor),\n/* harmony export */   EditorConsumer: () => (/* binding */ EditorConsumer),\n/* harmony export */   EditorContent: () => (/* binding */ EditorContent),\n/* harmony export */   EditorContext: () => (/* binding */ EditorContext),\n/* harmony export */   EditorProvider: () => (/* binding */ EditorProvider),\n/* harmony export */   Extension: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.Extension),\n/* harmony export */   FloatingMenu: () => (/* binding */ FloatingMenu),\n/* harmony export */   InputRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.InputRule),\n/* harmony export */   Mark: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.Mark),\n/* harmony export */   Node: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.Node),\n/* harmony export */   NodePos: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.NodePos),\n/* harmony export */   NodeView: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.NodeView),\n/* harmony export */   NodeViewContent: () => (/* binding */ NodeViewContent),\n/* harmony export */   NodeViewWrapper: () => (/* binding */ NodeViewWrapper),\n/* harmony export */   PasteRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.PasteRule),\n/* harmony export */   PureEditorContent: () => (/* binding */ PureEditorContent),\n/* harmony export */   ReactNodeView: () => (/* binding */ ReactNodeView),\n/* harmony export */   ReactNodeViewContext: () => (/* binding */ ReactNodeViewContext),\n/* harmony export */   ReactNodeViewRenderer: () => (/* binding */ ReactNodeViewRenderer),\n/* harmony export */   ReactRenderer: () => (/* binding */ ReactRenderer),\n/* harmony export */   Tracker: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.Tracker),\n/* harmony export */   callOrReturn: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.callOrReturn),\n/* harmony export */   canInsertNode: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.canInsertNode),\n/* harmony export */   combineTransactionSteps: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.combineTransactionSteps),\n/* harmony export */   createChainableState: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.createChainableState),\n/* harmony export */   createDocument: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.createDocument),\n/* harmony export */   createNodeFromContent: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.createNodeFromContent),\n/* harmony export */   createStyleTag: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.createStyleTag),\n/* harmony export */   defaultBlockAt: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.defaultBlockAt),\n/* harmony export */   deleteProps: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.deleteProps),\n/* harmony export */   elementFromString: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.elementFromString),\n/* harmony export */   escapeForRegEx: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.escapeForRegEx),\n/* harmony export */   extensions: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.extensions),\n/* harmony export */   findChildren: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.findChildren),\n/* harmony export */   findChildrenInRange: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.findChildrenInRange),\n/* harmony export */   findDuplicates: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.findDuplicates),\n/* harmony export */   findParentNode: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.findParentNode),\n/* harmony export */   findParentNodeClosestToPos: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.findParentNodeClosestToPos),\n/* harmony export */   fromString: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.fromString),\n/* harmony export */   generateHTML: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.generateHTML),\n/* harmony export */   generateJSON: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.generateJSON),\n/* harmony export */   generateText: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.generateText),\n/* harmony export */   getAttributes: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getAttributes),\n/* harmony export */   getAttributesFromExtensions: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getAttributesFromExtensions),\n/* harmony export */   getChangedRanges: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getChangedRanges),\n/* harmony export */   getDebugJSON: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getDebugJSON),\n/* harmony export */   getExtensionField: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getExtensionField),\n/* harmony export */   getHTMLFromFragment: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getHTMLFromFragment),\n/* harmony export */   getMarkAttributes: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getMarkAttributes),\n/* harmony export */   getMarkRange: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getMarkRange),\n/* harmony export */   getMarkType: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getMarkType),\n/* harmony export */   getMarksBetween: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getMarksBetween),\n/* harmony export */   getNodeAtPosition: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getNodeAtPosition),\n/* harmony export */   getNodeAttributes: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getNodeAttributes),\n/* harmony export */   getNodeType: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getNodeType),\n/* harmony export */   getRenderedAttributes: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getRenderedAttributes),\n/* harmony export */   getSchema: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getSchema),\n/* harmony export */   getSchemaByResolvedExtensions: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getSchemaByResolvedExtensions),\n/* harmony export */   getSchemaTypeByName: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getSchemaTypeByName),\n/* harmony export */   getSchemaTypeNameByName: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getSchemaTypeNameByName),\n/* harmony export */   getSplittedAttributes: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getSplittedAttributes),\n/* harmony export */   getText: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getText),\n/* harmony export */   getTextBetween: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getTextBetween),\n/* harmony export */   getTextContentFromNodes: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getTextContentFromNodes),\n/* harmony export */   getTextSerializersFromSchema: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getTextSerializersFromSchema),\n/* harmony export */   injectExtensionAttributesToParseRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.injectExtensionAttributesToParseRule),\n/* harmony export */   inputRulesPlugin: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.inputRulesPlugin),\n/* harmony export */   isActive: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isActive),\n/* harmony export */   isAtEndOfNode: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isAtEndOfNode),\n/* harmony export */   isAtStartOfNode: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isAtStartOfNode),\n/* harmony export */   isEmptyObject: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isEmptyObject),\n/* harmony export */   isExtensionRulesEnabled: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isExtensionRulesEnabled),\n/* harmony export */   isFunction: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isFunction),\n/* harmony export */   isList: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isList),\n/* harmony export */   isMacOS: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isMacOS),\n/* harmony export */   isMarkActive: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isMarkActive),\n/* harmony export */   isNodeActive: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isNodeActive),\n/* harmony export */   isNodeEmpty: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isNodeEmpty),\n/* harmony export */   isNodeSelection: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isNodeSelection),\n/* harmony export */   isNumber: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isNumber),\n/* harmony export */   isPlainObject: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isPlainObject),\n/* harmony export */   isRegExp: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isRegExp),\n/* harmony export */   isString: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isString),\n/* harmony export */   isTextSelection: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isTextSelection),\n/* harmony export */   isiOS: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isiOS),\n/* harmony export */   markInputRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.markInputRule),\n/* harmony export */   markPasteRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.markPasteRule),\n/* harmony export */   mergeAttributes: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.mergeAttributes),\n/* harmony export */   mergeDeep: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.mergeDeep),\n/* harmony export */   minMax: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.minMax),\n/* harmony export */   nodeInputRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.nodeInputRule),\n/* harmony export */   nodePasteRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.nodePasteRule),\n/* harmony export */   objectIncludes: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.objectIncludes),\n/* harmony export */   pasteRulesPlugin: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.pasteRulesPlugin),\n/* harmony export */   posToDOMRect: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.posToDOMRect),\n/* harmony export */   removeDuplicates: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.removeDuplicates),\n/* harmony export */   resolveFocusPosition: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.resolveFocusPosition),\n/* harmony export */   rewriteUnknownContent: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.rewriteUnknownContent),\n/* harmony export */   selectionToInsertionEnd: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.selectionToInsertionEnd),\n/* harmony export */   splitExtensions: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.splitExtensions),\n/* harmony export */   textInputRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.textInputRule),\n/* harmony export */   textPasteRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.textPasteRule),\n/* harmony export */   textblockTypeInputRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.textblockTypeInputRule),\n/* harmony export */   useCurrentEditor: () => (/* binding */ useCurrentEditor),\n/* harmony export */   useEditor: () => (/* binding */ useEditor),\n/* harmony export */   useEditorState: () => (/* binding */ useEditorState),\n/* harmony export */   useReactNodeView: () => (/* binding */ useReactNodeView),\n/* harmony export */   wrappingInputRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.wrappingInputRule)\n/* harmony export */ });\n/* harmony import */ var _tiptap_extension_bubble_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tiptap/extension-bubble-menu */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-bubble-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-bubble-menu/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_extension_floating_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tiptap/extension-floating-menu */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-floating-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-floating-menu/dist/index.js\");\n\n\n\n\n\n\n\nfunction getDefaultExportFromCjs (x) {\n\treturn x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;\n}\n\nvar shim = {exports: {}};\n\nvar useSyncExternalStoreShim_production_min = {};\n\n/**\n * @license React\n * use-sync-external-store-shim.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar hasRequiredUseSyncExternalStoreShim_production_min;\n\nfunction requireUseSyncExternalStoreShim_production_min () {\n\tif (hasRequiredUseSyncExternalStoreShim_production_min) return useSyncExternalStoreShim_production_min;\n\thasRequiredUseSyncExternalStoreShim_production_min = 1;\nvar e=react__WEBPACK_IMPORTED_MODULE_0__;function h(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var k=\"function\"===typeof Object.is?Object.is:h,l=e.useState,m=e.useEffect,n=e.useLayoutEffect,p=e.useDebugValue;function q(a,b){var d=b(),f=l({inst:{value:d,getSnapshot:b}}),c=f[0].inst,g=f[1];n(function(){c.value=d;c.getSnapshot=b;r(c)&&g({inst:c});},[a,d,b]);m(function(){r(c)&&g({inst:c});return a(function(){r(c)&&g({inst:c});})},[a]);p(d);return d}\n\tfunction r(a){var b=a.getSnapshot;a=a.value;try{var d=b();return !k(a,d)}catch(f){return !0}}function t(a,b){return b()}var u=\"undefined\"===typeof window||\"undefined\"===typeof window.document||\"undefined\"===typeof window.document.createElement?t:q;useSyncExternalStoreShim_production_min.useSyncExternalStore=void 0!==e.useSyncExternalStore?e.useSyncExternalStore:u;\n\treturn useSyncExternalStoreShim_production_min;\n}\n\nvar useSyncExternalStoreShim_development = {};\n\n/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar hasRequiredUseSyncExternalStoreShim_development;\n\nfunction requireUseSyncExternalStoreShim_development () {\n\tif (hasRequiredUseSyncExternalStoreShim_development) return useSyncExternalStoreShim_development;\n\thasRequiredUseSyncExternalStoreShim_development = 1;\n\n\tif (true) {\n\t  (function() {\n\n\t/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n\tif (\n\t  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n\t  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n\t    'function'\n\t) {\n\t  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n\t}\n\t          var React$1 = react__WEBPACK_IMPORTED_MODULE_0__;\n\n\tvar ReactSharedInternals = React$1.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\n\tfunction error(format) {\n\t  {\n\t    {\n\t      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n\t        args[_key2 - 1] = arguments[_key2];\n\t      }\n\n\t      printWarning('error', format, args);\n\t    }\n\t  }\n\t}\n\n\tfunction printWarning(level, format, args) {\n\t  // When changing this logic, you might want to also\n\t  // update consoleWithStackDev.www.js as well.\n\t  {\n\t    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n\t    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n\t    if (stack !== '') {\n\t      format += '%s';\n\t      args = args.concat([stack]);\n\t    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n\t    var argsWithFormat = args.map(function (item) {\n\t      return String(item);\n\t    }); // Careful: RN currently depends on this prefix\n\n\t    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n\t    // breaks IE9: https://github.com/facebook/react/issues/13610\n\t    // eslint-disable-next-line react-internal/no-production-logging\n\n\t    Function.prototype.apply.call(console[level], console, argsWithFormat);\n\t  }\n\t}\n\n\t/**\n\t * inlined Object.is polyfill to avoid requiring consumers ship their own\n\t * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n\t */\n\tfunction is(x, y) {\n\t  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n\t  ;\n\t}\n\n\tvar objectIs = typeof Object.is === 'function' ? Object.is : is;\n\n\t// dispatch for CommonJS interop named imports.\n\n\tvar useState = React$1.useState,\n\t    useEffect = React$1.useEffect,\n\t    useLayoutEffect = React$1.useLayoutEffect,\n\t    useDebugValue = React$1.useDebugValue;\n\tvar didWarnOld18Alpha = false;\n\tvar didWarnUncachedGetSnapshot = false; // Disclaimer: This shim breaks many of the rules of React, and only works\n\t// because of a very particular set of implementation details and assumptions\n\t// -- change any one of them and it will break. The most important assumption\n\t// is that updates are always synchronous, because concurrent rendering is\n\t// only available in versions of React that also have a built-in\n\t// useSyncExternalStore API. And we only use this shim when the built-in API\n\t// does not exist.\n\t//\n\t// Do not assume that the clever hacks used by this hook also work in general.\n\t// The point of this shim is to replace the need for hacks by other libraries.\n\n\tfunction useSyncExternalStore(subscribe, getSnapshot, // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n\t// React do not expose a way to check if we're hydrating. So users of the shim\n\t// will need to track that themselves and return the correct value\n\t// from `getSnapshot`.\n\tgetServerSnapshot) {\n\t  {\n\t    if (!didWarnOld18Alpha) {\n\t      if (React$1.startTransition !== undefined) {\n\t        didWarnOld18Alpha = true;\n\n\t        error('You are using an outdated, pre-release alpha of React 18 that ' + 'does not support useSyncExternalStore. The ' + 'use-sync-external-store shim will not work correctly. Upgrade ' + 'to a newer pre-release.');\n\t      }\n\t    }\n\t  } // Read the current snapshot from the store on every render. Again, this\n\t  // breaks the rules of React, and only works here because of specific\n\t  // implementation details, most importantly that updates are\n\t  // always synchronous.\n\n\n\t  var value = getSnapshot();\n\n\t  {\n\t    if (!didWarnUncachedGetSnapshot) {\n\t      var cachedValue = getSnapshot();\n\n\t      if (!objectIs(value, cachedValue)) {\n\t        error('The result of getSnapshot should be cached to avoid an infinite loop');\n\n\t        didWarnUncachedGetSnapshot = true;\n\t      }\n\t    }\n\t  } // Because updates are synchronous, we don't queue them. Instead we force a\n\t  // re-render whenever the subscribed state changes by updating an some\n\t  // arbitrary useState hook. Then, during render, we call getSnapshot to read\n\t  // the current value.\n\t  //\n\t  // Because we don't actually use the state returned by the useState hook, we\n\t  // can save a bit of memory by storing other stuff in that slot.\n\t  //\n\t  // To implement the early bailout, we need to track some things on a mutable\n\t  // object. Usually, we would put that in a useRef hook, but we can stash it in\n\t  // our useState hook instead.\n\t  //\n\t  // To force a re-render, we call forceUpdate({inst}). That works because the\n\t  // new object always fails an equality check.\n\n\n\t  var _useState = useState({\n\t    inst: {\n\t      value: value,\n\t      getSnapshot: getSnapshot\n\t    }\n\t  }),\n\t      inst = _useState[0].inst,\n\t      forceUpdate = _useState[1]; // Track the latest getSnapshot function with a ref. This needs to be updated\n\t  // in the layout phase so we can access it during the tearing check that\n\t  // happens on subscribe.\n\n\n\t  useLayoutEffect(function () {\n\t    inst.value = value;\n\t    inst.getSnapshot = getSnapshot; // Whenever getSnapshot or subscribe changes, we need to check in the\n\t    // commit phase if there was an interleaved mutation. In concurrent mode\n\t    // this can happen all the time, but even in synchronous mode, an earlier\n\t    // effect may have mutated the store.\n\n\t    if (checkIfSnapshotChanged(inst)) {\n\t      // Force a re-render.\n\t      forceUpdate({\n\t        inst: inst\n\t      });\n\t    }\n\t  }, [subscribe, value, getSnapshot]);\n\t  useEffect(function () {\n\t    // Check for changes right before subscribing. Subsequent changes will be\n\t    // detected in the subscription handler.\n\t    if (checkIfSnapshotChanged(inst)) {\n\t      // Force a re-render.\n\t      forceUpdate({\n\t        inst: inst\n\t      });\n\t    }\n\n\t    var handleStoreChange = function () {\n\t      // TODO: Because there is no cross-renderer API for batching updates, it's\n\t      // up to the consumer of this library to wrap their subscription event\n\t      // with unstable_batchedUpdates. Should we try to detect when this isn't\n\t      // the case and print a warning in development?\n\t      // The store changed. Check if the snapshot changed since the last time we\n\t      // read from the store.\n\t      if (checkIfSnapshotChanged(inst)) {\n\t        // Force a re-render.\n\t        forceUpdate({\n\t          inst: inst\n\t        });\n\t      }\n\t    }; // Subscribe to the store and return a clean-up function.\n\n\n\t    return subscribe(handleStoreChange);\n\t  }, [subscribe]);\n\t  useDebugValue(value);\n\t  return value;\n\t}\n\n\tfunction checkIfSnapshotChanged(inst) {\n\t  var latestGetSnapshot = inst.getSnapshot;\n\t  var prevValue = inst.value;\n\n\t  try {\n\t    var nextValue = latestGetSnapshot();\n\t    return !objectIs(prevValue, nextValue);\n\t  } catch (error) {\n\t    return true;\n\t  }\n\t}\n\n\tfunction useSyncExternalStore$1(subscribe, getSnapshot, getServerSnapshot) {\n\t  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n\t  // React do not expose a way to check if we're hydrating. So users of the shim\n\t  // will need to track that themselves and return the correct value\n\t  // from `getSnapshot`.\n\t  return getSnapshot();\n\t}\n\n\tvar canUseDOM = !!(typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined');\n\n\tvar isServerEnvironment = !canUseDOM;\n\n\tvar shim = isServerEnvironment ? useSyncExternalStore$1 : useSyncExternalStore;\n\tvar useSyncExternalStore$2 = React$1.useSyncExternalStore !== undefined ? React$1.useSyncExternalStore : shim;\n\n\tuseSyncExternalStoreShim_development.useSyncExternalStore = useSyncExternalStore$2;\n\t          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n\tif (\n\t  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n\t  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n\t    'function'\n\t) {\n\t  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n\t}\n\t        \n\t  })();\n\t}\n\treturn useSyncExternalStoreShim_development;\n}\n\nif (false) {} else {\n  shim.exports = requireUseSyncExternalStoreShim_development();\n}\n\nvar shimExports = shim.exports;\n\nconst mergeRefs = (...refs) => {\n    return (node) => {\n        refs.forEach(ref => {\n            if (typeof ref === 'function') {\n                ref(node);\n            }\n            else if (ref) {\n                ref.current = node;\n            }\n        });\n    };\n};\n/**\n * This component renders all of the editor's node views.\n */\nconst Portals = ({ contentComponent, }) => {\n    // For performance reasons, we render the node view portals on state changes only\n    const renderers = shimExports.useSyncExternalStore(contentComponent.subscribe, contentComponent.getSnapshot, contentComponent.getServerSnapshot);\n    // This allows us to directly render the portals without any additional wrapper\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, Object.values(renderers)));\n};\nfunction getInstance() {\n    const subscribers = new Set();\n    let renderers = {};\n    return {\n        /**\n         * Subscribe to the editor instance's changes.\n         */\n        subscribe(callback) {\n            subscribers.add(callback);\n            return () => {\n                subscribers.delete(callback);\n            };\n        },\n        getSnapshot() {\n            return renderers;\n        },\n        getServerSnapshot() {\n            return renderers;\n        },\n        /**\n         * Adds a new NodeView Renderer to the editor.\n         */\n        setRenderer(id, renderer) {\n            renderers = {\n                ...renderers,\n                [id]: react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(renderer.reactElement, renderer.element, id),\n            };\n            subscribers.forEach(subscriber => subscriber());\n        },\n        /**\n         * Removes a NodeView Renderer from the editor.\n         */\n        removeRenderer(id) {\n            const nextRenderers = { ...renderers };\n            delete nextRenderers[id];\n            renderers = nextRenderers;\n            subscribers.forEach(subscriber => subscriber());\n        },\n    };\n}\nclass PureEditorContent extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n    constructor(props) {\n        var _a;\n        super(props);\n        this.editorContentRef = react__WEBPACK_IMPORTED_MODULE_0__.createRef();\n        this.initialized = false;\n        this.state = {\n            hasContentComponentInitialized: Boolean((_a = props.editor) === null || _a === void 0 ? void 0 : _a.contentComponent),\n        };\n    }\n    componentDidMount() {\n        this.init();\n    }\n    componentDidUpdate() {\n        this.init();\n    }\n    init() {\n        const editor = this.props.editor;\n        if (editor && !editor.isDestroyed && editor.options.element) {\n            if (editor.contentComponent) {\n                return;\n            }\n            const element = this.editorContentRef.current;\n            element.append(...editor.options.element.childNodes);\n            editor.setOptions({\n                element,\n            });\n            editor.contentComponent = getInstance();\n            // Has the content component been initialized?\n            if (!this.state.hasContentComponentInitialized) {\n                // Subscribe to the content component\n                this.unsubscribeToContentComponent = editor.contentComponent.subscribe(() => {\n                    this.setState(prevState => {\n                        if (!prevState.hasContentComponentInitialized) {\n                            return {\n                                hasContentComponentInitialized: true,\n                            };\n                        }\n                        return prevState;\n                    });\n                    // Unsubscribe to previous content component\n                    if (this.unsubscribeToContentComponent) {\n                        this.unsubscribeToContentComponent();\n                    }\n                });\n            }\n            editor.createNodeViews();\n            this.initialized = true;\n        }\n    }\n    componentWillUnmount() {\n        const editor = this.props.editor;\n        if (!editor) {\n            return;\n        }\n        this.initialized = false;\n        if (!editor.isDestroyed) {\n            editor.view.setProps({\n                nodeViews: {},\n            });\n        }\n        if (this.unsubscribeToContentComponent) {\n            this.unsubscribeToContentComponent();\n        }\n        editor.contentComponent = null;\n        if (!editor.options.element.firstChild) {\n            return;\n        }\n        const newElement = document.createElement('div');\n        newElement.append(...editor.options.element.childNodes);\n        editor.setOptions({\n            element: newElement,\n        });\n    }\n    render() {\n        const { editor, innerRef, ...rest } = this.props;\n        return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null,\n            react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: mergeRefs(innerRef, this.editorContentRef), ...rest }),\n            (editor === null || editor === void 0 ? void 0 : editor.contentComponent) && react__WEBPACK_IMPORTED_MODULE_0__.createElement(Portals, { contentComponent: editor.contentComponent })));\n    }\n}\n// EditorContent should be re-created whenever the Editor instance changes\nconst EditorContentWithKey = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => {\n    const key = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n        return Math.floor(Math.random() * 0xffffffff).toString();\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [props.editor]);\n    // Can't use JSX here because it conflicts with the type definition of Vue's JSX, so use createElement\n    return react__WEBPACK_IMPORTED_MODULE_0__.createElement(PureEditorContent, {\n        key,\n        innerRef: ref,\n        ...props,\n    });\n});\nconst EditorContent = react__WEBPACK_IMPORTED_MODULE_0__.memo(EditorContentWithKey);\n\nvar react = function equal(a, b) {\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n\n    if ((a instanceof Map) && (b instanceof Map)) {\n      if (a.size !== b.size) return false;\n      for (i of a.entries())\n        if (!b.has(i[0])) return false;\n      for (i of a.entries())\n        if (!equal(i[1], b.get(i[0]))) return false;\n      return true;\n    }\n\n    if ((a instanceof Set) && (b instanceof Set)) {\n      if (a.size !== b.size) return false;\n      for (i of a.entries())\n        if (!b.has(i[0])) return false;\n      return true;\n    }\n\n    if (ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (a[i] !== b[i]) return false;\n      return true;\n    }\n\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n\n      if (key === '_owner' && a.$$typeof) {\n        // React-specific: avoid traversing React elements' _owner.\n        //  _owner contains circular references\n        // and is not needed when comparing the actual elements (and not their owners)\n        continue;\n      }\n\n      if (!equal(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a!==a && b!==b;\n};\n\nvar deepEqual = /*@__PURE__*/getDefaultExportFromCjs(react);\n\nvar withSelector = {exports: {}};\n\nvar withSelector_production_min = {};\n\n/**\n * @license React\n * use-sync-external-store-shim/with-selector.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar hasRequiredWithSelector_production_min;\n\nfunction requireWithSelector_production_min () {\n\tif (hasRequiredWithSelector_production_min) return withSelector_production_min;\n\thasRequiredWithSelector_production_min = 1;\nvar h=react__WEBPACK_IMPORTED_MODULE_0__,n=shimExports;function p(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var q=\"function\"===typeof Object.is?Object.is:p,r=n.useSyncExternalStore,t=h.useRef,u=h.useEffect,v=h.useMemo,w=h.useDebugValue;\n\twithSelector_production_min.useSyncExternalStoreWithSelector=function(a,b,e,l,g){var c=t(null);if(null===c.current){var f={hasValue:!1,value:null};c.current=f;}else f=c.current;c=v(function(){function a(a){if(!c){c=!0;d=a;a=l(a);if(void 0!==g&&f.hasValue){var b=f.value;if(g(b,a))return k=b}return k=a}b=k;if(q(d,a))return b;var e=l(a);if(void 0!==g&&g(b,e))return b;d=a;return k=e}var c=!1,d,k,m=void 0===e?null:e;return [function(){return a(b())},null===m?void 0:function(){return a(m())}]},[b,e,l,g]);var d=r(a,c[0],c[1]);\n\tu(function(){f.hasValue=!0;f.value=d;},[d]);w(d);return d};\n\treturn withSelector_production_min;\n}\n\nvar withSelector_development = {};\n\n/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar hasRequiredWithSelector_development;\n\nfunction requireWithSelector_development () {\n\tif (hasRequiredWithSelector_development) return withSelector_development;\n\thasRequiredWithSelector_development = 1;\n\n\tif (true) {\n\t  (function() {\n\n\t/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n\tif (\n\t  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n\t  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n\t    'function'\n\t) {\n\t  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n\t}\n\t          var React$1 = react__WEBPACK_IMPORTED_MODULE_0__;\n\tvar shim = shimExports;\n\n\t/**\n\t * inlined Object.is polyfill to avoid requiring consumers ship their own\n\t * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n\t */\n\tfunction is(x, y) {\n\t  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n\t  ;\n\t}\n\n\tvar objectIs = typeof Object.is === 'function' ? Object.is : is;\n\n\tvar useSyncExternalStore = shim.useSyncExternalStore;\n\n\t// for CommonJS interop.\n\n\tvar useRef = React$1.useRef,\n\t    useEffect = React$1.useEffect,\n\t    useMemo = React$1.useMemo,\n\t    useDebugValue = React$1.useDebugValue; // Same as useSyncExternalStore, but supports selector and isEqual arguments.\n\n\tfunction useSyncExternalStoreWithSelector(subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {\n\t  // Use this to track the rendered snapshot.\n\t  var instRef = useRef(null);\n\t  var inst;\n\n\t  if (instRef.current === null) {\n\t    inst = {\n\t      hasValue: false,\n\t      value: null\n\t    };\n\t    instRef.current = inst;\n\t  } else {\n\t    inst = instRef.current;\n\t  }\n\n\t  var _useMemo = useMemo(function () {\n\t    // Track the memoized state using closure variables that are local to this\n\t    // memoized instance of a getSnapshot function. Intentionally not using a\n\t    // useRef hook, because that state would be shared across all concurrent\n\t    // copies of the hook/component.\n\t    var hasMemo = false;\n\t    var memoizedSnapshot;\n\t    var memoizedSelection;\n\n\t    var memoizedSelector = function (nextSnapshot) {\n\t      if (!hasMemo) {\n\t        // The first time the hook is called, there is no memoized result.\n\t        hasMemo = true;\n\t        memoizedSnapshot = nextSnapshot;\n\n\t        var _nextSelection = selector(nextSnapshot);\n\n\t        if (isEqual !== undefined) {\n\t          // Even if the selector has changed, the currently rendered selection\n\t          // may be equal to the new selection. We should attempt to reuse the\n\t          // current value if possible, to preserve downstream memoizations.\n\t          if (inst.hasValue) {\n\t            var currentSelection = inst.value;\n\n\t            if (isEqual(currentSelection, _nextSelection)) {\n\t              memoizedSelection = currentSelection;\n\t              return currentSelection;\n\t            }\n\t          }\n\t        }\n\n\t        memoizedSelection = _nextSelection;\n\t        return _nextSelection;\n\t      } // We may be able to reuse the previous invocation's result.\n\n\n\t      // We may be able to reuse the previous invocation's result.\n\t      var prevSnapshot = memoizedSnapshot;\n\t      var prevSelection = memoizedSelection;\n\n\t      if (objectIs(prevSnapshot, nextSnapshot)) {\n\t        // The snapshot is the same as last time. Reuse the previous selection.\n\t        return prevSelection;\n\t      } // The snapshot has changed, so we need to compute a new selection.\n\n\n\t      // The snapshot has changed, so we need to compute a new selection.\n\t      var nextSelection = selector(nextSnapshot); // If a custom isEqual function is provided, use that to check if the data\n\t      // has changed. If it hasn't, return the previous selection. That signals\n\t      // to React that the selections are conceptually equal, and we can bail\n\t      // out of rendering.\n\n\t      // If a custom isEqual function is provided, use that to check if the data\n\t      // has changed. If it hasn't, return the previous selection. That signals\n\t      // to React that the selections are conceptually equal, and we can bail\n\t      // out of rendering.\n\t      if (isEqual !== undefined && isEqual(prevSelection, nextSelection)) {\n\t        return prevSelection;\n\t      }\n\n\t      memoizedSnapshot = nextSnapshot;\n\t      memoizedSelection = nextSelection;\n\t      return nextSelection;\n\t    }; // Assigning this to a constant so that Flow knows it can't change.\n\n\n\t    // Assigning this to a constant so that Flow knows it can't change.\n\t    var maybeGetServerSnapshot = getServerSnapshot === undefined ? null : getServerSnapshot;\n\n\t    var getSnapshotWithSelector = function () {\n\t      return memoizedSelector(getSnapshot());\n\t    };\n\n\t    var getServerSnapshotWithSelector = maybeGetServerSnapshot === null ? undefined : function () {\n\t      return memoizedSelector(maybeGetServerSnapshot());\n\t    };\n\t    return [getSnapshotWithSelector, getServerSnapshotWithSelector];\n\t  }, [getSnapshot, getServerSnapshot, selector, isEqual]),\n\t      getSelection = _useMemo[0],\n\t      getServerSelection = _useMemo[1];\n\n\t  var value = useSyncExternalStore(subscribe, getSelection, getServerSelection);\n\t  useEffect(function () {\n\t    inst.hasValue = true;\n\t    inst.value = value;\n\t  }, [value]);\n\t  useDebugValue(value);\n\t  return value;\n\t}\n\n\twithSelector_development.useSyncExternalStoreWithSelector = useSyncExternalStoreWithSelector;\n\t          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n\tif (\n\t  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n\t  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n\t    'function'\n\t) {\n\t  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n\t}\n\t        \n\t  })();\n\t}\n\treturn withSelector_development;\n}\n\nif (false) {} else {\n  withSelector.exports = requireWithSelector_development();\n}\n\nvar withSelectorExports = withSelector.exports;\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n/**\n * To synchronize the editor instance with the component state,\n * we need to create a separate instance that is not affected by the component re-renders.\n */\nclass EditorStateManager {\n    constructor(initialEditor) {\n        this.transactionNumber = 0;\n        this.lastTransactionNumber = 0;\n        this.subscribers = new Set();\n        this.editor = initialEditor;\n        this.lastSnapshot = { editor: initialEditor, transactionNumber: 0 };\n        this.getSnapshot = this.getSnapshot.bind(this);\n        this.getServerSnapshot = this.getServerSnapshot.bind(this);\n        this.watch = this.watch.bind(this);\n        this.subscribe = this.subscribe.bind(this);\n    }\n    /**\n     * Get the current editor instance.\n     */\n    getSnapshot() {\n        if (this.transactionNumber === this.lastTransactionNumber) {\n            return this.lastSnapshot;\n        }\n        this.lastTransactionNumber = this.transactionNumber;\n        this.lastSnapshot = { editor: this.editor, transactionNumber: this.transactionNumber };\n        return this.lastSnapshot;\n    }\n    /**\n     * Always disable the editor on the server-side.\n     */\n    getServerSnapshot() {\n        return { editor: null, transactionNumber: 0 };\n    }\n    /**\n     * Subscribe to the editor instance's changes.\n     */\n    subscribe(callback) {\n        this.subscribers.add(callback);\n        return () => {\n            this.subscribers.delete(callback);\n        };\n    }\n    /**\n     * Watch the editor instance for changes.\n     */\n    watch(nextEditor) {\n        this.editor = nextEditor;\n        if (this.editor) {\n            /**\n             * This will force a re-render when the editor state changes.\n             * This is to support things like `editor.can().toggleBold()` in components that `useEditor`.\n             * This could be more efficient, but it's a good trade-off for now.\n             */\n            const fn = () => {\n                this.transactionNumber += 1;\n                this.subscribers.forEach(callback => callback());\n            };\n            const currentEditor = this.editor;\n            currentEditor.on('transaction', fn);\n            return () => {\n                currentEditor.off('transaction', fn);\n            };\n        }\n        return undefined;\n    }\n}\n/**\n * This hook allows you to watch for changes on the editor instance.\n * It will allow you to select a part of the editor state and re-render the component when it changes.\n * @example\n * ```tsx\n * const editor = useEditor({...options})\n * const { currentSelection } = useEditorState({\n *  editor,\n *  selector: snapshot => ({ currentSelection: snapshot.editor.state.selection }),\n * })\n */\nfunction useEditorState(options) {\n    var _a;\n    const [editorStateManager] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new EditorStateManager(options.editor));\n    // Using the `useSyncExternalStore` hook to sync the editor instance with the component state\n    const selectedState = withSelectorExports.useSyncExternalStoreWithSelector(editorStateManager.subscribe, editorStateManager.getSnapshot, editorStateManager.getServerSnapshot, options.selector, (_a = options.equalityFn) !== null && _a !== void 0 ? _a : deepEqual);\n    useIsomorphicLayoutEffect(() => {\n        return editorStateManager.watch(options.editor);\n    }, [options.editor, editorStateManager]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue)(selectedState);\n    return selectedState;\n}\n\nconst isDev = \"development\" !== 'production';\nconst isSSR = typeof window === 'undefined';\nconst isNext = isSSR || Boolean(typeof window !== 'undefined' && window.next);\n/**\n * This class handles the creation, destruction, and re-creation of the editor instance.\n */\nclass EditorInstanceManager {\n    constructor(options) {\n        /**\n         * The current editor instance.\n         */\n        this.editor = null;\n        /**\n         * The subscriptions to notify when the editor instance\n         * has been created or destroyed.\n         */\n        this.subscriptions = new Set();\n        /**\n         * Whether the editor has been mounted.\n         */\n        this.isComponentMounted = false;\n        /**\n         * The most recent dependencies array.\n         */\n        this.previousDeps = null;\n        /**\n         * The unique instance ID. This is used to identify the editor instance. And will be re-generated for each new instance.\n         */\n        this.instanceId = '';\n        this.options = options;\n        this.subscriptions = new Set();\n        this.setEditor(this.getInitialEditor());\n        this.scheduleDestroy();\n        this.getEditor = this.getEditor.bind(this);\n        this.getServerSnapshot = this.getServerSnapshot.bind(this);\n        this.subscribe = this.subscribe.bind(this);\n        this.refreshEditorInstance = this.refreshEditorInstance.bind(this);\n        this.scheduleDestroy = this.scheduleDestroy.bind(this);\n        this.onRender = this.onRender.bind(this);\n        this.createEditor = this.createEditor.bind(this);\n    }\n    setEditor(editor) {\n        this.editor = editor;\n        this.instanceId = Math.random().toString(36).slice(2, 9);\n        // Notify all subscribers that the editor instance has been created\n        this.subscriptions.forEach(cb => cb());\n    }\n    getInitialEditor() {\n        if (this.options.current.immediatelyRender === undefined) {\n            if (isSSR || isNext) {\n                // TODO in the next major release, we should throw an error here\n                if (isDev) {\n                    /**\n                     * Throw an error in development, to make sure the developer is aware that tiptap cannot be SSR'd\n                     * and that they need to set `immediatelyRender` to `false` to avoid hydration mismatches.\n                     */\n                    console.warn('Tiptap Error: SSR has been detected, please set `immediatelyRender` explicitly to `false` to avoid hydration mismatches.');\n                }\n                // Best faith effort in production, run the code in the legacy mode to avoid hydration mismatches and errors in production\n                return null;\n            }\n            // Default to immediately rendering when client-side rendering\n            return this.createEditor();\n        }\n        if (this.options.current.immediatelyRender && isSSR && isDev) {\n            // Warn in development, to make sure the developer is aware that tiptap cannot be SSR'd, set `immediatelyRender` to `false` to avoid hydration mismatches.\n            throw new Error('Tiptap Error: SSR has been detected, and `immediatelyRender` has been set to `true` this is an unsupported configuration that may result in errors, explicitly set `immediatelyRender` to `false` to avoid hydration mismatches.');\n        }\n        if (this.options.current.immediatelyRender) {\n            return this.createEditor();\n        }\n        return null;\n    }\n    /**\n     * Create a new editor instance. And attach event listeners.\n     */\n    createEditor() {\n        const optionsToApply = {\n            ...this.options.current,\n            // Always call the most recent version of the callback function by default\n            onBeforeCreate: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onBeforeCreate) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n            onBlur: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onBlur) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n            onCreate: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onCreate) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n            onDestroy: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onDestroy) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n            onFocus: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onFocus) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n            onSelectionUpdate: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onSelectionUpdate) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n            onTransaction: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onTransaction) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n            onUpdate: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onUpdate) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n            onContentError: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onContentError) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n            onDrop: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onDrop) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n            onPaste: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onPaste) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n        };\n        const editor = new _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.Editor(optionsToApply);\n        // no need to keep track of the event listeners, they will be removed when the editor is destroyed\n        return editor;\n    }\n    /**\n     * Get the current editor instance.\n     */\n    getEditor() {\n        return this.editor;\n    }\n    /**\n     * Always disable the editor on the server-side.\n     */\n    getServerSnapshot() {\n        return null;\n    }\n    /**\n     * Subscribe to the editor instance's changes.\n     */\n    subscribe(onStoreChange) {\n        this.subscriptions.add(onStoreChange);\n        return () => {\n            this.subscriptions.delete(onStoreChange);\n        };\n    }\n    static compareOptions(a, b) {\n        return Object.keys(a).every(key => {\n            if (['onCreate', 'onBeforeCreate', 'onDestroy', 'onUpdate', 'onTransaction', 'onFocus', 'onBlur', 'onSelectionUpdate', 'onContentError', 'onDrop', 'onPaste'].includes(key)) {\n                // we don't want to compare callbacks, they are always different and only registered once\n                return true;\n            }\n            // We often encourage putting extensions inlined in the options object, so we will do a slightly deeper comparison here\n            if (key === 'extensions' && a.extensions && b.extensions) {\n                if (a.extensions.length !== b.extensions.length) {\n                    return false;\n                }\n                return a.extensions.every((extension, index) => {\n                    var _a;\n                    if (extension !== ((_a = b.extensions) === null || _a === void 0 ? void 0 : _a[index])) {\n                        return false;\n                    }\n                    return true;\n                });\n            }\n            if (a[key] !== b[key]) {\n                // if any of the options have changed, we should update the editor options\n                return false;\n            }\n            return true;\n        });\n    }\n    /**\n     * On each render, we will create, update, or destroy the editor instance.\n     * @param deps The dependencies to watch for changes\n     * @returns A cleanup function\n     */\n    onRender(deps) {\n        // The returned callback will run on each render\n        return () => {\n            this.isComponentMounted = true;\n            // Cleanup any scheduled destructions, since we are currently rendering\n            clearTimeout(this.scheduledDestructionTimeout);\n            if (this.editor && !this.editor.isDestroyed && deps.length === 0) {\n                // if the editor does exist & deps are empty, we don't need to re-initialize the editor generally\n                if (!EditorInstanceManager.compareOptions(this.options.current, this.editor.options)) {\n                    // But, the options are different, so we need to update the editor options\n                    // Still, this is faster than re-creating the editor\n                    this.editor.setOptions({\n                        ...this.options.current,\n                        editable: this.editor.isEditable,\n                    });\n                }\n            }\n            else {\n                // When the editor:\n                // - does not yet exist\n                // - is destroyed\n                // - the deps array changes\n                // We need to destroy the editor instance and re-initialize it\n                this.refreshEditorInstance(deps);\n            }\n            return () => {\n                this.isComponentMounted = false;\n                this.scheduleDestroy();\n            };\n        };\n    }\n    /**\n     * Recreate the editor instance if the dependencies have changed.\n     */\n    refreshEditorInstance(deps) {\n        if (this.editor && !this.editor.isDestroyed) {\n            // Editor instance already exists\n            if (this.previousDeps === null) {\n                // If lastDeps has not yet been initialized, reuse the current editor instance\n                this.previousDeps = deps;\n                return;\n            }\n            const depsAreEqual = this.previousDeps.length === deps.length\n                && this.previousDeps.every((dep, index) => dep === deps[index]);\n            if (depsAreEqual) {\n                // deps exist and are equal, no need to recreate\n                return;\n            }\n        }\n        if (this.editor && !this.editor.isDestroyed) {\n            // Destroy the editor instance if it exists\n            this.editor.destroy();\n        }\n        this.setEditor(this.createEditor());\n        // Update the lastDeps to the current deps\n        this.previousDeps = deps;\n    }\n    /**\n     * Schedule the destruction of the editor instance.\n     * This will only destroy the editor if it was not mounted on the next tick.\n     * This is to avoid destroying the editor instance when it's actually still mounted.\n     */\n    scheduleDestroy() {\n        const currentInstanceId = this.instanceId;\n        const currentEditor = this.editor;\n        // Wait two ticks to see if the component is still mounted\n        this.scheduledDestructionTimeout = setTimeout(() => {\n            if (this.isComponentMounted && this.instanceId === currentInstanceId) {\n                // If still mounted on the following tick, with the same instanceId, do not destroy the editor\n                if (currentEditor) {\n                    // just re-apply options as they might have changed\n                    currentEditor.setOptions(this.options.current);\n                }\n                return;\n            }\n            if (currentEditor && !currentEditor.isDestroyed) {\n                currentEditor.destroy();\n                if (this.instanceId === currentInstanceId) {\n                    this.setEditor(null);\n                }\n            }\n            // This allows the effect to run again between ticks\n            // which may save us from having to re-create the editor\n        }, 1);\n    }\n}\nfunction useEditor(options = {}, deps = []) {\n    const mostRecentOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(options);\n    mostRecentOptions.current = options;\n    const [instanceManager] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new EditorInstanceManager(mostRecentOptions));\n    const editor = shimExports.useSyncExternalStore(instanceManager.subscribe, instanceManager.getEditor, instanceManager.getServerSnapshot);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue)(editor);\n    // This effect will handle creating/updating the editor instance\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(instanceManager.onRender(deps));\n    // The default behavior is to re-render on each transaction\n    // This is legacy behavior that will be removed in future versions\n    useEditorState({\n        editor,\n        selector: ({ transactionNumber }) => {\n            if (options.shouldRerenderOnTransaction === false) {\n                // This will prevent the editor from re-rendering on each transaction\n                return null;\n            }\n            // This will avoid re-rendering on the first transaction when `immediatelyRender` is set to `true`\n            if (options.immediatelyRender && transactionNumber === 0) {\n                return 0;\n            }\n            return transactionNumber + 1;\n        },\n    });\n    return editor;\n}\n\nconst EditorContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n    editor: null,\n});\nconst EditorConsumer = EditorContext.Consumer;\n/**\n * A hook to get the current editor instance.\n */\nconst useCurrentEditor = () => (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(EditorContext);\n/**\n * This is the provider component for the editor.\n * It allows the editor to be accessible across the entire component tree\n * with `useCurrentEditor`.\n */\nfunction EditorProvider({ children, slotAfter, slotBefore, editorContainerProps = {}, ...editorOptions }) {\n    const editor = useEditor(editorOptions);\n    if (!editor) {\n        return null;\n    }\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(EditorContext.Provider, { value: { editor } },\n        slotBefore,\n        react__WEBPACK_IMPORTED_MODULE_0__.createElement(EditorConsumer, null, ({ editor: currentEditor }) => (react__WEBPACK_IMPORTED_MODULE_0__.createElement(EditorContent, { editor: currentEditor, ...editorContainerProps }))),\n        children,\n        slotAfter));\n}\n\nconst BubbleMenu = (props) => {\n    const [element, setElement] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const { editor: currentEditor } = useCurrentEditor();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        var _a;\n        if (!element) {\n            return;\n        }\n        if (((_a = props.editor) === null || _a === void 0 ? void 0 : _a.isDestroyed) || (currentEditor === null || currentEditor === void 0 ? void 0 : currentEditor.isDestroyed)) {\n            return;\n        }\n        const { pluginKey = 'bubbleMenu', editor, tippyOptions = {}, updateDelay, shouldShow = null, } = props;\n        const menuEditor = editor || currentEditor;\n        if (!menuEditor) {\n            console.warn('BubbleMenu component is not rendered inside of an editor component or does not have editor prop.');\n            return;\n        }\n        const plugin = (0,_tiptap_extension_bubble_menu__WEBPACK_IMPORTED_MODULE_3__.BubbleMenuPlugin)({\n            updateDelay,\n            editor: menuEditor,\n            element,\n            pluginKey,\n            shouldShow,\n            tippyOptions,\n        });\n        menuEditor.registerPlugin(plugin);\n        return () => { menuEditor.unregisterPlugin(pluginKey); };\n    }, [props.editor, currentEditor, element]);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setElement, className: props.className, style: { visibility: 'hidden' } }, props.children));\n};\n\nconst FloatingMenu = (props) => {\n    const [element, setElement] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const { editor: currentEditor } = useCurrentEditor();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        var _a;\n        if (!element) {\n            return;\n        }\n        if (((_a = props.editor) === null || _a === void 0 ? void 0 : _a.isDestroyed) || (currentEditor === null || currentEditor === void 0 ? void 0 : currentEditor.isDestroyed)) {\n            return;\n        }\n        const { pluginKey = 'floatingMenu', editor, tippyOptions = {}, shouldShow = null, } = props;\n        const menuEditor = editor || currentEditor;\n        if (!menuEditor) {\n            console.warn('FloatingMenu component is not rendered inside of an editor component or does not have editor prop.');\n            return;\n        }\n        const plugin = (0,_tiptap_extension_floating_menu__WEBPACK_IMPORTED_MODULE_4__.FloatingMenuPlugin)({\n            pluginKey,\n            editor: menuEditor,\n            element,\n            tippyOptions,\n            shouldShow,\n        });\n        menuEditor.registerPlugin(plugin);\n        return () => { menuEditor.unregisterPlugin(pluginKey); };\n    }, [\n        props.editor,\n        currentEditor,\n        element,\n    ]);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setElement, className: props.className, style: { visibility: 'hidden' } }, props.children));\n};\n\nconst ReactNodeViewContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n    onDragStart: undefined,\n});\nconst useReactNodeView = () => (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ReactNodeViewContext);\n\nconst NodeViewContent = props => {\n    const Tag = props.as || 'div';\n    const { nodeViewContentRef } = useReactNodeView();\n    return (\n    // @ts-ignore\n    react__WEBPACK_IMPORTED_MODULE_0__.createElement(Tag, { ...props, ref: nodeViewContentRef, \"data-node-view-content\": \"\", style: {\n            whiteSpace: 'pre-wrap',\n            ...props.style,\n        } }));\n};\n\nconst NodeViewWrapper = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, ref) => {\n    const { onDragStart } = useReactNodeView();\n    const Tag = props.as || 'div';\n    return (\n    // @ts-ignore\n    react__WEBPACK_IMPORTED_MODULE_0__.createElement(Tag, { ...props, ref: ref, \"data-node-view-wrapper\": \"\", onDragStart: onDragStart, style: {\n            whiteSpace: 'normal',\n            ...props.style,\n        } }));\n});\n\n/**\n * Check if a component is a class component.\n * @param Component\n * @returns {boolean}\n */\nfunction isClassComponent(Component) {\n    return !!(typeof Component === 'function'\n        && Component.prototype\n        && Component.prototype.isReactComponent);\n}\n/**\n * Check if a component is a forward ref component.\n * @param Component\n * @returns {boolean}\n */\nfunction isForwardRefComponent(Component) {\n    return !!(typeof Component === 'object'\n        && Component.$$typeof\n        && (Component.$$typeof.toString() === 'Symbol(react.forward_ref)'\n            || Component.$$typeof.description === 'react.forward_ref'));\n}\n/**\n * Check if a component is a memoized component.\n * @param Component\n * @returns {boolean}\n */\nfunction isMemoComponent(Component) {\n    return !!(typeof Component === 'object'\n        && Component.$$typeof\n        && (Component.$$typeof.toString() === 'Symbol(react.memo)' || Component.$$typeof.description === 'react.memo'));\n}\n/**\n * Check if a component can safely receive a ref prop.\n * This includes class components, forwardRef components, and memoized components\n * that wrap forwardRef or class components.\n * @param Component\n * @returns {boolean}\n */\nfunction canReceiveRef(Component) {\n    // Check if it's a class component\n    if (isClassComponent(Component)) {\n        return true;\n    }\n    // Check if it's a forwardRef component\n    if (isForwardRefComponent(Component)) {\n        return true;\n    }\n    // Check if it's a memoized component\n    if (isMemoComponent(Component)) {\n        // For memoized components, check the wrapped component\n        const wrappedComponent = Component.type;\n        if (wrappedComponent) {\n            return isClassComponent(wrappedComponent) || isForwardRefComponent(wrappedComponent);\n        }\n    }\n    return false;\n}\n/**\n * Check if we're running React 19+ by detecting if function components support ref props\n * @returns {boolean}\n */\nfunction isReact19Plus() {\n    // React 19 is detected by checking React version if available\n    // In practice, we'll use a more conservative approach and assume React 18 behavior\n    // unless we can definitively detect React 19\n    try {\n        // @ts-ignore\n        if (react__WEBPACK_IMPORTED_MODULE_0__.version) {\n            const majorVersion = parseInt(react__WEBPACK_IMPORTED_MODULE_0__.version.split('.')[0], 10);\n            return majorVersion >= 19;\n        }\n    }\n    catch {\n        // Fallback to React 18 behavior if we can't determine version\n    }\n    return false;\n}\n/**\n * The ReactRenderer class. It's responsible for rendering React components inside the editor.\n * @example\n * new ReactRenderer(MyComponent, {\n *   editor,\n *   props: {\n *     foo: 'bar',\n *   },\n *   as: 'span',\n * })\n*/\nclass ReactRenderer {\n    /**\n     * Immediately creates element and renders the provided React component.\n     */\n    constructor(component, { editor, props = {}, as = 'div', className = '', }) {\n        this.ref = null;\n        this.id = Math.floor(Math.random() * 0xFFFFFFFF).toString();\n        this.component = component;\n        this.editor = editor;\n        this.props = props;\n        this.element = document.createElement(as);\n        this.element.classList.add('react-renderer');\n        if (className) {\n            this.element.classList.add(...className.split(' '));\n        }\n        // If the editor is already initialized, we will need to\n        // synchronously render the component to ensure it renders\n        // together with Prosemirror's rendering.\n        if (this.editor.isInitialized) {\n            (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync)(() => {\n                this.render();\n            });\n        }\n        else {\n            queueMicrotask(() => {\n                this.render();\n            });\n        }\n    }\n    /**\n     * Render the React component.\n     */\n    render() {\n        var _a;\n        const Component = this.component;\n        const props = this.props;\n        const editor = this.editor;\n        // Handle ref forwarding with React 18/19 compatibility\n        const isReact19 = isReact19Plus();\n        const componentCanReceiveRef = canReceiveRef(Component);\n        const elementProps = { ...props };\n        // Always remove ref if the component cannot receive it (unless React 19+)\n        if (elementProps.ref && !(isReact19 || componentCanReceiveRef)) {\n            delete elementProps.ref;\n        }\n        // Only assign our own ref if allowed\n        if (!elementProps.ref && (isReact19 || componentCanReceiveRef)) {\n            // @ts-ignore - Setting ref prop for compatible components\n            elementProps.ref = (ref) => {\n                this.ref = ref;\n            };\n        }\n        this.reactElement = react__WEBPACK_IMPORTED_MODULE_0__.createElement(Component, { ...elementProps });\n        (_a = editor === null || editor === void 0 ? void 0 : editor.contentComponent) === null || _a === void 0 ? void 0 : _a.setRenderer(this.id, this);\n    }\n    /**\n     * Re-renders the React component with new props.\n     */\n    updateProps(props = {}) {\n        this.props = {\n            ...this.props,\n            ...props,\n        };\n        this.render();\n    }\n    /**\n     * Destroy the React component.\n     */\n    destroy() {\n        var _a;\n        const editor = this.editor;\n        (_a = editor === null || editor === void 0 ? void 0 : editor.contentComponent) === null || _a === void 0 ? void 0 : _a.removeRenderer(this.id);\n    }\n    /**\n     * Update the attributes of the element that holds the React component.\n     */\n    updateAttributes(attributes) {\n        Object.keys(attributes).forEach(key => {\n            this.element.setAttribute(key, attributes[key]);\n        });\n    }\n}\n\nclass ReactNodeView extends _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.NodeView {\n    constructor(component, props, options) {\n        super(component, props, options);\n        if (!this.node.isLeaf) {\n            if (this.options.contentDOMElementTag) {\n                this.contentDOMElement = document.createElement(this.options.contentDOMElementTag);\n            }\n            else {\n                this.contentDOMElement = document.createElement(this.node.isInline ? 'span' : 'div');\n            }\n            this.contentDOMElement.dataset.nodeViewContentReact = '';\n            this.contentDOMElement.dataset.nodeViewWrapper = '';\n            // For some reason the whiteSpace prop is not inherited properly in Chrome and Safari\n            // With this fix it seems to work fine\n            // See: https://github.com/ueberdosis/tiptap/issues/1197\n            this.contentDOMElement.style.whiteSpace = 'inherit';\n            const contentTarget = this.dom.querySelector('[data-node-view-content]');\n            if (!contentTarget) {\n                return;\n            }\n            contentTarget.appendChild(this.contentDOMElement);\n        }\n    }\n    /**\n     * Setup the React component.\n     * Called on initialization.\n     */\n    mount() {\n        const props = {\n            editor: this.editor,\n            node: this.node,\n            decorations: this.decorations,\n            innerDecorations: this.innerDecorations,\n            view: this.view,\n            selected: false,\n            extension: this.extension,\n            HTMLAttributes: this.HTMLAttributes,\n            getPos: () => this.getPos(),\n            updateAttributes: (attributes = {}) => this.updateAttributes(attributes),\n            deleteNode: () => this.deleteNode(),\n            ref: (0,react__WEBPACK_IMPORTED_MODULE_0__.createRef)(),\n        };\n        if (!this.component.displayName) {\n            const capitalizeFirstChar = (string) => {\n                return string.charAt(0).toUpperCase() + string.substring(1);\n            };\n            this.component.displayName = capitalizeFirstChar(this.extension.name);\n        }\n        const onDragStart = this.onDragStart.bind(this);\n        const nodeViewContentRef = element => {\n            if (element && this.contentDOMElement && element.firstChild !== this.contentDOMElement) {\n                // remove the nodeViewWrapper attribute from the element\n                if (element.hasAttribute('data-node-view-wrapper')) {\n                    element.removeAttribute('data-node-view-wrapper');\n                }\n                element.appendChild(this.contentDOMElement);\n            }\n        };\n        const context = { onDragStart, nodeViewContentRef };\n        const Component = this.component;\n        // For performance reasons, we memoize the provider component\n        // And all of the things it requires are declared outside of the component, so it doesn't need to re-render\n        const ReactNodeViewProvider = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(componentProps => {\n            return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(ReactNodeViewContext.Provider, { value: context }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Component, componentProps)));\n        });\n        ReactNodeViewProvider.displayName = 'ReactNodeView';\n        let as = this.node.isInline ? 'span' : 'div';\n        if (this.options.as) {\n            as = this.options.as;\n        }\n        const { className = '' } = this.options;\n        this.handleSelectionUpdate = this.handleSelectionUpdate.bind(this);\n        this.renderer = new ReactRenderer(ReactNodeViewProvider, {\n            editor: this.editor,\n            props,\n            as,\n            className: `node-${this.node.type.name} ${className}`.trim(),\n        });\n        this.editor.on('selectionUpdate', this.handleSelectionUpdate);\n        this.updateElementAttributes();\n    }\n    /**\n     * Return the DOM element.\n     * This is the element that will be used to display the node view.\n     */\n    get dom() {\n        var _a;\n        if (this.renderer.element.firstElementChild\n            && !((_a = this.renderer.element.firstElementChild) === null || _a === void 0 ? void 0 : _a.hasAttribute('data-node-view-wrapper'))) {\n            throw Error('Please use the NodeViewWrapper component for your node view.');\n        }\n        return this.renderer.element;\n    }\n    /**\n     * Return the content DOM element.\n     * This is the element that will be used to display the rich-text content of the node.\n     */\n    get contentDOM() {\n        if (this.node.isLeaf) {\n            return null;\n        }\n        return this.contentDOMElement;\n    }\n    /**\n     * On editor selection update, check if the node is selected.\n     * If it is, call `selectNode`, otherwise call `deselectNode`.\n     */\n    handleSelectionUpdate() {\n        const { from, to } = this.editor.state.selection;\n        const pos = this.getPos();\n        if (typeof pos !== 'number') {\n            return;\n        }\n        if (from <= pos && to >= pos + this.node.nodeSize) {\n            if (this.renderer.props.selected) {\n                return;\n            }\n            this.selectNode();\n        }\n        else {\n            if (!this.renderer.props.selected) {\n                return;\n            }\n            this.deselectNode();\n        }\n    }\n    /**\n     * On update, update the React component.\n     * To prevent unnecessary updates, the `update` option can be used.\n     */\n    update(node, decorations, innerDecorations) {\n        const rerenderComponent = (props) => {\n            this.renderer.updateProps(props);\n            if (typeof this.options.attrs === 'function') {\n                this.updateElementAttributes();\n            }\n        };\n        if (node.type !== this.node.type) {\n            return false;\n        }\n        if (typeof this.options.update === 'function') {\n            const oldNode = this.node;\n            const oldDecorations = this.decorations;\n            const oldInnerDecorations = this.innerDecorations;\n            this.node = node;\n            this.decorations = decorations;\n            this.innerDecorations = innerDecorations;\n            return this.options.update({\n                oldNode,\n                oldDecorations,\n                newNode: node,\n                newDecorations: decorations,\n                oldInnerDecorations,\n                innerDecorations,\n                updateProps: () => rerenderComponent({ node, decorations, innerDecorations }),\n            });\n        }\n        if (node === this.node\n            && this.decorations === decorations\n            && this.innerDecorations === innerDecorations) {\n            return true;\n        }\n        this.node = node;\n        this.decorations = decorations;\n        this.innerDecorations = innerDecorations;\n        rerenderComponent({ node, decorations, innerDecorations });\n        return true;\n    }\n    /**\n     * Select the node.\n     * Add the `selected` prop and the `ProseMirror-selectednode` class.\n     */\n    selectNode() {\n        this.renderer.updateProps({\n            selected: true,\n        });\n        this.renderer.element.classList.add('ProseMirror-selectednode');\n    }\n    /**\n     * Deselect the node.\n     * Remove the `selected` prop and the `ProseMirror-selectednode` class.\n     */\n    deselectNode() {\n        this.renderer.updateProps({\n            selected: false,\n        });\n        this.renderer.element.classList.remove('ProseMirror-selectednode');\n    }\n    /**\n     * Destroy the React component instance.\n     */\n    destroy() {\n        this.renderer.destroy();\n        this.editor.off('selectionUpdate', this.handleSelectionUpdate);\n        this.contentDOMElement = null;\n    }\n    /**\n     * Update the attributes of the top-level element that holds the React component.\n     * Applying the attributes defined in the `attrs` option.\n     */\n    updateElementAttributes() {\n        if (this.options.attrs) {\n            let attrsObj = {};\n            if (typeof this.options.attrs === 'function') {\n                const extensionAttributes = this.editor.extensionManager.attributes;\n                const HTMLAttributes = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getRenderedAttributes)(this.node, extensionAttributes);\n                attrsObj = this.options.attrs({ node: this.node, HTMLAttributes });\n            }\n            else {\n                attrsObj = this.options.attrs;\n            }\n            this.renderer.updateAttributes(attrsObj);\n        }\n    }\n}\n/**\n * Create a React node view renderer.\n */\nfunction ReactNodeViewRenderer(component, options) {\n    return props => {\n        // try to get the parent component\n        // this is important for vue devtools to show the component hierarchy correctly\n        // maybe it’s `undefined` because <editor-content> isn’t rendered yet\n        if (!props.editor.contentComponent) {\n            return {};\n        }\n        return new ReactNodeView(component, props, options);\n    };\n}\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+react@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1_react-dom@18.3._q3aad5pwjtroq2r7i35exxu3a4/node_modules/@tiptap/react/dist/index.js\n");

/***/ })

};
;