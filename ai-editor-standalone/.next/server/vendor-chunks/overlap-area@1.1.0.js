"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/overlap-area@1.1.0";
exports.ids = ["vendor-chunks/overlap-area@1.1.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/overlap-area@1.1.0/node_modules/overlap-area/dist/overlap-area.esm.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/overlap-area@1.1.0/node_modules/overlap-area/dist/overlap-area.esm.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertLines: () => (/* binding */ convertLines),\n/* harmony export */   findConnectedAreas: () => (/* binding */ findConnectedAreas),\n/* harmony export */   fitPoints: () => (/* binding */ fitPoints),\n/* harmony export */   getAreaSize: () => (/* binding */ getAreaSize),\n/* harmony export */   getDistanceFromPointToConstants: () => (/* binding */ getDistanceFromPointToConstants),\n/* harmony export */   getIntersectionPoints: () => (/* binding */ getIntersectionPoints),\n/* harmony export */   getIntersectionPointsByConstants: () => (/* binding */ getIntersectionPointsByConstants),\n/* harmony export */   getLinearConstants: () => (/* binding */ getLinearConstants),\n/* harmony export */   getMinMaxs: () => (/* binding */ getMinMaxs),\n/* harmony export */   getOverlapAreas: () => (/* binding */ getOverlapAreas),\n/* harmony export */   getOverlapPoints: () => (/* binding */ getOverlapPoints),\n/* harmony export */   getOverlapSize: () => (/* binding */ getOverlapSize),\n/* harmony export */   getPointsOnLines: () => (/* binding */ getPointsOnLines),\n/* harmony export */   getUnoverlapAreas: () => (/* binding */ getUnoverlapAreas),\n/* harmony export */   isInside: () => (/* binding */ isInside),\n/* harmony export */   isPointOnLine: () => (/* binding */ isPointOnLine)\n/* harmony export */ });\n/* harmony import */ var _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @daybrush/utils */ \"(ssr)/./node_modules/.pnpm/@daybrush+utils@1.13.0/node_modules/@daybrush/utils/dist/utils.esm.js\");\n/*\nCopyright (c) 2020 Daybrush\nname: overlap-area\nlicense: MIT\nauthor: Daybrush\nrepository: git+https://github.com/daybrush/overlap-area.git\nversion: 1.1.0\n*/\n\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\nfunction __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n\n  for (var r = Array(s), k = 0, i = 0; i < il; i++) for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++) r[k] = a[j];\n\n  return r;\n}\n\nfunction tinyThrottle(num) {\n  return (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.throttle)(num, _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.TINY_NUM);\n}\nfunction isSameConstants(linearConstants1, linearConstants2) {\n  return linearConstants1.every(function (v, i) {\n    return tinyThrottle(v - linearConstants2[i]) === 0;\n  });\n}\nfunction isSamePoint(point1, point2) {\n  return !tinyThrottle(point1[0] - point2[0]) && !tinyThrottle(point1[1] - point2[1]);\n}\nfunction flat(arr) {\n  return arr.reduce(function (prev, current) {\n    prev.push.apply(prev, current);\n    return prev;\n  }, []);\n}\n\n/**\n * @namespace OverlapArea\n */\n\n/**\n * Gets the size of a shape (polygon) made of points.\n * @memberof OverlapArea\n */\n\nfunction getAreaSize(points) {\n  if (points.length < 3) {\n    return 0;\n  }\n\n  return Math.abs((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.sum)(points.map(function (point, i) {\n    var nextPoint = points[i + 1] || points[0];\n    return point[0] * nextPoint[1] - nextPoint[0] * point[1];\n  }))) / 2;\n}\n/**\n * Get points that fit the rect,\n * @memberof OverlapArea\n */\n\nfunction fitPoints(points, rect) {\n  var width = rect.width,\n      height = rect.height,\n      left = rect.left,\n      top = rect.top;\n\n  var _a = getMinMaxs(points),\n      minX = _a.minX,\n      minY = _a.minY,\n      maxX = _a.maxX,\n      maxY = _a.maxY;\n\n  var ratioX = width / (maxX - minX);\n  var ratioY = height / (maxY - minY);\n  return points.map(function (point) {\n    return [left + (point[0] - minX) * ratioX, top + (point[1] - minY) * ratioY];\n  });\n}\n/**\n * Get the minimum and maximum points of the points.\n * @memberof OverlapArea\n */\n\nfunction getMinMaxs(points) {\n  var xs = points.map(function (point) {\n    return point[0];\n  });\n  var ys = points.map(function (point) {\n    return point[1];\n  });\n  return {\n    minX: Math.min.apply(Math, xs),\n    minY: Math.min.apply(Math, ys),\n    maxX: Math.max.apply(Math, xs),\n    maxY: Math.max.apply(Math, ys)\n  };\n}\n/**\n * Whether the point is in shape\n * @param - point pos\n * @param - shape points\n * @param - whether to check except line\n * @memberof OverlapArea\n */\n\nfunction isInside(pos, points, excludeLine) {\n  var x = pos[0],\n      y = pos[1];\n\n  var _a = getMinMaxs(points),\n      minX = _a.minX,\n      maxX = _a.maxX;\n\n  var xLine = [[minX, y], [maxX, y]];\n  var xLinearConstants = getLinearConstants(xLine[0], xLine[1]);\n  var lines = convertLines(points);\n  var intersectionPosInfos = [];\n  lines.forEach(function (line) {\n    var linearConstants = getLinearConstants(line[0], line[1]);\n    var standardPoint = line[0];\n\n    if (isSameConstants(xLinearConstants, linearConstants)) {\n      intersectionPosInfos.push({\n        pos: pos,\n        line: line,\n        type: \"line\"\n      });\n    } else {\n      var xPoints = getPointsOnLines(getIntersectionPointsByConstants(xLinearConstants, linearConstants), [xLine, line]);\n      xPoints.forEach(function (point) {\n        if (line.some(function (linePoint) {\n          return isSamePoint(linePoint, point);\n        })) {\n          intersectionPosInfos.push({\n            pos: point,\n            line: line,\n            type: \"point\"\n          });\n        } else if (tinyThrottle(standardPoint[1] - y) !== 0) {\n          intersectionPosInfos.push({\n            pos: point,\n            line: line,\n            type: \"intersection\"\n          });\n        }\n      });\n    }\n  });\n\n  if (!excludeLine) {\n    // on line\n    if ((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.find)(intersectionPosInfos, function (p) {\n      return p[0] === x;\n    })) {\n      return true;\n    }\n  }\n\n  var intersectionCount = 0;\n  var xMap = {};\n  intersectionPosInfos.forEach(function (_a) {\n    var pos = _a.pos,\n        type = _a.type,\n        line = _a.line;\n\n    if (pos[0] > x) {\n      return;\n    }\n\n    if (type === \"intersection\") {\n      ++intersectionCount;\n    } else if (type === \"line\") {\n      return;\n    } else if (type === \"point\") {\n      var point = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.find)(line, function (linePoint) {\n        return linePoint[1] !== y;\n      });\n      var prevValue = xMap[pos[0]];\n      var nextValue = point[1] > y ? 1 : -1;\n\n      if (!prevValue) {\n        xMap[pos[0]] = nextValue;\n      } else if (prevValue !== nextValue) {\n        ++intersectionCount;\n      }\n    }\n  });\n  return intersectionCount % 2 === 1;\n}\n/**\n * Get distance from point to constants. [a, b, c] (ax + by + c = 0)\n * @return [a, b, c]\n * @memberof OverlapArea\n */\n\nfunction getDistanceFromPointToConstants(_a, pos) {\n  var a = _a[0],\n      b = _a[1],\n      c = _a[2];\n  return (a * pos[0] + b * pos[1] + c) / (a * a + b * b);\n}\n/**\n * Get the coefficient of the linear function. [a, b, c] (ax + by + c = 0)\n * @return [a, b, c]\n * @memberof OverlapArea\n */\n\nfunction getLinearConstants(point1, point2) {\n  var x1 = point1[0],\n      y1 = point1[1];\n  var x2 = point2[0],\n      y2 = point2[1]; // ax + by + c = 0\n  // [a, b, c]\n\n  var dx = x2 - x1;\n  var dy = y2 - y1;\n\n  if (Math.abs(dx) < _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.TINY_NUM) {\n    dx = 0;\n  }\n\n  if (Math.abs(dy) < _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.TINY_NUM) {\n    dy = 0;\n  } // b > 0\n  // ax + by + c = 0\n\n\n  var a = 0;\n  var b = 0;\n  var c = 0;\n\n  if (!dx) {\n    if (dy) {\n      // -x + 1 = 0\n      a = -1;\n      c = x1;\n    }\n  } else if (!dy) {\n    // y - 1 = 0\n    b = 1;\n    c = -y1;\n  } else {\n    // y = -a(x - x1) + y1\n    // ax + y + a * x1 - y1 = 0\n    a = -dy / dx;\n    b = 1;\n    c = -a * x1 - y1;\n  }\n\n  return [a, b, c];\n}\n/**\n * Get intersection points with linear functions.\n * @memberof OverlapArea\n */\n\nfunction getIntersectionPointsByConstants(linearConstants1, linearConstants2) {\n  var a1 = linearConstants1[0],\n      b1 = linearConstants1[1],\n      c1 = linearConstants1[2];\n  var a2 = linearConstants2[0],\n      b2 = linearConstants2[1],\n      c2 = linearConstants2[2];\n  var isZeroA = a1 === 0 && a2 === 0;\n  var isZeroB = b1 === 0 && b2 === 0;\n  var results = [];\n\n  if (isZeroA && isZeroB) {\n    return [];\n  } else if (isZeroA) {\n    // b1 * y + c1 = 0\n    // b2 * y + c2 = 0\n    var y1 = -c1 / b1;\n    var y2 = -c2 / b2;\n\n    if (y1 !== y2) {\n      return [];\n    } else {\n      return [[-Infinity, y1], [Infinity, y1]];\n    }\n  } else if (isZeroB) {\n    // a1 * x + c1 = 0\n    // a2 * x + c2 = 0\n    var x1 = -c1 / a1;\n    var x2 = -c2 / a2;\n\n    if (x1 !== x2) {\n      return [];\n    } else {\n      return [[x1, -Infinity], [x1, Infinity]];\n    }\n  } else if (a1 === 0) {\n    // b1 * y + c1 = 0\n    // y = - c1 / b1;\n    // a2 * x + b2 * y + c2 = 0\n    var y = -c1 / b1;\n    var x = -(b2 * y + c2) / a2;\n    results = [[x, y]];\n  } else if (a2 === 0) {\n    // b2 * y + c2 = 0\n    // y = - c2 / b2;\n    // a1 * x + b1 * y + c1 = 0\n    var y = -c2 / b2;\n    var x = -(b1 * y + c1) / a1;\n    results = [[x, y]];\n  } else if (b1 === 0) {\n    // a1 * x + c1 = 0\n    // x = - c1 / a1;\n    // a2 * x + b2 * y + c2 = 0\n    var x = -c1 / a1;\n    var y = -(a2 * x + c2) / b2;\n    results = [[x, y]];\n  } else if (b2 === 0) {\n    // a2 * x + c2 = 0\n    // x = - c2 / a2;\n    // a1 * x + b1 * y + c1 = 0\n    var x = -c2 / a2;\n    var y = -(a1 * x + c1) / b1;\n    results = [[x, y]];\n  } else {\n    // a1 * x + b1 * y + c1 = 0\n    // a2 * x + b2 * y + c2 = 0\n    // b2 * a1 * x + b2 * b1 * y + b2 * c1 = 0\n    // b1 * a2 * x + b1 * b2 * y + b1 * c2 = 0\n    // (b2 * a1 - b1 * a2)  * x = (b1 * c2 - b2 * c1)\n    var x = (b1 * c2 - b2 * c1) / (b2 * a1 - b1 * a2);\n    var y = -(a1 * x + c1) / b1;\n    results = [[x, y]];\n  }\n\n  return results.map(function (result) {\n    return [result[0], result[1]];\n  });\n}\n/**\n * Get intersection points to the two lines.\n * @memberof OverlapArea\n */\n\nfunction getIntersectionPoints(line1, line2, isLimit) {\n  var points = getIntersectionPointsByConstants(getLinearConstants(line1[0], line1[1]), getLinearConstants(line2[0], line2[1]));\n\n  if (isLimit) {\n    return getPointsOnLines(points, [line1, line2]);\n  }\n\n  return points;\n}\nfunction isPointOnLine(pos, line) {\n  var linearConstants = getLinearConstants(line[0], line[1]);\n  return tinyThrottle(getDistanceFromPointToConstants(linearConstants, pos)) === 0;\n}\n/**\n * Get the points on the lines (between two points).\n * @memberof OverlapArea\n */\n\nfunction getPointsOnLines(points, lines) {\n  var minMaxs = lines.map(function (line) {\n    return [0, 1].map(function (order) {\n      return [Math.min(line[0][order], line[1][order]), Math.max(line[0][order], line[1][order])];\n    });\n  });\n  var results = [];\n\n  if (points.length === 2) {\n    var _a = points[0],\n        x = _a[0],\n        y = _a[1];\n\n    if (!tinyThrottle(x - points[1][0])) {\n      /// Math.max(minY1, minY2)\n      var top = Math.max.apply(Math, minMaxs.map(function (minMax) {\n        return minMax[1][0];\n      })); /// Math.min(maxY1, miax2)\n\n      var bottom = Math.min.apply(Math, minMaxs.map(function (minMax) {\n        return minMax[1][1];\n      }));\n\n      if (tinyThrottle(top - bottom) > 0) {\n        return [];\n      }\n\n      results = [[x, top], [x, bottom]];\n    } else if (!tinyThrottle(y - points[1][1])) {\n      /// Math.max(minY1, minY2)\n      var left = Math.max.apply(Math, minMaxs.map(function (minMax) {\n        return minMax[0][0];\n      })); /// Math.min(maxY1, miax2)\n\n      var right = Math.min.apply(Math, minMaxs.map(function (minMax) {\n        return minMax[0][1];\n      }));\n\n      if (tinyThrottle(left - right) > 0) {\n        return [];\n      }\n\n      results = [[left, y], [right, y]];\n    }\n  }\n\n  if (!results.length) {\n    results = points.filter(function (point) {\n      var pointX = point[0],\n          pointY = point[1];\n      return minMaxs.every(function (minMax) {\n        return 0 <= tinyThrottle(pointX - minMax[0][0]) && 0 <= tinyThrottle(minMax[0][1] - pointX) && 0 <= tinyThrottle(pointY - minMax[1][0]) && 0 <= tinyThrottle(minMax[1][1] - pointY);\n      });\n    });\n  }\n\n  return results.map(function (result) {\n    return [tinyThrottle(result[0]), tinyThrottle(result[1])];\n  });\n}\n/**\n* Convert two points into lines.\n* @function\n* @memberof OverlapArea\n*/\n\nfunction convertLines(points) {\n  return __spreadArrays(points.slice(1), [points[0]]).map(function (point, i) {\n    return [points[i], point];\n  });\n}\n\nfunction getOverlapPointInfos(points1, points2) {\n  var targetPoints1 = points1.slice();\n  var targetPoints2 = points2.slice();\n\n  if ((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getShapeDirection)(targetPoints1) === -1) {\n    targetPoints1.reverse();\n  }\n\n  if ((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getShapeDirection)(targetPoints2) === -1) {\n    targetPoints2.reverse();\n  }\n\n  var lines1 = convertLines(targetPoints1);\n  var lines2 = convertLines(targetPoints2);\n  var linearConstantsList1 = lines1.map(function (line1) {\n    return getLinearConstants(line1[0], line1[1]);\n  });\n  var linearConstantsList2 = lines2.map(function (line2) {\n    return getLinearConstants(line2[0], line2[1]);\n  });\n  var overlapInfos = [];\n  linearConstantsList1.forEach(function (linearConstants1, i) {\n    var line1 = lines1[i];\n    var linePointInfos = [];\n    linearConstantsList2.forEach(function (linearConstants2, j) {\n      var intersectionPoints = getIntersectionPointsByConstants(linearConstants1, linearConstants2);\n      var points = getPointsOnLines(intersectionPoints, [line1, lines2[j]]);\n      linePointInfos.push.apply(linePointInfos, points.map(function (pos) {\n        return {\n          index1: i,\n          index2: j,\n          pos: pos,\n          type: \"intersection\"\n        };\n      }));\n    });\n    linePointInfos.sort(function (a, b) {\n      return (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getDist)(line1[0], a.pos) - (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getDist)(line1[0], b.pos);\n    });\n    overlapInfos.push.apply(overlapInfos, linePointInfos);\n\n    if (isInside(line1[1], targetPoints2)) {\n      overlapInfos.push({\n        index1: i,\n        index2: -1,\n        pos: line1[1],\n        type: \"inside\"\n      });\n    }\n  });\n  lines2.forEach(function (line2, i) {\n    if (!isInside(line2[1], targetPoints1)) {\n      return;\n    }\n\n    var isNext = false;\n    var index = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.findIndex)(overlapInfos, function (_a) {\n      var index2 = _a.index2;\n\n      if (index2 === i) {\n        isNext = true;\n        return false;\n      }\n\n      if (isNext) {\n        return true;\n      }\n\n      return false;\n    });\n\n    if (index === -1) {\n      isNext = false;\n      index = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.findIndex)(overlapInfos, function (_a) {\n        var index1 = _a.index1,\n            index2 = _a.index2;\n\n        if (index1 === -1 && index2 + 1 === i) {\n          isNext = true;\n          return false;\n        }\n\n        if (isNext) {\n          return true;\n        }\n\n        return false;\n      });\n    }\n\n    if (index === -1) {\n      overlapInfos.push({\n        index1: -1,\n        index2: i,\n        pos: line2[1],\n        type: \"inside\"\n      });\n    } else {\n      overlapInfos.splice(index, 0, {\n        index1: -1,\n        index2: i,\n        pos: line2[1],\n        type: \"inside\"\n      });\n    }\n  });\n  var pointMap = {};\n  return overlapInfos.filter(function (_a) {\n    var pos = _a.pos;\n    var key = pos[0] + \"x\" + pos[1];\n\n    if (pointMap[key]) {\n      return false;\n    }\n\n    pointMap[key] = true;\n    return true;\n  });\n}\n/**\n* Get the points of the overlapped part of two shapes.\n* @function\n* @memberof OverlapArea\n*/\n\n\nfunction getOverlapPoints(points1, points2) {\n  var infos = getOverlapPointInfos(points1, points2);\n  return infos.map(function (_a) {\n    var pos = _a.pos;\n    return pos;\n  });\n}\n\nfunction isConnectedLine(line) {\n  var _a = line[0],\n      prevIndex1 = _a.index1,\n      prevIndex2 = _a.index2,\n      _b = line[1],\n      nextIndex1 = _b.index1,\n      nextIndex2 = _b.index2;\n\n  if (prevIndex1 !== -1) {\n    // same line\n    if (prevIndex1 === nextIndex1) {\n      return true;\n    }\n\n    if (prevIndex1 + 1 === nextIndex1) {\n      return true;\n    }\n  }\n\n  if (prevIndex2 !== -1) {\n    // same line\n    if (prevIndex2 === nextIndex2) {\n      return true;\n    }\n\n    if (prevIndex2 + 1 === nextIndex2) {\n      return true;\n    }\n  }\n\n  return false;\n}\n/**\n* Get the areas of the overlapped part of two shapes.\n* @function\n* @memberof OverlapArea\n*/\n\n\nfunction getOverlapAreas(points1, points2) {\n  var infos = getOverlapPointInfos(points1, points2);\n  var areas = [];\n  var area;\n  getOverlapPointInfos(points1, points2).forEach(function (info, i, arr) {\n    if (i === 0 || !isConnectedLine([arr[i - 1], info])) {\n      area = [info];\n      areas.push(area);\n    } else {\n      area.push(info);\n    }\n  });\n  return areas.map(function (area) {\n    return area.map(function (_a) {\n      var pos = _a.pos;\n      return pos;\n    });\n  });\n}\n\nfunction findReversedAreas(points1, points2, index, areas) {\n  if (index === void 0) {\n    index = 0;\n  }\n\n  if (areas === void 0) {\n    areas = [];\n  }\n\n  var isFirst = areas.length === 0;\n  var length = points1.length;\n  var nextIndex = points1[index] ? index : 0;\n\n  var nextPoints1 = __spreadArrays(points1.slice(nextIndex), points1.slice(0, nextIndex));\n\n  var _loop_1 = function (i) {\n    var point1 = nextPoints1[i];\n\n    if ((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.find)(points2, function (point2) {\n      return point2[0] === point1[0] && point2[1] === point1[1];\n    })) {\n      return \"continue\";\n    }\n\n    if (areas.some(function (nextArea) {\n      return (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.find)(nextArea, function (areaPoint) {\n        return areaPoint[0] === point1[0] && areaPoint[1] === point1[1];\n      });\n    })) {\n      if (isFirst) {\n        return \"continue\";\n      } else {\n        return \"break\";\n      }\n    }\n\n    var nextArea = void 0;\n\n    if (isFirst) {\n      nextArea = [];\n      areas.push(nextArea);\n    } else {\n      nextArea = areas[areas.length - 1];\n    }\n\n    nextArea.push(point1);\n    var line = [point1, points1[index + 1] || points1[0]];\n    var nextPoint2 = points2.filter(function (point2) {\n      return isPointOnLine(point2, line);\n    }).sort(function (a, b) {\n      return (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getDist)(point1, a) - (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getDist)(point1, b);\n    })[0];\n\n    if (!nextPoint2) {\n      findReversedAreas(nextPoints1, points2, i + 1, areas);\n      return \"break\";\n    } else {\n      var point2Index = points2.indexOf(nextPoint2);\n      findReversedAreas(points2, points1, point2Index, areas);\n\n      if (!isFirst) {\n        return \"break\";\n      }\n    }\n  };\n\n  for (var i = 0; i < length; ++i) {\n    var state_1 = _loop_1(i);\n\n    if (state_1 === \"break\") break;\n  }\n\n  return areas;\n}\n\nfunction findConnectedAreas(points1, points2) {\n  return findReversedAreas(points1, __spreadArrays(points2).reverse());\n}\n/**\n* Get non-overlapping areas of two shapes based on points1.\n* @memberof OverlapArea\n*/\n\nfunction getUnoverlapAreas(points1, points2) {\n  if (!points2.length) {\n    return [__spreadArrays(points1)];\n  }\n\n  var overlapAreas = getOverlapAreas(points1, points2);\n  var unoverlapAreas = [points1];\n  overlapAreas.forEach(function (overlapArea) {\n    var nextOverlapArea = __spreadArrays(overlapArea).reverse();\n\n    unoverlapAreas = flat(unoverlapAreas.map(function (area) {\n      var connectedAreas = findReversedAreas(area, nextOverlapArea);\n      var firstConnectedArea = connectedAreas[0];\n\n      if (connectedAreas.length === 1 && nextOverlapArea.every(function (point) {\n        return firstConnectedArea.indexOf(point) === -1;\n      })) {\n        var lastPoint_1 = firstConnectedArea[firstConnectedArea.length - 1];\n\n        var firstPoint = __spreadArrays(nextOverlapArea).sort(function (a, b) {\n          return (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getDist)(lastPoint_1, a) - (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getDist)(lastPoint_1, b);\n        })[0];\n\n        var firstIndex = nextOverlapArea.indexOf(firstPoint);\n        firstConnectedArea.push.apply(firstConnectedArea, __spreadArrays(nextOverlapArea.slice(firstIndex), nextOverlapArea.slice(0, firstIndex), [nextOverlapArea[firstIndex], lastPoint_1]));\n      }\n\n      return connectedAreas;\n    }));\n  });\n  return unoverlapAreas;\n}\n/**\n* Gets the size of the overlapped part of two shapes.\n* @function\n* @memberof OverlapArea\n*/\n\nfunction getOverlapSize(points1, points2) {\n  var points = getOverlapPoints(points1, points2);\n  return getAreaSize(points);\n}\n\n\n//# sourceMappingURL=overlap-area.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/overlap-area@1.1.0/node_modules/overlap-area/dist/overlap-area.esm.js\n");

/***/ })

};
;