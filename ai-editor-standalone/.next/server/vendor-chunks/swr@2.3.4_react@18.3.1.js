"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/swr@2.3.4_react@18.3.1";
exports.ids = ["vendor-chunks/swr@2.3.4_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/_internal/config-context-client-BoS53ST9.mjs":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/_internal/config-context-client-BoS53ST9.mjs ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (/* binding */ noop),\n/* harmony export */   B: () => (/* binding */ isPromiseLike),\n/* harmony export */   I: () => (/* binding */ IS_REACT_LEGACY),\n/* harmony export */   O: () => (/* binding */ OBJECT),\n/* harmony export */   S: () => (/* binding */ SWRConfigContext),\n/* harmony export */   U: () => (/* binding */ UNDEFINED),\n/* harmony export */   a: () => (/* binding */ isFunction),\n/* harmony export */   b: () => (/* binding */ SWRGlobalState),\n/* harmony export */   c: () => (/* binding */ cache),\n/* harmony export */   d: () => (/* binding */ defaultConfig),\n/* harmony export */   e: () => (/* binding */ isUndefined),\n/* harmony export */   f: () => (/* binding */ mergeConfigs),\n/* harmony export */   g: () => (/* binding */ SWRConfig),\n/* harmony export */   h: () => (/* binding */ initCache),\n/* harmony export */   i: () => (/* binding */ isWindowDefined),\n/* harmony export */   j: () => (/* binding */ mutate),\n/* harmony export */   k: () => (/* binding */ compare),\n/* harmony export */   l: () => (/* binding */ stableHash),\n/* harmony export */   m: () => (/* binding */ mergeObjects),\n/* harmony export */   n: () => (/* binding */ internalMutate),\n/* harmony export */   o: () => (/* binding */ getTimestamp),\n/* harmony export */   p: () => (/* binding */ preset),\n/* harmony export */   q: () => (/* binding */ defaultConfigOptions),\n/* harmony export */   r: () => (/* binding */ IS_SERVER),\n/* harmony export */   s: () => (/* binding */ serialize),\n/* harmony export */   t: () => (/* binding */ rAF),\n/* harmony export */   u: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   v: () => (/* binding */ slowConnection),\n/* harmony export */   w: () => (/* binding */ isDocumentDefined),\n/* harmony export */   x: () => (/* binding */ isLegacyDeno),\n/* harmony export */   y: () => (/* binding */ hasRequestAnimationFrame),\n/* harmony export */   z: () => (/* binding */ createCacheHelper)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _events_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./events.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/_internal/events.mjs\");\n/* harmony import */ var dequal_lite__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dequal/lite */ \"(ssr)/./node_modules/.pnpm/dequal@2.0.3/node_modules/dequal/lite/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ A,B,I,O,S,U,a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z auto */ \n\n\n// Global state used to deduplicate requests and store listeners\nconst SWRGlobalState = new WeakMap();\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\nconst mergeObjects = (a, b)=>({\n        ...a,\n        ...b\n    });\nconst isPromiseLike = (x)=>isFunction(x.then);\nconst EMPTY_CACHE = {};\nconst INITIAL_CACHE = {};\nconst STR_UNDEFINED = 'undefined';\n// NOTE: Use the function to guarantee it's re-evaluated between jsdom and node runtime for tests.\nconst isWindowDefined = \"undefined\" != STR_UNDEFINED;\nconst isDocumentDefined = typeof document != STR_UNDEFINED;\nconst isLegacyDeno = isWindowDefined && 'Deno' in window;\nconst hasRequestAnimationFrame = ()=>isWindowDefined && typeof window['requestAnimationFrame'] != STR_UNDEFINED;\nconst createCacheHelper = (cache, key)=>{\n    const state = SWRGlobalState.get(cache);\n    return [\n        // Getter\n        ()=>!isUndefined(key) && cache.get(key) || EMPTY_CACHE,\n        // Setter\n        (info)=>{\n            if (!isUndefined(key)) {\n                const prev = cache.get(key);\n                // Before writing to the store, we keep the value in the initial cache\n                // if it's not there yet.\n                if (!(key in INITIAL_CACHE)) {\n                    INITIAL_CACHE[key] = prev;\n                }\n                state[5](key, mergeObjects(prev, info), prev || EMPTY_CACHE);\n            }\n        },\n        // Subscriber\n        state[6],\n        // Get server cache snapshot\n        ()=>{\n            if (!isUndefined(key)) {\n                // If the cache was updated on the client, we return the stored initial value.\n                if (key in INITIAL_CACHE) return INITIAL_CACHE[key];\n            }\n            // If we haven't done any client-side updates, we return the current value.\n            return !isUndefined(key) && cache.get(key) || EMPTY_CACHE;\n        }\n    ];\n} // export { UNDEFINED, OBJECT, isUndefined, isFunction, mergeObjects, isPromiseLike }\n;\n/**\n * Due to the bug https://bugs.chromium.org/p/chromium/issues/detail?id=678075,\n * it's not reliable to detect if the browser is currently online or offline\n * based on `navigator.onLine`.\n * As a workaround, we always assume it's online on the first load, and change\n * the status upon `online` or `offline` events.\n */ let online = true;\nconst isOnline = ()=>online;\n// For node and React Native, `add/removeEventListener` doesn't exist on window.\nconst [onWindowEvent, offWindowEvent] = isWindowDefined && window.addEventListener ? [\n    window.addEventListener.bind(window),\n    window.removeEventListener.bind(window)\n] : [\n    noop,\n    noop\n];\nconst isVisible = ()=>{\n    const visibilityState = isDocumentDefined && document.visibilityState;\n    return isUndefined(visibilityState) || visibilityState !== 'hidden';\n};\nconst initFocus = (callback)=>{\n    // focus revalidate\n    if (isDocumentDefined) {\n        document.addEventListener('visibilitychange', callback);\n    }\n    onWindowEvent('focus', callback);\n    return ()=>{\n        if (isDocumentDefined) {\n            document.removeEventListener('visibilitychange', callback);\n        }\n        offWindowEvent('focus', callback);\n    };\n};\nconst initReconnect = (callback)=>{\n    // revalidate on reconnected\n    const onOnline = ()=>{\n        online = true;\n        callback();\n    };\n    // nothing to revalidate, just update the status\n    const onOffline = ()=>{\n        online = false;\n    };\n    onWindowEvent('online', onOnline);\n    onWindowEvent('offline', onOffline);\n    return ()=>{\n        offWindowEvent('online', onOnline);\n        offWindowEvent('offline', onOffline);\n    };\n};\nconst preset = {\n    isOnline,\n    isVisible\n};\nconst defaultConfigOptions = {\n    initFocus,\n    initReconnect\n};\nconst IS_REACT_LEGACY = !react__WEBPACK_IMPORTED_MODULE_0__.useId;\nconst IS_SERVER = !isWindowDefined || isLegacyDeno;\n// Polyfill requestAnimationFrame\nconst rAF = (f)=>hasRequestAnimationFrame() ? window['requestAnimationFrame'](f) : setTimeout(f, 1);\n// React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\nconst useIsomorphicLayoutEffect = IS_SERVER ? react__WEBPACK_IMPORTED_MODULE_0__.useEffect : react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect;\n// This assignment is to extend the Navigator type to use effectiveType.\nconst navigatorConnection = typeof navigator !== 'undefined' && navigator.connection;\n// Adjust the config based on slow connection status (<= 70Kbps).\nconst slowConnection = !IS_SERVER && navigatorConnection && ([\n    'slow-2g',\n    '2g'\n].includes(navigatorConnection.effectiveType) || navigatorConnection.saveData);\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst getTypeName = (value)=>OBJECT.prototype.toString.call(value);\nconst isObjectTypeName = (typeName, type)=>typeName === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const typeName = getTypeName(arg);\n    const isDate = isObjectTypeName(typeName, 'Date');\n    const isRegex = isObjectTypeName(typeName, 'RegExp');\n    const isPlainObject = isObjectTypeName(typeName, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n// Global timestamp.\nlet __timestamp = 0;\nconst getTimestamp = ()=>++__timestamp;\nasync function internalMutate(...args) {\n    const [cache, _key, _data, _opts] = args;\n    // When passing as a boolean, it's explicitly used to disable/enable\n    // revalidation.\n    const options = mergeObjects({\n        populateCache: true,\n        throwOnError: true\n    }, typeof _opts === 'boolean' ? {\n        revalidate: _opts\n    } : _opts || {});\n    let populateCache = options.populateCache;\n    const rollbackOnErrorOption = options.rollbackOnError;\n    let optimisticData = options.optimisticData;\n    const rollbackOnError = (error)=>{\n        return typeof rollbackOnErrorOption === 'function' ? rollbackOnErrorOption(error) : rollbackOnErrorOption !== false;\n    };\n    const throwOnError = options.throwOnError;\n    // If the second argument is a key filter, return the mutation results for all\n    // filtered keys.\n    if (isFunction(_key)) {\n        const keyFilter = _key;\n        const matchedKeys = [];\n        const it = cache.keys();\n        for (const key of it){\n            if (!/^\\$(inf|sub)\\$/.test(key) && keyFilter(cache.get(key)._k)) {\n                matchedKeys.push(key);\n            }\n        }\n        return Promise.all(matchedKeys.map(mutateByKey));\n    }\n    return mutateByKey(_key);\n    async function mutateByKey(_k) {\n        // Serialize key\n        const [key] = serialize(_k);\n        if (!key) return;\n        const [get, set] = createCacheHelper(cache, key);\n        const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n        const startRevalidate = ()=>{\n            const revalidators = EVENT_REVALIDATORS[key];\n            const revalidate = isFunction(options.revalidate) ? options.revalidate(get().data, _k) : options.revalidate !== false;\n            if (revalidate) {\n                // Invalidate the key by deleting the concurrent request markers so new\n                // requests will not be deduped.\n                delete FETCH[key];\n                delete PRELOAD[key];\n                if (revalidators && revalidators[0]) {\n                    return revalidators[0](_events_mjs__WEBPACK_IMPORTED_MODULE_2__.MUTATE_EVENT).then(()=>get().data);\n                }\n            }\n            return get().data;\n        };\n        // If there is no new data provided, revalidate the key with current state.\n        if (args.length < 3) {\n            // Revalidate and broadcast state.\n            return startRevalidate();\n        }\n        let data = _data;\n        let error;\n        let isError = false;\n        // Update global timestamps.\n        const beforeMutationTs = getTimestamp();\n        MUTATION[key] = [\n            beforeMutationTs,\n            0\n        ];\n        const hasOptimisticData = !isUndefined(optimisticData);\n        const state = get();\n        // `displayedData` is the current value on screen. It could be the optimistic value\n        // that is going to be overridden by a `committedData`, or get reverted back.\n        // `committedData` is the validated value that comes from a fetch or mutation.\n        const displayedData = state.data;\n        const currentData = state._c;\n        const committedData = isUndefined(currentData) ? displayedData : currentData;\n        // Do optimistic data update.\n        if (hasOptimisticData) {\n            optimisticData = isFunction(optimisticData) ? optimisticData(committedData, displayedData) : optimisticData;\n            // When we set optimistic data, backup the current committedData data in `_c`.\n            set({\n                data: optimisticData,\n                _c: committedData\n            });\n        }\n        if (isFunction(data)) {\n            // `data` is a function, call it passing current cache value.\n            try {\n                data = data(committedData);\n            } catch (err) {\n                // If it throws an error synchronously, we shouldn't update the cache.\n                error = err;\n                isError = true;\n            }\n        }\n        // `data` is a promise/thenable, resolve the final data first.\n        if (data && isPromiseLike(data)) {\n            // This means that the mutation is async, we need to check timestamps to\n            // avoid race conditions.\n            data = await data.catch((err)=>{\n                error = err;\n                isError = true;\n            });\n            // Check if other mutations have occurred since we've started this mutation.\n            // If there's a race we don't update cache or broadcast the change,\n            // just return the data.\n            if (beforeMutationTs !== MUTATION[key][0]) {\n                if (isError) throw error;\n                return data;\n            } else if (isError && hasOptimisticData && rollbackOnError(error)) {\n                // Rollback. Always populate the cache in this case but without\n                // transforming the data.\n                populateCache = true;\n                // Reset data to be the latest committed data, and clear the `_c` value.\n                set({\n                    data: committedData,\n                    _c: UNDEFINED\n                });\n            }\n        }\n        // If we should write back the cache after request.\n        if (populateCache) {\n            if (!isError) {\n                // Transform the result into data.\n                if (isFunction(populateCache)) {\n                    const populateCachedData = populateCache(data, committedData);\n                    set({\n                        data: populateCachedData,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                } else {\n                    // Only update cached data and reset the error if there's no error. Data can be `undefined` here.\n                    set({\n                        data,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                }\n            }\n        }\n        // Reset the timestamp to mark the mutation has ended.\n        MUTATION[key][1] = getTimestamp();\n        // Update existing SWR Hooks' internal states:\n        Promise.resolve(startRevalidate()).then(()=>{\n            // The mutation and revalidation are ended, we can clear it since the data is\n            // not an optimistic value anymore.\n            set({\n                _c: UNDEFINED\n            });\n        });\n        // Throw error or return data\n        if (isError) {\n            if (throwOnError) throw error;\n            return;\n        }\n        return data;\n    }\n}\nconst revalidateAllKeys = (revalidators, type)=>{\n    for(const key in revalidators){\n        if (revalidators[key][0]) revalidators[key][0](type);\n    }\n};\nconst initCache = (provider, options)=>{\n    // The global state for a specific provider will be used to deduplicate\n    // requests and store listeners. As well as a mutate function that is bound to\n    // the cache.\n    // The provider's global state might be already initialized. Let's try to get the\n    // global state associated with the provider first.\n    if (!SWRGlobalState.has(provider)) {\n        const opts = mergeObjects(defaultConfigOptions, options);\n        // If there's no global state bound to the provider, create a new one with the\n        // new mutate function.\n        const EVENT_REVALIDATORS = Object.create(null);\n        const mutate = internalMutate.bind(UNDEFINED, provider);\n        let unmount = noop;\n        const subscriptions = Object.create(null);\n        const subscribe = (key, callback)=>{\n            const subs = subscriptions[key] || [];\n            subscriptions[key] = subs;\n            subs.push(callback);\n            return ()=>subs.splice(subs.indexOf(callback), 1);\n        };\n        const setter = (key, value, prev)=>{\n            provider.set(key, value);\n            const subs = subscriptions[key];\n            if (subs) {\n                for (const fn of subs){\n                    fn(value, prev);\n                }\n            }\n        };\n        const initProvider = ()=>{\n            if (!SWRGlobalState.has(provider)) {\n                // Update the state if it's new, or if the provider has been extended.\n                SWRGlobalState.set(provider, [\n                    EVENT_REVALIDATORS,\n                    Object.create(null),\n                    Object.create(null),\n                    Object.create(null),\n                    mutate,\n                    setter,\n                    subscribe\n                ]);\n                if (!IS_SERVER) {\n                    // When listening to the native events for auto revalidations,\n                    // we intentionally put a delay (setTimeout) here to make sure they are\n                    // fired after immediate JavaScript executions, which can be\n                    // React's state updates.\n                    // This avoids some unnecessary revalidations such as\n                    // https://github.com/vercel/swr/issues/1680.\n                    const releaseFocus = opts.initFocus(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, _events_mjs__WEBPACK_IMPORTED_MODULE_2__.FOCUS_EVENT)));\n                    const releaseReconnect = opts.initReconnect(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, _events_mjs__WEBPACK_IMPORTED_MODULE_2__.RECONNECT_EVENT)));\n                    unmount = ()=>{\n                        releaseFocus && releaseFocus();\n                        releaseReconnect && releaseReconnect();\n                        // When un-mounting, we need to remove the cache provider from the state\n                        // storage too because it's a side-effect. Otherwise, when re-mounting we\n                        // will not re-register those event listeners.\n                        SWRGlobalState.delete(provider);\n                    };\n                }\n            }\n        };\n        initProvider();\n        // This is a new provider, we need to initialize it and setup DOM events\n        // listeners for `focus` and `reconnect` actions.\n        // We might want to inject an extra layer on top of `provider` in the future,\n        // such as key serialization, auto GC, etc.\n        // For now, it's just a `Map` interface without any modifications.\n        return [\n            provider,\n            mutate,\n            initProvider,\n            unmount\n        ];\n    }\n    return [\n        provider,\n        SWRGlobalState.get(provider)[4]\n    ];\n};\n// error retry\nconst onErrorRetry = (_, __, config, revalidate, opts)=>{\n    const maxRetryCount = config.errorRetryCount;\n    const currentRetryCount = opts.retryCount;\n    // Exponential backoff\n    const timeout = ~~((Math.random() + 0.5) * (1 << (currentRetryCount < 8 ? currentRetryCount : 8))) * config.errorRetryInterval;\n    if (!isUndefined(maxRetryCount) && currentRetryCount > maxRetryCount) {\n        return;\n    }\n    setTimeout(revalidate, timeout, opts);\n};\nconst compare = dequal_lite__WEBPACK_IMPORTED_MODULE_1__.dequal;\n// Default cache provider\nconst [cache, mutate] = initCache(new Map());\n// Default config\nconst defaultConfig = mergeObjects({\n    // events\n    onLoadingSlow: noop,\n    onSuccess: noop,\n    onError: noop,\n    onErrorRetry,\n    onDiscarded: noop,\n    // switches\n    revalidateOnFocus: true,\n    revalidateOnReconnect: true,\n    revalidateIfStale: true,\n    shouldRetryOnError: true,\n    // timeouts\n    errorRetryInterval: slowConnection ? 10000 : 5000,\n    focusThrottleInterval: 5 * 1000,\n    dedupingInterval: 2 * 1000,\n    loadingTimeout: slowConnection ? 5000 : 3000,\n    // providers\n    compare,\n    isPaused: ()=>false,\n    cache,\n    mutate,\n    fallback: {}\n}, preset);\nconst mergeConfigs = (a, b)=>{\n    // Need to create a new object to avoid mutating the original here.\n    const v = mergeObjects(a, b);\n    // If two configs are provided, merge their `use` and `fallback` options.\n    if (b) {\n        const { use: u1, fallback: f1 } = a;\n        const { use: u2, fallback: f2 } = b;\n        if (u1 && u2) {\n            v.use = u1.concat(u2);\n        }\n        if (f1 && f2) {\n            v.fallback = mergeObjects(f1, f2);\n        }\n    }\n    return v;\n};\nconst SWRConfigContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({});\nconst SWRConfig = (props)=>{\n    const { value } = props;\n    const parentConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(SWRConfigContext);\n    const isFunctionalConfig = isFunction(value);\n    const config = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"SWRConfig.useMemo[config]\": ()=>isFunctionalConfig ? value(parentConfig) : value\n    }[\"SWRConfig.useMemo[config]\"], [\n        isFunctionalConfig,\n        parentConfig,\n        value\n    ]);\n    // Extend parent context values and middleware.\n    const extendedConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"SWRConfig.useMemo[extendedConfig]\": ()=>isFunctionalConfig ? config : mergeConfigs(parentConfig, config)\n    }[\"SWRConfig.useMemo[extendedConfig]\"], [\n        isFunctionalConfig,\n        parentConfig,\n        config\n    ]);\n    // Should not use the inherited provider.\n    const provider = config && config.provider;\n    // initialize the cache only on first access.\n    const cacheContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(UNDEFINED);\n    if (provider && !cacheContextRef.current) {\n        cacheContextRef.current = initCache(provider(extendedConfig.cache || cache), config);\n    }\n    const cacheContext = cacheContextRef.current;\n    // Override the cache if a new provider is given.\n    if (cacheContext) {\n        extendedConfig.cache = cacheContext[0];\n        extendedConfig.mutate = cacheContext[1];\n    }\n    // Unsubscribe events.\n    useIsomorphicLayoutEffect({\n        \"SWRConfig.useIsomorphicLayoutEffect\": ()=>{\n            if (cacheContext) {\n                cacheContext[2] && cacheContext[2]();\n                return cacheContext[3];\n            }\n        }\n    }[\"SWRConfig.useIsomorphicLayoutEffect\"], []);\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(SWRConfigContext.Provider, mergeObjects(props, {\n        value: extendedConfig\n    }));\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/_internal/config-context-client-BoS53ST9.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/_internal/constants.mjs":
/*!*************************************************************************************************!*\
  !*** ./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/_internal/constants.mjs ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INFINITE_PREFIX: () => (/* binding */ INFINITE_PREFIX)\n/* harmony export */ });\nconst INFINITE_PREFIX = '$inf$';\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vc3dyQDIuMy40X3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvc3dyL2Rpc3QvX2ludGVybmFsL2NvbnN0YW50cy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztBQUUyQiIsInNvdXJjZXMiOlsiL2hvbWUvbXV6aS9naXRodWIvZGVlci1mbG93L2FpLWVkaXRvci1zdGFuZGFsb25lL25vZGVfbW9kdWxlcy8ucG5wbS9zd3JAMi4zLjRfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9zd3IvZGlzdC9faW50ZXJuYWwvY29uc3RhbnRzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBJTkZJTklURV9QUkVGSVggPSAnJGluZiQnO1xuXG5leHBvcnQgeyBJTkZJTklURV9QUkVGSVggfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/_internal/constants.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/_internal/events.mjs":
/*!**********************************************************************************************!*\
  !*** ./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/_internal/events.mjs ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ERROR_REVALIDATE_EVENT: () => (/* binding */ ERROR_REVALIDATE_EVENT),\n/* harmony export */   FOCUS_EVENT: () => (/* binding */ FOCUS_EVENT),\n/* harmony export */   MUTATE_EVENT: () => (/* binding */ MUTATE_EVENT),\n/* harmony export */   RECONNECT_EVENT: () => (/* binding */ RECONNECT_EVENT)\n/* harmony export */ });\nconst FOCUS_EVENT = 0;\nconst RECONNECT_EVENT = 1;\nconst MUTATE_EVENT = 2;\nconst ERROR_REVALIDATE_EVENT = 3;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vc3dyQDIuMy40X3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvc3dyL2Rpc3QvX2ludGVybmFsL2V2ZW50cy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUU4RSIsInNvdXJjZXMiOlsiL2hvbWUvbXV6aS9naXRodWIvZGVlci1mbG93L2FpLWVkaXRvci1zdGFuZGFsb25lL25vZGVfbW9kdWxlcy8ucG5wbS9zd3JAMi4zLjRfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9zd3IvZGlzdC9faW50ZXJuYWwvZXZlbnRzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBGT0NVU19FVkVOVCA9IDA7XG5jb25zdCBSRUNPTk5FQ1RfRVZFTlQgPSAxO1xuY29uc3QgTVVUQVRFX0VWRU5UID0gMjtcbmNvbnN0IEVSUk9SX1JFVkFMSURBVEVfRVZFTlQgPSAzO1xuXG5leHBvcnQgeyBFUlJPUl9SRVZBTElEQVRFX0VWRU5ULCBGT0NVU19FVkVOVCwgTVVUQVRFX0VWRU5ULCBSRUNPTk5FQ1RfRVZFTlQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/_internal/events.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/_internal/index.mjs":
/*!*********************************************************************************************!*\
  !*** ./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/_internal/index.mjs ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INFINITE_PREFIX: () => (/* reexport safe */ _constants_mjs__WEBPACK_IMPORTED_MODULE_2__.INFINITE_PREFIX),\n/* harmony export */   IS_REACT_LEGACY: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.I),\n/* harmony export */   IS_SERVER: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.r),\n/* harmony export */   OBJECT: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.O),\n/* harmony export */   SWRConfig: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.g),\n/* harmony export */   SWRGlobalState: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.b),\n/* harmony export */   UNDEFINED: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.U),\n/* harmony export */   cache: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   compare: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.k),\n/* harmony export */   createCacheHelper: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.z),\n/* harmony export */   defaultConfig: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.d),\n/* harmony export */   defaultConfigOptions: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.q),\n/* harmony export */   getTimestamp: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.o),\n/* harmony export */   hasRequestAnimationFrame: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.y),\n/* harmony export */   initCache: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.h),\n/* harmony export */   internalMutate: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.n),\n/* harmony export */   isDocumentDefined: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.w),\n/* harmony export */   isFunction: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.a),\n/* harmony export */   isLegacyDeno: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.x),\n/* harmony export */   isPromiseLike: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.B),\n/* harmony export */   isUndefined: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.e),\n/* harmony export */   isWindowDefined: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.i),\n/* harmony export */   mergeConfigs: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.f),\n/* harmony export */   mergeObjects: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.m),\n/* harmony export */   mutate: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.j),\n/* harmony export */   noop: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.A),\n/* harmony export */   normalize: () => (/* binding */ normalize),\n/* harmony export */   preload: () => (/* binding */ preload),\n/* harmony export */   preset: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.p),\n/* harmony export */   rAF: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.t),\n/* harmony export */   revalidateEvents: () => (/* reexport module object */ _events_mjs__WEBPACK_IMPORTED_MODULE_1__),\n/* harmony export */   serialize: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.s),\n/* harmony export */   slowConnection: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.v),\n/* harmony export */   stableHash: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.l),\n/* harmony export */   subscribeCallback: () => (/* binding */ subscribeCallback),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* reexport safe */ _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.u),\n/* harmony export */   useSWRConfig: () => (/* binding */ useSWRConfig),\n/* harmony export */   withArgs: () => (/* binding */ withArgs),\n/* harmony export */   withMiddleware: () => (/* binding */ withMiddleware)\n/* harmony export */ });\n/* harmony import */ var _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config-context-client-BoS53ST9.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/_internal/config-context-client-BoS53ST9.mjs\");\n/* harmony import */ var _events_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./events.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/_internal/events.mjs\");\n/* harmony import */ var _constants_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/_internal/constants.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\n\n\n\n\n// @ts-expect-error\nconst enableDevtools = _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.i && window.__SWR_DEVTOOLS_USE__;\nconst use = enableDevtools ? window.__SWR_DEVTOOLS_USE__ : [];\nconst setupDevTools = ()=>{\n    if (enableDevtools) {\n        // @ts-expect-error\n        window.__SWR_DEVTOOLS_REACT__ = react__WEBPACK_IMPORTED_MODULE_3__;\n    }\n};\n\nconst normalize = (args)=>{\n    return (0,_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(args[1]) ? [\n        args[0],\n        args[1],\n        args[2] || {}\n    ] : [\n        args[0],\n        null,\n        (args[1] === null ? args[2] : args[1]) || {}\n    ];\n};\n\nconst useSWRConfig = ()=>{\n    return (0,_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.m)(_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.d, (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.S));\n};\n\nconst preload = (key_, fetcher)=>{\n    const [key, fnArg] = (0,_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.s)(key_);\n    const [, , , PRELOAD] = _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.b.get(_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.c);\n    // Prevent preload to be called multiple times before used.\n    if (PRELOAD[key]) return PRELOAD[key];\n    const req = fetcher(fnArg);\n    PRELOAD[key] = req;\n    return req;\n};\nconst middleware = (useSWRNext)=>(key_, fetcher_, config)=>{\n        // fetcher might be a sync function, so this should not be an async function\n        const fetcher = fetcher_ && ((...args)=>{\n            const [key] = (0,_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.s)(key_);\n            const [, , , PRELOAD] = _config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.b.get(_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.c);\n            if (key.startsWith(_constants_mjs__WEBPACK_IMPORTED_MODULE_2__.INFINITE_PREFIX)) {\n                // we want the infinite fetcher to be called.\n                // handling of the PRELOAD cache happens there.\n                return fetcher_(...args);\n            }\n            const req = PRELOAD[key];\n            if ((0,_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.e)(req)) return fetcher_(...args);\n            delete PRELOAD[key];\n            return req;\n        });\n        return useSWRNext(key_, fetcher, config);\n    };\n\nconst BUILT_IN_MIDDLEWARE = use.concat(middleware);\n\n// It's tricky to pass generic types as parameters, so we just directly override\n// the types here.\nconst withArgs = (hook)=>{\n    return function useSWRArgs(...args) {\n        // Get the default and inherited configuration.\n        const fallbackConfig = useSWRConfig();\n        // Normalize arguments.\n        const [key, fn, _config] = normalize(args);\n        // Merge configurations.\n        const config = (0,_config_context_client_BoS53ST9_mjs__WEBPACK_IMPORTED_MODULE_0__.f)(fallbackConfig, _config);\n        // Apply middleware\n        let next = hook;\n        const { use } = config;\n        const middleware = (use || []).concat(BUILT_IN_MIDDLEWARE);\n        for(let i = middleware.length; i--;){\n            next = middleware[i](next);\n        }\n        return next(key, fn || config.fetcher || null, config);\n    };\n};\n\n// Add a callback function to a list of keyed callback functions and return\n// the unsubscribe function.\nconst subscribeCallback = (key, callbacks, callback)=>{\n    const keyedRevalidators = callbacks[key] || (callbacks[key] = []);\n    keyedRevalidators.push(callback);\n    return ()=>{\n        const index = keyedRevalidators.indexOf(callback);\n        if (index >= 0) {\n            // O(1): faster than splice\n            keyedRevalidators[index] = keyedRevalidators[keyedRevalidators.length - 1];\n            keyedRevalidators.pop();\n        }\n    };\n};\n\n// Create a custom hook with a middleware\nconst withMiddleware = (useSWR, middleware)=>{\n    return (...args)=>{\n        const [key, fn, config] = normalize(args);\n        const uses = (config.use || []).concat(middleware);\n        return useSWR(key, fn, {\n            ...config,\n            use: uses\n        });\n    };\n};\n\nsetupDevTools();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/_internal/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/index/index.mjs":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/index/index.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SWRConfig: () => (/* binding */ SWRConfig),\n/* harmony export */   \"default\": () => (/* binding */ useSWR),\n/* harmony export */   mutate: () => (/* reexport safe */ _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.j),\n/* harmony export */   preload: () => (/* reexport safe */ _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.preload),\n/* harmony export */   unstable_serialize: () => (/* binding */ unstable_serialize),\n/* harmony export */   useSWRConfig: () => (/* reexport safe */ _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.useSWRConfig)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-sync-external-store/shim/index.js */ \"(ssr)/./node_modules/.pnpm/use-sync-external-store@1.5.0_react@18.3.1/node_modules/use-sync-external-store/shim/index.js\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/_internal/config-context-client-BoS53ST9.mjs\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/_internal/events.mjs\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/_internal/index.mjs\");\n\n\n\n\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst getTypeName = (value)=>OBJECT.prototype.toString.call(value);\nconst isObjectTypeName = (typeName, type)=>typeName === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const typeName = getTypeName(arg);\n    const isDate = isObjectTypeName(typeName, 'Date');\n    const isRegex = isObjectTypeName(typeName, 'RegExp');\n    const isPlainObject = isObjectTypeName(typeName, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\nconst unstable_serialize = (key)=>serialize(key)[0];\n\n/// <reference types=\"react/experimental\" />\nconst use = react__WEBPACK_IMPORTED_MODULE_0__.use || // This extra generic is to avoid TypeScript mixing up the generic and JSX sytax\n// and emitting an error.\n// We assume that this is only for the `use(thenable)` case, not `use(context)`.\n// https://github.com/facebook/react/blob/aed00dacfb79d17c53218404c52b1c7aa59c4a89/packages/react-server/src/ReactFizzThenable.js#L45\n((thenable)=>{\n    switch(thenable.status){\n        case 'pending':\n            throw thenable;\n        case 'fulfilled':\n            return thenable.value;\n        case 'rejected':\n            throw thenable.reason;\n        default:\n            thenable.status = 'pending';\n            thenable.then((v)=>{\n                thenable.status = 'fulfilled';\n                thenable.value = v;\n            }, (e)=>{\n                thenable.status = 'rejected';\n                thenable.reason = e;\n            });\n            throw thenable;\n    }\n});\nconst WITH_DEDUPE = {\n    dedupe: true\n};\nconst useSWRHandler = (_key, fetcher, config)=>{\n    const { cache, compare, suspense, fallbackData, revalidateOnMount, revalidateIfStale, refreshInterval, refreshWhenHidden, refreshWhenOffline, keepPreviousData } = config;\n    const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.b.get(cache);\n    // `key` is the identifier of the SWR internal state,\n    // `fnArg` is the argument/arguments parsed from the key, which will be passed\n    // to the fetcher.\n    // All of them are derived from `_key`.\n    const [key, fnArg] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.s)(_key);\n    // If it's the initial render of this hook.\n    const initialMountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // If the hook is unmounted already. This will be used to prevent some effects\n    // to be called after unmounting.\n    const unmountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Refs to keep the key and config.\n    const keyRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(key);\n    const fetcherRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(fetcher);\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(config);\n    const getConfig = ()=>configRef.current;\n    const isActive = ()=>getConfig().isVisible() && getConfig().isOnline();\n    const [getCache, setCache, subscribeCache, getInitialCache] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.z)(cache, key);\n    const stateDependencies = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({}).current;\n    // Resolve the fallback data from either the inline option, or the global provider.\n    // If it's a promise, we simply let React suspend and resolve it for us.\n    const fallback = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(fallbackData) ? (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(config.fallback) ? _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.U : config.fallback[key] : fallbackData;\n    const isEqual = (prev, current)=>{\n        for(const _ in stateDependencies){\n            const t = _;\n            if (t === 'data') {\n                if (!compare(prev[t], current[t])) {\n                    if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(prev[t])) {\n                        return false;\n                    }\n                    if (!compare(returnedData, current[t])) {\n                        return false;\n                    }\n                }\n            } else {\n                if (current[t] !== prev[t]) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    };\n    const getSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const shouldStartRequest = (()=>{\n            if (!key) return false;\n            if (!fetcher) return false;\n            // If `revalidateOnMount` is set, we take the value directly.\n            if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(revalidateOnMount)) return revalidateOnMount;\n            // If it's paused, we skip revalidation.\n            if (getConfig().isPaused()) return false;\n            if (suspense) return false;\n            return revalidateIfStale !== false;\n        })();\n        // Get the cache and merge it with expected states.\n        const getSelectedCache = (state)=>{\n            // We only select the needed fields from the state.\n            const snapshot = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.m)(state);\n            delete snapshot._k;\n            if (!shouldStartRequest) {\n                return snapshot;\n            }\n            return {\n                isValidating: true,\n                isLoading: true,\n                ...snapshot\n            };\n        };\n        const cachedData = getCache();\n        const initialData = getInitialCache();\n        const clientSnapshot = getSelectedCache(cachedData);\n        const serverSnapshot = cachedData === initialData ? clientSnapshot : getSelectedCache(initialData);\n        // To make sure that we are returning the same object reference to avoid\n        // unnecessary re-renders, we keep the previous snapshot and use deep\n        // comparison to check if we need to return a new one.\n        let memorizedSnapshot = clientSnapshot;\n        return [\n            ()=>{\n                const newSnapshot = getSelectedCache(getCache());\n                const compareResult = isEqual(newSnapshot, memorizedSnapshot);\n                if (compareResult) {\n                    // Mentally, we should always return the `memorizedSnapshot` here\n                    // as there's no change between the new and old snapshots.\n                    // However, since the `isEqual` function only compares selected fields,\n                    // the values of the unselected fields might be changed. That's\n                    // simply because we didn't track them.\n                    // To support the case in https://github.com/vercel/swr/pull/2576,\n                    // we need to update these fields in the `memorizedSnapshot` too\n                    // with direct mutations to ensure the snapshot is always up-to-date\n                    // even for the unselected fields, but only trigger re-renders when\n                    // the selected fields are changed.\n                    memorizedSnapshot.data = newSnapshot.data;\n                    memorizedSnapshot.isLoading = newSnapshot.isLoading;\n                    memorizedSnapshot.isValidating = newSnapshot.isValidating;\n                    memorizedSnapshot.error = newSnapshot.error;\n                    return memorizedSnapshot;\n                } else {\n                    memorizedSnapshot = newSnapshot;\n                    return newSnapshot;\n                }\n            },\n            ()=>serverSnapshot\n        ];\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        cache,\n        key\n    ]);\n    // Get the current state that SWR should return.\n    const cached = (0,use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStore)((0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((callback)=>subscribeCache(key, (current, prev)=>{\n            if (!isEqual(prev, current)) callback();\n        }), // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        cache,\n        key\n    ]), getSnapshot[0], getSnapshot[1]);\n    const isInitialMount = !initialMountedRef.current;\n    const hasRevalidator = EVENT_REVALIDATORS[key] && EVENT_REVALIDATORS[key].length > 0;\n    const cachedData = cached.data;\n    const data = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cachedData) ? fallback && (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.B)(fallback) ? use(fallback) : fallback : cachedData;\n    const error = cached.error;\n    // Use a ref to store previously returned data. Use the initial data as its initial value.\n    const laggyDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(data);\n    const returnedData = keepPreviousData ? (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cachedData) ? (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(laggyDataRef.current) ? data : laggyDataRef.current : cachedData : data;\n    // - Suspense mode and there's stale data for the initial render.\n    // - Not suspense mode and there is no fallback data and `revalidateIfStale` is enabled.\n    // - `revalidateIfStale` is enabled but `data` is not defined.\n    const shouldDoInitialRevalidation = (()=>{\n        // if a key already has revalidators and also has error, we should not trigger revalidation\n        if (hasRevalidator && !(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(error)) return false;\n        // If `revalidateOnMount` is set, we take the value directly.\n        if (isInitialMount && !(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(revalidateOnMount)) return revalidateOnMount;\n        // If it's paused, we skip revalidation.\n        if (getConfig().isPaused()) return false;\n        // Under suspense mode, it will always fetch on render if there is no\n        // stale data so no need to revalidate immediately mount it again.\n        // If data exists, only revalidate if `revalidateIfStale` is true.\n        if (suspense) return (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) ? false : revalidateIfStale;\n        // If there is no stale data, we need to revalidate when mount;\n        // If `revalidateIfStale` is set to true, we will always revalidate.\n        return (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) || revalidateIfStale;\n    })();\n    // Resolve the default validating state:\n    // If it's able to validate, and it should revalidate when mount, this will be true.\n    const defaultValidatingState = !!(key && fetcher && isInitialMount && shouldDoInitialRevalidation);\n    const isValidating = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cached.isValidating) ? defaultValidatingState : cached.isValidating;\n    const isLoading = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cached.isLoading) ? defaultValidatingState : cached.isLoading;\n    // The revalidation function is a carefully crafted wrapper of the original\n    // `fetcher`, to correctly handle the many edge cases.\n    const revalidate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (revalidateOpts)=>{\n        const currentFetcher = fetcherRef.current;\n        if (!key || !currentFetcher || unmountedRef.current || getConfig().isPaused()) {\n            return false;\n        }\n        let newData;\n        let startAt;\n        let loading = true;\n        const opts = revalidateOpts || {};\n        // If there is no ongoing concurrent request, or `dedupe` is not set, a\n        // new request should be initiated.\n        const shouldStartNewRequest = !FETCH[key] || !opts.dedupe;\n        /*\n         For React 17\n         Do unmount check for calls:\n         If key has changed during the revalidation, or the component has been\n         unmounted, old dispatch and old event callbacks should not take any\n         effect\n\n        For React 18\n        only check if key has changed\n        https://github.com/reactwg/react-18/discussions/82\n      */ const callbackSafeguard = ()=>{\n            if (_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.I) {\n                return !unmountedRef.current && key === keyRef.current && initialMountedRef.current;\n            }\n            return key === keyRef.current;\n        };\n        // The final state object when the request finishes.\n        const finalState = {\n            isValidating: false,\n            isLoading: false\n        };\n        const finishRequestAndUpdateState = ()=>{\n            setCache(finalState);\n        };\n        const cleanupState = ()=>{\n            // Check if it's still the same request before deleting it.\n            const requestInfo = FETCH[key];\n            if (requestInfo && requestInfo[1] === startAt) {\n                delete FETCH[key];\n            }\n        };\n        // Start fetching. Change the `isValidating` state, update the cache.\n        const initialState = {\n            isValidating: true\n        };\n        // It is in the `isLoading` state, if and only if there is no cached data.\n        // This bypasses fallback data and laggy data.\n        if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(getCache().data)) {\n            initialState.isLoading = true;\n        }\n        try {\n            if (shouldStartNewRequest) {\n                setCache(initialState);\n                // If no cache is being rendered currently (it shows a blank page),\n                // we trigger the loading slow event.\n                if (config.loadingTimeout && (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(getCache().data)) {\n                    setTimeout(()=>{\n                        if (loading && callbackSafeguard()) {\n                            getConfig().onLoadingSlow(key, config);\n                        }\n                    }, config.loadingTimeout);\n                }\n                // Start the request and save the timestamp.\n                // Key must be truthy if entering here.\n                FETCH[key] = [\n                    currentFetcher(fnArg),\n                    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.o)()\n                ];\n            }\n            // Wait until the ongoing request is done. Deduplication is also\n            // considered here.\n            ;\n            [newData, startAt] = FETCH[key];\n            newData = await newData;\n            if (shouldStartNewRequest) {\n                // If the request isn't interrupted, clean it up after the\n                // deduplication interval.\n                setTimeout(cleanupState, config.dedupingInterval);\n            }\n            // If there're other ongoing request(s), started after the current one,\n            // we need to ignore the current one to avoid possible race conditions:\n            //   req1------------------>res1        (current one)\n            //        req2---------------->res2\n            // the request that fired later will always be kept.\n            // The timestamp maybe be `undefined` or a number\n            if (!FETCH[key] || FETCH[key][1] !== startAt) {\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Clear error.\n            finalState.error = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.U;\n            // If there're other mutations(s), that overlapped with the current revalidation:\n            // case 1:\n            //   req------------------>res\n            //       mutate------>end\n            // case 2:\n            //         req------------>res\n            //   mutate------>end\n            // case 3:\n            //   req------------------>res\n            //       mutate-------...---------->\n            // we have to ignore the revalidation result (res) because it's no longer fresh.\n            // meanwhile, a new revalidation should be triggered when the mutation ends.\n            const mutationInfo = MUTATION[key];\n            if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(mutationInfo) && // case 1\n            (startAt <= mutationInfo[0] || // case 2\n            startAt <= mutationInfo[1] || // case 3\n            mutationInfo[1] === 0)) {\n                finishRequestAndUpdateState();\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Deep compare with the latest state to avoid extra re-renders.\n            // For local state, compare and assign.\n            const cacheData = getCache().data;\n            // Since the compare fn could be custom fn\n            // cacheData might be different from newData even when compare fn returns True\n            finalState.data = compare(cacheData, newData) ? cacheData : newData;\n            // Trigger the successful callback if it's the original request.\n            if (shouldStartNewRequest) {\n                if (callbackSafeguard()) {\n                    getConfig().onSuccess(newData, key, config);\n                }\n            }\n        } catch (err) {\n            cleanupState();\n            const currentConfig = getConfig();\n            const { shouldRetryOnError } = currentConfig;\n            // Not paused, we continue handling the error. Otherwise, discard it.\n            if (!currentConfig.isPaused()) {\n                // Get a new error, don't use deep comparison for errors.\n                finalState.error = err;\n                // Error event and retry logic. Only for the actual request, not\n                // deduped ones.\n                if (shouldStartNewRequest && callbackSafeguard()) {\n                    currentConfig.onError(err, key, currentConfig);\n                    if (shouldRetryOnError === true || (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.a)(shouldRetryOnError) && shouldRetryOnError(err)) {\n                        if (!getConfig().revalidateOnFocus || !getConfig().revalidateOnReconnect || isActive()) {\n                            // If it's inactive, stop. It will auto-revalidate when\n                            // refocusing or reconnecting.\n                            // When retrying, deduplication is always enabled.\n                            currentConfig.onErrorRetry(err, key, currentConfig, (_opts)=>{\n                                const revalidators = EVENT_REVALIDATORS[key];\n                                if (revalidators && revalidators[0]) {\n                                    revalidators[0](_internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.ERROR_REVALIDATE_EVENT, _opts);\n                                }\n                            }, {\n                                retryCount: (opts.retryCount || 0) + 1,\n                                dedupe: true\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        // Mark loading as stopped.\n        loading = false;\n        // Update the current hook's state.\n        finishRequestAndUpdateState();\n        return true;\n    }, // `setState` is immutable, and `eventsCallback`, `fnArg`, and\n    // `keyValidating` are depending on `key`, so we can exclude them from\n    // the deps array.\n    //\n    // FIXME:\n    // `fn` and `config` might be changed during the lifecycle,\n    // but they might be changed every render like this.\n    // `useSWR('key', () => fetch('/api/'), { suspense: true })`\n    // So we omit the values from the deps array\n    // even though it might cause unexpected behaviors.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        key,\n        cache\n    ]);\n    // Similar to the global mutate but bound to the current cache and key.\n    // `cache` isn't allowed to change during the lifecycle.\n    const boundMutate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(// Use callback to make sure `keyRef.current` returns latest result every time\n    (...args)=>{\n        return (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.n)(cache, keyRef.current, ...args);\n    }, // eslint-disable-next-line react-hooks/exhaustive-deps\n    []);\n    // The logic for updating refs.\n    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.u)(()=>{\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        // Handle laggy data updates. If there's cached data of the current key,\n        // it'll be the correct reference.\n        if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cachedData)) {\n            laggyDataRef.current = cachedData;\n        }\n    });\n    // After mounted or key changed.\n    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.u)(()=>{\n        if (!key) return;\n        const softRevalidate = revalidate.bind(_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.U, WITH_DEDUPE);\n        let nextFocusRevalidatedAt = 0;\n        if (getConfig().revalidateOnFocus) {\n            const initNow = Date.now();\n            nextFocusRevalidatedAt = initNow + getConfig().focusThrottleInterval;\n        }\n        // Expose revalidators to global event listeners. So we can trigger\n        // revalidation from the outside.\n        const onRevalidate = (type, opts = {})=>{\n            if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.FOCUS_EVENT) {\n                const now = Date.now();\n                if (getConfig().revalidateOnFocus && now > nextFocusRevalidatedAt && isActive()) {\n                    nextFocusRevalidatedAt = now + getConfig().focusThrottleInterval;\n                    softRevalidate();\n                }\n            } else if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.RECONNECT_EVENT) {\n                if (getConfig().revalidateOnReconnect && isActive()) {\n                    softRevalidate();\n                }\n            } else if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.MUTATE_EVENT) {\n                return revalidate();\n            } else if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.ERROR_REVALIDATE_EVENT) {\n                return revalidate(opts);\n            }\n            return;\n        };\n        const unsubEvents = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.subscribeCallback)(key, EVENT_REVALIDATORS, onRevalidate);\n        // Mark the component as mounted and update corresponding refs.\n        unmountedRef.current = false;\n        keyRef.current = key;\n        initialMountedRef.current = true;\n        // Keep the original key in the cache.\n        setCache({\n            _k: fnArg\n        });\n        // Trigger a revalidation\n        if (shouldDoInitialRevalidation) {\n            // Performance optimization: if a request is already in progress for this key,\n            // skip the revalidation to avoid redundant work\n            if (!FETCH[key]) {\n                if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) || _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.r) {\n                    // Revalidate immediately.\n                    softRevalidate();\n                } else {\n                    // Delay the revalidate if we have data to return so we won't block\n                    // rendering.\n                    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.t)(softRevalidate);\n                }\n            }\n        }\n        return ()=>{\n            // Mark it as unmounted.\n            unmountedRef.current = true;\n            unsubEvents();\n        };\n    }, [\n        key\n    ]);\n    // Polling\n    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.u)(()=>{\n        let timer;\n        function next() {\n            // Use the passed interval\n            // ...or invoke the function with the updated data to get the interval\n            const interval = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.a)(refreshInterval) ? refreshInterval(getCache().data) : refreshInterval;\n            // We only start the next interval if `refreshInterval` is not 0, and:\n            // - `force` is true, which is the start of polling\n            // - or `timer` is not 0, which means the effect wasn't canceled\n            if (interval && timer !== -1) {\n                timer = setTimeout(execute, interval);\n            }\n        }\n        function execute() {\n            // Check if it's OK to execute:\n            // Only revalidate when the page is visible, online, and not errored.\n            if (!getCache().error && (refreshWhenHidden || getConfig().isVisible()) && (refreshWhenOffline || getConfig().isOnline())) {\n                revalidate(WITH_DEDUPE).then(next);\n            } else {\n                // Schedule the next interval to check again.\n                next();\n            }\n        }\n        next();\n        return ()=>{\n            if (timer) {\n                clearTimeout(timer);\n                timer = -1;\n            }\n        };\n    }, [\n        refreshInterval,\n        refreshWhenHidden,\n        refreshWhenOffline,\n        key\n    ]);\n    // Display debug info in React DevTools.\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue)(returnedData);\n    // In Suspense mode, we can't return the empty `data` state.\n    // If there is an `error`, the `error` needs to be thrown to the error boundary.\n    // If there is no `error`, the `revalidation` promise needs to be thrown to\n    // the suspense boundary.\n    if (suspense && (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) && key) {\n        // SWR should throw when trying to use Suspense on the server with React 18,\n        // without providing any fallback data. This causes hydration errors. See:\n        // https://github.com/vercel/swr/issues/1832\n        if (!_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.I && _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.r) {\n            throw new Error('Fallback data is required when using Suspense in SSR.');\n        }\n        // Always update fetcher and config refs even with the Suspense mode.\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        unmountedRef.current = false;\n        const req = PRELOAD[key];\n        if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(req)) {\n            const promise = boundMutate(req);\n            use(promise);\n        }\n        if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(error)) {\n            const promise = revalidate(WITH_DEDUPE);\n            if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(returnedData)) {\n                promise.status = 'fulfilled';\n                promise.value = true;\n            }\n            use(promise);\n        } else {\n            throw error;\n        }\n    }\n    const swrResponse = {\n        mutate: boundMutate,\n        get data () {\n            stateDependencies.data = true;\n            return returnedData;\n        },\n        get error () {\n            stateDependencies.error = true;\n            return error;\n        },\n        get isValidating () {\n            stateDependencies.isValidating = true;\n            return isValidating;\n        },\n        get isLoading () {\n            stateDependencies.isLoading = true;\n            return isLoading;\n        }\n    };\n    return swrResponse;\n};\nconst SWRConfig = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.O.defineProperty(_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.g, 'defaultValue', {\n    value: _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.d\n});\n/**\n * A hook to fetch data.\n *\n * @link https://swr.vercel.app\n * @example\n * ```jsx\n * import useSWR from 'swr'\n * function Profile() {\n *   const { data, error, isLoading } = useSWR('/api/user', fetcher)\n *   if (error) return <div>failed to load</div>\n *   if (isLoading) return <div>loading...</div>\n *   return <div>hello {data.name}!</div>\n * }\n * ```\n */ const useSWR = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.withArgs)(useSWRHandler);\n\n// useSWR\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/swr@2.3.4_react@18.3.1/node_modules/swr/dist/index/index.mjs\n");

/***/ })

};
;