"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-italic@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-italic@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-italic@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-italic/dist/index.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-italic@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-italic/dist/index.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Italic: () => (/* binding */ Italic),\n/* harmony export */   \"default\": () => (/* binding */ Italic),\n/* harmony export */   starInputRegex: () => (/* binding */ starInputRegex),\n/* harmony export */   starPasteRegex: () => (/* binding */ starPasteRegex),\n/* harmony export */   underscoreInputRegex: () => (/* binding */ underscoreInputRegex),\n/* harmony export */   underscorePasteRegex: () => (/* binding */ underscorePasteRegex)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n/**\n * Matches an italic to a *italic* on input.\n */\nconst starInputRegex = /(?:^|\\s)(\\*(?!\\s+\\*)((?:[^*]+))\\*(?!\\s+\\*))$/;\n/**\n * Matches an italic to a *italic* on paste.\n */\nconst starPasteRegex = /(?:^|\\s)(\\*(?!\\s+\\*)((?:[^*]+))\\*(?!\\s+\\*))/g;\n/**\n * Matches an italic to a _italic_ on input.\n */\nconst underscoreInputRegex = /(?:^|\\s)(_(?!\\s+_)((?:[^_]+))_(?!\\s+_))$/;\n/**\n * Matches an italic to a _italic_ on paste.\n */\nconst underscorePasteRegex = /(?:^|\\s)(_(?!\\s+_)((?:[^_]+))_(?!\\s+_))/g;\n/**\n * This extension allows you to create italic text.\n * @see https://www.tiptap.dev/api/marks/italic\n */\nconst Italic = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Mark.create({\n    name: 'italic',\n    addOptions() {\n        return {\n            HTMLAttributes: {},\n        };\n    },\n    parseHTML() {\n        return [\n            {\n                tag: 'em',\n            },\n            {\n                tag: 'i',\n                getAttrs: node => node.style.fontStyle !== 'normal' && null,\n            },\n            {\n                style: 'font-style=normal',\n                clearMark: mark => mark.type.name === this.name,\n            },\n            {\n                style: 'font-style=italic',\n            },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['em', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n    addCommands() {\n        return {\n            setItalic: () => ({ commands }) => {\n                return commands.setMark(this.name);\n            },\n            toggleItalic: () => ({ commands }) => {\n                return commands.toggleMark(this.name);\n            },\n            unsetItalic: () => ({ commands }) => {\n                return commands.unsetMark(this.name);\n            },\n        };\n    },\n    addKeyboardShortcuts() {\n        return {\n            'Mod-i': () => this.editor.commands.toggleItalic(),\n            'Mod-I': () => this.editor.commands.toggleItalic(),\n        };\n    },\n    addInputRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markInputRule)({\n                find: starInputRegex,\n                type: this.type,\n            }),\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markInputRule)({\n                find: underscoreInputRegex,\n                type: this.type,\n            }),\n        ];\n    },\n    addPasteRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markPasteRule)({\n                find: starPasteRegex,\n                type: this.type,\n            }),\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markPasteRule)({\n                find: underscorePasteRegex,\n                type: this.type,\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-italic@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-italic/dist/index.js\n");

/***/ })

};
;