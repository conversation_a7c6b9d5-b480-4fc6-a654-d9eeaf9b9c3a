"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-footnote";
exports.ids = ["vendor-chunks/micromark-extension-gfm-footnote"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-gfm-footnote/dev/lib/html.js":
/*!***********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-footnote/dev/lib/html.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultBackLabel: () => (/* binding */ defaultBackLabel),\n/* harmony export */   gfmFootnoteHtml: () => (/* binding */ gfmFootnoteHtml)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-normalize-identifier */ \"(ssr)/./node_modules/micromark-util-normalize-identifier/dev/index.js\");\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @import {HtmlOptions as Options} from 'micromark-extension-gfm-footnote'\n * @import {HtmlExtension} from 'micromark-util-types'\n */\n\n\n\n\n\nconst own = {}.hasOwnProperty\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Generate the default label that GitHub uses on backreferences.\n *\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Default label.\n */\nfunction defaultBackLabel(referenceIndex, rereferenceIndex) {\n  return (\n    'Back to reference ' +\n    (referenceIndex + 1) +\n    (rereferenceIndex > 1 ? '-' + rereferenceIndex : '')\n  )\n}\n\n/**\n * Create an extension for `micromark` to support GFM footnotes when\n * serializing to HTML.\n *\n * @param {Options | null | undefined} [options={}]\n *   Configuration (optional).\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GFM footnotes when serializing to HTML.\n */\nfunction gfmFootnoteHtml(options) {\n  const config = options || emptyOptions\n  const label = config.label || 'Footnotes'\n  const labelTagName = config.labelTagName || 'h2'\n  const labelAttributes =\n    config.labelAttributes === null || config.labelAttributes === undefined\n      ? 'class=\"sr-only\"'\n      : config.labelAttributes\n  const backLabel = config.backLabel || defaultBackLabel\n  const clobberPrefix =\n    config.clobberPrefix === null || config.clobberPrefix === undefined\n      ? 'user-content-'\n      : config.clobberPrefix\n  return {\n    enter: {\n      gfmFootnoteDefinition() {\n        const stack = this.getData('tightStack')\n        stack.push(false)\n      },\n      gfmFootnoteDefinitionLabelString() {\n        this.buffer()\n      },\n      gfmFootnoteCallString() {\n        this.buffer()\n      }\n    },\n    exit: {\n      gfmFootnoteDefinition() {\n        let definitions = this.getData('gfmFootnoteDefinitions')\n        const footnoteStack = this.getData('gfmFootnoteDefinitionStack')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(footnoteStack, 'expected `footnoteStack`')\n        const tightStack = this.getData('tightStack')\n        const current = footnoteStack.pop()\n        const value = this.resume()\n\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(current, 'expected to be in a footnote')\n\n        if (!definitions) {\n          this.setData('gfmFootnoteDefinitions', (definitions = {}))\n        }\n\n        if (!own.call(definitions, current)) definitions[current] = value\n\n        tightStack.pop()\n        this.setData('slurpOneLineEnding', true)\n        // “Hack” to prevent a line ending from showing up if we’re in a definition in\n        // an empty list item.\n        this.setData('lastWasTag')\n      },\n      gfmFootnoteDefinitionLabelString(token) {\n        let footnoteStack = this.getData('gfmFootnoteDefinitionStack')\n\n        if (!footnoteStack) {\n          this.setData('gfmFootnoteDefinitionStack', (footnoteStack = []))\n        }\n\n        footnoteStack.push((0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_1__.normalizeIdentifier)(this.sliceSerialize(token)))\n        this.resume() // Drop the label.\n        this.buffer() // Get ready for a value.\n      },\n      gfmFootnoteCallString(token) {\n        let calls = this.getData('gfmFootnoteCallOrder')\n        let counts = this.getData('gfmFootnoteCallCounts')\n        const id = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_1__.normalizeIdentifier)(this.sliceSerialize(token))\n        /** @type {number} */\n        let counter\n\n        this.resume()\n\n        if (!calls) this.setData('gfmFootnoteCallOrder', (calls = []))\n        if (!counts) this.setData('gfmFootnoteCallCounts', (counts = {}))\n\n        const index = calls.indexOf(id)\n        const safeId = (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_2__.sanitizeUri)(id.toLowerCase())\n\n        if (index === -1) {\n          calls.push(id)\n          counts[id] = 1\n          counter = calls.length\n        } else {\n          counts[id]++\n          counter = index + 1\n        }\n\n        const reuseCounter = counts[id]\n\n        this.tag(\n          '<sup><a href=\"#' +\n            clobberPrefix +\n            'fn-' +\n            safeId +\n            '\" id=\"' +\n            clobberPrefix +\n            'fnref-' +\n            safeId +\n            (reuseCounter > 1 ? '-' + reuseCounter : '') +\n            '\" data-footnote-ref=\"\" aria-describedby=\"footnote-label\">' +\n            String(counter) +\n            '</a></sup>'\n        )\n      },\n      null() {\n        const calls = this.getData('gfmFootnoteCallOrder') || []\n        const counts = this.getData('gfmFootnoteCallCounts') || {}\n        const definitions = this.getData('gfmFootnoteDefinitions') || {}\n        let index = -1\n\n        if (calls.length > 0) {\n          this.lineEndingIfNeeded()\n          this.tag(\n            '<section data-footnotes=\"\" class=\"footnotes\"><' +\n              labelTagName +\n              ' id=\"footnote-label\"' +\n              (labelAttributes ? ' ' + labelAttributes : '') +\n              '>'\n          )\n          this.raw(this.encode(label))\n          this.tag('</' + labelTagName + '>')\n          this.lineEndingIfNeeded()\n          this.tag('<ol>')\n        }\n\n        while (++index < calls.length) {\n          // Called definitions are always defined.\n          const id = calls[index]\n          const safeId = (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_2__.sanitizeUri)(id.toLowerCase())\n          let referenceIndex = 0\n          /** @type {Array<string>} */\n          const references = []\n\n          while (++referenceIndex <= counts[id]) {\n            references.push(\n              '<a href=\"#' +\n                clobberPrefix +\n                'fnref-' +\n                safeId +\n                (referenceIndex > 1 ? '-' + referenceIndex : '') +\n                '\" data-footnote-backref=\"\" aria-label=\"' +\n                this.encode(\n                  typeof backLabel === 'string'\n                    ? backLabel\n                    : backLabel(index, referenceIndex)\n                ) +\n                '\" class=\"data-footnote-backref\">↩' +\n                (referenceIndex > 1\n                  ? '<sup>' + referenceIndex + '</sup>'\n                  : '') +\n                '</a>'\n            )\n          }\n\n          const reference = references.join(' ')\n          let injected = false\n\n          this.lineEndingIfNeeded()\n          this.tag('<li id=\"' + clobberPrefix + 'fn-' + safeId + '\">')\n          this.lineEndingIfNeeded()\n          this.tag(\n            definitions[id].replace(/<\\/p>(?:\\r?\\n|\\r)?$/, function ($0) {\n              injected = true\n              return ' ' + reference + $0\n            })\n          )\n\n          if (!injected) {\n            this.lineEndingIfNeeded()\n            this.tag(reference)\n          }\n\n          this.lineEndingIfNeeded()\n          this.tag('</li>')\n        }\n\n        if (calls.length > 0) {\n          this.lineEndingIfNeeded()\n          this.tag('</ol>')\n          this.lineEndingIfNeeded()\n          this.tag('</section>')\n        }\n      }\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-footnote/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-footnote/dev/lib/syntax.js":
/*!*************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-footnote/dev/lib/syntax.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmFootnote: () => (/* binding */ gfmFootnote)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/blank-line.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-normalize-identifier */ \"(ssr)/./node_modules/micromark-util-normalize-identifier/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {Event, Exiter, Extension, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\n\n\n\n\n\n\n\nconst indent = {tokenize: tokenizeIndent, partial: true}\n\n// To do: micromark should support a `_hiddenGfmFootnoteSupport`, which only\n// affects label start (image).\n// That will let us drop `tokenizePotentialGfmFootnote*`.\n// It currently has a `_hiddenFootnoteSupport`, which affects that and more.\n// That can be removed when `micromark-extension-footnote` is archived.\n\n/**\n * Create an extension for `micromark` to enable GFM footnote syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to\n *   enable GFM footnote syntax.\n */\nfunction gfmFootnote() {\n  /** @type {Extension} */\n  return {\n    document: {\n      [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: {\n        name: 'gfmFootnoteDefinition',\n        tokenize: tokenizeDefinitionStart,\n        continuation: {tokenize: tokenizeDefinitionContinuation},\n        exit: gfmFootnoteDefinitionEnd\n      }\n    },\n    text: {\n      [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: {\n        name: 'gfmFootnoteCall',\n        tokenize: tokenizeGfmFootnoteCall\n      },\n      [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket]: {\n        name: 'gfmPotentialFootnoteCall',\n        add: 'after',\n        tokenize: tokenizePotentialGfmFootnoteCall,\n        resolveTo: resolveToPotentialGfmFootnoteCall\n      }\n    }\n  }\n}\n\n// To do: remove after micromark update.\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizePotentialGfmFootnoteCall(effects, ok, nok) {\n  const self = this\n  let index = self.events.length\n  const defined = self.parser.gfmFootnotes || (self.parser.gfmFootnotes = [])\n  /** @type {Token} */\n  let labelStart\n\n  // Find an opening.\n  while (index--) {\n    const token = self.events[index][1]\n\n    if (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.labelImage) {\n      labelStart = token\n      break\n    }\n\n    // Exit if we’ve walked far enough.\n    if (\n      token.type === 'gfmFootnoteCall' ||\n      token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.labelLink ||\n      token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.label ||\n      token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.image ||\n      token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.link\n    ) {\n      break\n    }\n  }\n\n  return start\n\n  /**\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket, 'expected `]`')\n\n    if (!labelStart || !labelStart._balanced) {\n      return nok(code)\n    }\n\n    const id = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_3__.normalizeIdentifier)(\n      self.sliceSerialize({start: labelStart.end, end: self.now()})\n    )\n\n    if (id.codePointAt(0) !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.caret || !defined.includes(id.slice(1))) {\n      return nok(code)\n    }\n\n    effects.enter('gfmFootnoteCallLabelMarker')\n    effects.consume(code)\n    effects.exit('gfmFootnoteCallLabelMarker')\n    return ok(code)\n  }\n}\n\n// To do: remove after micromark update.\n/** @type {Resolver} */\nfunction resolveToPotentialGfmFootnoteCall(events, context) {\n  let index = events.length\n  /** @type {Token | undefined} */\n  let labelStart\n\n  // Find an opening.\n  while (index--) {\n    if (\n      events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.labelImage &&\n      events[index][0] === 'enter'\n    ) {\n      labelStart = events[index][1]\n      break\n    }\n  }\n\n  (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(labelStart, 'expected `labelStart` to resolve')\n\n  // Change the `labelImageMarker` to a `data`.\n  events[index + 1][1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data\n  events[index + 3][1].type = 'gfmFootnoteCallLabelMarker'\n\n  // The whole (without `!`):\n  /** @type {Token} */\n  const call = {\n    type: 'gfmFootnoteCall',\n    start: Object.assign({}, events[index + 3][1].start),\n    end: Object.assign({}, events[events.length - 1][1].end)\n  }\n  // The `^` marker\n  /** @type {Token} */\n  const marker = {\n    type: 'gfmFootnoteCallMarker',\n    start: Object.assign({}, events[index + 3][1].end),\n    end: Object.assign({}, events[index + 3][1].end)\n  }\n  // Increment the end 1 character.\n  marker.end.column++\n  marker.end.offset++\n  marker.end._bufferIndex++\n  /** @type {Token} */\n  const string = {\n    type: 'gfmFootnoteCallString',\n    start: Object.assign({}, marker.end),\n    end: Object.assign({}, events[events.length - 1][1].start)\n  }\n  /** @type {Token} */\n  const chunk = {\n    type: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkString,\n    contentType: 'string',\n    start: Object.assign({}, string.start),\n    end: Object.assign({}, string.end)\n  }\n\n  /** @type {Array<Event>} */\n  const replacement = [\n    // Take the `labelImageMarker` (now `data`, the `!`)\n    events[index + 1],\n    events[index + 2],\n    ['enter', call, context],\n    // The `[`\n    events[index + 3],\n    events[index + 4],\n    // The `^`.\n    ['enter', marker, context],\n    ['exit', marker, context],\n    // Everything in between.\n    ['enter', string, context],\n    ['enter', chunk, context],\n    ['exit', chunk, context],\n    ['exit', string, context],\n    // The ending (`]`, properly parsed and labelled).\n    events[events.length - 2],\n    events[events.length - 1],\n    ['exit', call, context]\n  ]\n\n  events.splice(index, events.length - index + 1, ...replacement)\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeGfmFootnoteCall(effects, ok, nok) {\n  const self = this\n  const defined = self.parser.gfmFootnotes || (self.parser.gfmFootnotes = [])\n  let size = 0\n  /** @type {boolean} */\n  let data\n\n  // Note: the implementation of `markdown-rs` is different, because it houses\n  // core *and* extensions in one project.\n  // Therefore, it can include footnote logic inside `label-end`.\n  // We can’t do that, but luckily, we can parse footnotes in a simpler way than\n  // needed for labels.\n  return start\n\n  /**\n   * Start of footnote label.\n   *\n   * ```markdown\n   * > | a [^b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket, 'expected `[`')\n    effects.enter('gfmFootnoteCall')\n    effects.enter('gfmFootnoteCallLabelMarker')\n    effects.consume(code)\n    effects.exit('gfmFootnoteCallLabelMarker')\n    return callStart\n  }\n\n  /**\n   * After `[`, at `^`.\n   *\n   * ```markdown\n   * > | a [^b] c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function callStart(code) {\n    if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.caret) return nok(code)\n\n    effects.enter('gfmFootnoteCallMarker')\n    effects.consume(code)\n    effects.exit('gfmFootnoteCallMarker')\n    effects.enter('gfmFootnoteCallString')\n    effects.enter('chunkString').contentType = 'string'\n    return callData\n  }\n\n  /**\n   * In label.\n   *\n   * ```markdown\n   * > | a [^b] c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function callData(code) {\n    if (\n      // Too long.\n      size > micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.linkReferenceSizeMax ||\n      // Closing brace with nothing.\n      (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket && !data) ||\n      // Space or tab is not supported by GFM for some reason.\n      // `\\n` and `[` not being supported makes sense.\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)\n    ) {\n      return nok(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket) {\n      effects.exit('chunkString')\n      const token = effects.exit('gfmFootnoteCallString')\n\n      if (!defined.includes((0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_3__.normalizeIdentifier)(self.sliceSerialize(token)))) {\n        return nok(code)\n      }\n\n      effects.enter('gfmFootnoteCallLabelMarker')\n      effects.consume(code)\n      effects.exit('gfmFootnoteCallLabelMarker')\n      effects.exit('gfmFootnoteCall')\n      return ok\n    }\n\n    if (!(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)) {\n      data = true\n    }\n\n    size++\n    effects.consume(code)\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash ? callEscape : callData\n  }\n\n  /**\n   * On character after escape.\n   *\n   * ```markdown\n   * > | a [^b\\c] d\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function callEscape(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket\n    ) {\n      effects.consume(code)\n      size++\n      return callData\n    }\n\n    return callData(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeDefinitionStart(effects, ok, nok) {\n  const self = this\n  const defined = self.parser.gfmFootnotes || (self.parser.gfmFootnotes = [])\n  /** @type {string} */\n  let identifier\n  let size = 0\n  /** @type {boolean | undefined} */\n  let data\n\n  return start\n\n  /**\n   * Start of GFM footnote definition.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket, 'expected `[`')\n    effects.enter('gfmFootnoteDefinition')._container = true\n    effects.enter('gfmFootnoteDefinitionLabel')\n    effects.enter('gfmFootnoteDefinitionLabelMarker')\n    effects.consume(code)\n    effects.exit('gfmFootnoteDefinitionLabelMarker')\n    return labelAtMarker\n  }\n\n  /**\n   * In label, at caret.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelAtMarker(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.caret) {\n      effects.enter('gfmFootnoteDefinitionMarker')\n      effects.consume(code)\n      effects.exit('gfmFootnoteDefinitionMarker')\n      effects.enter('gfmFootnoteDefinitionLabelString')\n      effects.enter('chunkString').contentType = 'string'\n      return labelInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In label.\n   *\n   * > 👉 **Note**: `cmark-gfm` prevents whitespace from occurring in footnote\n   * > definition labels.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelInside(code) {\n    if (\n      // Too long.\n      size > micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.linkReferenceSizeMax ||\n      // Closing brace with nothing.\n      (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket && !data) ||\n      // Space or tab is not supported by GFM for some reason.\n      // `\\n` and `[` not being supported makes sense.\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)\n    ) {\n      return nok(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket) {\n      effects.exit('chunkString')\n      const token = effects.exit('gfmFootnoteDefinitionLabelString')\n      identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_3__.normalizeIdentifier)(self.sliceSerialize(token))\n      effects.enter('gfmFootnoteDefinitionLabelMarker')\n      effects.consume(code)\n      effects.exit('gfmFootnoteDefinitionLabelMarker')\n      effects.exit('gfmFootnoteDefinitionLabel')\n      return labelAfter\n    }\n\n    if (!(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)) {\n      data = true\n    }\n\n    size++\n    effects.consume(code)\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash ? labelEscape : labelInside\n  }\n\n  /**\n   * After `\\`, at a special character.\n   *\n   * > 👉 **Note**: `cmark-gfm` currently does not support escaped brackets:\n   * > <https://github.com/github/cmark-gfm/issues/240>\n   *\n   * ```markdown\n   * > | [^a\\*b]: c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEscape(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket\n    ) {\n      effects.consume(code)\n      size++\n      return labelInside\n    }\n\n    return labelInside(code)\n  }\n\n  /**\n   * After definition label.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelAfter(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.colon) {\n      effects.enter('definitionMarker')\n      effects.consume(code)\n      effects.exit('definitionMarker')\n\n      if (!defined.includes(identifier)) {\n        defined.push(identifier)\n      }\n\n      // Any whitespace after the marker is eaten, forming indented code\n      // is not possible.\n      // No space is also fine, just like a block quote marker.\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__.factorySpace)(\n        effects,\n        whitespaceAfter,\n        'gfmFootnoteDefinitionWhitespace'\n      )\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After definition prefix.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function whitespaceAfter(code) {\n    // `markdown-rs` has a wrapping token for the prefix that is closed here.\n    return ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeDefinitionContinuation(effects, ok, nok) {\n  /// Start of footnote definition continuation.\n  ///\n  /// ```markdown\n  ///   | [^a]: b\n  /// > |     c\n  ///     ^\n  /// ```\n  //\n  // Either a blank line, which is okay, or an indented thing.\n  return effects.check(micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__.blankLine, ok, effects.attempt(indent, ok, nok))\n}\n\n/** @type {Exiter} */\nfunction gfmFootnoteDefinitionEnd(effects) {\n  effects.exit('gfmFootnoteDefinition')\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeIndent(effects, ok, nok) {\n  const self = this\n\n  return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__.factorySpace)(\n    effects,\n    afterPrefix,\n    'gfmFootnoteDefinitionIndent',\n    micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize + 1\n  )\n\n  /**\n   * @type {State}\n   */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === 'gfmFootnoteDefinitionIndent' &&\n      tail[2].sliceSerialize(tail[1], true).length === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize\n      ? ok(code)\n      : nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-footnote/dev/lib/syntax.js\n");

/***/ })

};
;