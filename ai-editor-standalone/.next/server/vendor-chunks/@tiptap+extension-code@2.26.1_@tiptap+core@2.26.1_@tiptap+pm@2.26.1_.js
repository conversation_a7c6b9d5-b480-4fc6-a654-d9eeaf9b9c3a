"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-code@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-code@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-code@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-code/dist/index.js":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-code@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-code/dist/index.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Code: () => (/* binding */ Code),\n/* harmony export */   \"default\": () => (/* binding */ Code),\n/* harmony export */   inputRegex: () => (/* binding */ inputRegex),\n/* harmony export */   pasteRegex: () => (/* binding */ pasteRegex)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n/**\n * Regular expressions to match inline code blocks enclosed in backticks.\n *  It matches:\n *     - An opening backtick, followed by\n *     - Any text that doesn't include a backtick (captured for marking), followed by\n *     - A closing backtick.\n *  This ensures that any text between backticks is formatted as code,\n *  regardless of the surrounding characters (exception being another backtick).\n */\nconst inputRegex = /(^|[^`])`([^`]+)`(?!`)/;\n/**\n * Matches inline code while pasting.\n */\nconst pasteRegex = /(^|[^`])`([^`]+)`(?!`)/g;\n/**\n * This extension allows you to mark text as inline code.\n * @see https://tiptap.dev/api/marks/code\n */\nconst Code = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Mark.create({\n    name: 'code',\n    addOptions() {\n        return {\n            HTMLAttributes: {},\n        };\n    },\n    excludes: '_',\n    code: true,\n    exitable: true,\n    parseHTML() {\n        return [\n            { tag: 'code' },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['code', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n    addCommands() {\n        return {\n            setCode: () => ({ commands }) => {\n                return commands.setMark(this.name);\n            },\n            toggleCode: () => ({ commands }) => {\n                return commands.toggleMark(this.name);\n            },\n            unsetCode: () => ({ commands }) => {\n                return commands.unsetMark(this.name);\n            },\n        };\n    },\n    addKeyboardShortcuts() {\n        return {\n            'Mod-e': () => this.editor.commands.toggleCode(),\n        };\n    },\n    addInputRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markInputRule)({\n                find: inputRegex,\n                type: this.type,\n            }),\n        ];\n    },\n    addPasteRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markPasteRule)({\n                find: pasteRegex,\n                type: this.type,\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-code@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-code/dist/index.js\n");

/***/ })

};
;