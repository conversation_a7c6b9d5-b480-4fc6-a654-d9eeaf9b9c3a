"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/overlap-area";
exports.ids = ["vendor-chunks/overlap-area"];
exports.modules = {

/***/ "(ssr)/./node_modules/overlap-area/dist/overlap-area.esm.js":
/*!************************************************************!*\
  !*** ./node_modules/overlap-area/dist/overlap-area.esm.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertLines: () => (/* binding */ convertLines),\n/* harmony export */   findConnectedAreas: () => (/* binding */ findConnectedAreas),\n/* harmony export */   fitPoints: () => (/* binding */ fitPoints),\n/* harmony export */   getAreaSize: () => (/* binding */ getAreaSize),\n/* harmony export */   getDistanceFromPointToConstants: () => (/* binding */ getDistanceFromPointToConstants),\n/* harmony export */   getIntersectionPoints: () => (/* binding */ getIntersectionPoints),\n/* harmony export */   getIntersectionPointsByConstants: () => (/* binding */ getIntersectionPointsByConstants),\n/* harmony export */   getLinearConstants: () => (/* binding */ getLinearConstants),\n/* harmony export */   getMinMaxs: () => (/* binding */ getMinMaxs),\n/* harmony export */   getOverlapAreas: () => (/* binding */ getOverlapAreas),\n/* harmony export */   getOverlapPoints: () => (/* binding */ getOverlapPoints),\n/* harmony export */   getOverlapSize: () => (/* binding */ getOverlapSize),\n/* harmony export */   getPointsOnLines: () => (/* binding */ getPointsOnLines),\n/* harmony export */   getUnoverlapAreas: () => (/* binding */ getUnoverlapAreas),\n/* harmony export */   isInside: () => (/* binding */ isInside),\n/* harmony export */   isPointOnLine: () => (/* binding */ isPointOnLine)\n/* harmony export */ });\n/* harmony import */ var _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @daybrush/utils */ \"(ssr)/./node_modules/@daybrush/utils/dist/utils.esm.js\");\n/*\nCopyright (c) 2020 Daybrush\nname: overlap-area\nlicense: MIT\nauthor: Daybrush\nrepository: git+https://github.com/daybrush/overlap-area.git\nversion: 1.1.0\n*/\n\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\nfunction __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n\n  for (var r = Array(s), k = 0, i = 0; i < il; i++) for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++) r[k] = a[j];\n\n  return r;\n}\n\nfunction tinyThrottle(num) {\n  return (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.throttle)(num, _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.TINY_NUM);\n}\nfunction isSameConstants(linearConstants1, linearConstants2) {\n  return linearConstants1.every(function (v, i) {\n    return tinyThrottle(v - linearConstants2[i]) === 0;\n  });\n}\nfunction isSamePoint(point1, point2) {\n  return !tinyThrottle(point1[0] - point2[0]) && !tinyThrottle(point1[1] - point2[1]);\n}\nfunction flat(arr) {\n  return arr.reduce(function (prev, current) {\n    prev.push.apply(prev, current);\n    return prev;\n  }, []);\n}\n\n/**\n * @namespace OverlapArea\n */\n\n/**\n * Gets the size of a shape (polygon) made of points.\n * @memberof OverlapArea\n */\n\nfunction getAreaSize(points) {\n  if (points.length < 3) {\n    return 0;\n  }\n\n  return Math.abs((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.sum)(points.map(function (point, i) {\n    var nextPoint = points[i + 1] || points[0];\n    return point[0] * nextPoint[1] - nextPoint[0] * point[1];\n  }))) / 2;\n}\n/**\n * Get points that fit the rect,\n * @memberof OverlapArea\n */\n\nfunction fitPoints(points, rect) {\n  var width = rect.width,\n      height = rect.height,\n      left = rect.left,\n      top = rect.top;\n\n  var _a = getMinMaxs(points),\n      minX = _a.minX,\n      minY = _a.minY,\n      maxX = _a.maxX,\n      maxY = _a.maxY;\n\n  var ratioX = width / (maxX - minX);\n  var ratioY = height / (maxY - minY);\n  return points.map(function (point) {\n    return [left + (point[0] - minX) * ratioX, top + (point[1] - minY) * ratioY];\n  });\n}\n/**\n * Get the minimum and maximum points of the points.\n * @memberof OverlapArea\n */\n\nfunction getMinMaxs(points) {\n  var xs = points.map(function (point) {\n    return point[0];\n  });\n  var ys = points.map(function (point) {\n    return point[1];\n  });\n  return {\n    minX: Math.min.apply(Math, xs),\n    minY: Math.min.apply(Math, ys),\n    maxX: Math.max.apply(Math, xs),\n    maxY: Math.max.apply(Math, ys)\n  };\n}\n/**\n * Whether the point is in shape\n * @param - point pos\n * @param - shape points\n * @param - whether to check except line\n * @memberof OverlapArea\n */\n\nfunction isInside(pos, points, excludeLine) {\n  var x = pos[0],\n      y = pos[1];\n\n  var _a = getMinMaxs(points),\n      minX = _a.minX,\n      maxX = _a.maxX;\n\n  var xLine = [[minX, y], [maxX, y]];\n  var xLinearConstants = getLinearConstants(xLine[0], xLine[1]);\n  var lines = convertLines(points);\n  var intersectionPosInfos = [];\n  lines.forEach(function (line) {\n    var linearConstants = getLinearConstants(line[0], line[1]);\n    var standardPoint = line[0];\n\n    if (isSameConstants(xLinearConstants, linearConstants)) {\n      intersectionPosInfos.push({\n        pos: pos,\n        line: line,\n        type: \"line\"\n      });\n    } else {\n      var xPoints = getPointsOnLines(getIntersectionPointsByConstants(xLinearConstants, linearConstants), [xLine, line]);\n      xPoints.forEach(function (point) {\n        if (line.some(function (linePoint) {\n          return isSamePoint(linePoint, point);\n        })) {\n          intersectionPosInfos.push({\n            pos: point,\n            line: line,\n            type: \"point\"\n          });\n        } else if (tinyThrottle(standardPoint[1] - y) !== 0) {\n          intersectionPosInfos.push({\n            pos: point,\n            line: line,\n            type: \"intersection\"\n          });\n        }\n      });\n    }\n  });\n\n  if (!excludeLine) {\n    // on line\n    if ((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.find)(intersectionPosInfos, function (p) {\n      return p[0] === x;\n    })) {\n      return true;\n    }\n  }\n\n  var intersectionCount = 0;\n  var xMap = {};\n  intersectionPosInfos.forEach(function (_a) {\n    var pos = _a.pos,\n        type = _a.type,\n        line = _a.line;\n\n    if (pos[0] > x) {\n      return;\n    }\n\n    if (type === \"intersection\") {\n      ++intersectionCount;\n    } else if (type === \"line\") {\n      return;\n    } else if (type === \"point\") {\n      var point = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.find)(line, function (linePoint) {\n        return linePoint[1] !== y;\n      });\n      var prevValue = xMap[pos[0]];\n      var nextValue = point[1] > y ? 1 : -1;\n\n      if (!prevValue) {\n        xMap[pos[0]] = nextValue;\n      } else if (prevValue !== nextValue) {\n        ++intersectionCount;\n      }\n    }\n  });\n  return intersectionCount % 2 === 1;\n}\n/**\n * Get distance from point to constants. [a, b, c] (ax + by + c = 0)\n * @return [a, b, c]\n * @memberof OverlapArea\n */\n\nfunction getDistanceFromPointToConstants(_a, pos) {\n  var a = _a[0],\n      b = _a[1],\n      c = _a[2];\n  return (a * pos[0] + b * pos[1] + c) / (a * a + b * b);\n}\n/**\n * Get the coefficient of the linear function. [a, b, c] (ax + by + c = 0)\n * @return [a, b, c]\n * @memberof OverlapArea\n */\n\nfunction getLinearConstants(point1, point2) {\n  var x1 = point1[0],\n      y1 = point1[1];\n  var x2 = point2[0],\n      y2 = point2[1]; // ax + by + c = 0\n  // [a, b, c]\n\n  var dx = x2 - x1;\n  var dy = y2 - y1;\n\n  if (Math.abs(dx) < _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.TINY_NUM) {\n    dx = 0;\n  }\n\n  if (Math.abs(dy) < _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.TINY_NUM) {\n    dy = 0;\n  } // b > 0\n  // ax + by + c = 0\n\n\n  var a = 0;\n  var b = 0;\n  var c = 0;\n\n  if (!dx) {\n    if (dy) {\n      // -x + 1 = 0\n      a = -1;\n      c = x1;\n    }\n  } else if (!dy) {\n    // y - 1 = 0\n    b = 1;\n    c = -y1;\n  } else {\n    // y = -a(x - x1) + y1\n    // ax + y + a * x1 - y1 = 0\n    a = -dy / dx;\n    b = 1;\n    c = -a * x1 - y1;\n  }\n\n  return [a, b, c];\n}\n/**\n * Get intersection points with linear functions.\n * @memberof OverlapArea\n */\n\nfunction getIntersectionPointsByConstants(linearConstants1, linearConstants2) {\n  var a1 = linearConstants1[0],\n      b1 = linearConstants1[1],\n      c1 = linearConstants1[2];\n  var a2 = linearConstants2[0],\n      b2 = linearConstants2[1],\n      c2 = linearConstants2[2];\n  var isZeroA = a1 === 0 && a2 === 0;\n  var isZeroB = b1 === 0 && b2 === 0;\n  var results = [];\n\n  if (isZeroA && isZeroB) {\n    return [];\n  } else if (isZeroA) {\n    // b1 * y + c1 = 0\n    // b2 * y + c2 = 0\n    var y1 = -c1 / b1;\n    var y2 = -c2 / b2;\n\n    if (y1 !== y2) {\n      return [];\n    } else {\n      return [[-Infinity, y1], [Infinity, y1]];\n    }\n  } else if (isZeroB) {\n    // a1 * x + c1 = 0\n    // a2 * x + c2 = 0\n    var x1 = -c1 / a1;\n    var x2 = -c2 / a2;\n\n    if (x1 !== x2) {\n      return [];\n    } else {\n      return [[x1, -Infinity], [x1, Infinity]];\n    }\n  } else if (a1 === 0) {\n    // b1 * y + c1 = 0\n    // y = - c1 / b1;\n    // a2 * x + b2 * y + c2 = 0\n    var y = -c1 / b1;\n    var x = -(b2 * y + c2) / a2;\n    results = [[x, y]];\n  } else if (a2 === 0) {\n    // b2 * y + c2 = 0\n    // y = - c2 / b2;\n    // a1 * x + b1 * y + c1 = 0\n    var y = -c2 / b2;\n    var x = -(b1 * y + c1) / a1;\n    results = [[x, y]];\n  } else if (b1 === 0) {\n    // a1 * x + c1 = 0\n    // x = - c1 / a1;\n    // a2 * x + b2 * y + c2 = 0\n    var x = -c1 / a1;\n    var y = -(a2 * x + c2) / b2;\n    results = [[x, y]];\n  } else if (b2 === 0) {\n    // a2 * x + c2 = 0\n    // x = - c2 / a2;\n    // a1 * x + b1 * y + c1 = 0\n    var x = -c2 / a2;\n    var y = -(a1 * x + c1) / b1;\n    results = [[x, y]];\n  } else {\n    // a1 * x + b1 * y + c1 = 0\n    // a2 * x + b2 * y + c2 = 0\n    // b2 * a1 * x + b2 * b1 * y + b2 * c1 = 0\n    // b1 * a2 * x + b1 * b2 * y + b1 * c2 = 0\n    // (b2 * a1 - b1 * a2)  * x = (b1 * c2 - b2 * c1)\n    var x = (b1 * c2 - b2 * c1) / (b2 * a1 - b1 * a2);\n    var y = -(a1 * x + c1) / b1;\n    results = [[x, y]];\n  }\n\n  return results.map(function (result) {\n    return [result[0], result[1]];\n  });\n}\n/**\n * Get intersection points to the two lines.\n * @memberof OverlapArea\n */\n\nfunction getIntersectionPoints(line1, line2, isLimit) {\n  var points = getIntersectionPointsByConstants(getLinearConstants(line1[0], line1[1]), getLinearConstants(line2[0], line2[1]));\n\n  if (isLimit) {\n    return getPointsOnLines(points, [line1, line2]);\n  }\n\n  return points;\n}\nfunction isPointOnLine(pos, line) {\n  var linearConstants = getLinearConstants(line[0], line[1]);\n  return tinyThrottle(getDistanceFromPointToConstants(linearConstants, pos)) === 0;\n}\n/**\n * Get the points on the lines (between two points).\n * @memberof OverlapArea\n */\n\nfunction getPointsOnLines(points, lines) {\n  var minMaxs = lines.map(function (line) {\n    return [0, 1].map(function (order) {\n      return [Math.min(line[0][order], line[1][order]), Math.max(line[0][order], line[1][order])];\n    });\n  });\n  var results = [];\n\n  if (points.length === 2) {\n    var _a = points[0],\n        x = _a[0],\n        y = _a[1];\n\n    if (!tinyThrottle(x - points[1][0])) {\n      /// Math.max(minY1, minY2)\n      var top = Math.max.apply(Math, minMaxs.map(function (minMax) {\n        return minMax[1][0];\n      })); /// Math.min(maxY1, miax2)\n\n      var bottom = Math.min.apply(Math, minMaxs.map(function (minMax) {\n        return minMax[1][1];\n      }));\n\n      if (tinyThrottle(top - bottom) > 0) {\n        return [];\n      }\n\n      results = [[x, top], [x, bottom]];\n    } else if (!tinyThrottle(y - points[1][1])) {\n      /// Math.max(minY1, minY2)\n      var left = Math.max.apply(Math, minMaxs.map(function (minMax) {\n        return minMax[0][0];\n      })); /// Math.min(maxY1, miax2)\n\n      var right = Math.min.apply(Math, minMaxs.map(function (minMax) {\n        return minMax[0][1];\n      }));\n\n      if (tinyThrottle(left - right) > 0) {\n        return [];\n      }\n\n      results = [[left, y], [right, y]];\n    }\n  }\n\n  if (!results.length) {\n    results = points.filter(function (point) {\n      var pointX = point[0],\n          pointY = point[1];\n      return minMaxs.every(function (minMax) {\n        return 0 <= tinyThrottle(pointX - minMax[0][0]) && 0 <= tinyThrottle(minMax[0][1] - pointX) && 0 <= tinyThrottle(pointY - minMax[1][0]) && 0 <= tinyThrottle(minMax[1][1] - pointY);\n      });\n    });\n  }\n\n  return results.map(function (result) {\n    return [tinyThrottle(result[0]), tinyThrottle(result[1])];\n  });\n}\n/**\n* Convert two points into lines.\n* @function\n* @memberof OverlapArea\n*/\n\nfunction convertLines(points) {\n  return __spreadArrays(points.slice(1), [points[0]]).map(function (point, i) {\n    return [points[i], point];\n  });\n}\n\nfunction getOverlapPointInfos(points1, points2) {\n  var targetPoints1 = points1.slice();\n  var targetPoints2 = points2.slice();\n\n  if ((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getShapeDirection)(targetPoints1) === -1) {\n    targetPoints1.reverse();\n  }\n\n  if ((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getShapeDirection)(targetPoints2) === -1) {\n    targetPoints2.reverse();\n  }\n\n  var lines1 = convertLines(targetPoints1);\n  var lines2 = convertLines(targetPoints2);\n  var linearConstantsList1 = lines1.map(function (line1) {\n    return getLinearConstants(line1[0], line1[1]);\n  });\n  var linearConstantsList2 = lines2.map(function (line2) {\n    return getLinearConstants(line2[0], line2[1]);\n  });\n  var overlapInfos = [];\n  linearConstantsList1.forEach(function (linearConstants1, i) {\n    var line1 = lines1[i];\n    var linePointInfos = [];\n    linearConstantsList2.forEach(function (linearConstants2, j) {\n      var intersectionPoints = getIntersectionPointsByConstants(linearConstants1, linearConstants2);\n      var points = getPointsOnLines(intersectionPoints, [line1, lines2[j]]);\n      linePointInfos.push.apply(linePointInfos, points.map(function (pos) {\n        return {\n          index1: i,\n          index2: j,\n          pos: pos,\n          type: \"intersection\"\n        };\n      }));\n    });\n    linePointInfos.sort(function (a, b) {\n      return (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getDist)(line1[0], a.pos) - (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getDist)(line1[0], b.pos);\n    });\n    overlapInfos.push.apply(overlapInfos, linePointInfos);\n\n    if (isInside(line1[1], targetPoints2)) {\n      overlapInfos.push({\n        index1: i,\n        index2: -1,\n        pos: line1[1],\n        type: \"inside\"\n      });\n    }\n  });\n  lines2.forEach(function (line2, i) {\n    if (!isInside(line2[1], targetPoints1)) {\n      return;\n    }\n\n    var isNext = false;\n    var index = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.findIndex)(overlapInfos, function (_a) {\n      var index2 = _a.index2;\n\n      if (index2 === i) {\n        isNext = true;\n        return false;\n      }\n\n      if (isNext) {\n        return true;\n      }\n\n      return false;\n    });\n\n    if (index === -1) {\n      isNext = false;\n      index = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.findIndex)(overlapInfos, function (_a) {\n        var index1 = _a.index1,\n            index2 = _a.index2;\n\n        if (index1 === -1 && index2 + 1 === i) {\n          isNext = true;\n          return false;\n        }\n\n        if (isNext) {\n          return true;\n        }\n\n        return false;\n      });\n    }\n\n    if (index === -1) {\n      overlapInfos.push({\n        index1: -1,\n        index2: i,\n        pos: line2[1],\n        type: \"inside\"\n      });\n    } else {\n      overlapInfos.splice(index, 0, {\n        index1: -1,\n        index2: i,\n        pos: line2[1],\n        type: \"inside\"\n      });\n    }\n  });\n  var pointMap = {};\n  return overlapInfos.filter(function (_a) {\n    var pos = _a.pos;\n    var key = pos[0] + \"x\" + pos[1];\n\n    if (pointMap[key]) {\n      return false;\n    }\n\n    pointMap[key] = true;\n    return true;\n  });\n}\n/**\n* Get the points of the overlapped part of two shapes.\n* @function\n* @memberof OverlapArea\n*/\n\n\nfunction getOverlapPoints(points1, points2) {\n  var infos = getOverlapPointInfos(points1, points2);\n  return infos.map(function (_a) {\n    var pos = _a.pos;\n    return pos;\n  });\n}\n\nfunction isConnectedLine(line) {\n  var _a = line[0],\n      prevIndex1 = _a.index1,\n      prevIndex2 = _a.index2,\n      _b = line[1],\n      nextIndex1 = _b.index1,\n      nextIndex2 = _b.index2;\n\n  if (prevIndex1 !== -1) {\n    // same line\n    if (prevIndex1 === nextIndex1) {\n      return true;\n    }\n\n    if (prevIndex1 + 1 === nextIndex1) {\n      return true;\n    }\n  }\n\n  if (prevIndex2 !== -1) {\n    // same line\n    if (prevIndex2 === nextIndex2) {\n      return true;\n    }\n\n    if (prevIndex2 + 1 === nextIndex2) {\n      return true;\n    }\n  }\n\n  return false;\n}\n/**\n* Get the areas of the overlapped part of two shapes.\n* @function\n* @memberof OverlapArea\n*/\n\n\nfunction getOverlapAreas(points1, points2) {\n  var infos = getOverlapPointInfos(points1, points2);\n  var areas = [];\n  var area;\n  getOverlapPointInfos(points1, points2).forEach(function (info, i, arr) {\n    if (i === 0 || !isConnectedLine([arr[i - 1], info])) {\n      area = [info];\n      areas.push(area);\n    } else {\n      area.push(info);\n    }\n  });\n  return areas.map(function (area) {\n    return area.map(function (_a) {\n      var pos = _a.pos;\n      return pos;\n    });\n  });\n}\n\nfunction findReversedAreas(points1, points2, index, areas) {\n  if (index === void 0) {\n    index = 0;\n  }\n\n  if (areas === void 0) {\n    areas = [];\n  }\n\n  var isFirst = areas.length === 0;\n  var length = points1.length;\n  var nextIndex = points1[index] ? index : 0;\n\n  var nextPoints1 = __spreadArrays(points1.slice(nextIndex), points1.slice(0, nextIndex));\n\n  var _loop_1 = function (i) {\n    var point1 = nextPoints1[i];\n\n    if ((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.find)(points2, function (point2) {\n      return point2[0] === point1[0] && point2[1] === point1[1];\n    })) {\n      return \"continue\";\n    }\n\n    if (areas.some(function (nextArea) {\n      return (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.find)(nextArea, function (areaPoint) {\n        return areaPoint[0] === point1[0] && areaPoint[1] === point1[1];\n      });\n    })) {\n      if (isFirst) {\n        return \"continue\";\n      } else {\n        return \"break\";\n      }\n    }\n\n    var nextArea = void 0;\n\n    if (isFirst) {\n      nextArea = [];\n      areas.push(nextArea);\n    } else {\n      nextArea = areas[areas.length - 1];\n    }\n\n    nextArea.push(point1);\n    var line = [point1, points1[index + 1] || points1[0]];\n    var nextPoint2 = points2.filter(function (point2) {\n      return isPointOnLine(point2, line);\n    }).sort(function (a, b) {\n      return (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getDist)(point1, a) - (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getDist)(point1, b);\n    })[0];\n\n    if (!nextPoint2) {\n      findReversedAreas(nextPoints1, points2, i + 1, areas);\n      return \"break\";\n    } else {\n      var point2Index = points2.indexOf(nextPoint2);\n      findReversedAreas(points2, points1, point2Index, areas);\n\n      if (!isFirst) {\n        return \"break\";\n      }\n    }\n  };\n\n  for (var i = 0; i < length; ++i) {\n    var state_1 = _loop_1(i);\n\n    if (state_1 === \"break\") break;\n  }\n\n  return areas;\n}\n\nfunction findConnectedAreas(points1, points2) {\n  return findReversedAreas(points1, __spreadArrays(points2).reverse());\n}\n/**\n* Get non-overlapping areas of two shapes based on points1.\n* @memberof OverlapArea\n*/\n\nfunction getUnoverlapAreas(points1, points2) {\n  if (!points2.length) {\n    return [__spreadArrays(points1)];\n  }\n\n  var overlapAreas = getOverlapAreas(points1, points2);\n  var unoverlapAreas = [points1];\n  overlapAreas.forEach(function (overlapArea) {\n    var nextOverlapArea = __spreadArrays(overlapArea).reverse();\n\n    unoverlapAreas = flat(unoverlapAreas.map(function (area) {\n      var connectedAreas = findReversedAreas(area, nextOverlapArea);\n      var firstConnectedArea = connectedAreas[0];\n\n      if (connectedAreas.length === 1 && nextOverlapArea.every(function (point) {\n        return firstConnectedArea.indexOf(point) === -1;\n      })) {\n        var lastPoint_1 = firstConnectedArea[firstConnectedArea.length - 1];\n\n        var firstPoint = __spreadArrays(nextOverlapArea).sort(function (a, b) {\n          return (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getDist)(lastPoint_1, a) - (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getDist)(lastPoint_1, b);\n        })[0];\n\n        var firstIndex = nextOverlapArea.indexOf(firstPoint);\n        firstConnectedArea.push.apply(firstConnectedArea, __spreadArrays(nextOverlapArea.slice(firstIndex), nextOverlapArea.slice(0, firstIndex), [nextOverlapArea[firstIndex], lastPoint_1]));\n      }\n\n      return connectedAreas;\n    }));\n  });\n  return unoverlapAreas;\n}\n/**\n* Gets the size of the overlapped part of two shapes.\n* @function\n* @memberof OverlapArea\n*/\n\nfunction getOverlapSize(points1, points2) {\n  var points = getOverlapPoints(points1, points2);\n  return getAreaSize(points);\n}\n\n\n//# sourceMappingURL=overlap-area.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3ZlcmxhcC1hcmVhL2Rpc3Qvb3ZlcmxhcC1hcmVhLmVzbS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDdUc7O0FBRXZHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdEQUFnRCxRQUFROztBQUV4RCx1Q0FBdUMsUUFBUSxzREFBc0QsUUFBUTs7QUFFN0c7QUFDQTs7QUFFQTtBQUNBLFNBQVMseURBQVEsTUFBTSxxREFBUTtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxrQkFBa0Isb0RBQUc7QUFDckI7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWCxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQSxPQUFPO0FBQ1A7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQSxRQUFRLHFEQUFJO0FBQ1o7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBLE1BQU07QUFDTixrQkFBa0IscURBQUk7QUFDdEI7QUFDQSxPQUFPO0FBQ1A7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCO0FBQ3RCOztBQUVBO0FBQ0E7O0FBRUEscUJBQXFCLHFEQUFRO0FBQzdCO0FBQ0E7O0FBRUEscUJBQXFCLHFEQUFRO0FBQzdCO0FBQ0EsSUFBSTtBQUNKOzs7QUFHQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPLElBQUk7O0FBRVg7QUFDQTtBQUNBLE9BQU87O0FBRVA7QUFDQTtBQUNBOztBQUVBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLE9BQU8sSUFBSTs7QUFFWDtBQUNBO0FBQ0EsT0FBTzs7QUFFUDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0w7O0FBRUE7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSxNQUFNLGtFQUFpQjtBQUN2QjtBQUNBOztBQUVBLE1BQU0sa0VBQWlCO0FBQ3ZCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTDtBQUNBLGFBQWEsd0RBQU8sb0JBQW9CLHdEQUFPO0FBQy9DLEtBQUs7QUFDTDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxnQkFBZ0IsMERBQVM7QUFDekI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsS0FBSzs7QUFFTDtBQUNBO0FBQ0EsY0FBYywwREFBUztBQUN2QjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLE9BQU87QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOzs7QUFHQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7O0FBR0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLEdBQUc7QUFDSDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUEsUUFBUSxxREFBSTtBQUNaO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQSxhQUFhLHFEQUFJO0FBQ2pCO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTDtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxhQUFhLHdEQUFPLGNBQWMsd0RBQU87QUFDekMsS0FBSzs7QUFFTDtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGtCQUFrQixZQUFZO0FBQzlCOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLE9BQU87QUFDUDs7QUFFQTtBQUNBLGlCQUFpQix3REFBTyxtQkFBbUIsd0RBQU87QUFDbEQsU0FBUzs7QUFFVDtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUUrUztBQUMvUyIsInNvdXJjZXMiOlsiL2hvbWUvbXV6aS9naXRodWIvZGVlci1mbG93L2FpLWVkaXRvci1zdGFuZGFsb25lL25vZGVfbW9kdWxlcy9vdmVybGFwLWFyZWEvZGlzdC9vdmVybGFwLWFyZWEuZXNtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qXG5Db3B5cmlnaHQgKGMpIDIwMjAgRGF5YnJ1c2hcbm5hbWU6IG92ZXJsYXAtYXJlYVxubGljZW5zZTogTUlUXG5hdXRob3I6IERheWJydXNoXG5yZXBvc2l0b3J5OiBnaXQraHR0cHM6Ly9naXRodWIuY29tL2RheWJydXNoL292ZXJsYXAtYXJlYS5naXRcbnZlcnNpb246IDEuMS4wXG4qL1xuaW1wb3J0IHsgdGhyb3R0bGUsIFRJTllfTlVNLCBzdW0sIGZpbmQsIGdldERpc3QsIGdldFNoYXBlRGlyZWN0aW9uLCBmaW5kSW5kZXggfSBmcm9tICdAZGF5YnJ1c2gvdXRpbHMnO1xuXG4vKiEgKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKipcclxuQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uXHJcblxyXG5QZXJtaXNzaW9uIHRvIHVzZSwgY29weSwgbW9kaWZ5LCBhbmQvb3IgZGlzdHJpYnV0ZSB0aGlzIHNvZnR3YXJlIGZvciBhbnlcclxucHVycG9zZSB3aXRoIG9yIHdpdGhvdXQgZmVlIGlzIGhlcmVieSBncmFudGVkLlxyXG5cclxuVEhFIFNPRlRXQVJFIElTIFBST1ZJREVEIFwiQVMgSVNcIiBBTkQgVEhFIEFVVEhPUiBESVNDTEFJTVMgQUxMIFdBUlJBTlRJRVMgV0lUSFxyXG5SRUdBUkQgVE8gVEhJUyBTT0ZUV0FSRSBJTkNMVURJTkcgQUxMIElNUExJRUQgV0FSUkFOVElFUyBPRiBNRVJDSEFOVEFCSUxJVFlcclxuQU5EIEZJVE5FU1MuIElOIE5PIEVWRU5UIFNIQUxMIFRIRSBBVVRIT1IgQkUgTElBQkxFIEZPUiBBTlkgU1BFQ0lBTCwgRElSRUNULFxyXG5JTkRJUkVDVCwgT1IgQ09OU0VRVUVOVElBTCBEQU1BR0VTIE9SIEFOWSBEQU1BR0VTIFdIQVRTT0VWRVIgUkVTVUxUSU5HIEZST01cclxuTE9TUyBPRiBVU0UsIERBVEEgT1IgUFJPRklUUywgV0hFVEhFUiBJTiBBTiBBQ1RJT04gT0YgQ09OVFJBQ1QsIE5FR0xJR0VOQ0UgT1JcclxuT1RIRVIgVE9SVElPVVMgQUNUSU9OLCBBUklTSU5HIE9VVCBPRiBPUiBJTiBDT05ORUNUSU9OIFdJVEggVEhFIFVTRSBPUlxyXG5QRVJGT1JNQU5DRSBPRiBUSElTIFNPRlRXQVJFLlxyXG4qKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKiAqL1xuZnVuY3Rpb24gX19zcHJlYWRBcnJheXMoKSB7XG4gIGZvciAodmFyIHMgPSAwLCBpID0gMCwgaWwgPSBhcmd1bWVudHMubGVuZ3RoOyBpIDwgaWw7IGkrKykgcyArPSBhcmd1bWVudHNbaV0ubGVuZ3RoO1xuXG4gIGZvciAodmFyIHIgPSBBcnJheShzKSwgayA9IDAsIGkgPSAwOyBpIDwgaWw7IGkrKykgZm9yICh2YXIgYSA9IGFyZ3VtZW50c1tpXSwgaiA9IDAsIGpsID0gYS5sZW5ndGg7IGogPCBqbDsgaisrLCBrKyspIHJba10gPSBhW2pdO1xuXG4gIHJldHVybiByO1xufVxuXG5mdW5jdGlvbiB0aW55VGhyb3R0bGUobnVtKSB7XG4gIHJldHVybiB0aHJvdHRsZShudW0sIFRJTllfTlVNKTtcbn1cbmZ1bmN0aW9uIGlzU2FtZUNvbnN0YW50cyhsaW5lYXJDb25zdGFudHMxLCBsaW5lYXJDb25zdGFudHMyKSB7XG4gIHJldHVybiBsaW5lYXJDb25zdGFudHMxLmV2ZXJ5KGZ1bmN0aW9uICh2LCBpKSB7XG4gICAgcmV0dXJuIHRpbnlUaHJvdHRsZSh2IC0gbGluZWFyQ29uc3RhbnRzMltpXSkgPT09IDA7XG4gIH0pO1xufVxuZnVuY3Rpb24gaXNTYW1lUG9pbnQocG9pbnQxLCBwb2ludDIpIHtcbiAgcmV0dXJuICF0aW55VGhyb3R0bGUocG9pbnQxWzBdIC0gcG9pbnQyWzBdKSAmJiAhdGlueVRocm90dGxlKHBvaW50MVsxXSAtIHBvaW50MlsxXSk7XG59XG5mdW5jdGlvbiBmbGF0KGFycikge1xuICByZXR1cm4gYXJyLnJlZHVjZShmdW5jdGlvbiAocHJldiwgY3VycmVudCkge1xuICAgIHByZXYucHVzaC5hcHBseShwcmV2LCBjdXJyZW50KTtcbiAgICByZXR1cm4gcHJldjtcbiAgfSwgW10pO1xufVxuXG4vKipcbiAqIEBuYW1lc3BhY2UgT3ZlcmxhcEFyZWFcbiAqL1xuXG4vKipcbiAqIEdldHMgdGhlIHNpemUgb2YgYSBzaGFwZSAocG9seWdvbikgbWFkZSBvZiBwb2ludHMuXG4gKiBAbWVtYmVyb2YgT3ZlcmxhcEFyZWFcbiAqL1xuXG5mdW5jdGlvbiBnZXRBcmVhU2l6ZShwb2ludHMpIHtcbiAgaWYgKHBvaW50cy5sZW5ndGggPCAzKSB7XG4gICAgcmV0dXJuIDA7XG4gIH1cblxuICByZXR1cm4gTWF0aC5hYnMoc3VtKHBvaW50cy5tYXAoZnVuY3Rpb24gKHBvaW50LCBpKSB7XG4gICAgdmFyIG5leHRQb2ludCA9IHBvaW50c1tpICsgMV0gfHwgcG9pbnRzWzBdO1xuICAgIHJldHVybiBwb2ludFswXSAqIG5leHRQb2ludFsxXSAtIG5leHRQb2ludFswXSAqIHBvaW50WzFdO1xuICB9KSkpIC8gMjtcbn1cbi8qKlxuICogR2V0IHBvaW50cyB0aGF0IGZpdCB0aGUgcmVjdCxcbiAqIEBtZW1iZXJvZiBPdmVybGFwQXJlYVxuICovXG5cbmZ1bmN0aW9uIGZpdFBvaW50cyhwb2ludHMsIHJlY3QpIHtcbiAgdmFyIHdpZHRoID0gcmVjdC53aWR0aCxcbiAgICAgIGhlaWdodCA9IHJlY3QuaGVpZ2h0LFxuICAgICAgbGVmdCA9IHJlY3QubGVmdCxcbiAgICAgIHRvcCA9IHJlY3QudG9wO1xuXG4gIHZhciBfYSA9IGdldE1pbk1heHMocG9pbnRzKSxcbiAgICAgIG1pblggPSBfYS5taW5YLFxuICAgICAgbWluWSA9IF9hLm1pblksXG4gICAgICBtYXhYID0gX2EubWF4WCxcbiAgICAgIG1heFkgPSBfYS5tYXhZO1xuXG4gIHZhciByYXRpb1ggPSB3aWR0aCAvIChtYXhYIC0gbWluWCk7XG4gIHZhciByYXRpb1kgPSBoZWlnaHQgLyAobWF4WSAtIG1pblkpO1xuICByZXR1cm4gcG9pbnRzLm1hcChmdW5jdGlvbiAocG9pbnQpIHtcbiAgICByZXR1cm4gW2xlZnQgKyAocG9pbnRbMF0gLSBtaW5YKSAqIHJhdGlvWCwgdG9wICsgKHBvaW50WzFdIC0gbWluWSkgKiByYXRpb1ldO1xuICB9KTtcbn1cbi8qKlxuICogR2V0IHRoZSBtaW5pbXVtIGFuZCBtYXhpbXVtIHBvaW50cyBvZiB0aGUgcG9pbnRzLlxuICogQG1lbWJlcm9mIE92ZXJsYXBBcmVhXG4gKi9cblxuZnVuY3Rpb24gZ2V0TWluTWF4cyhwb2ludHMpIHtcbiAgdmFyIHhzID0gcG9pbnRzLm1hcChmdW5jdGlvbiAocG9pbnQpIHtcbiAgICByZXR1cm4gcG9pbnRbMF07XG4gIH0pO1xuICB2YXIgeXMgPSBwb2ludHMubWFwKGZ1bmN0aW9uIChwb2ludCkge1xuICAgIHJldHVybiBwb2ludFsxXTtcbiAgfSk7XG4gIHJldHVybiB7XG4gICAgbWluWDogTWF0aC5taW4uYXBwbHkoTWF0aCwgeHMpLFxuICAgIG1pblk6IE1hdGgubWluLmFwcGx5KE1hdGgsIHlzKSxcbiAgICBtYXhYOiBNYXRoLm1heC5hcHBseShNYXRoLCB4cyksXG4gICAgbWF4WTogTWF0aC5tYXguYXBwbHkoTWF0aCwgeXMpXG4gIH07XG59XG4vKipcbiAqIFdoZXRoZXIgdGhlIHBvaW50IGlzIGluIHNoYXBlXG4gKiBAcGFyYW0gLSBwb2ludCBwb3NcbiAqIEBwYXJhbSAtIHNoYXBlIHBvaW50c1xuICogQHBhcmFtIC0gd2hldGhlciB0byBjaGVjayBleGNlcHQgbGluZVxuICogQG1lbWJlcm9mIE92ZXJsYXBBcmVhXG4gKi9cblxuZnVuY3Rpb24gaXNJbnNpZGUocG9zLCBwb2ludHMsIGV4Y2x1ZGVMaW5lKSB7XG4gIHZhciB4ID0gcG9zWzBdLFxuICAgICAgeSA9IHBvc1sxXTtcblxuICB2YXIgX2EgPSBnZXRNaW5NYXhzKHBvaW50cyksXG4gICAgICBtaW5YID0gX2EubWluWCxcbiAgICAgIG1heFggPSBfYS5tYXhYO1xuXG4gIHZhciB4TGluZSA9IFtbbWluWCwgeV0sIFttYXhYLCB5XV07XG4gIHZhciB4TGluZWFyQ29uc3RhbnRzID0gZ2V0TGluZWFyQ29uc3RhbnRzKHhMaW5lWzBdLCB4TGluZVsxXSk7XG4gIHZhciBsaW5lcyA9IGNvbnZlcnRMaW5lcyhwb2ludHMpO1xuICB2YXIgaW50ZXJzZWN0aW9uUG9zSW5mb3MgPSBbXTtcbiAgbGluZXMuZm9yRWFjaChmdW5jdGlvbiAobGluZSkge1xuICAgIHZhciBsaW5lYXJDb25zdGFudHMgPSBnZXRMaW5lYXJDb25zdGFudHMobGluZVswXSwgbGluZVsxXSk7XG4gICAgdmFyIHN0YW5kYXJkUG9pbnQgPSBsaW5lWzBdO1xuXG4gICAgaWYgKGlzU2FtZUNvbnN0YW50cyh4TGluZWFyQ29uc3RhbnRzLCBsaW5lYXJDb25zdGFudHMpKSB7XG4gICAgICBpbnRlcnNlY3Rpb25Qb3NJbmZvcy5wdXNoKHtcbiAgICAgICAgcG9zOiBwb3MsXG4gICAgICAgIGxpbmU6IGxpbmUsXG4gICAgICAgIHR5cGU6IFwibGluZVwiXG4gICAgICB9KTtcbiAgICB9IGVsc2Uge1xuICAgICAgdmFyIHhQb2ludHMgPSBnZXRQb2ludHNPbkxpbmVzKGdldEludGVyc2VjdGlvblBvaW50c0J5Q29uc3RhbnRzKHhMaW5lYXJDb25zdGFudHMsIGxpbmVhckNvbnN0YW50cyksIFt4TGluZSwgbGluZV0pO1xuICAgICAgeFBvaW50cy5mb3JFYWNoKGZ1bmN0aW9uIChwb2ludCkge1xuICAgICAgICBpZiAobGluZS5zb21lKGZ1bmN0aW9uIChsaW5lUG9pbnQpIHtcbiAgICAgICAgICByZXR1cm4gaXNTYW1lUG9pbnQobGluZVBvaW50LCBwb2ludCk7XG4gICAgICAgIH0pKSB7XG4gICAgICAgICAgaW50ZXJzZWN0aW9uUG9zSW5mb3MucHVzaCh7XG4gICAgICAgICAgICBwb3M6IHBvaW50LFxuICAgICAgICAgICAgbGluZTogbGluZSxcbiAgICAgICAgICAgIHR5cGU6IFwicG9pbnRcIlxuICAgICAgICAgIH0pO1xuICAgICAgICB9IGVsc2UgaWYgKHRpbnlUaHJvdHRsZShzdGFuZGFyZFBvaW50WzFdIC0geSkgIT09IDApIHtcbiAgICAgICAgICBpbnRlcnNlY3Rpb25Qb3NJbmZvcy5wdXNoKHtcbiAgICAgICAgICAgIHBvczogcG9pbnQsXG4gICAgICAgICAgICBsaW5lOiBsaW5lLFxuICAgICAgICAgICAgdHlwZTogXCJpbnRlcnNlY3Rpb25cIlxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICB9XG4gIH0pO1xuXG4gIGlmICghZXhjbHVkZUxpbmUpIHtcbiAgICAvLyBvbiBsaW5lXG4gICAgaWYgKGZpbmQoaW50ZXJzZWN0aW9uUG9zSW5mb3MsIGZ1bmN0aW9uIChwKSB7XG4gICAgICByZXR1cm4gcFswXSA9PT0geDtcbiAgICB9KSkge1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICB9XG5cbiAgdmFyIGludGVyc2VjdGlvbkNvdW50ID0gMDtcbiAgdmFyIHhNYXAgPSB7fTtcbiAgaW50ZXJzZWN0aW9uUG9zSW5mb3MuZm9yRWFjaChmdW5jdGlvbiAoX2EpIHtcbiAgICB2YXIgcG9zID0gX2EucG9zLFxuICAgICAgICB0eXBlID0gX2EudHlwZSxcbiAgICAgICAgbGluZSA9IF9hLmxpbmU7XG5cbiAgICBpZiAocG9zWzBdID4geCkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGlmICh0eXBlID09PSBcImludGVyc2VjdGlvblwiKSB7XG4gICAgICArK2ludGVyc2VjdGlvbkNvdW50O1xuICAgIH0gZWxzZSBpZiAodHlwZSA9PT0gXCJsaW5lXCIpIHtcbiAgICAgIHJldHVybjtcbiAgICB9IGVsc2UgaWYgKHR5cGUgPT09IFwicG9pbnRcIikge1xuICAgICAgdmFyIHBvaW50ID0gZmluZChsaW5lLCBmdW5jdGlvbiAobGluZVBvaW50KSB7XG4gICAgICAgIHJldHVybiBsaW5lUG9pbnRbMV0gIT09IHk7XG4gICAgICB9KTtcbiAgICAgIHZhciBwcmV2VmFsdWUgPSB4TWFwW3Bvc1swXV07XG4gICAgICB2YXIgbmV4dFZhbHVlID0gcG9pbnRbMV0gPiB5ID8gMSA6IC0xO1xuXG4gICAgICBpZiAoIXByZXZWYWx1ZSkge1xuICAgICAgICB4TWFwW3Bvc1swXV0gPSBuZXh0VmFsdWU7XG4gICAgICB9IGVsc2UgaWYgKHByZXZWYWx1ZSAhPT0gbmV4dFZhbHVlKSB7XG4gICAgICAgICsraW50ZXJzZWN0aW9uQ291bnQ7XG4gICAgICB9XG4gICAgfVxuICB9KTtcbiAgcmV0dXJuIGludGVyc2VjdGlvbkNvdW50ICUgMiA9PT0gMTtcbn1cbi8qKlxuICogR2V0IGRpc3RhbmNlIGZyb20gcG9pbnQgdG8gY29uc3RhbnRzLiBbYSwgYiwgY10gKGF4ICsgYnkgKyBjID0gMClcbiAqIEByZXR1cm4gW2EsIGIsIGNdXG4gKiBAbWVtYmVyb2YgT3ZlcmxhcEFyZWFcbiAqL1xuXG5mdW5jdGlvbiBnZXREaXN0YW5jZUZyb21Qb2ludFRvQ29uc3RhbnRzKF9hLCBwb3MpIHtcbiAgdmFyIGEgPSBfYVswXSxcbiAgICAgIGIgPSBfYVsxXSxcbiAgICAgIGMgPSBfYVsyXTtcbiAgcmV0dXJuIChhICogcG9zWzBdICsgYiAqIHBvc1sxXSArIGMpIC8gKGEgKiBhICsgYiAqIGIpO1xufVxuLyoqXG4gKiBHZXQgdGhlIGNvZWZmaWNpZW50IG9mIHRoZSBsaW5lYXIgZnVuY3Rpb24uIFthLCBiLCBjXSAoYXggKyBieSArIGMgPSAwKVxuICogQHJldHVybiBbYSwgYiwgY11cbiAqIEBtZW1iZXJvZiBPdmVybGFwQXJlYVxuICovXG5cbmZ1bmN0aW9uIGdldExpbmVhckNvbnN0YW50cyhwb2ludDEsIHBvaW50Mikge1xuICB2YXIgeDEgPSBwb2ludDFbMF0sXG4gICAgICB5MSA9IHBvaW50MVsxXTtcbiAgdmFyIHgyID0gcG9pbnQyWzBdLFxuICAgICAgeTIgPSBwb2ludDJbMV07IC8vIGF4ICsgYnkgKyBjID0gMFxuICAvLyBbYSwgYiwgY11cblxuICB2YXIgZHggPSB4MiAtIHgxO1xuICB2YXIgZHkgPSB5MiAtIHkxO1xuXG4gIGlmIChNYXRoLmFicyhkeCkgPCBUSU5ZX05VTSkge1xuICAgIGR4ID0gMDtcbiAgfVxuXG4gIGlmIChNYXRoLmFicyhkeSkgPCBUSU5ZX05VTSkge1xuICAgIGR5ID0gMDtcbiAgfSAvLyBiID4gMFxuICAvLyBheCArIGJ5ICsgYyA9IDBcblxuXG4gIHZhciBhID0gMDtcbiAgdmFyIGIgPSAwO1xuICB2YXIgYyA9IDA7XG5cbiAgaWYgKCFkeCkge1xuICAgIGlmIChkeSkge1xuICAgICAgLy8gLXggKyAxID0gMFxuICAgICAgYSA9IC0xO1xuICAgICAgYyA9IHgxO1xuICAgIH1cbiAgfSBlbHNlIGlmICghZHkpIHtcbiAgICAvLyB5IC0gMSA9IDBcbiAgICBiID0gMTtcbiAgICBjID0gLXkxO1xuICB9IGVsc2Uge1xuICAgIC8vIHkgPSAtYSh4IC0geDEpICsgeTFcbiAgICAvLyBheCArIHkgKyBhICogeDEgLSB5MSA9IDBcbiAgICBhID0gLWR5IC8gZHg7XG4gICAgYiA9IDE7XG4gICAgYyA9IC1hICogeDEgLSB5MTtcbiAgfVxuXG4gIHJldHVybiBbYSwgYiwgY107XG59XG4vKipcbiAqIEdldCBpbnRlcnNlY3Rpb24gcG9pbnRzIHdpdGggbGluZWFyIGZ1bmN0aW9ucy5cbiAqIEBtZW1iZXJvZiBPdmVybGFwQXJlYVxuICovXG5cbmZ1bmN0aW9uIGdldEludGVyc2VjdGlvblBvaW50c0J5Q29uc3RhbnRzKGxpbmVhckNvbnN0YW50czEsIGxpbmVhckNvbnN0YW50czIpIHtcbiAgdmFyIGExID0gbGluZWFyQ29uc3RhbnRzMVswXSxcbiAgICAgIGIxID0gbGluZWFyQ29uc3RhbnRzMVsxXSxcbiAgICAgIGMxID0gbGluZWFyQ29uc3RhbnRzMVsyXTtcbiAgdmFyIGEyID0gbGluZWFyQ29uc3RhbnRzMlswXSxcbiAgICAgIGIyID0gbGluZWFyQ29uc3RhbnRzMlsxXSxcbiAgICAgIGMyID0gbGluZWFyQ29uc3RhbnRzMlsyXTtcbiAgdmFyIGlzWmVyb0EgPSBhMSA9PT0gMCAmJiBhMiA9PT0gMDtcbiAgdmFyIGlzWmVyb0IgPSBiMSA9PT0gMCAmJiBiMiA9PT0gMDtcbiAgdmFyIHJlc3VsdHMgPSBbXTtcblxuICBpZiAoaXNaZXJvQSAmJiBpc1plcm9CKSB7XG4gICAgcmV0dXJuIFtdO1xuICB9IGVsc2UgaWYgKGlzWmVyb0EpIHtcbiAgICAvLyBiMSAqIHkgKyBjMSA9IDBcbiAgICAvLyBiMiAqIHkgKyBjMiA9IDBcbiAgICB2YXIgeTEgPSAtYzEgLyBiMTtcbiAgICB2YXIgeTIgPSAtYzIgLyBiMjtcblxuICAgIGlmICh5MSAhPT0geTIpIHtcbiAgICAgIHJldHVybiBbXTtcbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuIFtbLUluZmluaXR5LCB5MV0sIFtJbmZpbml0eSwgeTFdXTtcbiAgICB9XG4gIH0gZWxzZSBpZiAoaXNaZXJvQikge1xuICAgIC8vIGExICogeCArIGMxID0gMFxuICAgIC8vIGEyICogeCArIGMyID0gMFxuICAgIHZhciB4MSA9IC1jMSAvIGExO1xuICAgIHZhciB4MiA9IC1jMiAvIGEyO1xuXG4gICAgaWYgKHgxICE9PSB4Mikge1xuICAgICAgcmV0dXJuIFtdO1xuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4gW1t4MSwgLUluZmluaXR5XSwgW3gxLCBJbmZpbml0eV1dO1xuICAgIH1cbiAgfSBlbHNlIGlmIChhMSA9PT0gMCkge1xuICAgIC8vIGIxICogeSArIGMxID0gMFxuICAgIC8vIHkgPSAtIGMxIC8gYjE7XG4gICAgLy8gYTIgKiB4ICsgYjIgKiB5ICsgYzIgPSAwXG4gICAgdmFyIHkgPSAtYzEgLyBiMTtcbiAgICB2YXIgeCA9IC0oYjIgKiB5ICsgYzIpIC8gYTI7XG4gICAgcmVzdWx0cyA9IFtbeCwgeV1dO1xuICB9IGVsc2UgaWYgKGEyID09PSAwKSB7XG4gICAgLy8gYjIgKiB5ICsgYzIgPSAwXG4gICAgLy8geSA9IC0gYzIgLyBiMjtcbiAgICAvLyBhMSAqIHggKyBiMSAqIHkgKyBjMSA9IDBcbiAgICB2YXIgeSA9IC1jMiAvIGIyO1xuICAgIHZhciB4ID0gLShiMSAqIHkgKyBjMSkgLyBhMTtcbiAgICByZXN1bHRzID0gW1t4LCB5XV07XG4gIH0gZWxzZSBpZiAoYjEgPT09IDApIHtcbiAgICAvLyBhMSAqIHggKyBjMSA9IDBcbiAgICAvLyB4ID0gLSBjMSAvIGExO1xuICAgIC8vIGEyICogeCArIGIyICogeSArIGMyID0gMFxuICAgIHZhciB4ID0gLWMxIC8gYTE7XG4gICAgdmFyIHkgPSAtKGEyICogeCArIGMyKSAvIGIyO1xuICAgIHJlc3VsdHMgPSBbW3gsIHldXTtcbiAgfSBlbHNlIGlmIChiMiA9PT0gMCkge1xuICAgIC8vIGEyICogeCArIGMyID0gMFxuICAgIC8vIHggPSAtIGMyIC8gYTI7XG4gICAgLy8gYTEgKiB4ICsgYjEgKiB5ICsgYzEgPSAwXG4gICAgdmFyIHggPSAtYzIgLyBhMjtcbiAgICB2YXIgeSA9IC0oYTEgKiB4ICsgYzEpIC8gYjE7XG4gICAgcmVzdWx0cyA9IFtbeCwgeV1dO1xuICB9IGVsc2Uge1xuICAgIC8vIGExICogeCArIGIxICogeSArIGMxID0gMFxuICAgIC8vIGEyICogeCArIGIyICogeSArIGMyID0gMFxuICAgIC8vIGIyICogYTEgKiB4ICsgYjIgKiBiMSAqIHkgKyBiMiAqIGMxID0gMFxuICAgIC8vIGIxICogYTIgKiB4ICsgYjEgKiBiMiAqIHkgKyBiMSAqIGMyID0gMFxuICAgIC8vIChiMiAqIGExIC0gYjEgKiBhMikgICogeCA9IChiMSAqIGMyIC0gYjIgKiBjMSlcbiAgICB2YXIgeCA9IChiMSAqIGMyIC0gYjIgKiBjMSkgLyAoYjIgKiBhMSAtIGIxICogYTIpO1xuICAgIHZhciB5ID0gLShhMSAqIHggKyBjMSkgLyBiMTtcbiAgICByZXN1bHRzID0gW1t4LCB5XV07XG4gIH1cblxuICByZXR1cm4gcmVzdWx0cy5tYXAoZnVuY3Rpb24gKHJlc3VsdCkge1xuICAgIHJldHVybiBbcmVzdWx0WzBdLCByZXN1bHRbMV1dO1xuICB9KTtcbn1cbi8qKlxuICogR2V0IGludGVyc2VjdGlvbiBwb2ludHMgdG8gdGhlIHR3byBsaW5lcy5cbiAqIEBtZW1iZXJvZiBPdmVybGFwQXJlYVxuICovXG5cbmZ1bmN0aW9uIGdldEludGVyc2VjdGlvblBvaW50cyhsaW5lMSwgbGluZTIsIGlzTGltaXQpIHtcbiAgdmFyIHBvaW50cyA9IGdldEludGVyc2VjdGlvblBvaW50c0J5Q29uc3RhbnRzKGdldExpbmVhckNvbnN0YW50cyhsaW5lMVswXSwgbGluZTFbMV0pLCBnZXRMaW5lYXJDb25zdGFudHMobGluZTJbMF0sIGxpbmUyWzFdKSk7XG5cbiAgaWYgKGlzTGltaXQpIHtcbiAgICByZXR1cm4gZ2V0UG9pbnRzT25MaW5lcyhwb2ludHMsIFtsaW5lMSwgbGluZTJdKTtcbiAgfVxuXG4gIHJldHVybiBwb2ludHM7XG59XG5mdW5jdGlvbiBpc1BvaW50T25MaW5lKHBvcywgbGluZSkge1xuICB2YXIgbGluZWFyQ29uc3RhbnRzID0gZ2V0TGluZWFyQ29uc3RhbnRzKGxpbmVbMF0sIGxpbmVbMV0pO1xuICByZXR1cm4gdGlueVRocm90dGxlKGdldERpc3RhbmNlRnJvbVBvaW50VG9Db25zdGFudHMobGluZWFyQ29uc3RhbnRzLCBwb3MpKSA9PT0gMDtcbn1cbi8qKlxuICogR2V0IHRoZSBwb2ludHMgb24gdGhlIGxpbmVzIChiZXR3ZWVuIHR3byBwb2ludHMpLlxuICogQG1lbWJlcm9mIE92ZXJsYXBBcmVhXG4gKi9cblxuZnVuY3Rpb24gZ2V0UG9pbnRzT25MaW5lcyhwb2ludHMsIGxpbmVzKSB7XG4gIHZhciBtaW5NYXhzID0gbGluZXMubWFwKGZ1bmN0aW9uIChsaW5lKSB7XG4gICAgcmV0dXJuIFswLCAxXS5tYXAoZnVuY3Rpb24gKG9yZGVyKSB7XG4gICAgICByZXR1cm4gW01hdGgubWluKGxpbmVbMF1bb3JkZXJdLCBsaW5lWzFdW29yZGVyXSksIE1hdGgubWF4KGxpbmVbMF1bb3JkZXJdLCBsaW5lWzFdW29yZGVyXSldO1xuICAgIH0pO1xuICB9KTtcbiAgdmFyIHJlc3VsdHMgPSBbXTtcblxuICBpZiAocG9pbnRzLmxlbmd0aCA9PT0gMikge1xuICAgIHZhciBfYSA9IHBvaW50c1swXSxcbiAgICAgICAgeCA9IF9hWzBdLFxuICAgICAgICB5ID0gX2FbMV07XG5cbiAgICBpZiAoIXRpbnlUaHJvdHRsZSh4IC0gcG9pbnRzWzFdWzBdKSkge1xuICAgICAgLy8vIE1hdGgubWF4KG1pblkxLCBtaW5ZMilcbiAgICAgIHZhciB0b3AgPSBNYXRoLm1heC5hcHBseShNYXRoLCBtaW5NYXhzLm1hcChmdW5jdGlvbiAobWluTWF4KSB7XG4gICAgICAgIHJldHVybiBtaW5NYXhbMV1bMF07XG4gICAgICB9KSk7IC8vLyBNYXRoLm1pbihtYXhZMSwgbWlheDIpXG5cbiAgICAgIHZhciBib3R0b20gPSBNYXRoLm1pbi5hcHBseShNYXRoLCBtaW5NYXhzLm1hcChmdW5jdGlvbiAobWluTWF4KSB7XG4gICAgICAgIHJldHVybiBtaW5NYXhbMV1bMV07XG4gICAgICB9KSk7XG5cbiAgICAgIGlmICh0aW55VGhyb3R0bGUodG9wIC0gYm90dG9tKSA+IDApIHtcbiAgICAgICAgcmV0dXJuIFtdO1xuICAgICAgfVxuXG4gICAgICByZXN1bHRzID0gW1t4LCB0b3BdLCBbeCwgYm90dG9tXV07XG4gICAgfSBlbHNlIGlmICghdGlueVRocm90dGxlKHkgLSBwb2ludHNbMV1bMV0pKSB7XG4gICAgICAvLy8gTWF0aC5tYXgobWluWTEsIG1pblkyKVxuICAgICAgdmFyIGxlZnQgPSBNYXRoLm1heC5hcHBseShNYXRoLCBtaW5NYXhzLm1hcChmdW5jdGlvbiAobWluTWF4KSB7XG4gICAgICAgIHJldHVybiBtaW5NYXhbMF1bMF07XG4gICAgICB9KSk7IC8vLyBNYXRoLm1pbihtYXhZMSwgbWlheDIpXG5cbiAgICAgIHZhciByaWdodCA9IE1hdGgubWluLmFwcGx5KE1hdGgsIG1pbk1heHMubWFwKGZ1bmN0aW9uIChtaW5NYXgpIHtcbiAgICAgICAgcmV0dXJuIG1pbk1heFswXVsxXTtcbiAgICAgIH0pKTtcblxuICAgICAgaWYgKHRpbnlUaHJvdHRsZShsZWZ0IC0gcmlnaHQpID4gMCkge1xuICAgICAgICByZXR1cm4gW107XG4gICAgICB9XG5cbiAgICAgIHJlc3VsdHMgPSBbW2xlZnQsIHldLCBbcmlnaHQsIHldXTtcbiAgICB9XG4gIH1cblxuICBpZiAoIXJlc3VsdHMubGVuZ3RoKSB7XG4gICAgcmVzdWx0cyA9IHBvaW50cy5maWx0ZXIoZnVuY3Rpb24gKHBvaW50KSB7XG4gICAgICB2YXIgcG9pbnRYID0gcG9pbnRbMF0sXG4gICAgICAgICAgcG9pbnRZID0gcG9pbnRbMV07XG4gICAgICByZXR1cm4gbWluTWF4cy5ldmVyeShmdW5jdGlvbiAobWluTWF4KSB7XG4gICAgICAgIHJldHVybiAwIDw9IHRpbnlUaHJvdHRsZShwb2ludFggLSBtaW5NYXhbMF1bMF0pICYmIDAgPD0gdGlueVRocm90dGxlKG1pbk1heFswXVsxXSAtIHBvaW50WCkgJiYgMCA8PSB0aW55VGhyb3R0bGUocG9pbnRZIC0gbWluTWF4WzFdWzBdKSAmJiAwIDw9IHRpbnlUaHJvdHRsZShtaW5NYXhbMV1bMV0gLSBwb2ludFkpO1xuICAgICAgfSk7XG4gICAgfSk7XG4gIH1cblxuICByZXR1cm4gcmVzdWx0cy5tYXAoZnVuY3Rpb24gKHJlc3VsdCkge1xuICAgIHJldHVybiBbdGlueVRocm90dGxlKHJlc3VsdFswXSksIHRpbnlUaHJvdHRsZShyZXN1bHRbMV0pXTtcbiAgfSk7XG59XG4vKipcbiogQ29udmVydCB0d28gcG9pbnRzIGludG8gbGluZXMuXG4qIEBmdW5jdGlvblxuKiBAbWVtYmVyb2YgT3ZlcmxhcEFyZWFcbiovXG5cbmZ1bmN0aW9uIGNvbnZlcnRMaW5lcyhwb2ludHMpIHtcbiAgcmV0dXJuIF9fc3ByZWFkQXJyYXlzKHBvaW50cy5zbGljZSgxKSwgW3BvaW50c1swXV0pLm1hcChmdW5jdGlvbiAocG9pbnQsIGkpIHtcbiAgICByZXR1cm4gW3BvaW50c1tpXSwgcG9pbnRdO1xuICB9KTtcbn1cblxuZnVuY3Rpb24gZ2V0T3ZlcmxhcFBvaW50SW5mb3MocG9pbnRzMSwgcG9pbnRzMikge1xuICB2YXIgdGFyZ2V0UG9pbnRzMSA9IHBvaW50czEuc2xpY2UoKTtcbiAgdmFyIHRhcmdldFBvaW50czIgPSBwb2ludHMyLnNsaWNlKCk7XG5cbiAgaWYgKGdldFNoYXBlRGlyZWN0aW9uKHRhcmdldFBvaW50czEpID09PSAtMSkge1xuICAgIHRhcmdldFBvaW50czEucmV2ZXJzZSgpO1xuICB9XG5cbiAgaWYgKGdldFNoYXBlRGlyZWN0aW9uKHRhcmdldFBvaW50czIpID09PSAtMSkge1xuICAgIHRhcmdldFBvaW50czIucmV2ZXJzZSgpO1xuICB9XG5cbiAgdmFyIGxpbmVzMSA9IGNvbnZlcnRMaW5lcyh0YXJnZXRQb2ludHMxKTtcbiAgdmFyIGxpbmVzMiA9IGNvbnZlcnRMaW5lcyh0YXJnZXRQb2ludHMyKTtcbiAgdmFyIGxpbmVhckNvbnN0YW50c0xpc3QxID0gbGluZXMxLm1hcChmdW5jdGlvbiAobGluZTEpIHtcbiAgICByZXR1cm4gZ2V0TGluZWFyQ29uc3RhbnRzKGxpbmUxWzBdLCBsaW5lMVsxXSk7XG4gIH0pO1xuICB2YXIgbGluZWFyQ29uc3RhbnRzTGlzdDIgPSBsaW5lczIubWFwKGZ1bmN0aW9uIChsaW5lMikge1xuICAgIHJldHVybiBnZXRMaW5lYXJDb25zdGFudHMobGluZTJbMF0sIGxpbmUyWzFdKTtcbiAgfSk7XG4gIHZhciBvdmVybGFwSW5mb3MgPSBbXTtcbiAgbGluZWFyQ29uc3RhbnRzTGlzdDEuZm9yRWFjaChmdW5jdGlvbiAobGluZWFyQ29uc3RhbnRzMSwgaSkge1xuICAgIHZhciBsaW5lMSA9IGxpbmVzMVtpXTtcbiAgICB2YXIgbGluZVBvaW50SW5mb3MgPSBbXTtcbiAgICBsaW5lYXJDb25zdGFudHNMaXN0Mi5mb3JFYWNoKGZ1bmN0aW9uIChsaW5lYXJDb25zdGFudHMyLCBqKSB7XG4gICAgICB2YXIgaW50ZXJzZWN0aW9uUG9pbnRzID0gZ2V0SW50ZXJzZWN0aW9uUG9pbnRzQnlDb25zdGFudHMobGluZWFyQ29uc3RhbnRzMSwgbGluZWFyQ29uc3RhbnRzMik7XG4gICAgICB2YXIgcG9pbnRzID0gZ2V0UG9pbnRzT25MaW5lcyhpbnRlcnNlY3Rpb25Qb2ludHMsIFtsaW5lMSwgbGluZXMyW2pdXSk7XG4gICAgICBsaW5lUG9pbnRJbmZvcy5wdXNoLmFwcGx5KGxpbmVQb2ludEluZm9zLCBwb2ludHMubWFwKGZ1bmN0aW9uIChwb3MpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBpbmRleDE6IGksXG4gICAgICAgICAgaW5kZXgyOiBqLFxuICAgICAgICAgIHBvczogcG9zLFxuICAgICAgICAgIHR5cGU6IFwiaW50ZXJzZWN0aW9uXCJcbiAgICAgICAgfTtcbiAgICAgIH0pKTtcbiAgICB9KTtcbiAgICBsaW5lUG9pbnRJbmZvcy5zb3J0KGZ1bmN0aW9uIChhLCBiKSB7XG4gICAgICByZXR1cm4gZ2V0RGlzdChsaW5lMVswXSwgYS5wb3MpIC0gZ2V0RGlzdChsaW5lMVswXSwgYi5wb3MpO1xuICAgIH0pO1xuICAgIG92ZXJsYXBJbmZvcy5wdXNoLmFwcGx5KG92ZXJsYXBJbmZvcywgbGluZVBvaW50SW5mb3MpO1xuXG4gICAgaWYgKGlzSW5zaWRlKGxpbmUxWzFdLCB0YXJnZXRQb2ludHMyKSkge1xuICAgICAgb3ZlcmxhcEluZm9zLnB1c2goe1xuICAgICAgICBpbmRleDE6IGksXG4gICAgICAgIGluZGV4MjogLTEsXG4gICAgICAgIHBvczogbGluZTFbMV0sXG4gICAgICAgIHR5cGU6IFwiaW5zaWRlXCJcbiAgICAgIH0pO1xuICAgIH1cbiAgfSk7XG4gIGxpbmVzMi5mb3JFYWNoKGZ1bmN0aW9uIChsaW5lMiwgaSkge1xuICAgIGlmICghaXNJbnNpZGUobGluZTJbMV0sIHRhcmdldFBvaW50czEpKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdmFyIGlzTmV4dCA9IGZhbHNlO1xuICAgIHZhciBpbmRleCA9IGZpbmRJbmRleChvdmVybGFwSW5mb3MsIGZ1bmN0aW9uIChfYSkge1xuICAgICAgdmFyIGluZGV4MiA9IF9hLmluZGV4MjtcblxuICAgICAgaWYgKGluZGV4MiA9PT0gaSkge1xuICAgICAgICBpc05leHQgPSB0cnVlO1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG5cbiAgICAgIGlmIChpc05leHQpIHtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9KTtcblxuICAgIGlmIChpbmRleCA9PT0gLTEpIHtcbiAgICAgIGlzTmV4dCA9IGZhbHNlO1xuICAgICAgaW5kZXggPSBmaW5kSW5kZXgob3ZlcmxhcEluZm9zLCBmdW5jdGlvbiAoX2EpIHtcbiAgICAgICAgdmFyIGluZGV4MSA9IF9hLmluZGV4MSxcbiAgICAgICAgICAgIGluZGV4MiA9IF9hLmluZGV4MjtcblxuICAgICAgICBpZiAoaW5kZXgxID09PSAtMSAmJiBpbmRleDIgKyAxID09PSBpKSB7XG4gICAgICAgICAgaXNOZXh0ID0gdHJ1ZTtcbiAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cblxuICAgICAgICBpZiAoaXNOZXh0KSB7XG4gICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9KTtcbiAgICB9XG5cbiAgICBpZiAoaW5kZXggPT09IC0xKSB7XG4gICAgICBvdmVybGFwSW5mb3MucHVzaCh7XG4gICAgICAgIGluZGV4MTogLTEsXG4gICAgICAgIGluZGV4MjogaSxcbiAgICAgICAgcG9zOiBsaW5lMlsxXSxcbiAgICAgICAgdHlwZTogXCJpbnNpZGVcIlxuICAgICAgfSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIG92ZXJsYXBJbmZvcy5zcGxpY2UoaW5kZXgsIDAsIHtcbiAgICAgICAgaW5kZXgxOiAtMSxcbiAgICAgICAgaW5kZXgyOiBpLFxuICAgICAgICBwb3M6IGxpbmUyWzFdLFxuICAgICAgICB0eXBlOiBcImluc2lkZVwiXG4gICAgICB9KTtcbiAgICB9XG4gIH0pO1xuICB2YXIgcG9pbnRNYXAgPSB7fTtcbiAgcmV0dXJuIG92ZXJsYXBJbmZvcy5maWx0ZXIoZnVuY3Rpb24gKF9hKSB7XG4gICAgdmFyIHBvcyA9IF9hLnBvcztcbiAgICB2YXIga2V5ID0gcG9zWzBdICsgXCJ4XCIgKyBwb3NbMV07XG5cbiAgICBpZiAocG9pbnRNYXBba2V5XSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cblxuICAgIHBvaW50TWFwW2tleV0gPSB0cnVlO1xuICAgIHJldHVybiB0cnVlO1xuICB9KTtcbn1cbi8qKlxuKiBHZXQgdGhlIHBvaW50cyBvZiB0aGUgb3ZlcmxhcHBlZCBwYXJ0IG9mIHR3byBzaGFwZXMuXG4qIEBmdW5jdGlvblxuKiBAbWVtYmVyb2YgT3ZlcmxhcEFyZWFcbiovXG5cblxuZnVuY3Rpb24gZ2V0T3ZlcmxhcFBvaW50cyhwb2ludHMxLCBwb2ludHMyKSB7XG4gIHZhciBpbmZvcyA9IGdldE92ZXJsYXBQb2ludEluZm9zKHBvaW50czEsIHBvaW50czIpO1xuICByZXR1cm4gaW5mb3MubWFwKGZ1bmN0aW9uIChfYSkge1xuICAgIHZhciBwb3MgPSBfYS5wb3M7XG4gICAgcmV0dXJuIHBvcztcbiAgfSk7XG59XG5cbmZ1bmN0aW9uIGlzQ29ubmVjdGVkTGluZShsaW5lKSB7XG4gIHZhciBfYSA9IGxpbmVbMF0sXG4gICAgICBwcmV2SW5kZXgxID0gX2EuaW5kZXgxLFxuICAgICAgcHJldkluZGV4MiA9IF9hLmluZGV4MixcbiAgICAgIF9iID0gbGluZVsxXSxcbiAgICAgIG5leHRJbmRleDEgPSBfYi5pbmRleDEsXG4gICAgICBuZXh0SW5kZXgyID0gX2IuaW5kZXgyO1xuXG4gIGlmIChwcmV2SW5kZXgxICE9PSAtMSkge1xuICAgIC8vIHNhbWUgbGluZVxuICAgIGlmIChwcmV2SW5kZXgxID09PSBuZXh0SW5kZXgxKSB7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG5cbiAgICBpZiAocHJldkluZGV4MSArIDEgPT09IG5leHRJbmRleDEpIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgfVxuXG4gIGlmIChwcmV2SW5kZXgyICE9PSAtMSkge1xuICAgIC8vIHNhbWUgbGluZVxuICAgIGlmIChwcmV2SW5kZXgyID09PSBuZXh0SW5kZXgyKSB7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG5cbiAgICBpZiAocHJldkluZGV4MiArIDEgPT09IG5leHRJbmRleDIpIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiBmYWxzZTtcbn1cbi8qKlxuKiBHZXQgdGhlIGFyZWFzIG9mIHRoZSBvdmVybGFwcGVkIHBhcnQgb2YgdHdvIHNoYXBlcy5cbiogQGZ1bmN0aW9uXG4qIEBtZW1iZXJvZiBPdmVybGFwQXJlYVxuKi9cblxuXG5mdW5jdGlvbiBnZXRPdmVybGFwQXJlYXMocG9pbnRzMSwgcG9pbnRzMikge1xuICB2YXIgaW5mb3MgPSBnZXRPdmVybGFwUG9pbnRJbmZvcyhwb2ludHMxLCBwb2ludHMyKTtcbiAgdmFyIGFyZWFzID0gW107XG4gIHZhciBhcmVhO1xuICBnZXRPdmVybGFwUG9pbnRJbmZvcyhwb2ludHMxLCBwb2ludHMyKS5mb3JFYWNoKGZ1bmN0aW9uIChpbmZvLCBpLCBhcnIpIHtcbiAgICBpZiAoaSA9PT0gMCB8fCAhaXNDb25uZWN0ZWRMaW5lKFthcnJbaSAtIDFdLCBpbmZvXSkpIHtcbiAgICAgIGFyZWEgPSBbaW5mb107XG4gICAgICBhcmVhcy5wdXNoKGFyZWEpO1xuICAgIH0gZWxzZSB7XG4gICAgICBhcmVhLnB1c2goaW5mbyk7XG4gICAgfVxuICB9KTtcbiAgcmV0dXJuIGFyZWFzLm1hcChmdW5jdGlvbiAoYXJlYSkge1xuICAgIHJldHVybiBhcmVhLm1hcChmdW5jdGlvbiAoX2EpIHtcbiAgICAgIHZhciBwb3MgPSBfYS5wb3M7XG4gICAgICByZXR1cm4gcG9zO1xuICAgIH0pO1xuICB9KTtcbn1cblxuZnVuY3Rpb24gZmluZFJldmVyc2VkQXJlYXMocG9pbnRzMSwgcG9pbnRzMiwgaW5kZXgsIGFyZWFzKSB7XG4gIGlmIChpbmRleCA9PT0gdm9pZCAwKSB7XG4gICAgaW5kZXggPSAwO1xuICB9XG5cbiAgaWYgKGFyZWFzID09PSB2b2lkIDApIHtcbiAgICBhcmVhcyA9IFtdO1xuICB9XG5cbiAgdmFyIGlzRmlyc3QgPSBhcmVhcy5sZW5ndGggPT09IDA7XG4gIHZhciBsZW5ndGggPSBwb2ludHMxLmxlbmd0aDtcbiAgdmFyIG5leHRJbmRleCA9IHBvaW50czFbaW5kZXhdID8gaW5kZXggOiAwO1xuXG4gIHZhciBuZXh0UG9pbnRzMSA9IF9fc3ByZWFkQXJyYXlzKHBvaW50czEuc2xpY2UobmV4dEluZGV4KSwgcG9pbnRzMS5zbGljZSgwLCBuZXh0SW5kZXgpKTtcblxuICB2YXIgX2xvb3BfMSA9IGZ1bmN0aW9uIChpKSB7XG4gICAgdmFyIHBvaW50MSA9IG5leHRQb2ludHMxW2ldO1xuXG4gICAgaWYgKGZpbmQocG9pbnRzMiwgZnVuY3Rpb24gKHBvaW50Mikge1xuICAgICAgcmV0dXJuIHBvaW50MlswXSA9PT0gcG9pbnQxWzBdICYmIHBvaW50MlsxXSA9PT0gcG9pbnQxWzFdO1xuICAgIH0pKSB7XG4gICAgICByZXR1cm4gXCJjb250aW51ZVwiO1xuICAgIH1cblxuICAgIGlmIChhcmVhcy5zb21lKGZ1bmN0aW9uIChuZXh0QXJlYSkge1xuICAgICAgcmV0dXJuIGZpbmQobmV4dEFyZWEsIGZ1bmN0aW9uIChhcmVhUG9pbnQpIHtcbiAgICAgICAgcmV0dXJuIGFyZWFQb2ludFswXSA9PT0gcG9pbnQxWzBdICYmIGFyZWFQb2ludFsxXSA9PT0gcG9pbnQxWzFdO1xuICAgICAgfSk7XG4gICAgfSkpIHtcbiAgICAgIGlmIChpc0ZpcnN0KSB7XG4gICAgICAgIHJldHVybiBcImNvbnRpbnVlXCI7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4gXCJicmVha1wiO1xuICAgICAgfVxuICAgIH1cblxuICAgIHZhciBuZXh0QXJlYSA9IHZvaWQgMDtcblxuICAgIGlmIChpc0ZpcnN0KSB7XG4gICAgICBuZXh0QXJlYSA9IFtdO1xuICAgICAgYXJlYXMucHVzaChuZXh0QXJlYSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIG5leHRBcmVhID0gYXJlYXNbYXJlYXMubGVuZ3RoIC0gMV07XG4gICAgfVxuXG4gICAgbmV4dEFyZWEucHVzaChwb2ludDEpO1xuICAgIHZhciBsaW5lID0gW3BvaW50MSwgcG9pbnRzMVtpbmRleCArIDFdIHx8IHBvaW50czFbMF1dO1xuICAgIHZhciBuZXh0UG9pbnQyID0gcG9pbnRzMi5maWx0ZXIoZnVuY3Rpb24gKHBvaW50Mikge1xuICAgICAgcmV0dXJuIGlzUG9pbnRPbkxpbmUocG9pbnQyLCBsaW5lKTtcbiAgICB9KS5zb3J0KGZ1bmN0aW9uIChhLCBiKSB7XG4gICAgICByZXR1cm4gZ2V0RGlzdChwb2ludDEsIGEpIC0gZ2V0RGlzdChwb2ludDEsIGIpO1xuICAgIH0pWzBdO1xuXG4gICAgaWYgKCFuZXh0UG9pbnQyKSB7XG4gICAgICBmaW5kUmV2ZXJzZWRBcmVhcyhuZXh0UG9pbnRzMSwgcG9pbnRzMiwgaSArIDEsIGFyZWFzKTtcbiAgICAgIHJldHVybiBcImJyZWFrXCI7XG4gICAgfSBlbHNlIHtcbiAgICAgIHZhciBwb2ludDJJbmRleCA9IHBvaW50czIuaW5kZXhPZihuZXh0UG9pbnQyKTtcbiAgICAgIGZpbmRSZXZlcnNlZEFyZWFzKHBvaW50czIsIHBvaW50czEsIHBvaW50MkluZGV4LCBhcmVhcyk7XG5cbiAgICAgIGlmICghaXNGaXJzdCkge1xuICAgICAgICByZXR1cm4gXCJicmVha1wiO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICBmb3IgKHZhciBpID0gMDsgaSA8IGxlbmd0aDsgKytpKSB7XG4gICAgdmFyIHN0YXRlXzEgPSBfbG9vcF8xKGkpO1xuXG4gICAgaWYgKHN0YXRlXzEgPT09IFwiYnJlYWtcIikgYnJlYWs7XG4gIH1cblxuICByZXR1cm4gYXJlYXM7XG59XG5cbmZ1bmN0aW9uIGZpbmRDb25uZWN0ZWRBcmVhcyhwb2ludHMxLCBwb2ludHMyKSB7XG4gIHJldHVybiBmaW5kUmV2ZXJzZWRBcmVhcyhwb2ludHMxLCBfX3NwcmVhZEFycmF5cyhwb2ludHMyKS5yZXZlcnNlKCkpO1xufVxuLyoqXG4qIEdldCBub24tb3ZlcmxhcHBpbmcgYXJlYXMgb2YgdHdvIHNoYXBlcyBiYXNlZCBvbiBwb2ludHMxLlxuKiBAbWVtYmVyb2YgT3ZlcmxhcEFyZWFcbiovXG5cbmZ1bmN0aW9uIGdldFVub3ZlcmxhcEFyZWFzKHBvaW50czEsIHBvaW50czIpIHtcbiAgaWYgKCFwb2ludHMyLmxlbmd0aCkge1xuICAgIHJldHVybiBbX19zcHJlYWRBcnJheXMocG9pbnRzMSldO1xuICB9XG5cbiAgdmFyIG92ZXJsYXBBcmVhcyA9IGdldE92ZXJsYXBBcmVhcyhwb2ludHMxLCBwb2ludHMyKTtcbiAgdmFyIHVub3ZlcmxhcEFyZWFzID0gW3BvaW50czFdO1xuICBvdmVybGFwQXJlYXMuZm9yRWFjaChmdW5jdGlvbiAob3ZlcmxhcEFyZWEpIHtcbiAgICB2YXIgbmV4dE92ZXJsYXBBcmVhID0gX19zcHJlYWRBcnJheXMob3ZlcmxhcEFyZWEpLnJldmVyc2UoKTtcblxuICAgIHVub3ZlcmxhcEFyZWFzID0gZmxhdCh1bm92ZXJsYXBBcmVhcy5tYXAoZnVuY3Rpb24gKGFyZWEpIHtcbiAgICAgIHZhciBjb25uZWN0ZWRBcmVhcyA9IGZpbmRSZXZlcnNlZEFyZWFzKGFyZWEsIG5leHRPdmVybGFwQXJlYSk7XG4gICAgICB2YXIgZmlyc3RDb25uZWN0ZWRBcmVhID0gY29ubmVjdGVkQXJlYXNbMF07XG5cbiAgICAgIGlmIChjb25uZWN0ZWRBcmVhcy5sZW5ndGggPT09IDEgJiYgbmV4dE92ZXJsYXBBcmVhLmV2ZXJ5KGZ1bmN0aW9uIChwb2ludCkge1xuICAgICAgICByZXR1cm4gZmlyc3RDb25uZWN0ZWRBcmVhLmluZGV4T2YocG9pbnQpID09PSAtMTtcbiAgICAgIH0pKSB7XG4gICAgICAgIHZhciBsYXN0UG9pbnRfMSA9IGZpcnN0Q29ubmVjdGVkQXJlYVtmaXJzdENvbm5lY3RlZEFyZWEubGVuZ3RoIC0gMV07XG5cbiAgICAgICAgdmFyIGZpcnN0UG9pbnQgPSBfX3NwcmVhZEFycmF5cyhuZXh0T3ZlcmxhcEFyZWEpLnNvcnQoZnVuY3Rpb24gKGEsIGIpIHtcbiAgICAgICAgICByZXR1cm4gZ2V0RGlzdChsYXN0UG9pbnRfMSwgYSkgLSBnZXREaXN0KGxhc3RQb2ludF8xLCBiKTtcbiAgICAgICAgfSlbMF07XG5cbiAgICAgICAgdmFyIGZpcnN0SW5kZXggPSBuZXh0T3ZlcmxhcEFyZWEuaW5kZXhPZihmaXJzdFBvaW50KTtcbiAgICAgICAgZmlyc3RDb25uZWN0ZWRBcmVhLnB1c2guYXBwbHkoZmlyc3RDb25uZWN0ZWRBcmVhLCBfX3NwcmVhZEFycmF5cyhuZXh0T3ZlcmxhcEFyZWEuc2xpY2UoZmlyc3RJbmRleCksIG5leHRPdmVybGFwQXJlYS5zbGljZSgwLCBmaXJzdEluZGV4KSwgW25leHRPdmVybGFwQXJlYVtmaXJzdEluZGV4XSwgbGFzdFBvaW50XzFdKSk7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBjb25uZWN0ZWRBcmVhcztcbiAgICB9KSk7XG4gIH0pO1xuICByZXR1cm4gdW5vdmVybGFwQXJlYXM7XG59XG4vKipcbiogR2V0cyB0aGUgc2l6ZSBvZiB0aGUgb3ZlcmxhcHBlZCBwYXJ0IG9mIHR3byBzaGFwZXMuXG4qIEBmdW5jdGlvblxuKiBAbWVtYmVyb2YgT3ZlcmxhcEFyZWFcbiovXG5cbmZ1bmN0aW9uIGdldE92ZXJsYXBTaXplKHBvaW50czEsIHBvaW50czIpIHtcbiAgdmFyIHBvaW50cyA9IGdldE92ZXJsYXBQb2ludHMocG9pbnRzMSwgcG9pbnRzMik7XG4gIHJldHVybiBnZXRBcmVhU2l6ZShwb2ludHMpO1xufVxuXG5leHBvcnQgeyBjb252ZXJ0TGluZXMsIGZpbmRDb25uZWN0ZWRBcmVhcywgZml0UG9pbnRzLCBnZXRBcmVhU2l6ZSwgZ2V0RGlzdGFuY2VGcm9tUG9pbnRUb0NvbnN0YW50cywgZ2V0SW50ZXJzZWN0aW9uUG9pbnRzLCBnZXRJbnRlcnNlY3Rpb25Qb2ludHNCeUNvbnN0YW50cywgZ2V0TGluZWFyQ29uc3RhbnRzLCBnZXRNaW5NYXhzLCBnZXRPdmVybGFwQXJlYXMsIGdldE92ZXJsYXBQb2ludHMsIGdldE92ZXJsYXBTaXplLCBnZXRQb2ludHNPbkxpbmVzLCBnZXRVbm92ZXJsYXBBcmVhcywgaXNJbnNpZGUsIGlzUG9pbnRPbkxpbmUgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW92ZXJsYXAtYXJlYS5lc20uanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/overlap-area/dist/overlap-area.esm.js\n");

/***/ })

};
;