"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-character-count@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-character-count@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-character-count@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-character-count/dist/index.js":
/*!*******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-character-count@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-character-count/dist/index.js ***!
  \*******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CharacterCount: () => (/* binding */ CharacterCount),\n/* harmony export */   \"default\": () => (/* binding */ CharacterCount)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/state/dist/index.js\");\n\n\n\n/**\n * This extension allows you to count the characters and words of your document.\n * @see https://tiptap.dev/api/extensions/character-count\n */\nconst CharacterCount = _tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Extension.create({\n    name: 'characterCount',\n    addOptions() {\n        return {\n            limit: null,\n            mode: 'textSize',\n            textCounter: text => text.length,\n            wordCounter: text => text.split(' ').filter(word => word !== '').length,\n        };\n    },\n    addStorage() {\n        return {\n            characters: () => 0,\n            words: () => 0,\n        };\n    },\n    onBeforeCreate() {\n        this.storage.characters = options => {\n            const node = (options === null || options === void 0 ? void 0 : options.node) || this.editor.state.doc;\n            const mode = (options === null || options === void 0 ? void 0 : options.mode) || this.options.mode;\n            if (mode === 'textSize') {\n                const text = node.textBetween(0, node.content.size, undefined, ' ');\n                return this.options.textCounter(text);\n            }\n            return node.nodeSize;\n        };\n        this.storage.words = options => {\n            const node = (options === null || options === void 0 ? void 0 : options.node) || this.editor.state.doc;\n            const text = node.textBetween(0, node.content.size, ' ', ' ');\n            return this.options.wordCounter(text);\n        };\n    },\n    addProseMirrorPlugins() {\n        let initialEvaluationDone = false;\n        return [\n            new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n                key: new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey('characterCount'),\n                appendTransaction: (transactions, oldState, newState) => {\n                    if (initialEvaluationDone) {\n                        return;\n                    }\n                    const limit = this.options.limit;\n                    if (limit === null || limit === undefined || limit === 0) {\n                        initialEvaluationDone = true;\n                        return;\n                    }\n                    const initialContentSize = this.storage.characters({ node: newState.doc });\n                    if (initialContentSize > limit) {\n                        const over = initialContentSize - limit;\n                        const from = 0;\n                        const to = over;\n                        console.warn(`[CharacterCount] Initial content exceeded limit of ${limit} characters. Content was automatically trimmed.`);\n                        const tr = newState.tr.deleteRange(from, to);\n                        initialEvaluationDone = true;\n                        return tr;\n                    }\n                    initialEvaluationDone = true;\n                },\n                filterTransaction: (transaction, state) => {\n                    const limit = this.options.limit;\n                    // Nothing has changed or no limit is defined. Ignore it.\n                    if (!transaction.docChanged || limit === 0 || limit === null || limit === undefined) {\n                        return true;\n                    }\n                    const oldSize = this.storage.characters({ node: state.doc });\n                    const newSize = this.storage.characters({ node: transaction.doc });\n                    // Everything is in the limit. Good.\n                    if (newSize <= limit) {\n                        return true;\n                    }\n                    // The limit has already been exceeded but will be reduced.\n                    if (oldSize > limit && newSize > limit && newSize <= oldSize) {\n                        return true;\n                    }\n                    // The limit has already been exceeded and will be increased further.\n                    if (oldSize > limit && newSize > limit && newSize > oldSize) {\n                        return false;\n                    }\n                    const isPaste = transaction.getMeta('paste');\n                    // Block all exceeding transactions that were not pasted.\n                    if (!isPaste) {\n                        return false;\n                    }\n                    // For pasted content, we try to remove the exceeding content.\n                    const pos = transaction.selection.$head.pos;\n                    const over = newSize - limit;\n                    const from = pos - over;\n                    const to = pos;\n                    // It’s probably a bad idea to mutate transactions within `filterTransaction`\n                    // but for now this is working fine.\n                    transaction.deleteRange(from, to);\n                    // In some situations, the limit will continue to be exceeded after trimming.\n                    // This happens e.g. when truncating within a complex node (e.g. table)\n                    // and ProseMirror has to close this node again.\n                    // If this is the case, we prevent the transaction completely.\n                    const updatedSize = this.storage.characters({ node: transaction.doc });\n                    if (updatedSize > limit) {\n                        return false;\n                    }\n                    return true;\n                },\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-character-count@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-character-count/dist/index.js\n");

/***/ })

};
;