"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-heading@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-heading@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-heading@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-heading/dist/index.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-heading@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-heading/dist/index.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Heading: () => (/* binding */ Heading),\n/* harmony export */   \"default\": () => (/* binding */ Heading)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n/**\n * This extension allows you to create headings.\n * @see https://www.tiptap.dev/api/nodes/heading\n */\nconst Heading = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Node.create({\n    name: 'heading',\n    addOptions() {\n        return {\n            levels: [1, 2, 3, 4, 5, 6],\n            HTMLAttributes: {},\n        };\n    },\n    content: 'inline*',\n    group: 'block',\n    defining: true,\n    addAttributes() {\n        return {\n            level: {\n                default: 1,\n                rendered: false,\n            },\n        };\n    },\n    parseHTML() {\n        return this.options.levels\n            .map((level) => ({\n            tag: `h${level}`,\n            attrs: { level },\n        }));\n    },\n    renderHTML({ node, HTMLAttributes }) {\n        const hasLevel = this.options.levels.includes(node.attrs.level);\n        const level = hasLevel\n            ? node.attrs.level\n            : this.options.levels[0];\n        return [`h${level}`, (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n    addCommands() {\n        return {\n            setHeading: attributes => ({ commands }) => {\n                if (!this.options.levels.includes(attributes.level)) {\n                    return false;\n                }\n                return commands.setNode(this.name, attributes);\n            },\n            toggleHeading: attributes => ({ commands }) => {\n                if (!this.options.levels.includes(attributes.level)) {\n                    return false;\n                }\n                return commands.toggleNode(this.name, 'paragraph', attributes);\n            },\n        };\n    },\n    addKeyboardShortcuts() {\n        return this.options.levels.reduce((items, level) => ({\n            ...items,\n            ...{\n                [`Mod-Alt-${level}`]: () => this.editor.commands.toggleHeading({ level }),\n            },\n        }), {});\n    },\n    addInputRules() {\n        return this.options.levels.map(level => {\n            return (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.textblockTypeInputRule)({\n                find: new RegExp(`^(#{${Math.min(...this.options.levels)},${level}})\\\\s$`),\n                type: this.type,\n                getAttributes: {\n                    level,\n                },\n            });\n        });\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-heading@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-heading/dist/index.js\n");

/***/ })

};
;