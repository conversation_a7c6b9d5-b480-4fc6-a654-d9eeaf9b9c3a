"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-image@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-image@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-image@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-image/dist/index.js":
/*!*****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-image@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-image/dist/index.js ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Image: () => (/* binding */ Image),\n/* harmony export */   \"default\": () => (/* binding */ Image),\n/* harmony export */   inputRegex: () => (/* binding */ inputRegex)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n/**\n * Matches an image to a ![image](src \"title\") on input.\n */\nconst inputRegex = /(?:^|\\s)(!\\[(.+|:?)]\\((\\S+)(?:(?:\\s+)[\"'](\\S+)[\"'])?\\))$/;\n/**\n * This extension allows you to insert images.\n * @see https://www.tiptap.dev/api/nodes/image\n */\nconst Image = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Node.create({\n    name: 'image',\n    addOptions() {\n        return {\n            inline: false,\n            allowBase64: false,\n            HTMLAttributes: {},\n        };\n    },\n    inline() {\n        return this.options.inline;\n    },\n    group() {\n        return this.options.inline ? 'inline' : 'block';\n    },\n    draggable: true,\n    addAttributes() {\n        return {\n            src: {\n                default: null,\n            },\n            alt: {\n                default: null,\n            },\n            title: {\n                default: null,\n            },\n        };\n    },\n    parseHTML() {\n        return [\n            {\n                tag: this.options.allowBase64\n                    ? 'img[src]'\n                    : 'img[src]:not([src^=\"data:\"])',\n            },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['img', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes)];\n    },\n    addCommands() {\n        return {\n            setImage: options => ({ commands }) => {\n                return commands.insertContent({\n                    type: this.name,\n                    attrs: options,\n                });\n            },\n        };\n    },\n    addInputRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.nodeInputRule)({\n                find: inputRegex,\n                type: this.type,\n                getAttributes: match => {\n                    const [, , alt, src, title] = match;\n                    return { src, alt, title };\n                },\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-image@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-image/dist/index.js\n");

/***/ })

};
;