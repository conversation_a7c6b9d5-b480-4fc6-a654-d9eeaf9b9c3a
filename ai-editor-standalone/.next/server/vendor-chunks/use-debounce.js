"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-debounce";
exports.ids = ["vendor-chunks/use-debounce"];
exports.modules = {

/***/ "(ssr)/./node_modules/use-debounce/dist/index.module.js":
/*!********************************************************!*\
  !*** ./node_modules/use-debounce/dist/index.module.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDebounce: () => (/* binding */ a),\n/* harmony export */   useDebouncedCallback: () => (/* binding */ c),\n/* harmony export */   useThrottledCallback: () => (/* binding */ o)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\nfunction c(e,u,c,i){var a=this,o=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null),f=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0),l=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0),v=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null),m=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]),d=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(),g=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(),p=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(e),w=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(!0);p.current=e;var s=\"undefined\"!=typeof window,x=!u&&0!==u&&s;if(\"function\"!=typeof e)throw new TypeError(\"Expected a function\");u=+u||0;var h=!!(c=c||{}).leading,y=!(\"trailing\"in c)||!!c.trailing,F=\"maxWait\"in c,A=\"debounceOnServer\"in c&&!!c.debounceOnServer,D=F?Math.max(+c.maxWait||0,u):null;(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function(){return w.current=!0,function(){w.current=!1}},[]);var T=(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function(){var r=function(r){var n=m.current,t=d.current;return m.current=d.current=null,f.current=r,l.current=l.current||r,g.current=p.current.apply(t,n)},n=function(r,n){x&&cancelAnimationFrame(v.current),v.current=x?requestAnimationFrame(r):setTimeout(r,n)},t=function(r){if(!w.current)return!1;var n=r-o.current;return!o.current||n>=u||n<0||F&&r-f.current>=D},e=function(n){return v.current=null,y&&m.current?r(n):(m.current=d.current=null,g.current)},c=function r(){var c=Date.now();if(h&&l.current===f.current&&T(),t(c))return e(c);if(w.current){var i=u-(c-o.current),a=F?Math.min(i,D-(c-f.current)):i;n(r,a)}},T=function(){i&&i({})},W=function(){if(s||A){var e=Date.now(),i=t(e);if(m.current=[].slice.call(arguments),d.current=a,o.current=e,i){if(!v.current&&w.current)return f.current=o.current,n(c,u),h?r(o.current):g.current;if(F)return n(c,u),r(o.current)}return v.current||n(c,u),g.current}};return W.cancel=function(){v.current&&(x?cancelAnimationFrame(v.current):clearTimeout(v.current)),f.current=0,m.current=o.current=d.current=v.current=null},W.isPending=function(){return!!v.current},W.flush=function(){return v.current?e(Date.now()):g.current},W},[h,F,u,D,y,x,s,A,i]);return T}function i(r,n){return r===n}function a(n,t,a){var o=a&&a.equalityFn||i,f=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(n),l=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({})[1],v=c((0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(r){f.current=r,l({})},[l]),t,a,l),m=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(n);return o(m.current,n)||(v(n),m.current=n),[f.current,v]}function o(r,n,t){var e=void 0===t?{}:t,u=e.leading,i=e.trailing;return c(r,n,{maxWait:n,leading:void 0===u||u,trailing:void 0===i||i})}\n//# sourceMappingURL=index.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-debounce/dist/index.module.js\n");

/***/ })

};
;