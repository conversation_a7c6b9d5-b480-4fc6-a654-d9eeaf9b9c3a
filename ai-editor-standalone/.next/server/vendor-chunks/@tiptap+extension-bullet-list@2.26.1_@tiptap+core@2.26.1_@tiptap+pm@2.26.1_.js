"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-bullet-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-bullet-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-bullet-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-bullet-list/dist/index.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-bullet-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-bullet-list/dist/index.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BulletList: () => (/* binding */ BulletList),\n/* harmony export */   \"default\": () => (/* binding */ BulletList),\n/* harmony export */   inputRegex: () => (/* binding */ inputRegex)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\nconst ListItemName = 'listItem';\nconst TextStyleName = 'textStyle';\n/**\n * Matches a bullet list to a dash or asterisk.\n */\nconst inputRegex = /^\\s*([-+*])\\s$/;\n/**\n * This extension allows you to create bullet lists.\n * This requires the ListItem extension\n * @see https://tiptap.dev/api/nodes/bullet-list\n * @see https://tiptap.dev/api/nodes/list-item.\n */\nconst BulletList = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Node.create({\n    name: 'bulletList',\n    addOptions() {\n        return {\n            itemTypeName: 'listItem',\n            HTMLAttributes: {},\n            keepMarks: false,\n            keepAttributes: false,\n        };\n    },\n    group: 'block list',\n    content() {\n        return `${this.options.itemTypeName}+`;\n    },\n    parseHTML() {\n        return [\n            { tag: 'ul' },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['ul', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n    addCommands() {\n        return {\n            toggleBulletList: () => ({ commands, chain }) => {\n                if (this.options.keepAttributes) {\n                    return chain().toggleList(this.name, this.options.itemTypeName, this.options.keepMarks).updateAttributes(ListItemName, this.editor.getAttributes(TextStyleName)).run();\n                }\n                return commands.toggleList(this.name, this.options.itemTypeName, this.options.keepMarks);\n            },\n        };\n    },\n    addKeyboardShortcuts() {\n        return {\n            'Mod-Shift-8': () => this.editor.commands.toggleBulletList(),\n        };\n    },\n    addInputRules() {\n        let inputRule = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.wrappingInputRule)({\n            find: inputRegex,\n            type: this.type,\n        });\n        if (this.options.keepMarks || this.options.keepAttributes) {\n            inputRule = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.wrappingInputRule)({\n                find: inputRegex,\n                type: this.type,\n                keepMarks: this.options.keepMarks,\n                keepAttributes: this.options.keepAttributes,\n                getAttributes: () => { return this.editor.getAttributes(TextStyleName); },\n                editor: this.editor,\n            });\n        }\n        return [\n            inputRule,\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-bullet-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-bullet-list/dist/index.js\n");

/***/ })

};
;