"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-horizontal-rule@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-horizontal-rule@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-horizontal-rule@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-horizontal-rule/dist/index.js":
/*!*******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-horizontal-rule@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-horizontal-rule/dist/index.js ***!
  \*******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HorizontalRule: () => (/* binding */ HorizontalRule),\n/* harmony export */   \"default\": () => (/* binding */ HorizontalRule)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/state/dist/index.js\");\n\n\n\n/**\n * This extension allows you to insert horizontal rules.\n * @see https://www.tiptap.dev/api/nodes/horizontal-rule\n */\nconst HorizontalRule = _tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Node.create({\n    name: 'horizontalRule',\n    addOptions() {\n        return {\n            HTMLAttributes: {},\n        };\n    },\n    group: 'block',\n    parseHTML() {\n        return [{ tag: 'hr' }];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['hr', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes)];\n    },\n    addCommands() {\n        return {\n            setHorizontalRule: () => ({ chain, state }) => {\n                // Check if we can insert the node at the current selection\n                if (!(0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.canInsertNode)(state, state.schema.nodes[this.name])) {\n                    return false;\n                }\n                const { selection } = state;\n                const { $from: $originFrom, $to: $originTo } = selection;\n                const currentChain = chain();\n                if ($originFrom.parentOffset === 0) {\n                    currentChain.insertContentAt({\n                        from: Math.max($originFrom.pos - 1, 0),\n                        to: $originTo.pos,\n                    }, {\n                        type: this.name,\n                    });\n                }\n                else if ((0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.isNodeSelection)(selection)) {\n                    currentChain.insertContentAt($originTo.pos, {\n                        type: this.name,\n                    });\n                }\n                else {\n                    currentChain.insertContent({ type: this.name });\n                }\n                return (currentChain\n                    // set cursor after horizontal rule\n                    .command(({ tr, dispatch }) => {\n                    var _a;\n                    if (dispatch) {\n                        const { $to } = tr.selection;\n                        const posAfter = $to.end();\n                        if ($to.nodeAfter) {\n                            if ($to.nodeAfter.isTextblock) {\n                                tr.setSelection(_tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.create(tr.doc, $to.pos + 1));\n                            }\n                            else if ($to.nodeAfter.isBlock) {\n                                tr.setSelection(_tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection.create(tr.doc, $to.pos));\n                            }\n                            else {\n                                tr.setSelection(_tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.create(tr.doc, $to.pos));\n                            }\n                        }\n                        else {\n                            // add node after horizontal rule if it’s the end of the document\n                            const node = (_a = $to.parent.type.contentMatch.defaultType) === null || _a === void 0 ? void 0 : _a.create();\n                            if (node) {\n                                tr.insert(posAfter, node);\n                                tr.setSelection(_tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.create(tr.doc, posAfter + 1));\n                            }\n                        }\n                        tr.scrollIntoView();\n                    }\n                    return true;\n                })\n                    .run());\n            },\n        };\n    },\n    addInputRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.nodeInputRule)({\n                find: /^(?:---|—-|___\\s|\\*\\*\\*\\s)$/,\n                type: this.type,\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-horizontal-rule@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-horizontal-rule/dist/index.js\n");

/***/ })

};
;