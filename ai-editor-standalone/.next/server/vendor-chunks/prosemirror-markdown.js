"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-markdown";
exports.ids = ["vendor-chunks/prosemirror-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/prosemirror-markdown/dist/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/prosemirror-markdown/dist/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarkdownParser: () => (/* binding */ MarkdownParser),\n/* harmony export */   MarkdownSerializer: () => (/* binding */ MarkdownSerializer),\n/* harmony export */   MarkdownSerializerState: () => (/* binding */ MarkdownSerializerState),\n/* harmony export */   defaultMarkdownParser: () => (/* binding */ defaultMarkdownParser),\n/* harmony export */   defaultMarkdownSerializer: () => (/* binding */ defaultMarkdownSerializer),\n/* harmony export */   schema: () => (/* binding */ schema)\n/* harmony export */ });\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/./node_modules/prosemirror-model/dist/index.js\");\n/* harmony import */ var markdown_it__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! markdown-it */ \"(ssr)/./node_modules/markdown-it/index.mjs\");\n\n\n\n/**\nDocument schema for the data model used by CommonMark.\n*/\nconst schema = new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Schema({\n    nodes: {\n        doc: {\n            content: \"block+\"\n        },\n        paragraph: {\n            content: \"inline*\",\n            group: \"block\",\n            parseDOM: [{ tag: \"p\" }],\n            toDOM() { return [\"p\", 0]; }\n        },\n        blockquote: {\n            content: \"block+\",\n            group: \"block\",\n            parseDOM: [{ tag: \"blockquote\" }],\n            toDOM() { return [\"blockquote\", 0]; }\n        },\n        horizontal_rule: {\n            group: \"block\",\n            parseDOM: [{ tag: \"hr\" }],\n            toDOM() { return [\"div\", [\"hr\"]]; }\n        },\n        heading: {\n            attrs: { level: { default: 1 } },\n            content: \"(text | image)*\",\n            group: \"block\",\n            defining: true,\n            parseDOM: [{ tag: \"h1\", attrs: { level: 1 } },\n                { tag: \"h2\", attrs: { level: 2 } },\n                { tag: \"h3\", attrs: { level: 3 } },\n                { tag: \"h4\", attrs: { level: 4 } },\n                { tag: \"h5\", attrs: { level: 5 } },\n                { tag: \"h6\", attrs: { level: 6 } }],\n            toDOM(node) { return [\"h\" + node.attrs.level, 0]; }\n        },\n        code_block: {\n            content: \"text*\",\n            group: \"block\",\n            code: true,\n            defining: true,\n            marks: \"\",\n            attrs: { params: { default: \"\" } },\n            parseDOM: [{ tag: \"pre\", preserveWhitespace: \"full\", getAttrs: node => ({ params: node.getAttribute(\"data-params\") || \"\" }) }],\n            toDOM(node) { return [\"pre\", node.attrs.params ? { \"data-params\": node.attrs.params } : {}, [\"code\", 0]]; }\n        },\n        ordered_list: {\n            content: \"list_item+\",\n            group: \"block\",\n            attrs: { order: { default: 1 }, tight: { default: false } },\n            parseDOM: [{ tag: \"ol\", getAttrs(dom) {\n                        return { order: dom.hasAttribute(\"start\") ? +dom.getAttribute(\"start\") : 1,\n                            tight: dom.hasAttribute(\"data-tight\") };\n                    } }],\n            toDOM(node) {\n                return [\"ol\", { start: node.attrs.order == 1 ? null : node.attrs.order,\n                        \"data-tight\": node.attrs.tight ? \"true\" : null }, 0];\n            }\n        },\n        bullet_list: {\n            content: \"list_item+\",\n            group: \"block\",\n            attrs: { tight: { default: false } },\n            parseDOM: [{ tag: \"ul\", getAttrs: dom => ({ tight: dom.hasAttribute(\"data-tight\") }) }],\n            toDOM(node) { return [\"ul\", { \"data-tight\": node.attrs.tight ? \"true\" : null }, 0]; }\n        },\n        list_item: {\n            content: \"block+\",\n            defining: true,\n            parseDOM: [{ tag: \"li\" }],\n            toDOM() { return [\"li\", 0]; }\n        },\n        text: {\n            group: \"inline\"\n        },\n        image: {\n            inline: true,\n            attrs: {\n                src: {},\n                alt: { default: null },\n                title: { default: null }\n            },\n            group: \"inline\",\n            draggable: true,\n            parseDOM: [{ tag: \"img[src]\", getAttrs(dom) {\n                        return {\n                            src: dom.getAttribute(\"src\"),\n                            title: dom.getAttribute(\"title\"),\n                            alt: dom.getAttribute(\"alt\")\n                        };\n                    } }],\n            toDOM(node) { return [\"img\", node.attrs]; }\n        },\n        hard_break: {\n            inline: true,\n            group: \"inline\",\n            selectable: false,\n            parseDOM: [{ tag: \"br\" }],\n            toDOM() { return [\"br\"]; }\n        }\n    },\n    marks: {\n        em: {\n            parseDOM: [\n                { tag: \"i\" }, { tag: \"em\" },\n                { style: \"font-style=italic\" },\n                { style: \"font-style=normal\", clearMark: m => m.type.name == \"em\" }\n            ],\n            toDOM() { return [\"em\"]; }\n        },\n        strong: {\n            parseDOM: [\n                { tag: \"strong\" },\n                { tag: \"b\", getAttrs: node => node.style.fontWeight != \"normal\" && null },\n                { style: \"font-weight=400\", clearMark: m => m.type.name == \"strong\" },\n                { style: \"font-weight\", getAttrs: value => /^(bold(er)?|[5-9]\\d{2,})$/.test(value) && null }\n            ],\n            toDOM() { return [\"strong\"]; }\n        },\n        link: {\n            attrs: {\n                href: {},\n                title: { default: null }\n            },\n            inclusive: false,\n            parseDOM: [{ tag: \"a[href]\", getAttrs(dom) {\n                        return { href: dom.getAttribute(\"href\"), title: dom.getAttribute(\"title\") };\n                    } }],\n            toDOM(node) { return [\"a\", node.attrs]; }\n        },\n        code: {\n            code: true,\n            parseDOM: [{ tag: \"code\" }],\n            toDOM() { return [\"code\"]; }\n        }\n    }\n});\n\n// @ts-ignore\nfunction maybeMerge(a, b) {\n    if (a.isText && b.isText && prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Mark.sameSet(a.marks, b.marks))\n        return a.withText(a.text + b.text);\n}\n// Object used to track the context of a running parse.\nclass MarkdownParseState {\n    constructor(schema, tokenHandlers) {\n        this.schema = schema;\n        this.tokenHandlers = tokenHandlers;\n        this.stack = [{ type: schema.topNodeType, attrs: null, content: [], marks: prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Mark.none }];\n    }\n    top() {\n        return this.stack[this.stack.length - 1];\n    }\n    push(elt) {\n        if (this.stack.length)\n            this.top().content.push(elt);\n    }\n    // Adds the given text to the current position in the document,\n    // using the current marks as styling.\n    addText(text) {\n        if (!text)\n            return;\n        let top = this.top(), nodes = top.content, last = nodes[nodes.length - 1];\n        let node = this.schema.text(text, top.marks), merged;\n        if (last && (merged = maybeMerge(last, node)))\n            nodes[nodes.length - 1] = merged;\n        else\n            nodes.push(node);\n    }\n    // Adds the given mark to the set of active marks.\n    openMark(mark) {\n        let top = this.top();\n        top.marks = mark.addToSet(top.marks);\n    }\n    // Removes the given mark from the set of active marks.\n    closeMark(mark) {\n        let top = this.top();\n        top.marks = mark.removeFromSet(top.marks);\n    }\n    parseTokens(toks) {\n        for (let i = 0; i < toks.length; i++) {\n            let tok = toks[i];\n            let handler = this.tokenHandlers[tok.type];\n            if (!handler)\n                throw new Error(\"Token type `\" + tok.type + \"` not supported by Markdown parser\");\n            handler(this, tok, toks, i);\n        }\n    }\n    // Add a node at the current position.\n    addNode(type, attrs, content) {\n        let top = this.top();\n        let node = type.createAndFill(attrs, content, top ? top.marks : []);\n        if (!node)\n            return null;\n        this.push(node);\n        return node;\n    }\n    // Wrap subsequent content in a node of the given type.\n    openNode(type, attrs) {\n        this.stack.push({ type: type, attrs: attrs, content: [], marks: prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Mark.none });\n    }\n    // Close and return the node that is currently on top of the stack.\n    closeNode() {\n        let info = this.stack.pop();\n        return this.addNode(info.type, info.attrs, info.content);\n    }\n}\nfunction attrs(spec, token, tokens, i) {\n    if (spec.getAttrs)\n        return spec.getAttrs(token, tokens, i);\n    // For backwards compatibility when `attrs` is a Function\n    else if (spec.attrs instanceof Function)\n        return spec.attrs(token);\n    else\n        return spec.attrs;\n}\n// Code content is represented as a single token with a `content`\n// property in Markdown-it.\nfunction noCloseToken(spec, type) {\n    return spec.noCloseToken || type == \"code_inline\" || type == \"code_block\" || type == \"fence\";\n}\nfunction withoutTrailingNewline(str) {\n    return str[str.length - 1] == \"\\n\" ? str.slice(0, str.length - 1) : str;\n}\nfunction noOp() { }\nfunction tokenHandlers(schema, tokens) {\n    let handlers = Object.create(null);\n    for (let type in tokens) {\n        let spec = tokens[type];\n        if (spec.block) {\n            let nodeType = schema.nodeType(spec.block);\n            if (noCloseToken(spec, type)) {\n                handlers[type] = (state, tok, tokens, i) => {\n                    state.openNode(nodeType, attrs(spec, tok, tokens, i));\n                    state.addText(withoutTrailingNewline(tok.content));\n                    state.closeNode();\n                };\n            }\n            else {\n                handlers[type + \"_open\"] = (state, tok, tokens, i) => state.openNode(nodeType, attrs(spec, tok, tokens, i));\n                handlers[type + \"_close\"] = state => state.closeNode();\n            }\n        }\n        else if (spec.node) {\n            let nodeType = schema.nodeType(spec.node);\n            handlers[type] = (state, tok, tokens, i) => state.addNode(nodeType, attrs(spec, tok, tokens, i));\n        }\n        else if (spec.mark) {\n            let markType = schema.marks[spec.mark];\n            if (noCloseToken(spec, type)) {\n                handlers[type] = (state, tok, tokens, i) => {\n                    state.openMark(markType.create(attrs(spec, tok, tokens, i)));\n                    state.addText(withoutTrailingNewline(tok.content));\n                    state.closeMark(markType);\n                };\n            }\n            else {\n                handlers[type + \"_open\"] = (state, tok, tokens, i) => state.openMark(markType.create(attrs(spec, tok, tokens, i)));\n                handlers[type + \"_close\"] = state => state.closeMark(markType);\n            }\n        }\n        else if (spec.ignore) {\n            if (noCloseToken(spec, type)) {\n                handlers[type] = noOp;\n            }\n            else {\n                handlers[type + \"_open\"] = noOp;\n                handlers[type + \"_close\"] = noOp;\n            }\n        }\n        else {\n            throw new RangeError(\"Unrecognized parsing spec \" + JSON.stringify(spec));\n        }\n    }\n    handlers.text = (state, tok) => state.addText(tok.content);\n    handlers.inline = (state, tok) => state.parseTokens(tok.children);\n    handlers.softbreak = handlers.softbreak || (state => state.addText(\" \"));\n    return handlers;\n}\n/**\nA configuration of a Markdown parser. Such a parser uses\n[markdown-it](https://github.com/markdown-it/markdown-it) to\ntokenize a file, and then runs the custom rules it is given over\nthe tokens to create a ProseMirror document tree.\n*/\nclass MarkdownParser {\n    /**\n    Create a parser with the given configuration. You can configure\n    the markdown-it parser to parse the dialect you want, and provide\n    a description of the ProseMirror entities those tokens map to in\n    the `tokens` object, which maps token names to descriptions of\n    what to do with them. Such a description is an object, and may\n    have the following properties:\n    */\n    constructor(\n    /**\n    The parser's document schema.\n    */\n    schema, \n    /**\n    This parser's markdown-it tokenizer.\n    */\n    tokenizer, \n    /**\n    The value of the `tokens` object used to construct this\n    parser. Can be useful to copy and modify to base other parsers\n    on.\n    */\n    tokens) {\n        this.schema = schema;\n        this.tokenizer = tokenizer;\n        this.tokens = tokens;\n        this.tokenHandlers = tokenHandlers(schema, tokens);\n    }\n    /**\n    Parse a string as [CommonMark](http://commonmark.org/) markup,\n    and create a ProseMirror document as prescribed by this parser's\n    rules.\n    \n    The second argument, when given, is passed through to the\n    [Markdown\n    parser](https://markdown-it.github.io/markdown-it/#MarkdownIt.parse).\n    */\n    parse(text, markdownEnv = {}) {\n        let state = new MarkdownParseState(this.schema, this.tokenHandlers), doc;\n        state.parseTokens(this.tokenizer.parse(text, markdownEnv));\n        do {\n            doc = state.closeNode();\n        } while (state.stack.length);\n        return doc || this.schema.topNodeType.createAndFill();\n    }\n}\nfunction listIsTight(tokens, i) {\n    while (++i < tokens.length)\n        if (tokens[i].type != \"list_item_open\")\n            return tokens[i].hidden;\n    return false;\n}\n/**\nA parser parsing unextended [CommonMark](http://commonmark.org/),\nwithout inline HTML, and producing a document in the basic schema.\n*/\nconst defaultMarkdownParser = new MarkdownParser(schema, (0,markdown_it__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"commonmark\", { html: false }), {\n    blockquote: { block: \"blockquote\" },\n    paragraph: { block: \"paragraph\" },\n    list_item: { block: \"list_item\" },\n    bullet_list: { block: \"bullet_list\", getAttrs: (_, tokens, i) => ({ tight: listIsTight(tokens, i) }) },\n    ordered_list: { block: \"ordered_list\", getAttrs: (tok, tokens, i) => ({\n            order: +tok.attrGet(\"start\") || 1,\n            tight: listIsTight(tokens, i)\n        }) },\n    heading: { block: \"heading\", getAttrs: tok => ({ level: +tok.tag.slice(1) }) },\n    code_block: { block: \"code_block\", noCloseToken: true },\n    fence: { block: \"code_block\", getAttrs: tok => ({ params: tok.info || \"\" }), noCloseToken: true },\n    hr: { node: \"horizontal_rule\" },\n    image: { node: \"image\", getAttrs: tok => ({\n            src: tok.attrGet(\"src\"),\n            title: tok.attrGet(\"title\") || null,\n            alt: tok.children[0] && tok.children[0].content || null\n        }) },\n    hardbreak: { node: \"hard_break\" },\n    em: { mark: \"em\" },\n    strong: { mark: \"strong\" },\n    link: { mark: \"link\", getAttrs: tok => ({\n            href: tok.attrGet(\"href\"),\n            title: tok.attrGet(\"title\") || null\n        }) },\n    code_inline: { mark: \"code\", noCloseToken: true }\n});\n\nconst blankMark = { open: \"\", close: \"\", mixable: true };\n/**\nA specification for serializing a ProseMirror document as\nMarkdown/CommonMark text.\n*/\nclass MarkdownSerializer {\n    /**\n    Construct a serializer with the given configuration. The `nodes`\n    object should map node names in a given schema to function that\n    take a serializer state and such a node, and serialize the node.\n    */\n    constructor(\n    /**\n    The node serializer functions for this serializer.\n    */\n    nodes, \n    /**\n    The mark serializer info.\n    */\n    marks, options = {}) {\n        this.nodes = nodes;\n        this.marks = marks;\n        this.options = options;\n    }\n    /**\n    Serialize the content of the given node to\n    [CommonMark](http://commonmark.org/).\n    */\n    serialize(content, options = {}) {\n        options = Object.assign({}, this.options, options);\n        let state = new MarkdownSerializerState(this.nodes, this.marks, options);\n        state.renderContent(content);\n        return state.out;\n    }\n}\n/**\nA serializer for the [basic schema](https://prosemirror.net/docs/ref/#schema).\n*/\nconst defaultMarkdownSerializer = new MarkdownSerializer({\n    blockquote(state, node) {\n        state.wrapBlock(\"> \", null, node, () => state.renderContent(node));\n    },\n    code_block(state, node) {\n        // Make sure the front matter fences are longer than any dash sequence within it\n        const backticks = node.textContent.match(/`{3,}/gm);\n        const fence = backticks ? (backticks.sort().slice(-1)[0] + \"`\") : \"```\";\n        state.write(fence + (node.attrs.params || \"\") + \"\\n\");\n        state.text(node.textContent, false);\n        // Add a newline to the current content before adding closing marker\n        state.write(\"\\n\");\n        state.write(fence);\n        state.closeBlock(node);\n    },\n    heading(state, node) {\n        state.write(state.repeat(\"#\", node.attrs.level) + \" \");\n        state.renderInline(node, false);\n        state.closeBlock(node);\n    },\n    horizontal_rule(state, node) {\n        state.write(node.attrs.markup || \"---\");\n        state.closeBlock(node);\n    },\n    bullet_list(state, node) {\n        state.renderList(node, \"  \", () => (node.attrs.bullet || \"*\") + \" \");\n    },\n    ordered_list(state, node) {\n        let start = node.attrs.order || 1;\n        let maxW = String(start + node.childCount - 1).length;\n        let space = state.repeat(\" \", maxW + 2);\n        state.renderList(node, space, i => {\n            let nStr = String(start + i);\n            return state.repeat(\" \", maxW - nStr.length) + nStr + \". \";\n        });\n    },\n    list_item(state, node) {\n        state.renderContent(node);\n    },\n    paragraph(state, node) {\n        state.renderInline(node);\n        state.closeBlock(node);\n    },\n    image(state, node) {\n        state.write(\"![\" + state.esc(node.attrs.alt || \"\") + \"](\" + node.attrs.src.replace(/[\\(\\)]/g, \"\\\\$&\") +\n            (node.attrs.title ? ' \"' + node.attrs.title.replace(/\"/g, '\\\\\"') + '\"' : \"\") + \")\");\n    },\n    hard_break(state, node, parent, index) {\n        for (let i = index + 1; i < parent.childCount; i++)\n            if (parent.child(i).type != node.type) {\n                state.write(\"\\\\\\n\");\n                return;\n            }\n    },\n    text(state, node) {\n        state.text(node.text, !state.inAutolink);\n    }\n}, {\n    em: { open: \"*\", close: \"*\", mixable: true, expelEnclosingWhitespace: true },\n    strong: { open: \"**\", close: \"**\", mixable: true, expelEnclosingWhitespace: true },\n    link: {\n        open(state, mark, parent, index) {\n            state.inAutolink = isPlainURL(mark, parent, index);\n            return state.inAutolink ? \"<\" : \"[\";\n        },\n        close(state, mark, parent, index) {\n            let { inAutolink } = state;\n            state.inAutolink = undefined;\n            return inAutolink ? \">\"\n                : \"](\" + mark.attrs.href.replace(/[\\(\\)\"]/g, \"\\\\$&\") + (mark.attrs.title ? ` \"${mark.attrs.title.replace(/\"/g, '\\\\\"')}\"` : \"\") + \")\";\n        },\n        mixable: true\n    },\n    code: { open(_state, _mark, parent, index) { return backticksFor(parent.child(index), -1); },\n        close(_state, _mark, parent, index) { return backticksFor(parent.child(index - 1), 1); },\n        escape: false }\n});\nfunction backticksFor(node, side) {\n    let ticks = /`+/g, m, len = 0;\n    if (node.isText)\n        while (m = ticks.exec(node.text))\n            len = Math.max(len, m[0].length);\n    let result = len > 0 && side > 0 ? \" `\" : \"`\";\n    for (let i = 0; i < len; i++)\n        result += \"`\";\n    if (len > 0 && side < 0)\n        result += \" \";\n    return result;\n}\nfunction isPlainURL(link, parent, index) {\n    if (link.attrs.title || !/^\\w+:/.test(link.attrs.href))\n        return false;\n    let content = parent.child(index);\n    if (!content.isText || content.text != link.attrs.href || content.marks[content.marks.length - 1] != link)\n        return false;\n    return index == parent.childCount - 1 || !link.isInSet(parent.child(index + 1).marks);\n}\n/**\nThis is an object used to track state and expose\nmethods related to markdown serialization. Instances are passed to\nnode and mark serialization methods (see `toMarkdown`).\n*/\nclass MarkdownSerializerState {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    nodes, \n    /**\n    @internal\n    */\n    marks, \n    /**\n    The options passed to the serializer.\n    */\n    options) {\n        this.nodes = nodes;\n        this.marks = marks;\n        this.options = options;\n        /**\n        @internal\n        */\n        this.delim = \"\";\n        /**\n        @internal\n        */\n        this.out = \"\";\n        /**\n        @internal\n        */\n        this.closed = null;\n        /**\n        @internal\n        */\n        this.inAutolink = undefined;\n        /**\n        @internal\n        */\n        this.atBlockStart = false;\n        /**\n        @internal\n        */\n        this.inTightList = false;\n        if (typeof this.options.tightLists == \"undefined\")\n            this.options.tightLists = false;\n        if (typeof this.options.hardBreakNodeName == \"undefined\")\n            this.options.hardBreakNodeName = \"hard_break\";\n    }\n    /**\n    @internal\n    */\n    flushClose(size = 2) {\n        if (this.closed) {\n            if (!this.atBlank())\n                this.out += \"\\n\";\n            if (size > 1) {\n                let delimMin = this.delim;\n                let trim = /\\s+$/.exec(delimMin);\n                if (trim)\n                    delimMin = delimMin.slice(0, delimMin.length - trim[0].length);\n                for (let i = 1; i < size; i++)\n                    this.out += delimMin + \"\\n\";\n            }\n            this.closed = null;\n        }\n    }\n    /**\n    @internal\n    */\n    getMark(name) {\n        let info = this.marks[name];\n        if (!info) {\n            if (this.options.strict !== false)\n                throw new Error(`Mark type \\`${name}\\` not supported by Markdown renderer`);\n            info = blankMark;\n        }\n        return info;\n    }\n    /**\n    Render a block, prefixing each line with `delim`, and the first\n    line in `firstDelim`. `node` should be the node that is closed at\n    the end of the block, and `f` is a function that renders the\n    content of the block.\n    */\n    wrapBlock(delim, firstDelim, node, f) {\n        let old = this.delim;\n        this.write(firstDelim != null ? firstDelim : delim);\n        this.delim += delim;\n        f();\n        this.delim = old;\n        this.closeBlock(node);\n    }\n    /**\n    @internal\n    */\n    atBlank() {\n        return /(^|\\n)$/.test(this.out);\n    }\n    /**\n    Ensure the current content ends with a newline.\n    */\n    ensureNewLine() {\n        if (!this.atBlank())\n            this.out += \"\\n\";\n    }\n    /**\n    Prepare the state for writing output (closing closed paragraphs,\n    adding delimiters, and so on), and then optionally add content\n    (unescaped) to the output.\n    */\n    write(content) {\n        this.flushClose();\n        if (this.delim && this.atBlank())\n            this.out += this.delim;\n        if (content)\n            this.out += content;\n    }\n    /**\n    Close the block for the given node.\n    */\n    closeBlock(node) {\n        this.closed = node;\n    }\n    /**\n    Add the given text to the document. When escape is not `false`,\n    it will be escaped.\n    */\n    text(text, escape = true) {\n        let lines = text.split(\"\\n\");\n        for (let i = 0; i < lines.length; i++) {\n            this.write();\n            // Escape exclamation marks in front of links\n            if (!escape && lines[i][0] == \"[\" && /(^|[^\\\\])\\!$/.test(this.out))\n                this.out = this.out.slice(0, this.out.length - 1) + \"\\\\!\";\n            this.out += escape ? this.esc(lines[i], this.atBlockStart) : lines[i];\n            if (i != lines.length - 1)\n                this.out += \"\\n\";\n        }\n    }\n    /**\n    Render the given node as a block.\n    */\n    render(node, parent, index) {\n        if (this.nodes[node.type.name]) {\n            this.nodes[node.type.name](this, node, parent, index);\n        }\n        else {\n            if (this.options.strict !== false) {\n                throw new Error(\"Token type `\" + node.type.name + \"` not supported by Markdown renderer\");\n            }\n            else if (!node.type.isLeaf) {\n                if (node.type.inlineContent)\n                    this.renderInline(node);\n                else\n                    this.renderContent(node);\n                if (node.isBlock)\n                    this.closeBlock(node);\n            }\n        }\n    }\n    /**\n    Render the contents of `parent` as block nodes.\n    */\n    renderContent(parent) {\n        parent.forEach((node, _, i) => this.render(node, parent, i));\n    }\n    /**\n    Render the contents of `parent` as inline content.\n    */\n    renderInline(parent, fromBlockStart = true) {\n        this.atBlockStart = fromBlockStart;\n        let active = [], trailing = \"\";\n        let progress = (node, offset, index) => {\n            let marks = node ? node.marks : [];\n            // Remove marks from `hard_break` that are the last node inside\n            // that mark to prevent parser edge cases with new lines just\n            // before closing marks.\n            if (node && node.type.name === this.options.hardBreakNodeName)\n                marks = marks.filter(m => {\n                    if (index + 1 == parent.childCount)\n                        return false;\n                    let next = parent.child(index + 1);\n                    return m.isInSet(next.marks) && (!next.isText || /\\S/.test(next.text));\n                });\n            let leading = trailing;\n            trailing = \"\";\n            // If whitespace has to be expelled from the node, adjust\n            // leading and trailing accordingly.\n            if (node && node.isText && marks.some(mark => {\n                let info = this.getMark(mark.type.name);\n                return info && info.expelEnclosingWhitespace && !mark.isInSet(active);\n            })) {\n                let [_, lead, rest] = /^(\\s*)(.*)$/m.exec(node.text);\n                if (lead) {\n                    leading += lead;\n                    node = rest ? node.withText(rest) : null;\n                    if (!node)\n                        marks = active;\n                }\n            }\n            if (node && node.isText && marks.some(mark => {\n                let info = this.getMark(mark.type.name);\n                return info && info.expelEnclosingWhitespace &&\n                    (index == parent.childCount - 1 || !mark.isInSet(parent.child(index + 1).marks));\n            })) {\n                let [_, rest, trail] = /^(.*?)(\\s*)$/m.exec(node.text);\n                if (trail) {\n                    trailing = trail;\n                    node = rest ? node.withText(rest) : null;\n                    if (!node)\n                        marks = active;\n                }\n            }\n            let inner = marks.length ? marks[marks.length - 1] : null;\n            let noEsc = inner && this.getMark(inner.type.name).escape === false;\n            let len = marks.length - (noEsc ? 1 : 0);\n            // Try to reorder 'mixable' marks, such as em and strong, which\n            // in Markdown may be opened and closed in different order, so\n            // that order of the marks for the token matches the order in\n            // active.\n            outer: for (let i = 0; i < len; i++) {\n                let mark = marks[i];\n                if (!this.getMark(mark.type.name).mixable)\n                    break;\n                for (let j = 0; j < active.length; j++) {\n                    let other = active[j];\n                    if (!this.getMark(other.type.name).mixable)\n                        break;\n                    if (mark.eq(other)) {\n                        if (i > j)\n                            marks = marks.slice(0, j).concat(mark).concat(marks.slice(j, i)).concat(marks.slice(i + 1, len));\n                        else if (j > i)\n                            marks = marks.slice(0, i).concat(marks.slice(i + 1, j)).concat(mark).concat(marks.slice(j, len));\n                        continue outer;\n                    }\n                }\n            }\n            // Find the prefix of the mark set that didn't change\n            let keep = 0;\n            while (keep < Math.min(active.length, len) && marks[keep].eq(active[keep]))\n                ++keep;\n            // Close the marks that need to be closed\n            while (keep < active.length)\n                this.text(this.markString(active.pop(), false, parent, index), false);\n            // Output any previously expelled trailing whitespace outside the marks\n            if (leading)\n                this.text(leading);\n            // Open the marks that need to be opened\n            if (node) {\n                while (active.length < len) {\n                    let add = marks[active.length];\n                    active.push(add);\n                    this.text(this.markString(add, true, parent, index), false);\n                    this.atBlockStart = false;\n                }\n                // Render the node. Special case code marks, since their content\n                // may not be escaped.\n                if (noEsc && node.isText)\n                    this.text(this.markString(inner, true, parent, index) + node.text +\n                        this.markString(inner, false, parent, index + 1), false);\n                else\n                    this.render(node, parent, index);\n                this.atBlockStart = false;\n            }\n            // After the first non-empty text node is rendered, the end of output\n            // is no longer at block start.\n            //\n            // FIXME: If a non-text node writes something to the output for this\n            // block, the end of output is also no longer at block start. But how\n            // can we detect that?\n            if ((node === null || node === void 0 ? void 0 : node.isText) && node.nodeSize > 0) {\n                this.atBlockStart = false;\n            }\n        };\n        parent.forEach(progress);\n        progress(null, 0, parent.childCount);\n        this.atBlockStart = false;\n    }\n    /**\n    Render a node's content as a list. `delim` should be the extra\n    indentation added to all lines except the first in an item,\n    `firstDelim` is a function going from an item index to a\n    delimiter for the first line of the item.\n    */\n    renderList(node, delim, firstDelim) {\n        if (this.closed && this.closed.type == node.type)\n            this.flushClose(3);\n        else if (this.inTightList)\n            this.flushClose(1);\n        let isTight = typeof node.attrs.tight != \"undefined\" ? node.attrs.tight : this.options.tightLists;\n        let prevTight = this.inTightList;\n        this.inTightList = isTight;\n        node.forEach((child, _, i) => {\n            if (i && isTight)\n                this.flushClose(1);\n            this.wrapBlock(delim, firstDelim(i), node, () => this.render(child, node, i));\n        });\n        this.inTightList = prevTight;\n    }\n    /**\n    Escape the given string so that it can safely appear in Markdown\n    content. If `startOfLine` is true, also escape characters that\n    have special meaning only at the start of the line.\n    */\n    esc(str, startOfLine = false) {\n        str = str.replace(/[`*\\\\~\\[\\]_]/g, (m, i) => m == \"_\" && i > 0 && i + 1 < str.length && str[i - 1].match(/\\w/) && str[i + 1].match(/\\w/) ? m : \"\\\\\" + m);\n        if (startOfLine)\n            str = str.replace(/^(\\+[ ]|[\\-*>])/, \"\\\\$&\").replace(/^(\\s*)(#{1,6})(\\s|$)/, '$1\\\\$2$3').replace(/^(\\s*\\d+)\\.\\s/, \"$1\\\\. \");\n        if (this.options.escapeExtraCharacters)\n            str = str.replace(this.options.escapeExtraCharacters, \"\\\\$&\");\n        return str;\n    }\n    /**\n    @internal\n    */\n    quote(str) {\n        let wrap = str.indexOf('\"') == -1 ? '\"\"' : str.indexOf(\"'\") == -1 ? \"''\" : \"()\";\n        return wrap[0] + str + wrap[1];\n    }\n    /**\n    Repeat the given string `n` times.\n    */\n    repeat(str, n) {\n        let out = \"\";\n        for (let i = 0; i < n; i++)\n            out += str;\n        return out;\n    }\n    /**\n    Get the markdown string for a given opening or closing mark.\n    */\n    markString(mark, open, parent, index) {\n        let info = this.getMark(mark.type.name);\n        let value = open ? info.open : info.close;\n        return typeof value == \"string\" ? value : value(this, mark, parent, index);\n    }\n    /**\n    Get leading and trailing whitespace from a string. Values of\n    leading or trailing property of the return object will be undefined\n    if there is no match.\n    */\n    getEnclosingWhitespace(text) {\n        return {\n            leading: (text.match(/^(\\s+)/) || [undefined])[0],\n            trailing: (text.match(/(\\s+)$/) || [undefined])[0]\n        };\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-markdown/dist/index.js\n");

/***/ })

};
;