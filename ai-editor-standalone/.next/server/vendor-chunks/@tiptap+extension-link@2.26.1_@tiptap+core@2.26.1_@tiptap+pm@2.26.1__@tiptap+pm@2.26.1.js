"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-link@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-link@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-link@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-link/dist/index.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-link@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-link/dist/index.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Link: () => (/* binding */ Link),\n/* harmony export */   \"default\": () => (/* binding */ Link),\n/* harmony export */   isAllowedUri: () => (/* binding */ isAllowedUri),\n/* harmony export */   pasteRegex: () => (/* binding */ pasteRegex)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var linkifyjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! linkifyjs */ \"(ssr)/./node_modules/.pnpm/linkifyjs@4.3.2/node_modules/linkifyjs/dist/linkify.mjs\");\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/state/dist/index.js\");\n\n\n\n\n// From DOMPurify\n// https://github.com/cure53/DOMPurify/blob/main/src/regexp.ts\nconst UNICODE_WHITESPACE_PATTERN = '[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]';\nconst UNICODE_WHITESPACE_REGEX = new RegExp(UNICODE_WHITESPACE_PATTERN);\nconst UNICODE_WHITESPACE_REGEX_END = new RegExp(`${UNICODE_WHITESPACE_PATTERN}$`);\nconst UNICODE_WHITESPACE_REGEX_GLOBAL = new RegExp(UNICODE_WHITESPACE_PATTERN, 'g');\n\n/**\n * Check if the provided tokens form a valid link structure, which can either be a single link token\n * or a link token surrounded by parentheses or square brackets.\n *\n * This ensures that only complete and valid text is hyperlinked, preventing cases where a valid\n * top-level domain (TLD) is immediately followed by an invalid character, like a number. For\n * example, with the `find` method from Linkify, entering `example.com1` would result in\n * `example.com` being linked and the trailing `1` left as plain text. By using the `tokenize`\n * method, we can perform more comprehensive validation on the input text.\n */\nfunction isValidLinkStructure(tokens) {\n    if (tokens.length === 1) {\n        return tokens[0].isLink;\n    }\n    if (tokens.length === 3 && tokens[1].isLink) {\n        return ['()', '[]'].includes(tokens[0].value + tokens[2].value);\n    }\n    return false;\n}\n/**\n * This plugin allows you to automatically add links to your editor.\n * @param options The plugin options\n * @returns The plugin instance\n */\nfunction autolink(options) {\n    return new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_1__.Plugin({\n        key: new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_1__.PluginKey('autolink'),\n        appendTransaction: (transactions, oldState, newState) => {\n            /**\n             * Does the transaction change the document?\n             */\n            const docChanges = transactions.some(transaction => transaction.docChanged) && !oldState.doc.eq(newState.doc);\n            /**\n             * Prevent autolink if the transaction is not a document change or if the transaction has the meta `preventAutolink`.\n             */\n            const preventAutolink = transactions.some(transaction => transaction.getMeta('preventAutolink'));\n            /**\n             * Prevent autolink if the transaction is not a document change\n             * or if the transaction has the meta `preventAutolink`.\n             */\n            if (!docChanges || preventAutolink) {\n                return;\n            }\n            const { tr } = newState;\n            const transform = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.combineTransactionSteps)(oldState.doc, [...transactions]);\n            const changes = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getChangedRanges)(transform);\n            changes.forEach(({ newRange }) => {\n                // Now let’s see if we can add new links.\n                const nodesInChangedRanges = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.findChildrenInRange)(newState.doc, newRange, node => node.isTextblock);\n                let textBlock;\n                let textBeforeWhitespace;\n                if (nodesInChangedRanges.length > 1) {\n                    // Grab the first node within the changed ranges (ex. the first of two paragraphs when hitting enter).\n                    textBlock = nodesInChangedRanges[0];\n                    textBeforeWhitespace = newState.doc.textBetween(textBlock.pos, textBlock.pos + textBlock.node.nodeSize, undefined, ' ');\n                }\n                else if (nodesInChangedRanges.length) {\n                    const endText = newState.doc.textBetween(newRange.from, newRange.to, ' ', ' ');\n                    if (!UNICODE_WHITESPACE_REGEX_END.test(endText)) {\n                        return;\n                    }\n                    textBlock = nodesInChangedRanges[0];\n                    textBeforeWhitespace = newState.doc.textBetween(textBlock.pos, newRange.to, undefined, ' ');\n                }\n                if (textBlock && textBeforeWhitespace) {\n                    const wordsBeforeWhitespace = textBeforeWhitespace.split(UNICODE_WHITESPACE_REGEX).filter(Boolean);\n                    if (wordsBeforeWhitespace.length <= 0) {\n                        return false;\n                    }\n                    const lastWordBeforeSpace = wordsBeforeWhitespace[wordsBeforeWhitespace.length - 1];\n                    const lastWordAndBlockOffset = textBlock.pos + textBeforeWhitespace.lastIndexOf(lastWordBeforeSpace);\n                    if (!lastWordBeforeSpace) {\n                        return false;\n                    }\n                    const linksBeforeSpace = (0,linkifyjs__WEBPACK_IMPORTED_MODULE_0__.tokenize)(lastWordBeforeSpace).map(t => t.toObject(options.defaultProtocol));\n                    if (!isValidLinkStructure(linksBeforeSpace)) {\n                        return false;\n                    }\n                    linksBeforeSpace\n                        .filter(link => link.isLink)\n                        // Calculate link position.\n                        .map(link => ({\n                        ...link,\n                        from: lastWordAndBlockOffset + link.start + 1,\n                        to: lastWordAndBlockOffset + link.end + 1,\n                    }))\n                        // ignore link inside code mark\n                        .filter(link => {\n                        if (!newState.schema.marks.code) {\n                            return true;\n                        }\n                        return !newState.doc.rangeHasMark(link.from, link.to, newState.schema.marks.code);\n                    })\n                        // validate link\n                        .filter(link => options.validate(link.value))\n                        // check whether should autolink\n                        .filter(link => options.shouldAutoLink(link.value))\n                        // Add link mark.\n                        .forEach(link => {\n                        if ((0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getMarksBetween)(link.from, link.to, newState.doc).some(item => item.mark.type === options.type)) {\n                            return;\n                        }\n                        tr.addMark(link.from, link.to, options.type.create({\n                            href: link.href,\n                        }));\n                    });\n                }\n            });\n            if (!tr.steps.length) {\n                return;\n            }\n            return tr;\n        },\n    });\n}\n\nfunction clickHandler(options) {\n    return new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_1__.Plugin({\n        key: new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_1__.PluginKey('handleClickLink'),\n        props: {\n            handleClick: (view, pos, event) => {\n                var _a, _b;\n                if (event.button !== 0) {\n                    return false;\n                }\n                if (!view.editable) {\n                    return false;\n                }\n                let a = event.target;\n                const els = [];\n                while (a.nodeName !== 'DIV') {\n                    els.push(a);\n                    a = a.parentNode;\n                }\n                if (!els.find(value => value.nodeName === 'A')) {\n                    return false;\n                }\n                const attrs = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getAttributes)(view.state, options.type.name);\n                const link = event.target;\n                const href = (_a = link === null || link === void 0 ? void 0 : link.href) !== null && _a !== void 0 ? _a : attrs.href;\n                const target = (_b = link === null || link === void 0 ? void 0 : link.target) !== null && _b !== void 0 ? _b : attrs.target;\n                if (link && href) {\n                    window.open(href, target);\n                    return true;\n                }\n                return false;\n            },\n        },\n    });\n}\n\nfunction pasteHandler(options) {\n    return new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_1__.Plugin({\n        key: new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_1__.PluginKey('handlePasteLink'),\n        props: {\n            handlePaste: (view, event, slice) => {\n                const { state } = view;\n                const { selection } = state;\n                const { empty } = selection;\n                if (empty) {\n                    return false;\n                }\n                let textContent = '';\n                slice.content.forEach(node => {\n                    textContent += node.textContent;\n                });\n                const link = (0,linkifyjs__WEBPACK_IMPORTED_MODULE_0__.find)(textContent, { defaultProtocol: options.defaultProtocol }).find(item => item.isLink && item.value === textContent);\n                if (!textContent || !link) {\n                    return false;\n                }\n                return options.editor.commands.setMark(options.type, {\n                    href: link.href,\n                });\n            },\n        },\n    });\n}\n\nconst pasteRegex = /https?:\\/\\/(?:www\\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\\.[a-zA-Z]{2,}\\b(?:[-a-zA-Z0-9@:%._+~#=?!&/]*)(?:[-a-zA-Z0-9@:%._+~#=?!&/]*)/gi;\nfunction isAllowedUri(uri, protocols) {\n    const allowedProtocols = [\n        'http',\n        'https',\n        'ftp',\n        'ftps',\n        'mailto',\n        'tel',\n        'callto',\n        'sms',\n        'cid',\n        'xmpp',\n    ];\n    if (protocols) {\n        protocols.forEach(protocol => {\n            const nextProtocol = typeof protocol === 'string' ? protocol : protocol.scheme;\n            if (nextProtocol) {\n                allowedProtocols.push(nextProtocol);\n            }\n        });\n    }\n    return (!uri\n        || uri.replace(UNICODE_WHITESPACE_REGEX_GLOBAL, '').match(new RegExp(\n        // eslint-disable-next-line no-useless-escape\n        `^(?:(?:${allowedProtocols.join('|')}):|[^a-z]|[a-z0-9+.\\-]+(?:[^a-z+.\\-:]|$))`, 'i')));\n}\n/**\n * This extension allows you to create links.\n * @see https://www.tiptap.dev/api/marks/link\n */\nconst Link = _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.Mark.create({\n    name: 'link',\n    priority: 1000,\n    keepOnSplit: false,\n    exitable: true,\n    onCreate() {\n        if (this.options.validate && !this.options.shouldAutoLink) {\n            // Copy the validate function to the shouldAutoLink option\n            this.options.shouldAutoLink = this.options.validate;\n            console.warn('The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.');\n        }\n        this.options.protocols.forEach(protocol => {\n            if (typeof protocol === 'string') {\n                (0,linkifyjs__WEBPACK_IMPORTED_MODULE_0__.registerCustomProtocol)(protocol);\n                return;\n            }\n            (0,linkifyjs__WEBPACK_IMPORTED_MODULE_0__.registerCustomProtocol)(protocol.scheme, protocol.optionalSlashes);\n        });\n    },\n    onDestroy() {\n        (0,linkifyjs__WEBPACK_IMPORTED_MODULE_0__.reset)();\n    },\n    inclusive() {\n        return this.options.autolink;\n    },\n    addOptions() {\n        return {\n            openOnClick: true,\n            linkOnPaste: true,\n            autolink: true,\n            protocols: [],\n            defaultProtocol: 'http',\n            HTMLAttributes: {\n                target: '_blank',\n                rel: 'noopener noreferrer nofollow',\n                class: null,\n            },\n            isAllowedUri: (url, ctx) => !!isAllowedUri(url, ctx.protocols),\n            validate: url => !!url,\n            shouldAutoLink: url => !!url,\n        };\n    },\n    addAttributes() {\n        return {\n            href: {\n                default: null,\n                parseHTML(element) {\n                    return element.getAttribute('href');\n                },\n            },\n            target: {\n                default: this.options.HTMLAttributes.target,\n            },\n            rel: {\n                default: this.options.HTMLAttributes.rel,\n            },\n            class: {\n                default: this.options.HTMLAttributes.class,\n            },\n        };\n    },\n    parseHTML() {\n        return [\n            {\n                tag: 'a[href]',\n                getAttrs: dom => {\n                    const href = dom.getAttribute('href');\n                    // prevent XSS attacks\n                    if (!href\n                        || !this.options.isAllowedUri(href, {\n                            defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n                            protocols: this.options.protocols,\n                            defaultProtocol: this.options.defaultProtocol,\n                        })) {\n                        return false;\n                    }\n                    return null;\n                },\n            },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        // prevent XSS attacks\n        if (!this.options.isAllowedUri(HTMLAttributes.href, {\n            defaultValidate: href => !!isAllowedUri(href, this.options.protocols),\n            protocols: this.options.protocols,\n            defaultProtocol: this.options.defaultProtocol,\n        })) {\n            // strip out the href\n            return [\n                'a',\n                (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.mergeAttributes)(this.options.HTMLAttributes, { ...HTMLAttributes, href: '' }),\n                0,\n            ];\n        }\n        return ['a', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n    addCommands() {\n        return {\n            setLink: attributes => ({ chain }) => {\n                const { href } = attributes;\n                if (!this.options.isAllowedUri(href, {\n                    defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n                    protocols: this.options.protocols,\n                    defaultProtocol: this.options.defaultProtocol,\n                })) {\n                    return false;\n                }\n                return chain().setMark(this.name, attributes).setMeta('preventAutolink', true).run();\n            },\n            toggleLink: attributes => ({ chain }) => {\n                const { href } = attributes;\n                if (!this.options.isAllowedUri(href, {\n                    defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n                    protocols: this.options.protocols,\n                    defaultProtocol: this.options.defaultProtocol,\n                })) {\n                    return false;\n                }\n                return chain()\n                    .toggleMark(this.name, attributes, { extendEmptyMarkRange: true })\n                    .setMeta('preventAutolink', true)\n                    .run();\n            },\n            unsetLink: () => ({ chain }) => {\n                return chain()\n                    .unsetMark(this.name, { extendEmptyMarkRange: true })\n                    .setMeta('preventAutolink', true)\n                    .run();\n            },\n        };\n    },\n    addPasteRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.markPasteRule)({\n                find: text => {\n                    const foundLinks = [];\n                    if (text) {\n                        const { protocols, defaultProtocol } = this.options;\n                        const links = (0,linkifyjs__WEBPACK_IMPORTED_MODULE_0__.find)(text).filter(item => item.isLink\n                            && this.options.isAllowedUri(item.value, {\n                                defaultValidate: href => !!isAllowedUri(href, protocols),\n                                protocols,\n                                defaultProtocol,\n                            }));\n                        if (links.length) {\n                            links.forEach(link => foundLinks.push({\n                                text: link.value,\n                                data: {\n                                    href: link.href,\n                                },\n                                index: link.start,\n                            }));\n                        }\n                    }\n                    return foundLinks;\n                },\n                type: this.type,\n                getAttributes: match => {\n                    var _a;\n                    return {\n                        href: (_a = match.data) === null || _a === void 0 ? void 0 : _a.href,\n                    };\n                },\n            }),\n        ];\n    },\n    addProseMirrorPlugins() {\n        const plugins = [];\n        const { protocols, defaultProtocol } = this.options;\n        if (this.options.autolink) {\n            plugins.push(autolink({\n                type: this.type,\n                defaultProtocol: this.options.defaultProtocol,\n                validate: url => this.options.isAllowedUri(url, {\n                    defaultValidate: href => !!isAllowedUri(href, protocols),\n                    protocols,\n                    defaultProtocol,\n                }),\n                shouldAutoLink: this.options.shouldAutoLink,\n            }));\n        }\n        if (this.options.openOnClick === true) {\n            plugins.push(clickHandler({\n                type: this.type,\n            }));\n        }\n        if (this.options.linkOnPaste) {\n            plugins.push(pasteHandler({\n                editor: this.editor,\n                defaultProtocol: this.options.defaultProtocol,\n                type: this.type,\n            }));\n        }\n        return plugins;\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-link@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-link/dist/index.js\n");

/***/ })

};
;