"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tippy.js@6.3.7";
exports.ids = ["vendor-chunks/tippy.js@6.3.7"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/tippy.js@6.3.7/node_modules/tippy.js/dist/tippy.esm.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/tippy.js@6.3.7/node_modules/tippy.js/dist/tippy.esm.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animateFill: () => (/* binding */ animateFill),\n/* harmony export */   createSingleton: () => (/* binding */ createSingleton),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   delegate: () => (/* binding */ delegate),\n/* harmony export */   followCursor: () => (/* binding */ followCursor),\n/* harmony export */   hideAll: () => (/* binding */ hideAll),\n/* harmony export */   inlinePositioning: () => (/* binding */ inlinePositioning),\n/* harmony export */   roundArrow: () => (/* binding */ ROUND_ARROW),\n/* harmony export */   sticky: () => (/* binding */ sticky)\n/* harmony export */ });\n/* harmony import */ var _popperjs_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @popperjs/core */ \"(ssr)/./node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/popper.js\");\n/* harmony import */ var _popperjs_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @popperjs/core */ \"(ssr)/./node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/applyStyles.js\");\n/**!\n* tippy.js v6.3.7\n* (c) 2017-2021 atomiks\n* MIT License\n*/\n\n\nvar ROUND_ARROW = '<svg width=\"16\" height=\"6\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z\"></svg>';\nvar BOX_CLASS = \"tippy-box\";\nvar CONTENT_CLASS = \"tippy-content\";\nvar BACKDROP_CLASS = \"tippy-backdrop\";\nvar ARROW_CLASS = \"tippy-arrow\";\nvar SVG_ARROW_CLASS = \"tippy-svg-arrow\";\nvar TOUCH_OPTIONS = {\n  passive: true,\n  capture: true\n};\nvar TIPPY_DEFAULT_APPEND_TO = function TIPPY_DEFAULT_APPEND_TO() {\n  return document.body;\n};\n\nfunction hasOwnProperty(obj, key) {\n  return {}.hasOwnProperty.call(obj, key);\n}\nfunction getValueAtIndexOrReturn(value, index, defaultValue) {\n  if (Array.isArray(value)) {\n    var v = value[index];\n    return v == null ? Array.isArray(defaultValue) ? defaultValue[index] : defaultValue : v;\n  }\n\n  return value;\n}\nfunction isType(value, type) {\n  var str = {}.toString.call(value);\n  return str.indexOf('[object') === 0 && str.indexOf(type + \"]\") > -1;\n}\nfunction invokeWithArgsOrReturn(value, args) {\n  return typeof value === 'function' ? value.apply(void 0, args) : value;\n}\nfunction debounce(fn, ms) {\n  // Avoid wrapping in `setTimeout` if ms is 0 anyway\n  if (ms === 0) {\n    return fn;\n  }\n\n  var timeout;\n  return function (arg) {\n    clearTimeout(timeout);\n    timeout = setTimeout(function () {\n      fn(arg);\n    }, ms);\n  };\n}\nfunction removeProperties(obj, keys) {\n  var clone = Object.assign({}, obj);\n  keys.forEach(function (key) {\n    delete clone[key];\n  });\n  return clone;\n}\nfunction splitBySpaces(value) {\n  return value.split(/\\s+/).filter(Boolean);\n}\nfunction normalizeToArray(value) {\n  return [].concat(value);\n}\nfunction pushIfUnique(arr, value) {\n  if (arr.indexOf(value) === -1) {\n    arr.push(value);\n  }\n}\nfunction unique(arr) {\n  return arr.filter(function (item, index) {\n    return arr.indexOf(item) === index;\n  });\n}\nfunction getBasePlacement(placement) {\n  return placement.split('-')[0];\n}\nfunction arrayFrom(value) {\n  return [].slice.call(value);\n}\nfunction removeUndefinedProps(obj) {\n  return Object.keys(obj).reduce(function (acc, key) {\n    if (obj[key] !== undefined) {\n      acc[key] = obj[key];\n    }\n\n    return acc;\n  }, {});\n}\n\nfunction div() {\n  return document.createElement('div');\n}\nfunction isElement(value) {\n  return ['Element', 'Fragment'].some(function (type) {\n    return isType(value, type);\n  });\n}\nfunction isNodeList(value) {\n  return isType(value, 'NodeList');\n}\nfunction isMouseEvent(value) {\n  return isType(value, 'MouseEvent');\n}\nfunction isReferenceElement(value) {\n  return !!(value && value._tippy && value._tippy.reference === value);\n}\nfunction getArrayOfElements(value) {\n  if (isElement(value)) {\n    return [value];\n  }\n\n  if (isNodeList(value)) {\n    return arrayFrom(value);\n  }\n\n  if (Array.isArray(value)) {\n    return value;\n  }\n\n  return arrayFrom(document.querySelectorAll(value));\n}\nfunction setTransitionDuration(els, value) {\n  els.forEach(function (el) {\n    if (el) {\n      el.style.transitionDuration = value + \"ms\";\n    }\n  });\n}\nfunction setVisibilityState(els, state) {\n  els.forEach(function (el) {\n    if (el) {\n      el.setAttribute('data-state', state);\n    }\n  });\n}\nfunction getOwnerDocument(elementOrElements) {\n  var _element$ownerDocumen;\n\n  var _normalizeToArray = normalizeToArray(elementOrElements),\n      element = _normalizeToArray[0]; // Elements created via a <template> have an ownerDocument with no reference to the body\n\n\n  return element != null && (_element$ownerDocumen = element.ownerDocument) != null && _element$ownerDocumen.body ? element.ownerDocument : document;\n}\nfunction isCursorOutsideInteractiveBorder(popperTreeData, event) {\n  var clientX = event.clientX,\n      clientY = event.clientY;\n  return popperTreeData.every(function (_ref) {\n    var popperRect = _ref.popperRect,\n        popperState = _ref.popperState,\n        props = _ref.props;\n    var interactiveBorder = props.interactiveBorder;\n    var basePlacement = getBasePlacement(popperState.placement);\n    var offsetData = popperState.modifiersData.offset;\n\n    if (!offsetData) {\n      return true;\n    }\n\n    var topDistance = basePlacement === 'bottom' ? offsetData.top.y : 0;\n    var bottomDistance = basePlacement === 'top' ? offsetData.bottom.y : 0;\n    var leftDistance = basePlacement === 'right' ? offsetData.left.x : 0;\n    var rightDistance = basePlacement === 'left' ? offsetData.right.x : 0;\n    var exceedsTop = popperRect.top - clientY + topDistance > interactiveBorder;\n    var exceedsBottom = clientY - popperRect.bottom - bottomDistance > interactiveBorder;\n    var exceedsLeft = popperRect.left - clientX + leftDistance > interactiveBorder;\n    var exceedsRight = clientX - popperRect.right - rightDistance > interactiveBorder;\n    return exceedsTop || exceedsBottom || exceedsLeft || exceedsRight;\n  });\n}\nfunction updateTransitionEndListener(box, action, listener) {\n  var method = action + \"EventListener\"; // some browsers apparently support `transition` (unprefixed) but only fire\n  // `webkitTransitionEnd`...\n\n  ['transitionend', 'webkitTransitionEnd'].forEach(function (event) {\n    box[method](event, listener);\n  });\n}\n/**\n * Compared to xxx.contains, this function works for dom structures with shadow\n * dom\n */\n\nfunction actualContains(parent, child) {\n  var target = child;\n\n  while (target) {\n    var _target$getRootNode;\n\n    if (parent.contains(target)) {\n      return true;\n    }\n\n    target = target.getRootNode == null ? void 0 : (_target$getRootNode = target.getRootNode()) == null ? void 0 : _target$getRootNode.host;\n  }\n\n  return false;\n}\n\nvar currentInput = {\n  isTouch: false\n};\nvar lastMouseMoveTime = 0;\n/**\n * When a `touchstart` event is fired, it's assumed the user is using touch\n * input. We'll bind a `mousemove` event listener to listen for mouse input in\n * the future. This way, the `isTouch` property is fully dynamic and will handle\n * hybrid devices that use a mix of touch + mouse input.\n */\n\nfunction onDocumentTouchStart() {\n  if (currentInput.isTouch) {\n    return;\n  }\n\n  currentInput.isTouch = true;\n\n  if (window.performance) {\n    document.addEventListener('mousemove', onDocumentMouseMove);\n  }\n}\n/**\n * When two `mousemove` event are fired consecutively within 20ms, it's assumed\n * the user is using mouse input again. `mousemove` can fire on touch devices as\n * well, but very rarely that quickly.\n */\n\nfunction onDocumentMouseMove() {\n  var now = performance.now();\n\n  if (now - lastMouseMoveTime < 20) {\n    currentInput.isTouch = false;\n    document.removeEventListener('mousemove', onDocumentMouseMove);\n  }\n\n  lastMouseMoveTime = now;\n}\n/**\n * When an element is in focus and has a tippy, leaving the tab/window and\n * returning causes it to show again. For mouse users this is unexpected, but\n * for keyboard use it makes sense.\n * TODO: find a better technique to solve this problem\n */\n\nfunction onWindowBlur() {\n  var activeElement = document.activeElement;\n\n  if (isReferenceElement(activeElement)) {\n    var instance = activeElement._tippy;\n\n    if (activeElement.blur && !instance.state.isVisible) {\n      activeElement.blur();\n    }\n  }\n}\nfunction bindGlobalEventListeners() {\n  document.addEventListener('touchstart', onDocumentTouchStart, TOUCH_OPTIONS);\n  window.addEventListener('blur', onWindowBlur);\n}\n\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined';\nvar isIE11 = isBrowser ? // @ts-ignore\n!!window.msCrypto : false;\n\nfunction createMemoryLeakWarning(method) {\n  var txt = method === 'destroy' ? 'n already-' : ' ';\n  return [method + \"() was called on a\" + txt + \"destroyed instance. This is a no-op but\", 'indicates a potential memory leak.'].join(' ');\n}\nfunction clean(value) {\n  var spacesAndTabs = /[ \\t]{2,}/g;\n  var lineStartWithSpaces = /^[ \\t]*/gm;\n  return value.replace(spacesAndTabs, ' ').replace(lineStartWithSpaces, '').trim();\n}\n\nfunction getDevMessage(message) {\n  return clean(\"\\n  %ctippy.js\\n\\n  %c\" + clean(message) + \"\\n\\n  %c\\uD83D\\uDC77\\u200D This is a development-only message. It will be removed in production.\\n  \");\n}\n\nfunction getFormattedMessage(message) {\n  return [getDevMessage(message), // title\n  'color: #00C584; font-size: 1.3em; font-weight: bold;', // message\n  'line-height: 1.5', // footer\n  'color: #a6a095;'];\n} // Assume warnings and errors never have the same message\n\nvar visitedMessages;\n\nif (true) {\n  resetVisitedMessages();\n}\n\nfunction resetVisitedMessages() {\n  visitedMessages = new Set();\n}\nfunction warnWhen(condition, message) {\n  if (condition && !visitedMessages.has(message)) {\n    var _console;\n\n    visitedMessages.add(message);\n\n    (_console = console).warn.apply(_console, getFormattedMessage(message));\n  }\n}\nfunction errorWhen(condition, message) {\n  if (condition && !visitedMessages.has(message)) {\n    var _console2;\n\n    visitedMessages.add(message);\n\n    (_console2 = console).error.apply(_console2, getFormattedMessage(message));\n  }\n}\nfunction validateTargets(targets) {\n  var didPassFalsyValue = !targets;\n  var didPassPlainObject = Object.prototype.toString.call(targets) === '[object Object]' && !targets.addEventListener;\n  errorWhen(didPassFalsyValue, ['tippy() was passed', '`' + String(targets) + '`', 'as its targets (first) argument. Valid types are: String, Element,', 'Element[], or NodeList.'].join(' '));\n  errorWhen(didPassPlainObject, ['tippy() was passed a plain object which is not supported as an argument', 'for virtual positioning. Use props.getReferenceClientRect instead.'].join(' '));\n}\n\nvar pluginProps = {\n  animateFill: false,\n  followCursor: false,\n  inlinePositioning: false,\n  sticky: false\n};\nvar renderProps = {\n  allowHTML: false,\n  animation: 'fade',\n  arrow: true,\n  content: '',\n  inertia: false,\n  maxWidth: 350,\n  role: 'tooltip',\n  theme: '',\n  zIndex: 9999\n};\nvar defaultProps = Object.assign({\n  appendTo: TIPPY_DEFAULT_APPEND_TO,\n  aria: {\n    content: 'auto',\n    expanded: 'auto'\n  },\n  delay: 0,\n  duration: [300, 250],\n  getReferenceClientRect: null,\n  hideOnClick: true,\n  ignoreAttributes: false,\n  interactive: false,\n  interactiveBorder: 2,\n  interactiveDebounce: 0,\n  moveTransition: '',\n  offset: [0, 10],\n  onAfterUpdate: function onAfterUpdate() {},\n  onBeforeUpdate: function onBeforeUpdate() {},\n  onCreate: function onCreate() {},\n  onDestroy: function onDestroy() {},\n  onHidden: function onHidden() {},\n  onHide: function onHide() {},\n  onMount: function onMount() {},\n  onShow: function onShow() {},\n  onShown: function onShown() {},\n  onTrigger: function onTrigger() {},\n  onUntrigger: function onUntrigger() {},\n  onClickOutside: function onClickOutside() {},\n  placement: 'top',\n  plugins: [],\n  popperOptions: {},\n  render: null,\n  showOnCreate: false,\n  touch: true,\n  trigger: 'mouseenter focus',\n  triggerTarget: null\n}, pluginProps, renderProps);\nvar defaultKeys = Object.keys(defaultProps);\nvar setDefaultProps = function setDefaultProps(partialProps) {\n  /* istanbul ignore else */\n  if (true) {\n    validateProps(partialProps, []);\n  }\n\n  var keys = Object.keys(partialProps);\n  keys.forEach(function (key) {\n    defaultProps[key] = partialProps[key];\n  });\n};\nfunction getExtendedPassedProps(passedProps) {\n  var plugins = passedProps.plugins || [];\n  var pluginProps = plugins.reduce(function (acc, plugin) {\n    var name = plugin.name,\n        defaultValue = plugin.defaultValue;\n\n    if (name) {\n      var _name;\n\n      acc[name] = passedProps[name] !== undefined ? passedProps[name] : (_name = defaultProps[name]) != null ? _name : defaultValue;\n    }\n\n    return acc;\n  }, {});\n  return Object.assign({}, passedProps, pluginProps);\n}\nfunction getDataAttributeProps(reference, plugins) {\n  var propKeys = plugins ? Object.keys(getExtendedPassedProps(Object.assign({}, defaultProps, {\n    plugins: plugins\n  }))) : defaultKeys;\n  var props = propKeys.reduce(function (acc, key) {\n    var valueAsString = (reference.getAttribute(\"data-tippy-\" + key) || '').trim();\n\n    if (!valueAsString) {\n      return acc;\n    }\n\n    if (key === 'content') {\n      acc[key] = valueAsString;\n    } else {\n      try {\n        acc[key] = JSON.parse(valueAsString);\n      } catch (e) {\n        acc[key] = valueAsString;\n      }\n    }\n\n    return acc;\n  }, {});\n  return props;\n}\nfunction evaluateProps(reference, props) {\n  var out = Object.assign({}, props, {\n    content: invokeWithArgsOrReturn(props.content, [reference])\n  }, props.ignoreAttributes ? {} : getDataAttributeProps(reference, props.plugins));\n  out.aria = Object.assign({}, defaultProps.aria, out.aria);\n  out.aria = {\n    expanded: out.aria.expanded === 'auto' ? props.interactive : out.aria.expanded,\n    content: out.aria.content === 'auto' ? props.interactive ? null : 'describedby' : out.aria.content\n  };\n  return out;\n}\nfunction validateProps(partialProps, plugins) {\n  if (partialProps === void 0) {\n    partialProps = {};\n  }\n\n  if (plugins === void 0) {\n    plugins = [];\n  }\n\n  var keys = Object.keys(partialProps);\n  keys.forEach(function (prop) {\n    var nonPluginProps = removeProperties(defaultProps, Object.keys(pluginProps));\n    var didPassUnknownProp = !hasOwnProperty(nonPluginProps, prop); // Check if the prop exists in `plugins`\n\n    if (didPassUnknownProp) {\n      didPassUnknownProp = plugins.filter(function (plugin) {\n        return plugin.name === prop;\n      }).length === 0;\n    }\n\n    warnWhen(didPassUnknownProp, [\"`\" + prop + \"`\", \"is not a valid prop. You may have spelled it incorrectly, or if it's\", 'a plugin, forgot to pass it in an array as props.plugins.', '\\n\\n', 'All props: https://atomiks.github.io/tippyjs/v6/all-props/\\n', 'Plugins: https://atomiks.github.io/tippyjs/v6/plugins/'].join(' '));\n  });\n}\n\nvar innerHTML = function innerHTML() {\n  return 'innerHTML';\n};\n\nfunction dangerouslySetInnerHTML(element, html) {\n  element[innerHTML()] = html;\n}\n\nfunction createArrowElement(value) {\n  var arrow = div();\n\n  if (value === true) {\n    arrow.className = ARROW_CLASS;\n  } else {\n    arrow.className = SVG_ARROW_CLASS;\n\n    if (isElement(value)) {\n      arrow.appendChild(value);\n    } else {\n      dangerouslySetInnerHTML(arrow, value);\n    }\n  }\n\n  return arrow;\n}\n\nfunction setContent(content, props) {\n  if (isElement(props.content)) {\n    dangerouslySetInnerHTML(content, '');\n    content.appendChild(props.content);\n  } else if (typeof props.content !== 'function') {\n    if (props.allowHTML) {\n      dangerouslySetInnerHTML(content, props.content);\n    } else {\n      content.textContent = props.content;\n    }\n  }\n}\nfunction getChildren(popper) {\n  var box = popper.firstElementChild;\n  var boxChildren = arrayFrom(box.children);\n  return {\n    box: box,\n    content: boxChildren.find(function (node) {\n      return node.classList.contains(CONTENT_CLASS);\n    }),\n    arrow: boxChildren.find(function (node) {\n      return node.classList.contains(ARROW_CLASS) || node.classList.contains(SVG_ARROW_CLASS);\n    }),\n    backdrop: boxChildren.find(function (node) {\n      return node.classList.contains(BACKDROP_CLASS);\n    })\n  };\n}\nfunction render(instance) {\n  var popper = div();\n  var box = div();\n  box.className = BOX_CLASS;\n  box.setAttribute('data-state', 'hidden');\n  box.setAttribute('tabindex', '-1');\n  var content = div();\n  content.className = CONTENT_CLASS;\n  content.setAttribute('data-state', 'hidden');\n  setContent(content, instance.props);\n  popper.appendChild(box);\n  box.appendChild(content);\n  onUpdate(instance.props, instance.props);\n\n  function onUpdate(prevProps, nextProps) {\n    var _getChildren = getChildren(popper),\n        box = _getChildren.box,\n        content = _getChildren.content,\n        arrow = _getChildren.arrow;\n\n    if (nextProps.theme) {\n      box.setAttribute('data-theme', nextProps.theme);\n    } else {\n      box.removeAttribute('data-theme');\n    }\n\n    if (typeof nextProps.animation === 'string') {\n      box.setAttribute('data-animation', nextProps.animation);\n    } else {\n      box.removeAttribute('data-animation');\n    }\n\n    if (nextProps.inertia) {\n      box.setAttribute('data-inertia', '');\n    } else {\n      box.removeAttribute('data-inertia');\n    }\n\n    box.style.maxWidth = typeof nextProps.maxWidth === 'number' ? nextProps.maxWidth + \"px\" : nextProps.maxWidth;\n\n    if (nextProps.role) {\n      box.setAttribute('role', nextProps.role);\n    } else {\n      box.removeAttribute('role');\n    }\n\n    if (prevProps.content !== nextProps.content || prevProps.allowHTML !== nextProps.allowHTML) {\n      setContent(content, instance.props);\n    }\n\n    if (nextProps.arrow) {\n      if (!arrow) {\n        box.appendChild(createArrowElement(nextProps.arrow));\n      } else if (prevProps.arrow !== nextProps.arrow) {\n        box.removeChild(arrow);\n        box.appendChild(createArrowElement(nextProps.arrow));\n      }\n    } else if (arrow) {\n      box.removeChild(arrow);\n    }\n  }\n\n  return {\n    popper: popper,\n    onUpdate: onUpdate\n  };\n} // Runtime check to identify if the render function is the default one; this\n// way we can apply default CSS transitions logic and it can be tree-shaken away\n\nrender.$$tippy = true;\n\nvar idCounter = 1;\nvar mouseMoveListeners = []; // Used by `hideAll()`\n\nvar mountedInstances = [];\nfunction createTippy(reference, passedProps) {\n  var props = evaluateProps(reference, Object.assign({}, defaultProps, getExtendedPassedProps(removeUndefinedProps(passedProps)))); // ===========================================================================\n  // 🔒 Private members\n  // ===========================================================================\n\n  var showTimeout;\n  var hideTimeout;\n  var scheduleHideAnimationFrame;\n  var isVisibleFromClick = false;\n  var didHideDueToDocumentMouseDown = false;\n  var didTouchMove = false;\n  var ignoreOnFirstUpdate = false;\n  var lastTriggerEvent;\n  var currentTransitionEndListener;\n  var onFirstUpdate;\n  var listeners = [];\n  var debouncedOnMouseMove = debounce(onMouseMove, props.interactiveDebounce);\n  var currentTarget; // ===========================================================================\n  // 🔑 Public members\n  // ===========================================================================\n\n  var id = idCounter++;\n  var popperInstance = null;\n  var plugins = unique(props.plugins);\n  var state = {\n    // Is the instance currently enabled?\n    isEnabled: true,\n    // Is the tippy currently showing and not transitioning out?\n    isVisible: false,\n    // Has the instance been destroyed?\n    isDestroyed: false,\n    // Is the tippy currently mounted to the DOM?\n    isMounted: false,\n    // Has the tippy finished transitioning in?\n    isShown: false\n  };\n  var instance = {\n    // properties\n    id: id,\n    reference: reference,\n    popper: div(),\n    popperInstance: popperInstance,\n    props: props,\n    state: state,\n    plugins: plugins,\n    // methods\n    clearDelayTimeouts: clearDelayTimeouts,\n    setProps: setProps,\n    setContent: setContent,\n    show: show,\n    hide: hide,\n    hideWithInteractivity: hideWithInteractivity,\n    enable: enable,\n    disable: disable,\n    unmount: unmount,\n    destroy: destroy\n  }; // TODO: Investigate why this early return causes a TDZ error in the tests —\n  // it doesn't seem to happen in the browser\n\n  /* istanbul ignore if */\n\n  if (!props.render) {\n    if (true) {\n      errorWhen(true, 'render() function has not been supplied.');\n    }\n\n    return instance;\n  } // ===========================================================================\n  // Initial mutations\n  // ===========================================================================\n\n\n  var _props$render = props.render(instance),\n      popper = _props$render.popper,\n      onUpdate = _props$render.onUpdate;\n\n  popper.setAttribute('data-tippy-root', '');\n  popper.id = \"tippy-\" + instance.id;\n  instance.popper = popper;\n  reference._tippy = instance;\n  popper._tippy = instance;\n  var pluginsHooks = plugins.map(function (plugin) {\n    return plugin.fn(instance);\n  });\n  var hasAriaExpanded = reference.hasAttribute('aria-expanded');\n  addListeners();\n  handleAriaExpandedAttribute();\n  handleStyles();\n  invokeHook('onCreate', [instance]);\n\n  if (props.showOnCreate) {\n    scheduleShow();\n  } // Prevent a tippy with a delay from hiding if the cursor left then returned\n  // before it started hiding\n\n\n  popper.addEventListener('mouseenter', function () {\n    if (instance.props.interactive && instance.state.isVisible) {\n      instance.clearDelayTimeouts();\n    }\n  });\n  popper.addEventListener('mouseleave', function () {\n    if (instance.props.interactive && instance.props.trigger.indexOf('mouseenter') >= 0) {\n      getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    }\n  });\n  return instance; // ===========================================================================\n  // 🔒 Private methods\n  // ===========================================================================\n\n  function getNormalizedTouchSettings() {\n    var touch = instance.props.touch;\n    return Array.isArray(touch) ? touch : [touch, 0];\n  }\n\n  function getIsCustomTouchBehavior() {\n    return getNormalizedTouchSettings()[0] === 'hold';\n  }\n\n  function getIsDefaultRenderFn() {\n    var _instance$props$rende;\n\n    // @ts-ignore\n    return !!((_instance$props$rende = instance.props.render) != null && _instance$props$rende.$$tippy);\n  }\n\n  function getCurrentTarget() {\n    return currentTarget || reference;\n  }\n\n  function getDocument() {\n    var parent = getCurrentTarget().parentNode;\n    return parent ? getOwnerDocument(parent) : document;\n  }\n\n  function getDefaultTemplateChildren() {\n    return getChildren(popper);\n  }\n\n  function getDelay(isShow) {\n    // For touch or keyboard input, force `0` delay for UX reasons\n    // Also if the instance is mounted but not visible (transitioning out),\n    // ignore delay\n    if (instance.state.isMounted && !instance.state.isVisible || currentInput.isTouch || lastTriggerEvent && lastTriggerEvent.type === 'focus') {\n      return 0;\n    }\n\n    return getValueAtIndexOrReturn(instance.props.delay, isShow ? 0 : 1, defaultProps.delay);\n  }\n\n  function handleStyles(fromHide) {\n    if (fromHide === void 0) {\n      fromHide = false;\n    }\n\n    popper.style.pointerEvents = instance.props.interactive && !fromHide ? '' : 'none';\n    popper.style.zIndex = \"\" + instance.props.zIndex;\n  }\n\n  function invokeHook(hook, args, shouldInvokePropsHook) {\n    if (shouldInvokePropsHook === void 0) {\n      shouldInvokePropsHook = true;\n    }\n\n    pluginsHooks.forEach(function (pluginHooks) {\n      if (pluginHooks[hook]) {\n        pluginHooks[hook].apply(pluginHooks, args);\n      }\n    });\n\n    if (shouldInvokePropsHook) {\n      var _instance$props;\n\n      (_instance$props = instance.props)[hook].apply(_instance$props, args);\n    }\n  }\n\n  function handleAriaContentAttribute() {\n    var aria = instance.props.aria;\n\n    if (!aria.content) {\n      return;\n    }\n\n    var attr = \"aria-\" + aria.content;\n    var id = popper.id;\n    var nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach(function (node) {\n      var currentValue = node.getAttribute(attr);\n\n      if (instance.state.isVisible) {\n        node.setAttribute(attr, currentValue ? currentValue + \" \" + id : id);\n      } else {\n        var nextValue = currentValue && currentValue.replace(id, '').trim();\n\n        if (nextValue) {\n          node.setAttribute(attr, nextValue);\n        } else {\n          node.removeAttribute(attr);\n        }\n      }\n    });\n  }\n\n  function handleAriaExpandedAttribute() {\n    if (hasAriaExpanded || !instance.props.aria.expanded) {\n      return;\n    }\n\n    var nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach(function (node) {\n      if (instance.props.interactive) {\n        node.setAttribute('aria-expanded', instance.state.isVisible && node === getCurrentTarget() ? 'true' : 'false');\n      } else {\n        node.removeAttribute('aria-expanded');\n      }\n    });\n  }\n\n  function cleanupInteractiveMouseListeners() {\n    getDocument().removeEventListener('mousemove', debouncedOnMouseMove);\n    mouseMoveListeners = mouseMoveListeners.filter(function (listener) {\n      return listener !== debouncedOnMouseMove;\n    });\n  }\n\n  function onDocumentPress(event) {\n    // Moved finger to scroll instead of an intentional tap outside\n    if (currentInput.isTouch) {\n      if (didTouchMove || event.type === 'mousedown') {\n        return;\n      }\n    }\n\n    var actualTarget = event.composedPath && event.composedPath()[0] || event.target; // Clicked on interactive popper\n\n    if (instance.props.interactive && actualContains(popper, actualTarget)) {\n      return;\n    } // Clicked on the event listeners target\n\n\n    if (normalizeToArray(instance.props.triggerTarget || reference).some(function (el) {\n      return actualContains(el, actualTarget);\n    })) {\n      if (currentInput.isTouch) {\n        return;\n      }\n\n      if (instance.state.isVisible && instance.props.trigger.indexOf('click') >= 0) {\n        return;\n      }\n    } else {\n      invokeHook('onClickOutside', [instance, event]);\n    }\n\n    if (instance.props.hideOnClick === true) {\n      instance.clearDelayTimeouts();\n      instance.hide(); // `mousedown` event is fired right before `focus` if pressing the\n      // currentTarget. This lets a tippy with `focus` trigger know that it\n      // should not show\n\n      didHideDueToDocumentMouseDown = true;\n      setTimeout(function () {\n        didHideDueToDocumentMouseDown = false;\n      }); // The listener gets added in `scheduleShow()`, but this may be hiding it\n      // before it shows, and hide()'s early bail-out behavior can prevent it\n      // from being cleaned up\n\n      if (!instance.state.isMounted) {\n        removeDocumentPress();\n      }\n    }\n  }\n\n  function onTouchMove() {\n    didTouchMove = true;\n  }\n\n  function onTouchStart() {\n    didTouchMove = false;\n  }\n\n  function addDocumentPress() {\n    var doc = getDocument();\n    doc.addEventListener('mousedown', onDocumentPress, true);\n    doc.addEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.addEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.addEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function removeDocumentPress() {\n    var doc = getDocument();\n    doc.removeEventListener('mousedown', onDocumentPress, true);\n    doc.removeEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.removeEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.removeEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function onTransitionedOut(duration, callback) {\n    onTransitionEnd(duration, function () {\n      if (!instance.state.isVisible && popper.parentNode && popper.parentNode.contains(popper)) {\n        callback();\n      }\n    });\n  }\n\n  function onTransitionedIn(duration, callback) {\n    onTransitionEnd(duration, callback);\n  }\n\n  function onTransitionEnd(duration, callback) {\n    var box = getDefaultTemplateChildren().box;\n\n    function listener(event) {\n      if (event.target === box) {\n        updateTransitionEndListener(box, 'remove', listener);\n        callback();\n      }\n    } // Make callback synchronous if duration is 0\n    // `transitionend` won't fire otherwise\n\n\n    if (duration === 0) {\n      return callback();\n    }\n\n    updateTransitionEndListener(box, 'remove', currentTransitionEndListener);\n    updateTransitionEndListener(box, 'add', listener);\n    currentTransitionEndListener = listener;\n  }\n\n  function on(eventType, handler, options) {\n    if (options === void 0) {\n      options = false;\n    }\n\n    var nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach(function (node) {\n      node.addEventListener(eventType, handler, options);\n      listeners.push({\n        node: node,\n        eventType: eventType,\n        handler: handler,\n        options: options\n      });\n    });\n  }\n\n  function addListeners() {\n    if (getIsCustomTouchBehavior()) {\n      on('touchstart', onTrigger, {\n        passive: true\n      });\n      on('touchend', onMouseLeave, {\n        passive: true\n      });\n    }\n\n    splitBySpaces(instance.props.trigger).forEach(function (eventType) {\n      if (eventType === 'manual') {\n        return;\n      }\n\n      on(eventType, onTrigger);\n\n      switch (eventType) {\n        case 'mouseenter':\n          on('mouseleave', onMouseLeave);\n          break;\n\n        case 'focus':\n          on(isIE11 ? 'focusout' : 'blur', onBlurOrFocusOut);\n          break;\n\n        case 'focusin':\n          on('focusout', onBlurOrFocusOut);\n          break;\n      }\n    });\n  }\n\n  function removeListeners() {\n    listeners.forEach(function (_ref) {\n      var node = _ref.node,\n          eventType = _ref.eventType,\n          handler = _ref.handler,\n          options = _ref.options;\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function onTrigger(event) {\n    var _lastTriggerEvent;\n\n    var shouldScheduleClickHide = false;\n\n    if (!instance.state.isEnabled || isEventListenerStopped(event) || didHideDueToDocumentMouseDown) {\n      return;\n    }\n\n    var wasFocused = ((_lastTriggerEvent = lastTriggerEvent) == null ? void 0 : _lastTriggerEvent.type) === 'focus';\n    lastTriggerEvent = event;\n    currentTarget = event.currentTarget;\n    handleAriaExpandedAttribute();\n\n    if (!instance.state.isVisible && isMouseEvent(event)) {\n      // If scrolling, `mouseenter` events can be fired if the cursor lands\n      // over a new target, but `mousemove` events don't get fired. This\n      // causes interactive tooltips to get stuck open until the cursor is\n      // moved\n      mouseMoveListeners.forEach(function (listener) {\n        return listener(event);\n      });\n    } // Toggle show/hide when clicking click-triggered tooltips\n\n\n    if (event.type === 'click' && (instance.props.trigger.indexOf('mouseenter') < 0 || isVisibleFromClick) && instance.props.hideOnClick !== false && instance.state.isVisible) {\n      shouldScheduleClickHide = true;\n    } else {\n      scheduleShow(event);\n    }\n\n    if (event.type === 'click') {\n      isVisibleFromClick = !shouldScheduleClickHide;\n    }\n\n    if (shouldScheduleClickHide && !wasFocused) {\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseMove(event) {\n    var target = event.target;\n    var isCursorOverReferenceOrPopper = getCurrentTarget().contains(target) || popper.contains(target);\n\n    if (event.type === 'mousemove' && isCursorOverReferenceOrPopper) {\n      return;\n    }\n\n    var popperTreeData = getNestedPopperTree().concat(popper).map(function (popper) {\n      var _instance$popperInsta;\n\n      var instance = popper._tippy;\n      var state = (_instance$popperInsta = instance.popperInstance) == null ? void 0 : _instance$popperInsta.state;\n\n      if (state) {\n        return {\n          popperRect: popper.getBoundingClientRect(),\n          popperState: state,\n          props: props\n        };\n      }\n\n      return null;\n    }).filter(Boolean);\n\n    if (isCursorOutsideInteractiveBorder(popperTreeData, event)) {\n      cleanupInteractiveMouseListeners();\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseLeave(event) {\n    var shouldBail = isEventListenerStopped(event) || instance.props.trigger.indexOf('click') >= 0 && isVisibleFromClick;\n\n    if (shouldBail) {\n      return;\n    }\n\n    if (instance.props.interactive) {\n      instance.hideWithInteractivity(event);\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function onBlurOrFocusOut(event) {\n    if (instance.props.trigger.indexOf('focusin') < 0 && event.target !== getCurrentTarget()) {\n      return;\n    } // If focus was moved to within the popper\n\n\n    if (instance.props.interactive && event.relatedTarget && popper.contains(event.relatedTarget)) {\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function isEventListenerStopped(event) {\n    return currentInput.isTouch ? getIsCustomTouchBehavior() !== event.type.indexOf('touch') >= 0 : false;\n  }\n\n  function createPopperInstance() {\n    destroyPopperInstance();\n    var _instance$props2 = instance.props,\n        popperOptions = _instance$props2.popperOptions,\n        placement = _instance$props2.placement,\n        offset = _instance$props2.offset,\n        getReferenceClientRect = _instance$props2.getReferenceClientRect,\n        moveTransition = _instance$props2.moveTransition;\n    var arrow = getIsDefaultRenderFn() ? getChildren(popper).arrow : null;\n    var computedReference = getReferenceClientRect ? {\n      getBoundingClientRect: getReferenceClientRect,\n      contextElement: getReferenceClientRect.contextElement || getCurrentTarget()\n    } : reference;\n    var tippyModifier = {\n      name: '$$tippy',\n      enabled: true,\n      phase: 'beforeWrite',\n      requires: ['computeStyles'],\n      fn: function fn(_ref2) {\n        var state = _ref2.state;\n\n        if (getIsDefaultRenderFn()) {\n          var _getDefaultTemplateCh = getDefaultTemplateChildren(),\n              box = _getDefaultTemplateCh.box;\n\n          ['placement', 'reference-hidden', 'escaped'].forEach(function (attr) {\n            if (attr === 'placement') {\n              box.setAttribute('data-placement', state.placement);\n            } else {\n              if (state.attributes.popper[\"data-popper-\" + attr]) {\n                box.setAttribute(\"data-\" + attr, '');\n              } else {\n                box.removeAttribute(\"data-\" + attr);\n              }\n            }\n          });\n          state.attributes.popper = {};\n        }\n      }\n    };\n    var modifiers = [{\n      name: 'offset',\n      options: {\n        offset: offset\n      }\n    }, {\n      name: 'preventOverflow',\n      options: {\n        padding: {\n          top: 2,\n          bottom: 2,\n          left: 5,\n          right: 5\n        }\n      }\n    }, {\n      name: 'flip',\n      options: {\n        padding: 5\n      }\n    }, {\n      name: 'computeStyles',\n      options: {\n        adaptive: !moveTransition\n      }\n    }, tippyModifier];\n\n    if (getIsDefaultRenderFn() && arrow) {\n      modifiers.push({\n        name: 'arrow',\n        options: {\n          element: arrow,\n          padding: 3\n        }\n      });\n    }\n\n    modifiers.push.apply(modifiers, (popperOptions == null ? void 0 : popperOptions.modifiers) || []);\n    instance.popperInstance = (0,_popperjs_core__WEBPACK_IMPORTED_MODULE_0__.createPopper)(computedReference, popper, Object.assign({}, popperOptions, {\n      placement: placement,\n      onFirstUpdate: onFirstUpdate,\n      modifiers: modifiers\n    }));\n  }\n\n  function destroyPopperInstance() {\n    if (instance.popperInstance) {\n      instance.popperInstance.destroy();\n      instance.popperInstance = null;\n    }\n  }\n\n  function mount() {\n    var appendTo = instance.props.appendTo;\n    var parentNode; // By default, we'll append the popper to the triggerTargets's parentNode so\n    // it's directly after the reference element so the elements inside the\n    // tippy can be tabbed to\n    // If there are clipping issues, the user can specify a different appendTo\n    // and ensure focus management is handled correctly manually\n\n    var node = getCurrentTarget();\n\n    if (instance.props.interactive && appendTo === TIPPY_DEFAULT_APPEND_TO || appendTo === 'parent') {\n      parentNode = node.parentNode;\n    } else {\n      parentNode = invokeWithArgsOrReturn(appendTo, [node]);\n    } // The popper element needs to exist on the DOM before its position can be\n    // updated as Popper needs to read its dimensions\n\n\n    if (!parentNode.contains(popper)) {\n      parentNode.appendChild(popper);\n    }\n\n    instance.state.isMounted = true;\n    createPopperInstance();\n    /* istanbul ignore else */\n\n    if (true) {\n      // Accessibility check\n      warnWhen(instance.props.interactive && appendTo === defaultProps.appendTo && node.nextElementSibling !== popper, ['Interactive tippy element may not be accessible via keyboard', 'navigation because it is not directly after the reference element', 'in the DOM source order.', '\\n\\n', 'Using a wrapper <div> or <span> tag around the reference element', 'solves this by creating a new parentNode context.', '\\n\\n', 'Specifying `appendTo: document.body` silences this warning, but it', 'assumes you are using a focus management solution to handle', 'keyboard navigation.', '\\n\\n', 'See: https://atomiks.github.io/tippyjs/v6/accessibility/#interactivity'].join(' '));\n    }\n  }\n\n  function getNestedPopperTree() {\n    return arrayFrom(popper.querySelectorAll('[data-tippy-root]'));\n  }\n\n  function scheduleShow(event) {\n    instance.clearDelayTimeouts();\n\n    if (event) {\n      invokeHook('onTrigger', [instance, event]);\n    }\n\n    addDocumentPress();\n    var delay = getDelay(true);\n\n    var _getNormalizedTouchSe = getNormalizedTouchSettings(),\n        touchValue = _getNormalizedTouchSe[0],\n        touchDelay = _getNormalizedTouchSe[1];\n\n    if (currentInput.isTouch && touchValue === 'hold' && touchDelay) {\n      delay = touchDelay;\n    }\n\n    if (delay) {\n      showTimeout = setTimeout(function () {\n        instance.show();\n      }, delay);\n    } else {\n      instance.show();\n    }\n  }\n\n  function scheduleHide(event) {\n    instance.clearDelayTimeouts();\n    invokeHook('onUntrigger', [instance, event]);\n\n    if (!instance.state.isVisible) {\n      removeDocumentPress();\n      return;\n    } // For interactive tippies, scheduleHide is added to a document.body handler\n    // from onMouseLeave so must intercept scheduled hides from mousemove/leave\n    // events when trigger contains mouseenter and click, and the tip is\n    // currently shown as a result of a click.\n\n\n    if (instance.props.trigger.indexOf('mouseenter') >= 0 && instance.props.trigger.indexOf('click') >= 0 && ['mouseleave', 'mousemove'].indexOf(event.type) >= 0 && isVisibleFromClick) {\n      return;\n    }\n\n    var delay = getDelay(false);\n\n    if (delay) {\n      hideTimeout = setTimeout(function () {\n        if (instance.state.isVisible) {\n          instance.hide();\n        }\n      }, delay);\n    } else {\n      // Fixes a `transitionend` problem when it fires 1 frame too\n      // late sometimes, we don't want hide() to be called.\n      scheduleHideAnimationFrame = requestAnimationFrame(function () {\n        instance.hide();\n      });\n    }\n  } // ===========================================================================\n  // 🔑 Public methods\n  // ===========================================================================\n\n\n  function enable() {\n    instance.state.isEnabled = true;\n  }\n\n  function disable() {\n    // Disabling the instance should also hide it\n    // https://github.com/atomiks/tippy.js-react/issues/106\n    instance.hide();\n    instance.state.isEnabled = false;\n  }\n\n  function clearDelayTimeouts() {\n    clearTimeout(showTimeout);\n    clearTimeout(hideTimeout);\n    cancelAnimationFrame(scheduleHideAnimationFrame);\n  }\n\n  function setProps(partialProps) {\n    /* istanbul ignore else */\n    if (true) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('setProps'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    invokeHook('onBeforeUpdate', [instance, partialProps]);\n    removeListeners();\n    var prevProps = instance.props;\n    var nextProps = evaluateProps(reference, Object.assign({}, prevProps, removeUndefinedProps(partialProps), {\n      ignoreAttributes: true\n    }));\n    instance.props = nextProps;\n    addListeners();\n\n    if (prevProps.interactiveDebounce !== nextProps.interactiveDebounce) {\n      cleanupInteractiveMouseListeners();\n      debouncedOnMouseMove = debounce(onMouseMove, nextProps.interactiveDebounce);\n    } // Ensure stale aria-expanded attributes are removed\n\n\n    if (prevProps.triggerTarget && !nextProps.triggerTarget) {\n      normalizeToArray(prevProps.triggerTarget).forEach(function (node) {\n        node.removeAttribute('aria-expanded');\n      });\n    } else if (nextProps.triggerTarget) {\n      reference.removeAttribute('aria-expanded');\n    }\n\n    handleAriaExpandedAttribute();\n    handleStyles();\n\n    if (onUpdate) {\n      onUpdate(prevProps, nextProps);\n    }\n\n    if (instance.popperInstance) {\n      createPopperInstance(); // Fixes an issue with nested tippies if they are all getting re-rendered,\n      // and the nested ones get re-rendered first.\n      // https://github.com/atomiks/tippyjs-react/issues/177\n      // TODO: find a cleaner / more efficient solution(!)\n\n      getNestedPopperTree().forEach(function (nestedPopper) {\n        // React (and other UI libs likely) requires a rAF wrapper as it flushes\n        // its work in one\n        requestAnimationFrame(nestedPopper._tippy.popperInstance.forceUpdate);\n      });\n    }\n\n    invokeHook('onAfterUpdate', [instance, partialProps]);\n  }\n\n  function setContent(content) {\n    instance.setProps({\n      content: content\n    });\n  }\n\n  function show() {\n    /* istanbul ignore else */\n    if (true) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('show'));\n    } // Early bail-out\n\n\n    var isAlreadyVisible = instance.state.isVisible;\n    var isDestroyed = instance.state.isDestroyed;\n    var isDisabled = !instance.state.isEnabled;\n    var isTouchAndTouchDisabled = currentInput.isTouch && !instance.props.touch;\n    var duration = getValueAtIndexOrReturn(instance.props.duration, 0, defaultProps.duration);\n\n    if (isAlreadyVisible || isDestroyed || isDisabled || isTouchAndTouchDisabled) {\n      return;\n    } // Normalize `disabled` behavior across browsers.\n    // Firefox allows events on disabled elements, but Chrome doesn't.\n    // Using a wrapper element (i.e. <span>) is recommended.\n\n\n    if (getCurrentTarget().hasAttribute('disabled')) {\n      return;\n    }\n\n    invokeHook('onShow', [instance], false);\n\n    if (instance.props.onShow(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = true;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'visible';\n    }\n\n    handleStyles();\n    addDocumentPress();\n\n    if (!instance.state.isMounted) {\n      popper.style.transition = 'none';\n    } // If flipping to the opposite side after hiding at least once, the\n    // animation will use the wrong placement without resetting the duration\n\n\n    if (getIsDefaultRenderFn()) {\n      var _getDefaultTemplateCh2 = getDefaultTemplateChildren(),\n          box = _getDefaultTemplateCh2.box,\n          content = _getDefaultTemplateCh2.content;\n\n      setTransitionDuration([box, content], 0);\n    }\n\n    onFirstUpdate = function onFirstUpdate() {\n      var _instance$popperInsta2;\n\n      if (!instance.state.isVisible || ignoreOnFirstUpdate) {\n        return;\n      }\n\n      ignoreOnFirstUpdate = true; // reflow\n\n      void popper.offsetHeight;\n      popper.style.transition = instance.props.moveTransition;\n\n      if (getIsDefaultRenderFn() && instance.props.animation) {\n        var _getDefaultTemplateCh3 = getDefaultTemplateChildren(),\n            _box = _getDefaultTemplateCh3.box,\n            _content = _getDefaultTemplateCh3.content;\n\n        setTransitionDuration([_box, _content], duration);\n        setVisibilityState([_box, _content], 'visible');\n      }\n\n      handleAriaContentAttribute();\n      handleAriaExpandedAttribute();\n      pushIfUnique(mountedInstances, instance); // certain modifiers (e.g. `maxSize`) require a second update after the\n      // popper has been positioned for the first time\n\n      (_instance$popperInsta2 = instance.popperInstance) == null ? void 0 : _instance$popperInsta2.forceUpdate();\n      invokeHook('onMount', [instance]);\n\n      if (instance.props.animation && getIsDefaultRenderFn()) {\n        onTransitionedIn(duration, function () {\n          instance.state.isShown = true;\n          invokeHook('onShown', [instance]);\n        });\n      }\n    };\n\n    mount();\n  }\n\n  function hide() {\n    /* istanbul ignore else */\n    if (true) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('hide'));\n    } // Early bail-out\n\n\n    var isAlreadyHidden = !instance.state.isVisible;\n    var isDestroyed = instance.state.isDestroyed;\n    var isDisabled = !instance.state.isEnabled;\n    var duration = getValueAtIndexOrReturn(instance.props.duration, 1, defaultProps.duration);\n\n    if (isAlreadyHidden || isDestroyed || isDisabled) {\n      return;\n    }\n\n    invokeHook('onHide', [instance], false);\n\n    if (instance.props.onHide(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = false;\n    instance.state.isShown = false;\n    ignoreOnFirstUpdate = false;\n    isVisibleFromClick = false;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'hidden';\n    }\n\n    cleanupInteractiveMouseListeners();\n    removeDocumentPress();\n    handleStyles(true);\n\n    if (getIsDefaultRenderFn()) {\n      var _getDefaultTemplateCh4 = getDefaultTemplateChildren(),\n          box = _getDefaultTemplateCh4.box,\n          content = _getDefaultTemplateCh4.content;\n\n      if (instance.props.animation) {\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'hidden');\n      }\n    }\n\n    handleAriaContentAttribute();\n    handleAriaExpandedAttribute();\n\n    if (instance.props.animation) {\n      if (getIsDefaultRenderFn()) {\n        onTransitionedOut(duration, instance.unmount);\n      }\n    } else {\n      instance.unmount();\n    }\n  }\n\n  function hideWithInteractivity(event) {\n    /* istanbul ignore else */\n    if (true) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('hideWithInteractivity'));\n    }\n\n    getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    pushIfUnique(mouseMoveListeners, debouncedOnMouseMove);\n    debouncedOnMouseMove(event);\n  }\n\n  function unmount() {\n    /* istanbul ignore else */\n    if (true) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('unmount'));\n    }\n\n    if (instance.state.isVisible) {\n      instance.hide();\n    }\n\n    if (!instance.state.isMounted) {\n      return;\n    }\n\n    destroyPopperInstance(); // If a popper is not interactive, it will be appended outside the popper\n    // tree by default. This seems mainly for interactive tippies, but we should\n    // find a workaround if possible\n\n    getNestedPopperTree().forEach(function (nestedPopper) {\n      nestedPopper._tippy.unmount();\n    });\n\n    if (popper.parentNode) {\n      popper.parentNode.removeChild(popper);\n    }\n\n    mountedInstances = mountedInstances.filter(function (i) {\n      return i !== instance;\n    });\n    instance.state.isMounted = false;\n    invokeHook('onHidden', [instance]);\n  }\n\n  function destroy() {\n    /* istanbul ignore else */\n    if (true) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('destroy'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    instance.clearDelayTimeouts();\n    instance.unmount();\n    removeListeners();\n    delete reference._tippy;\n    instance.state.isDestroyed = true;\n    invokeHook('onDestroy', [instance]);\n  }\n}\n\nfunction tippy(targets, optionalProps) {\n  if (optionalProps === void 0) {\n    optionalProps = {};\n  }\n\n  var plugins = defaultProps.plugins.concat(optionalProps.plugins || []);\n  /* istanbul ignore else */\n\n  if (true) {\n    validateTargets(targets);\n    validateProps(optionalProps, plugins);\n  }\n\n  bindGlobalEventListeners();\n  var passedProps = Object.assign({}, optionalProps, {\n    plugins: plugins\n  });\n  var elements = getArrayOfElements(targets);\n  /* istanbul ignore else */\n\n  if (true) {\n    var isSingleContentElement = isElement(passedProps.content);\n    var isMoreThanOneReferenceElement = elements.length > 1;\n    warnWhen(isSingleContentElement && isMoreThanOneReferenceElement, ['tippy() was passed an Element as the `content` prop, but more than', 'one tippy instance was created by this invocation. This means the', 'content element will only be appended to the last tippy instance.', '\\n\\n', 'Instead, pass the .innerHTML of the element, or use a function that', 'returns a cloned version of the element instead.', '\\n\\n', '1) content: element.innerHTML\\n', '2) content: () => element.cloneNode(true)'].join(' '));\n  }\n\n  var instances = elements.reduce(function (acc, reference) {\n    var instance = reference && createTippy(reference, passedProps);\n\n    if (instance) {\n      acc.push(instance);\n    }\n\n    return acc;\n  }, []);\n  return isElement(targets) ? instances[0] : instances;\n}\n\ntippy.defaultProps = defaultProps;\ntippy.setDefaultProps = setDefaultProps;\ntippy.currentInput = currentInput;\nvar hideAll = function hideAll(_temp) {\n  var _ref = _temp === void 0 ? {} : _temp,\n      excludedReferenceOrInstance = _ref.exclude,\n      duration = _ref.duration;\n\n  mountedInstances.forEach(function (instance) {\n    var isExcluded = false;\n\n    if (excludedReferenceOrInstance) {\n      isExcluded = isReferenceElement(excludedReferenceOrInstance) ? instance.reference === excludedReferenceOrInstance : instance.popper === excludedReferenceOrInstance.popper;\n    }\n\n    if (!isExcluded) {\n      var originalDuration = instance.props.duration;\n      instance.setProps({\n        duration: duration\n      });\n      instance.hide();\n\n      if (!instance.state.isDestroyed) {\n        instance.setProps({\n          duration: originalDuration\n        });\n      }\n    }\n  });\n};\n\n// every time the popper is destroyed (i.e. a new target), removing the styles\n// and causing transitions to break for singletons when the console is open, but\n// most notably for non-transform styles being used, `gpuAcceleration: false`.\n\nvar applyStylesModifier = Object.assign({}, _popperjs_core__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n  effect: function effect(_ref) {\n    var state = _ref.state;\n    var initialStyles = {\n      popper: {\n        position: state.options.strategy,\n        left: '0',\n        top: '0',\n        margin: '0'\n      },\n      arrow: {\n        position: 'absolute'\n      },\n      reference: {}\n    };\n    Object.assign(state.elements.popper.style, initialStyles.popper);\n    state.styles = initialStyles;\n\n    if (state.elements.arrow) {\n      Object.assign(state.elements.arrow.style, initialStyles.arrow);\n    } // intentionally return no cleanup function\n    // return () => { ... }\n\n  }\n});\n\nvar createSingleton = function createSingleton(tippyInstances, optionalProps) {\n  var _optionalProps$popper;\n\n  if (optionalProps === void 0) {\n    optionalProps = {};\n  }\n\n  /* istanbul ignore else */\n  if (true) {\n    errorWhen(!Array.isArray(tippyInstances), ['The first argument passed to createSingleton() must be an array of', 'tippy instances. The passed value was', String(tippyInstances)].join(' '));\n  }\n\n  var individualInstances = tippyInstances;\n  var references = [];\n  var triggerTargets = [];\n  var currentTarget;\n  var overrides = optionalProps.overrides;\n  var interceptSetPropsCleanups = [];\n  var shownOnCreate = false;\n\n  function setTriggerTargets() {\n    triggerTargets = individualInstances.map(function (instance) {\n      return normalizeToArray(instance.props.triggerTarget || instance.reference);\n    }).reduce(function (acc, item) {\n      return acc.concat(item);\n    }, []);\n  }\n\n  function setReferences() {\n    references = individualInstances.map(function (instance) {\n      return instance.reference;\n    });\n  }\n\n  function enableInstances(isEnabled) {\n    individualInstances.forEach(function (instance) {\n      if (isEnabled) {\n        instance.enable();\n      } else {\n        instance.disable();\n      }\n    });\n  }\n\n  function interceptSetProps(singleton) {\n    return individualInstances.map(function (instance) {\n      var originalSetProps = instance.setProps;\n\n      instance.setProps = function (props) {\n        originalSetProps(props);\n\n        if (instance.reference === currentTarget) {\n          singleton.setProps(props);\n        }\n      };\n\n      return function () {\n        instance.setProps = originalSetProps;\n      };\n    });\n  } // have to pass singleton, as it maybe undefined on first call\n\n\n  function prepareInstance(singleton, target) {\n    var index = triggerTargets.indexOf(target); // bail-out\n\n    if (target === currentTarget) {\n      return;\n    }\n\n    currentTarget = target;\n    var overrideProps = (overrides || []).concat('content').reduce(function (acc, prop) {\n      acc[prop] = individualInstances[index].props[prop];\n      return acc;\n    }, {});\n    singleton.setProps(Object.assign({}, overrideProps, {\n      getReferenceClientRect: typeof overrideProps.getReferenceClientRect === 'function' ? overrideProps.getReferenceClientRect : function () {\n        var _references$index;\n\n        return (_references$index = references[index]) == null ? void 0 : _references$index.getBoundingClientRect();\n      }\n    }));\n  }\n\n  enableInstances(false);\n  setReferences();\n  setTriggerTargets();\n  var plugin = {\n    fn: function fn() {\n      return {\n        onDestroy: function onDestroy() {\n          enableInstances(true);\n        },\n        onHidden: function onHidden() {\n          currentTarget = null;\n        },\n        onClickOutside: function onClickOutside(instance) {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            currentTarget = null;\n          }\n        },\n        onShow: function onShow(instance) {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            prepareInstance(instance, references[0]);\n          }\n        },\n        onTrigger: function onTrigger(instance, event) {\n          prepareInstance(instance, event.currentTarget);\n        }\n      };\n    }\n  };\n  var singleton = tippy(div(), Object.assign({}, removeProperties(optionalProps, ['overrides']), {\n    plugins: [plugin].concat(optionalProps.plugins || []),\n    triggerTarget: triggerTargets,\n    popperOptions: Object.assign({}, optionalProps.popperOptions, {\n      modifiers: [].concat(((_optionalProps$popper = optionalProps.popperOptions) == null ? void 0 : _optionalProps$popper.modifiers) || [], [applyStylesModifier])\n    })\n  }));\n  var originalShow = singleton.show;\n\n  singleton.show = function (target) {\n    originalShow(); // first time, showOnCreate or programmatic call with no params\n    // default to showing first instance\n\n    if (!currentTarget && target == null) {\n      return prepareInstance(singleton, references[0]);\n    } // triggered from event (do nothing as prepareInstance already called by onTrigger)\n    // programmatic call with no params when already visible (do nothing again)\n\n\n    if (currentTarget && target == null) {\n      return;\n    } // target is index of instance\n\n\n    if (typeof target === 'number') {\n      return references[target] && prepareInstance(singleton, references[target]);\n    } // target is a child tippy instance\n\n\n    if (individualInstances.indexOf(target) >= 0) {\n      var ref = target.reference;\n      return prepareInstance(singleton, ref);\n    } // target is a ReferenceElement\n\n\n    if (references.indexOf(target) >= 0) {\n      return prepareInstance(singleton, target);\n    }\n  };\n\n  singleton.showNext = function () {\n    var first = references[0];\n\n    if (!currentTarget) {\n      return singleton.show(0);\n    }\n\n    var index = references.indexOf(currentTarget);\n    singleton.show(references[index + 1] || first);\n  };\n\n  singleton.showPrevious = function () {\n    var last = references[references.length - 1];\n\n    if (!currentTarget) {\n      return singleton.show(last);\n    }\n\n    var index = references.indexOf(currentTarget);\n    var target = references[index - 1] || last;\n    singleton.show(target);\n  };\n\n  var originalSetProps = singleton.setProps;\n\n  singleton.setProps = function (props) {\n    overrides = props.overrides || overrides;\n    originalSetProps(props);\n  };\n\n  singleton.setInstances = function (nextInstances) {\n    enableInstances(true);\n    interceptSetPropsCleanups.forEach(function (fn) {\n      return fn();\n    });\n    individualInstances = nextInstances;\n    enableInstances(false);\n    setReferences();\n    setTriggerTargets();\n    interceptSetPropsCleanups = interceptSetProps(singleton);\n    singleton.setProps({\n      triggerTarget: triggerTargets\n    });\n  };\n\n  interceptSetPropsCleanups = interceptSetProps(singleton);\n  return singleton;\n};\n\nvar BUBBLING_EVENTS_MAP = {\n  mouseover: 'mouseenter',\n  focusin: 'focus',\n  click: 'click'\n};\n/**\n * Creates a delegate instance that controls the creation of tippy instances\n * for child elements (`target` CSS selector).\n */\n\nfunction delegate(targets, props) {\n  /* istanbul ignore else */\n  if (true) {\n    errorWhen(!(props && props.target), ['You must specity a `target` prop indicating a CSS selector string matching', 'the target elements that should receive a tippy.'].join(' '));\n  }\n\n  var listeners = [];\n  var childTippyInstances = [];\n  var disabled = false;\n  var target = props.target;\n  var nativeProps = removeProperties(props, ['target']);\n  var parentProps = Object.assign({}, nativeProps, {\n    trigger: 'manual',\n    touch: false\n  });\n  var childProps = Object.assign({\n    touch: defaultProps.touch\n  }, nativeProps, {\n    showOnCreate: true\n  });\n  var returnValue = tippy(targets, parentProps);\n  var normalizedReturnValue = normalizeToArray(returnValue);\n\n  function onTrigger(event) {\n    if (!event.target || disabled) {\n      return;\n    }\n\n    var targetNode = event.target.closest(target);\n\n    if (!targetNode) {\n      return;\n    } // Get relevant trigger with fallbacks:\n    // 1. Check `data-tippy-trigger` attribute on target node\n    // 2. Fallback to `trigger` passed to `delegate()`\n    // 3. Fallback to `defaultProps.trigger`\n\n\n    var trigger = targetNode.getAttribute('data-tippy-trigger') || props.trigger || defaultProps.trigger; // @ts-ignore\n\n    if (targetNode._tippy) {\n      return;\n    }\n\n    if (event.type === 'touchstart' && typeof childProps.touch === 'boolean') {\n      return;\n    }\n\n    if (event.type !== 'touchstart' && trigger.indexOf(BUBBLING_EVENTS_MAP[event.type]) < 0) {\n      return;\n    }\n\n    var instance = tippy(targetNode, childProps);\n\n    if (instance) {\n      childTippyInstances = childTippyInstances.concat(instance);\n    }\n  }\n\n  function on(node, eventType, handler, options) {\n    if (options === void 0) {\n      options = false;\n    }\n\n    node.addEventListener(eventType, handler, options);\n    listeners.push({\n      node: node,\n      eventType: eventType,\n      handler: handler,\n      options: options\n    });\n  }\n\n  function addEventListeners(instance) {\n    var reference = instance.reference;\n    on(reference, 'touchstart', onTrigger, TOUCH_OPTIONS);\n    on(reference, 'mouseover', onTrigger);\n    on(reference, 'focusin', onTrigger);\n    on(reference, 'click', onTrigger);\n  }\n\n  function removeEventListeners() {\n    listeners.forEach(function (_ref) {\n      var node = _ref.node,\n          eventType = _ref.eventType,\n          handler = _ref.handler,\n          options = _ref.options;\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function applyMutations(instance) {\n    var originalDestroy = instance.destroy;\n    var originalEnable = instance.enable;\n    var originalDisable = instance.disable;\n\n    instance.destroy = function (shouldDestroyChildInstances) {\n      if (shouldDestroyChildInstances === void 0) {\n        shouldDestroyChildInstances = true;\n      }\n\n      if (shouldDestroyChildInstances) {\n        childTippyInstances.forEach(function (instance) {\n          instance.destroy();\n        });\n      }\n\n      childTippyInstances = [];\n      removeEventListeners();\n      originalDestroy();\n    };\n\n    instance.enable = function () {\n      originalEnable();\n      childTippyInstances.forEach(function (instance) {\n        return instance.enable();\n      });\n      disabled = false;\n    };\n\n    instance.disable = function () {\n      originalDisable();\n      childTippyInstances.forEach(function (instance) {\n        return instance.disable();\n      });\n      disabled = true;\n    };\n\n    addEventListeners(instance);\n  }\n\n  normalizedReturnValue.forEach(applyMutations);\n  return returnValue;\n}\n\nvar animateFill = {\n  name: 'animateFill',\n  defaultValue: false,\n  fn: function fn(instance) {\n    var _instance$props$rende;\n\n    // @ts-ignore\n    if (!((_instance$props$rende = instance.props.render) != null && _instance$props$rende.$$tippy)) {\n      if (true) {\n        errorWhen(instance.props.animateFill, 'The `animateFill` plugin requires the default render function.');\n      }\n\n      return {};\n    }\n\n    var _getChildren = getChildren(instance.popper),\n        box = _getChildren.box,\n        content = _getChildren.content;\n\n    var backdrop = instance.props.animateFill ? createBackdropElement() : null;\n    return {\n      onCreate: function onCreate() {\n        if (backdrop) {\n          box.insertBefore(backdrop, box.firstElementChild);\n          box.setAttribute('data-animatefill', '');\n          box.style.overflow = 'hidden';\n          instance.setProps({\n            arrow: false,\n            animation: 'shift-away'\n          });\n        }\n      },\n      onMount: function onMount() {\n        if (backdrop) {\n          var transitionDuration = box.style.transitionDuration;\n          var duration = Number(transitionDuration.replace('ms', '')); // The content should fade in after the backdrop has mostly filled the\n          // tooltip element. `clip-path` is the other alternative but is not\n          // well-supported and is buggy on some devices.\n\n          content.style.transitionDelay = Math.round(duration / 10) + \"ms\";\n          backdrop.style.transitionDuration = transitionDuration;\n          setVisibilityState([backdrop], 'visible');\n        }\n      },\n      onShow: function onShow() {\n        if (backdrop) {\n          backdrop.style.transitionDuration = '0ms';\n        }\n      },\n      onHide: function onHide() {\n        if (backdrop) {\n          setVisibilityState([backdrop], 'hidden');\n        }\n      }\n    };\n  }\n};\n\nfunction createBackdropElement() {\n  var backdrop = div();\n  backdrop.className = BACKDROP_CLASS;\n  setVisibilityState([backdrop], 'hidden');\n  return backdrop;\n}\n\nvar mouseCoords = {\n  clientX: 0,\n  clientY: 0\n};\nvar activeInstances = [];\n\nfunction storeMouseCoords(_ref) {\n  var clientX = _ref.clientX,\n      clientY = _ref.clientY;\n  mouseCoords = {\n    clientX: clientX,\n    clientY: clientY\n  };\n}\n\nfunction addMouseCoordsListener(doc) {\n  doc.addEventListener('mousemove', storeMouseCoords);\n}\n\nfunction removeMouseCoordsListener(doc) {\n  doc.removeEventListener('mousemove', storeMouseCoords);\n}\n\nvar followCursor = {\n  name: 'followCursor',\n  defaultValue: false,\n  fn: function fn(instance) {\n    var reference = instance.reference;\n    var doc = getOwnerDocument(instance.props.triggerTarget || reference);\n    var isInternalUpdate = false;\n    var wasFocusEvent = false;\n    var isUnmounted = true;\n    var prevProps = instance.props;\n\n    function getIsInitialBehavior() {\n      return instance.props.followCursor === 'initial' && instance.state.isVisible;\n    }\n\n    function addListener() {\n      doc.addEventListener('mousemove', onMouseMove);\n    }\n\n    function removeListener() {\n      doc.removeEventListener('mousemove', onMouseMove);\n    }\n\n    function unsetGetReferenceClientRect() {\n      isInternalUpdate = true;\n      instance.setProps({\n        getReferenceClientRect: null\n      });\n      isInternalUpdate = false;\n    }\n\n    function onMouseMove(event) {\n      // If the instance is interactive, avoid updating the position unless it's\n      // over the reference element\n      var isCursorOverReference = event.target ? reference.contains(event.target) : true;\n      var followCursor = instance.props.followCursor;\n      var clientX = event.clientX,\n          clientY = event.clientY;\n      var rect = reference.getBoundingClientRect();\n      var relativeX = clientX - rect.left;\n      var relativeY = clientY - rect.top;\n\n      if (isCursorOverReference || !instance.props.interactive) {\n        instance.setProps({\n          // @ts-ignore - unneeded DOMRect properties\n          getReferenceClientRect: function getReferenceClientRect() {\n            var rect = reference.getBoundingClientRect();\n            var x = clientX;\n            var y = clientY;\n\n            if (followCursor === 'initial') {\n              x = rect.left + relativeX;\n              y = rect.top + relativeY;\n            }\n\n            var top = followCursor === 'horizontal' ? rect.top : y;\n            var right = followCursor === 'vertical' ? rect.right : x;\n            var bottom = followCursor === 'horizontal' ? rect.bottom : y;\n            var left = followCursor === 'vertical' ? rect.left : x;\n            return {\n              width: right - left,\n              height: bottom - top,\n              top: top,\n              right: right,\n              bottom: bottom,\n              left: left\n            };\n          }\n        });\n      }\n    }\n\n    function create() {\n      if (instance.props.followCursor) {\n        activeInstances.push({\n          instance: instance,\n          doc: doc\n        });\n        addMouseCoordsListener(doc);\n      }\n    }\n\n    function destroy() {\n      activeInstances = activeInstances.filter(function (data) {\n        return data.instance !== instance;\n      });\n\n      if (activeInstances.filter(function (data) {\n        return data.doc === doc;\n      }).length === 0) {\n        removeMouseCoordsListener(doc);\n      }\n    }\n\n    return {\n      onCreate: create,\n      onDestroy: destroy,\n      onBeforeUpdate: function onBeforeUpdate() {\n        prevProps = instance.props;\n      },\n      onAfterUpdate: function onAfterUpdate(_, _ref2) {\n        var followCursor = _ref2.followCursor;\n\n        if (isInternalUpdate) {\n          return;\n        }\n\n        if (followCursor !== undefined && prevProps.followCursor !== followCursor) {\n          destroy();\n\n          if (followCursor) {\n            create();\n\n            if (instance.state.isMounted && !wasFocusEvent && !getIsInitialBehavior()) {\n              addListener();\n            }\n          } else {\n            removeListener();\n            unsetGetReferenceClientRect();\n          }\n        }\n      },\n      onMount: function onMount() {\n        if (instance.props.followCursor && !wasFocusEvent) {\n          if (isUnmounted) {\n            onMouseMove(mouseCoords);\n            isUnmounted = false;\n          }\n\n          if (!getIsInitialBehavior()) {\n            addListener();\n          }\n        }\n      },\n      onTrigger: function onTrigger(_, event) {\n        if (isMouseEvent(event)) {\n          mouseCoords = {\n            clientX: event.clientX,\n            clientY: event.clientY\n          };\n        }\n\n        wasFocusEvent = event.type === 'focus';\n      },\n      onHidden: function onHidden() {\n        if (instance.props.followCursor) {\n          unsetGetReferenceClientRect();\n          removeListener();\n          isUnmounted = true;\n        }\n      }\n    };\n  }\n};\n\nfunction getProps(props, modifier) {\n  var _props$popperOptions;\n\n  return {\n    popperOptions: Object.assign({}, props.popperOptions, {\n      modifiers: [].concat((((_props$popperOptions = props.popperOptions) == null ? void 0 : _props$popperOptions.modifiers) || []).filter(function (_ref) {\n        var name = _ref.name;\n        return name !== modifier.name;\n      }), [modifier])\n    })\n  };\n}\n\nvar inlinePositioning = {\n  name: 'inlinePositioning',\n  defaultValue: false,\n  fn: function fn(instance) {\n    var reference = instance.reference;\n\n    function isEnabled() {\n      return !!instance.props.inlinePositioning;\n    }\n\n    var placement;\n    var cursorRectIndex = -1;\n    var isInternalUpdate = false;\n    var triedPlacements = [];\n    var modifier = {\n      name: 'tippyInlinePositioning',\n      enabled: true,\n      phase: 'afterWrite',\n      fn: function fn(_ref2) {\n        var state = _ref2.state;\n\n        if (isEnabled()) {\n          if (triedPlacements.indexOf(state.placement) !== -1) {\n            triedPlacements = [];\n          }\n\n          if (placement !== state.placement && triedPlacements.indexOf(state.placement) === -1) {\n            triedPlacements.push(state.placement);\n            instance.setProps({\n              // @ts-ignore - unneeded DOMRect properties\n              getReferenceClientRect: function getReferenceClientRect() {\n                return _getReferenceClientRect(state.placement);\n              }\n            });\n          }\n\n          placement = state.placement;\n        }\n      }\n    };\n\n    function _getReferenceClientRect(placement) {\n      return getInlineBoundingClientRect(getBasePlacement(placement), reference.getBoundingClientRect(), arrayFrom(reference.getClientRects()), cursorRectIndex);\n    }\n\n    function setInternalProps(partialProps) {\n      isInternalUpdate = true;\n      instance.setProps(partialProps);\n      isInternalUpdate = false;\n    }\n\n    function addModifier() {\n      if (!isInternalUpdate) {\n        setInternalProps(getProps(instance.props, modifier));\n      }\n    }\n\n    return {\n      onCreate: addModifier,\n      onAfterUpdate: addModifier,\n      onTrigger: function onTrigger(_, event) {\n        if (isMouseEvent(event)) {\n          var rects = arrayFrom(instance.reference.getClientRects());\n          var cursorRect = rects.find(function (rect) {\n            return rect.left - 2 <= event.clientX && rect.right + 2 >= event.clientX && rect.top - 2 <= event.clientY && rect.bottom + 2 >= event.clientY;\n          });\n          var index = rects.indexOf(cursorRect);\n          cursorRectIndex = index > -1 ? index : cursorRectIndex;\n        }\n      },\n      onHidden: function onHidden() {\n        cursorRectIndex = -1;\n      }\n    };\n  }\n};\nfunction getInlineBoundingClientRect(currentBasePlacement, boundingRect, clientRects, cursorRectIndex) {\n  // Not an inline element, or placement is not yet known\n  if (clientRects.length < 2 || currentBasePlacement === null) {\n    return boundingRect;\n  } // There are two rects and they are disjoined\n\n\n  if (clientRects.length === 2 && cursorRectIndex >= 0 && clientRects[0].left > clientRects[1].right) {\n    return clientRects[cursorRectIndex] || boundingRect;\n  }\n\n  switch (currentBasePlacement) {\n    case 'top':\n    case 'bottom':\n      {\n        var firstRect = clientRects[0];\n        var lastRect = clientRects[clientRects.length - 1];\n        var isTop = currentBasePlacement === 'top';\n        var top = firstRect.top;\n        var bottom = lastRect.bottom;\n        var left = isTop ? firstRect.left : lastRect.left;\n        var right = isTop ? firstRect.right : lastRect.right;\n        var width = right - left;\n        var height = bottom - top;\n        return {\n          top: top,\n          bottom: bottom,\n          left: left,\n          right: right,\n          width: width,\n          height: height\n        };\n      }\n\n    case 'left':\n    case 'right':\n      {\n        var minLeft = Math.min.apply(Math, clientRects.map(function (rects) {\n          return rects.left;\n        }));\n        var maxRight = Math.max.apply(Math, clientRects.map(function (rects) {\n          return rects.right;\n        }));\n        var measureRects = clientRects.filter(function (rect) {\n          return currentBasePlacement === 'left' ? rect.left === minLeft : rect.right === maxRight;\n        });\n        var _top = measureRects[0].top;\n        var _bottom = measureRects[measureRects.length - 1].bottom;\n        var _left = minLeft;\n        var _right = maxRight;\n\n        var _width = _right - _left;\n\n        var _height = _bottom - _top;\n\n        return {\n          top: _top,\n          bottom: _bottom,\n          left: _left,\n          right: _right,\n          width: _width,\n          height: _height\n        };\n      }\n\n    default:\n      {\n        return boundingRect;\n      }\n  }\n}\n\nvar sticky = {\n  name: 'sticky',\n  defaultValue: false,\n  fn: function fn(instance) {\n    var reference = instance.reference,\n        popper = instance.popper;\n\n    function getReference() {\n      return instance.popperInstance ? instance.popperInstance.state.elements.reference : reference;\n    }\n\n    function shouldCheck(value) {\n      return instance.props.sticky === true || instance.props.sticky === value;\n    }\n\n    var prevRefRect = null;\n    var prevPopRect = null;\n\n    function updatePosition() {\n      var currentRefRect = shouldCheck('reference') ? getReference().getBoundingClientRect() : null;\n      var currentPopRect = shouldCheck('popper') ? popper.getBoundingClientRect() : null;\n\n      if (currentRefRect && areRectsDifferent(prevRefRect, currentRefRect) || currentPopRect && areRectsDifferent(prevPopRect, currentPopRect)) {\n        if (instance.popperInstance) {\n          instance.popperInstance.update();\n        }\n      }\n\n      prevRefRect = currentRefRect;\n      prevPopRect = currentPopRect;\n\n      if (instance.state.isMounted) {\n        requestAnimationFrame(updatePosition);\n      }\n    }\n\n    return {\n      onMount: function onMount() {\n        if (instance.props.sticky) {\n          updatePosition();\n        }\n      }\n    };\n  }\n};\n\nfunction areRectsDifferent(rectA, rectB) {\n  if (rectA && rectB) {\n    return rectA.top !== rectB.top || rectA.right !== rectB.right || rectA.bottom !== rectB.bottom || rectA.left !== rectB.left;\n  }\n\n  return true;\n}\n\ntippy.setDefaultProps({\n  render: render\n});\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (tippy);\n\n//# sourceMappingURL=tippy.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/tippy.js@6.3.7/node_modules/tippy.js/dist/tippy.esm.js\n");

/***/ })

};
;