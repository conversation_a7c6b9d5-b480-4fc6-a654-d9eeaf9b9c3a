"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-to-hast";
exports.ids = ["vendor-chunks/mdast-util-to-hast"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/footer.js":
/*!*******************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/footer.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultFootnoteBackContent: () => (/* binding */ defaultFootnoteBackContent),\n/* harmony export */   defaultFootnoteBackLabel: () => (/* binding */ defaultFootnoteBackLabel),\n/* harmony export */   footer: () => (/* binding */ footer)\n/* harmony export */ });\n/* harmony import */ var _ungap_structured_clone__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ungap/structured-clone */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/index.js\");\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('./state.js').State} State\n */\n\n/**\n * @callback FootnoteBackContentTemplate\n *   Generate content for the backreference dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent> | ElementContent | string}\n *   Content for the backreference when linking back from definitions to their\n *   reference.\n *\n * @callback FootnoteBackLabelTemplate\n *   Generate a back label dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Back label to use when linking back from definitions to their reference.\n */\n\n\n\n\n/**\n * Generate the default content that GitHub uses on backreferences.\n *\n * @param {number} _\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent>}\n *   Content.\n */\nfunction defaultFootnoteBackContent(_, rereferenceIndex) {\n  /** @type {Array<ElementContent>} */\n  const result = [{type: 'text', value: '↩'}]\n\n  if (rereferenceIndex > 1) {\n    result.push({\n      type: 'element',\n      tagName: 'sup',\n      properties: {},\n      children: [{type: 'text', value: String(rereferenceIndex)}]\n    })\n  }\n\n  return result\n}\n\n/**\n * Generate the default label that GitHub uses on backreferences.\n *\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Label.\n */\nfunction defaultFootnoteBackLabel(referenceIndex, rereferenceIndex) {\n  return (\n    'Back to reference ' +\n    (referenceIndex + 1) +\n    (rereferenceIndex > 1 ? '-' + rereferenceIndex : '')\n  )\n}\n\n/**\n * Generate a hast footer for called footnote definitions.\n *\n * @param {State} state\n *   Info passed around.\n * @returns {Element | undefined}\n *   `section` element or `undefined`.\n */\n// eslint-disable-next-line complexity\nfunction footer(state) {\n  const clobberPrefix =\n    typeof state.options.clobberPrefix === 'string'\n      ? state.options.clobberPrefix\n      : 'user-content-'\n  const footnoteBackContent =\n    state.options.footnoteBackContent || defaultFootnoteBackContent\n  const footnoteBackLabel =\n    state.options.footnoteBackLabel || defaultFootnoteBackLabel\n  const footnoteLabel = state.options.footnoteLabel || 'Footnotes'\n  const footnoteLabelTagName = state.options.footnoteLabelTagName || 'h2'\n  const footnoteLabelProperties = state.options.footnoteLabelProperties || {\n    className: ['sr-only']\n  }\n  /** @type {Array<ElementContent>} */\n  const listItems = []\n  let referenceIndex = -1\n\n  while (++referenceIndex < state.footnoteOrder.length) {\n    const definition = state.footnoteById.get(\n      state.footnoteOrder[referenceIndex]\n    )\n\n    if (!definition) {\n      continue\n    }\n\n    const content = state.all(definition)\n    const id = String(definition.identifier).toUpperCase()\n    const safeId = (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.normalizeUri)(id.toLowerCase())\n    let rereferenceIndex = 0\n    /** @type {Array<ElementContent>} */\n    const backReferences = []\n    const counts = state.footnoteCounts.get(id)\n\n    // eslint-disable-next-line no-unmodified-loop-condition\n    while (counts !== undefined && ++rereferenceIndex <= counts) {\n      if (backReferences.length > 0) {\n        backReferences.push({type: 'text', value: ' '})\n      }\n\n      let children =\n        typeof footnoteBackContent === 'string'\n          ? footnoteBackContent\n          : footnoteBackContent(referenceIndex, rereferenceIndex)\n\n      if (typeof children === 'string') {\n        children = {type: 'text', value: children}\n      }\n\n      backReferences.push({\n        type: 'element',\n        tagName: 'a',\n        properties: {\n          href:\n            '#' +\n            clobberPrefix +\n            'fnref-' +\n            safeId +\n            (rereferenceIndex > 1 ? '-' + rereferenceIndex : ''),\n          dataFootnoteBackref: '',\n          ariaLabel:\n            typeof footnoteBackLabel === 'string'\n              ? footnoteBackLabel\n              : footnoteBackLabel(referenceIndex, rereferenceIndex),\n          className: ['data-footnote-backref']\n        },\n        children: Array.isArray(children) ? children : [children]\n      })\n    }\n\n    const tail = content[content.length - 1]\n\n    if (tail && tail.type === 'element' && tail.tagName === 'p') {\n      const tailTail = tail.children[tail.children.length - 1]\n      if (tailTail && tailTail.type === 'text') {\n        tailTail.value += ' '\n      } else {\n        tail.children.push({type: 'text', value: ' '})\n      }\n\n      tail.children.push(...backReferences)\n    } else {\n      content.push(...backReferences)\n    }\n\n    /** @type {Element} */\n    const listItem = {\n      type: 'element',\n      tagName: 'li',\n      properties: {id: clobberPrefix + 'fn-' + safeId},\n      children: state.wrap(content, true)\n    }\n\n    state.patch(definition, listItem)\n\n    listItems.push(listItem)\n  }\n\n  if (listItems.length === 0) {\n    return\n  }\n\n  return {\n    type: 'element',\n    tagName: 'section',\n    properties: {dataFootnotes: true, className: ['footnotes']},\n    children: [\n      {\n        type: 'element',\n        tagName: footnoteLabelTagName,\n        properties: {\n          ...(0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(footnoteLabelProperties),\n          id: 'footnote-label'\n        },\n        children: [{type: 'text', value: footnoteLabel}]\n      },\n      {type: 'text', value: '\\n'},\n      {\n        type: 'element',\n        tagName: 'ol',\n        properties: {},\n        children: state.wrap(listItems, true)\n      },\n      {type: 'text', value: '\\n'}\n    ]\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/footer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/blockquote.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/blockquote.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blockquote: () => (/* binding */ blockquote)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Blockquote} Blockquote\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `blockquote` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Blockquote} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction blockquote(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'blockquote',\n    properties: {},\n    children: state.wrap(state.all(node), true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9ibG9ja3F1b3RlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsd0JBQXdCO0FBQ3JDLGFBQWEsNEJBQTRCO0FBQ3pDLGFBQWEsNkJBQTZCO0FBQzFDOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsV0FBVyxZQUFZO0FBQ3ZCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQLGFBQWEsU0FBUztBQUN0QjtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvbXV6aS9naXRodWIvZGVlci1mbG93L2FpLWVkaXRvci1zdGFuZGFsb25lL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL2Jsb2NrcXVvdGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5CbG9ja3F1b3RlfSBCbG9ja3F1b3RlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbi8vIE1ha2UgVlMgQ29kZSBzaG93IHJlZmVyZW5jZXMgdG8gdGhlIGFib3ZlIHR5cGVzLlxuJydcblxuLyoqXG4gKiBUdXJuIGFuIG1kYXN0IGBibG9ja3F1b3RlYCBub2RlIGludG8gaGFzdC5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQuXG4gKiBAcGFyYW0ge0Jsb2NrcXVvdGV9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBibG9ja3F1b3RlKHN0YXRlLCBub2RlKSB7XG4gIC8qKiBAdHlwZSB7RWxlbWVudH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge1xuICAgIHR5cGU6ICdlbGVtZW50JyxcbiAgICB0YWdOYW1lOiAnYmxvY2txdW90ZScsXG4gICAgcHJvcGVydGllczoge30sXG4gICAgY2hpbGRyZW46IHN0YXRlLndyYXAoc3RhdGUuYWxsKG5vZGUpLCB0cnVlKVxuICB9XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHN0YXRlLmFwcGx5RGF0YShub2RlLCByZXN1bHQpXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/blockquote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/break.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/break.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hardBreak: () => (/* binding */ hardBreak)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').Break} Break\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `break` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Break} node\n *   mdast node.\n * @returns {Array<Element | Text>}\n *   hast element content.\n */\nfunction hardBreak(state, node) {\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'br', properties: {}, children: []}\n  state.patch(node, result)\n  return [state.applyData(node, result), {type: 'text', value: '\\n'}]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9icmVhay5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLHdCQUF3QjtBQUNyQyxhQUFhLHFCQUFxQjtBQUNsQyxhQUFhLHVCQUF1QjtBQUNwQyxhQUFhLDZCQUE2QjtBQUMxQzs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUCxhQUFhLFNBQVM7QUFDdEIsa0JBQWtCLDhDQUE4QztBQUNoRTtBQUNBLDBDQUEwQywwQkFBMEI7QUFDcEUiLCJzb3VyY2VzIjpbIi9ob21lL211emkvZ2l0aHViL2RlZXItZmxvdy9haS1lZGl0b3Itc3RhbmRhbG9uZS9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9icmVhay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuVGV4dH0gVGV4dFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5CcmVha30gQnJlYWtcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYGJyZWFrYCBub2RlIGludG8gaGFzdC5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQuXG4gKiBAcGFyYW0ge0JyZWFrfSBub2RlXG4gKiAgIG1kYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7QXJyYXk8RWxlbWVudCB8IFRleHQ+fVxuICogICBoYXN0IGVsZW1lbnQgY29udGVudC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGhhcmRCcmVhayhzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge0VsZW1lbnR9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHt0eXBlOiAnZWxlbWVudCcsIHRhZ05hbWU6ICdicicsIHByb3BlcnRpZXM6IHt9LCBjaGlsZHJlbjogW119XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIFtzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KSwge3R5cGU6ICd0ZXh0JywgdmFsdWU6ICdcXG4nfV1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/break.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/code.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/code.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: () => (/* binding */ code)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Code} Code\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `code` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Code} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction code(state, node) {\n  const value = node.value ? node.value + '\\n' : ''\n  /** @type {Properties} */\n  const properties = {}\n\n  if (node.lang) {\n    properties.className = ['language-' + node.lang]\n  }\n\n  // Create `<code>`.\n  /** @type {Element} */\n  let result = {\n    type: 'element',\n    tagName: 'code',\n    properties,\n    children: [{type: 'text', value}]\n  }\n\n  if (node.meta) {\n    result.data = {meta: node.meta}\n  }\n\n  state.patch(node, result)\n  result = state.applyData(node, result)\n\n  // Create `<pre>`.\n  result = {type: 'element', tagName: 'pre', properties: {}, children: [result]}\n  state.patch(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/delete.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/delete.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strikethrough: () => (/* binding */ strikethrough)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Delete} Delete\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `delete` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Delete} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction strikethrough(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'del',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9kZWxldGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSx3QkFBd0I7QUFDckMsYUFBYSx3QkFBd0I7QUFDckMsYUFBYSw2QkFBNkI7QUFDMUM7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1AsYUFBYSxTQUFTO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9tdXppL2dpdGh1Yi9kZWVyLWZsb3cvYWktZWRpdG9yLXN0YW5kYWxvbmUvbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvZGVsZXRlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuRGVsZXRlfSBEZWxldGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYGRlbGV0ZWAgbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtEZWxldGV9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBzdHJpa2V0aHJvdWdoKHN0YXRlLCBub2RlKSB7XG4gIC8qKiBAdHlwZSB7RWxlbWVudH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge1xuICAgIHR5cGU6ICdlbGVtZW50JyxcbiAgICB0YWdOYW1lOiAnZGVsJyxcbiAgICBwcm9wZXJ0aWVzOiB7fSxcbiAgICBjaGlsZHJlbjogc3RhdGUuYWxsKG5vZGUpXG4gIH1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/delete.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/emphasis.js":
/*!******************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/emphasis.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   emphasis: () => (/* binding */ emphasis)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Emphasis} Emphasis\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `emphasis` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Emphasis} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction emphasis(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'em',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9lbXBoYXNpcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLHdCQUF3QjtBQUNyQyxhQUFhLDBCQUEwQjtBQUN2QyxhQUFhLDZCQUE2QjtBQUMxQzs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsVUFBVTtBQUNyQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUCxhQUFhLFNBQVM7QUFDdEI7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL211emkvZ2l0aHViL2RlZXItZmxvdy9haS1lZGl0b3Itc3RhbmRhbG9uZS9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9lbXBoYXNpcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkVtcGhhc2lzfSBFbXBoYXNpc1xuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vc3RhdGUuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG4vLyBNYWtlIFZTIENvZGUgc2hvdyByZWZlcmVuY2VzIHRvIHRoZSBhYm92ZSB0eXBlcy5cbicnXG5cbi8qKlxuICogVHVybiBhbiBtZGFzdCBgZW1waGFzaXNgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7RW1waGFzaXN9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBlbXBoYXNpcyhzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge0VsZW1lbnR9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHtcbiAgICB0eXBlOiAnZWxlbWVudCcsXG4gICAgdGFnTmFtZTogJ2VtJyxcbiAgICBwcm9wZXJ0aWVzOiB7fSxcbiAgICBjaGlsZHJlbjogc3RhdGUuYWxsKG5vZGUpXG4gIH1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/emphasis.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js":
/*!****************************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   footnoteReference: () => (/* binding */ footnoteReference)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').FootnoteReference} FootnoteReference\n * @typedef {import('../state.js').State} State\n */\n\n\n\n/**\n * Turn an mdast `footnoteReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {FootnoteReference} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction footnoteReference(state, node) {\n  const clobberPrefix =\n    typeof state.options.clobberPrefix === 'string'\n      ? state.options.clobberPrefix\n      : 'user-content-'\n  const id = String(node.identifier).toUpperCase()\n  const safeId = (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.normalizeUri)(id.toLowerCase())\n  const index = state.footnoteOrder.indexOf(id)\n  /** @type {number} */\n  let counter\n\n  let reuseCounter = state.footnoteCounts.get(id)\n\n  if (reuseCounter === undefined) {\n    reuseCounter = 0\n    state.footnoteOrder.push(id)\n    counter = state.footnoteOrder.length\n  } else {\n    counter = index + 1\n  }\n\n  reuseCounter += 1\n  state.footnoteCounts.set(id, reuseCounter)\n\n  /** @type {Element} */\n  const link = {\n    type: 'element',\n    tagName: 'a',\n    properties: {\n      href: '#' + clobberPrefix + 'fn-' + safeId,\n      id:\n        clobberPrefix +\n        'fnref-' +\n        safeId +\n        (reuseCounter > 1 ? '-' + reuseCounter : ''),\n      dataFootnoteRef: true,\n      ariaDescribedBy: ['footnote-label']\n    },\n    children: [{type: 'text', value: String(counter)}]\n  }\n  state.patch(node, link)\n\n  /** @type {Element} */\n  const sup = {\n    type: 'element',\n    tagName: 'sup',\n    properties: {},\n    children: [link]\n  }\n  state.patch(node, sup)\n  return state.applyData(node, sup)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/heading.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/heading.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   heading: () => (/* binding */ heading)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Heading} Heading\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `heading` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Heading} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction heading(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'h' + node.depth,\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9oZWFkaW5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsd0JBQXdCO0FBQ3JDLGFBQWEseUJBQXlCO0FBQ3RDLGFBQWEsNkJBQTZCO0FBQzFDOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQLGFBQWEsU0FBUztBQUN0QjtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvbXV6aS9naXRodWIvZGVlci1mbG93L2FpLWVkaXRvci1zdGFuZGFsb25lL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL2hlYWRpbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5IZWFkaW5nfSBIZWFkaW5nXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbi8vIE1ha2UgVlMgQ29kZSBzaG93IHJlZmVyZW5jZXMgdG8gdGhlIGFib3ZlIHR5cGVzLlxuJydcblxuLyoqXG4gKiBUdXJuIGFuIG1kYXN0IGBoZWFkaW5nYCBub2RlIGludG8gaGFzdC5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQuXG4gKiBAcGFyYW0ge0hlYWRpbmd9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBoZWFkaW5nKHN0YXRlLCBub2RlKSB7XG4gIC8qKiBAdHlwZSB7RWxlbWVudH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge1xuICAgIHR5cGU6ICdlbGVtZW50JyxcbiAgICB0YWdOYW1lOiAnaCcgKyBub2RlLmRlcHRoLFxuICAgIHByb3BlcnRpZXM6IHt9LFxuICAgIGNoaWxkcmVuOiBzdGF0ZS5hbGwobm9kZSlcbiAgfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/heading.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/html.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/html.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   html: () => (/* binding */ html)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Html} Html\n * @typedef {import('../state.js').State} State\n * @typedef {import('../../index.js').Raw} Raw\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `html` node into hast (`raw` node in dangerous mode, otherwise\n * nothing).\n *\n * @param {State} state\n *   Info passed around.\n * @param {Html} node\n *   mdast node.\n * @returns {Element | Raw | undefined}\n *   hast node.\n */\nfunction html(state, node) {\n  if (state.options.allowDangerousHtml) {\n    /** @type {Raw} */\n    const result = {type: 'raw', value: node.value}\n    state.patch(node, result)\n    return state.applyData(node, result)\n  }\n\n  return undefined\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9odG1sLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsd0JBQXdCO0FBQ3JDLGFBQWEsc0JBQXNCO0FBQ25DLGFBQWEsNkJBQTZCO0FBQzFDLGFBQWEsOEJBQThCO0FBQzNDOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxXQUFXLE1BQU07QUFDakI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1A7QUFDQSxlQUFlLEtBQUs7QUFDcEIsb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9tdXppL2dpdGh1Yi9kZWVyLWZsb3cvYWktZWRpdG9yLXN0YW5kYWxvbmUvbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvaHRtbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkh0bWx9IEh0bWxcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi8uLi9pbmRleC5qcycpLlJhd30gUmF3XG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYGh0bWxgIG5vZGUgaW50byBoYXN0IChgcmF3YCBub2RlIGluIGRhbmdlcm91cyBtb2RlLCBvdGhlcndpc2VcbiAqIG5vdGhpbmcpLlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7SHRtbH0gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0VsZW1lbnQgfCBSYXcgfCB1bmRlZmluZWR9XG4gKiAgIGhhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGh0bWwoc3RhdGUsIG5vZGUpIHtcbiAgaWYgKHN0YXRlLm9wdGlvbnMuYWxsb3dEYW5nZXJvdXNIdG1sKSB7XG4gICAgLyoqIEB0eXBlIHtSYXd9ICovXG4gICAgY29uc3QgcmVzdWx0ID0ge3R5cGU6ICdyYXcnLCB2YWx1ZTogbm9kZS52YWx1ZX1cbiAgICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gICAgcmV0dXJuIHN0YXRlLmFwcGx5RGF0YShub2RlLCByZXN1bHQpXG4gIH1cblxuICByZXR1cm4gdW5kZWZpbmVkXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image-reference.js":
/*!*************************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/image-reference.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   imageReference: () => (/* binding */ imageReference)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/* harmony import */ var _revert_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../revert.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/revert.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ImageReference} ImageReference\n * @typedef {import('../state.js').State} State\n */\n\n\n\n\n/**\n * Turn an mdast `imageReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ImageReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */\nfunction imageReference(state, node) {\n  const id = String(node.identifier).toUpperCase()\n  const definition = state.definitionById.get(id)\n\n  if (!definition) {\n    return (0,_revert_js__WEBPACK_IMPORTED_MODULE_0__.revert)(state, node)\n  }\n\n  /** @type {Properties} */\n  const properties = {src: (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_1__.normalizeUri)(definition.url || ''), alt: node.alt}\n\n  if (definition.title !== null && definition.title !== undefined) {\n    properties.title = definition.title\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'img', properties, children: []}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/image.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   image: () => (/* binding */ image)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Image} Image\n * @typedef {import('../state.js').State} State\n */\n\n\n\n/**\n * Turn an mdast `image` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Image} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction image(state, node) {\n  /** @type {Properties} */\n  const properties = {src: (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.normalizeUri)(node.url)}\n\n  if (node.alt !== null && node.alt !== undefined) {\n    properties.alt = node.alt\n  }\n\n  if (node.title !== null && node.title !== undefined) {\n    properties.title = node.title\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'img', properties, children: []}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handlers: () => (/* binding */ handlers)\n/* harmony export */ });\n/* harmony import */ var _blockquote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./blockquote.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/blockquote.js\");\n/* harmony import */ var _break_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./break.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/break.js\");\n/* harmony import */ var _code_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./code.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/code.js\");\n/* harmony import */ var _delete_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./delete.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/delete.js\");\n/* harmony import */ var _emphasis_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./emphasis.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/emphasis.js\");\n/* harmony import */ var _footnote_reference_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./footnote-reference.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js\");\n/* harmony import */ var _heading_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./heading.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/heading.js\");\n/* harmony import */ var _html_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./html.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/html.js\");\n/* harmony import */ var _image_reference_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./image-reference.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image-reference.js\");\n/* harmony import */ var _image_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./image.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image.js\");\n/* harmony import */ var _inline_code_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./inline-code.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/inline-code.js\");\n/* harmony import */ var _link_reference_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./link-reference.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link-reference.js\");\n/* harmony import */ var _link_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./link.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link.js\");\n/* harmony import */ var _list_item_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./list-item.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list-item.js\");\n/* harmony import */ var _list_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./list.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list.js\");\n/* harmony import */ var _paragraph_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./paragraph.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/paragraph.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./root.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/root.js\");\n/* harmony import */ var _strong_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./strong.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/strong.js\");\n/* harmony import */ var _table_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./table.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table.js\");\n/* harmony import */ var _table_row_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./table-row.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-row.js\");\n/* harmony import */ var _table_cell_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./table-cell.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-cell.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/text.js\");\n/* harmony import */ var _thematic_break_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./thematic-break.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Default handlers for nodes.\n *\n * @satisfies {import('../state.js').Handlers}\n */\nconst handlers = {\n  blockquote: _blockquote_js__WEBPACK_IMPORTED_MODULE_0__.blockquote,\n  break: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,\n  code: _code_js__WEBPACK_IMPORTED_MODULE_2__.code,\n  delete: _delete_js__WEBPACK_IMPORTED_MODULE_3__.strikethrough,\n  emphasis: _emphasis_js__WEBPACK_IMPORTED_MODULE_4__.emphasis,\n  footnoteReference: _footnote_reference_js__WEBPACK_IMPORTED_MODULE_5__.footnoteReference,\n  heading: _heading_js__WEBPACK_IMPORTED_MODULE_6__.heading,\n  html: _html_js__WEBPACK_IMPORTED_MODULE_7__.html,\n  imageReference: _image_reference_js__WEBPACK_IMPORTED_MODULE_8__.imageReference,\n  image: _image_js__WEBPACK_IMPORTED_MODULE_9__.image,\n  inlineCode: _inline_code_js__WEBPACK_IMPORTED_MODULE_10__.inlineCode,\n  linkReference: _link_reference_js__WEBPACK_IMPORTED_MODULE_11__.linkReference,\n  link: _link_js__WEBPACK_IMPORTED_MODULE_12__.link,\n  listItem: _list_item_js__WEBPACK_IMPORTED_MODULE_13__.listItem,\n  list: _list_js__WEBPACK_IMPORTED_MODULE_14__.list,\n  paragraph: _paragraph_js__WEBPACK_IMPORTED_MODULE_15__.paragraph,\n  // @ts-expect-error: root is different, but hard to type.\n  root: _root_js__WEBPACK_IMPORTED_MODULE_16__.root,\n  strong: _strong_js__WEBPACK_IMPORTED_MODULE_17__.strong,\n  table: _table_js__WEBPACK_IMPORTED_MODULE_18__.table,\n  tableCell: _table_cell_js__WEBPACK_IMPORTED_MODULE_19__.tableCell,\n  tableRow: _table_row_js__WEBPACK_IMPORTED_MODULE_20__.tableRow,\n  text: _text_js__WEBPACK_IMPORTED_MODULE_21__.text,\n  thematicBreak: _thematic_break_js__WEBPACK_IMPORTED_MODULE_22__.thematicBreak,\n  toml: ignore,\n  yaml: ignore,\n  definition: ignore,\n  footnoteDefinition: ignore\n}\n\n// Return nothing for nodes that are ignored.\nfunction ignore() {\n  return undefined\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/inline-code.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/inline-code.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inlineCode: () => (/* binding */ inlineCode)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').InlineCode} InlineCode\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `inlineCode` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {InlineCode} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction inlineCode(state, node) {\n  /** @type {Text} */\n  const text = {type: 'text', value: node.value.replace(/\\r?\\n|\\r/g, ' ')}\n  state.patch(node, text)\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'code',\n    properties: {},\n    children: [text]\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9pbmxpbmUtY29kZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLHdCQUF3QjtBQUNyQyxhQUFhLHFCQUFxQjtBQUNsQyxhQUFhLDRCQUE0QjtBQUN6QyxhQUFhLDZCQUE2QjtBQUMxQzs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsWUFBWTtBQUN2QjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUCxhQUFhLE1BQU07QUFDbkIsZ0JBQWdCO0FBQ2hCOztBQUVBLGFBQWEsU0FBUztBQUN0QjtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL2hvbWUvbXV6aS9naXRodWIvZGVlci1mbG93L2FpLWVkaXRvci1zdGFuZGFsb25lL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL2lubGluZS1jb2RlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5UZXh0fSBUZXh0XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLklubGluZUNvZGV9IElubGluZUNvZGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYGlubGluZUNvZGVgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7SW5saW5lQ29kZX0gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0VsZW1lbnR9XG4gKiAgIGhhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGlubGluZUNvZGUoc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtUZXh0fSAqL1xuICBjb25zdCB0ZXh0ID0ge3R5cGU6ICd0ZXh0JywgdmFsdWU6IG5vZGUudmFsdWUucmVwbGFjZSgvXFxyP1xcbnxcXHIvZywgJyAnKX1cbiAgc3RhdGUucGF0Y2gobm9kZSwgdGV4dClcblxuICAvKiogQHR5cGUge0VsZW1lbnR9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHtcbiAgICB0eXBlOiAnZWxlbWVudCcsXG4gICAgdGFnTmFtZTogJ2NvZGUnLFxuICAgIHByb3BlcnRpZXM6IHt9LFxuICAgIGNoaWxkcmVuOiBbdGV4dF1cbiAgfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/inline-code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link-reference.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/link-reference.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   linkReference: () => (/* binding */ linkReference)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/* harmony import */ var _revert_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../revert.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/revert.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').LinkReference} LinkReference\n * @typedef {import('../state.js').State} State\n */\n\n\n\n\n/**\n * Turn an mdast `linkReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {LinkReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */\nfunction linkReference(state, node) {\n  const id = String(node.identifier).toUpperCase()\n  const definition = state.definitionById.get(id)\n\n  if (!definition) {\n    return (0,_revert_js__WEBPACK_IMPORTED_MODULE_0__.revert)(state, node)\n  }\n\n  /** @type {Properties} */\n  const properties = {href: (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_1__.normalizeUri)(definition.url || '')}\n\n  if (definition.title !== null && definition.title !== undefined) {\n    properties.title = definition.title\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'a',\n    properties,\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/link.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   link: () => (/* binding */ link)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Link} Link\n * @typedef {import('../state.js').State} State\n */\n\n\n\n/**\n * Turn an mdast `link` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Link} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction link(state, node) {\n  /** @type {Properties} */\n  const properties = {href: (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.normalizeUri)(node.url)}\n\n  if (node.title !== null && node.title !== undefined) {\n    properties.title = node.title\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'a',\n    properties,\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9saW5rLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLHdCQUF3QjtBQUNyQyxhQUFhLDJCQUEyQjtBQUN4QyxhQUFhLHNCQUFzQjtBQUNuQyxhQUFhLDZCQUE2QjtBQUMxQzs7QUFFd0Q7O0FBRXhEO0FBQ0E7QUFDQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsTUFBTTtBQUNqQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUCxhQUFhLFlBQVk7QUFDekIsc0JBQXNCLE1BQU0seUVBQVk7O0FBRXhDO0FBQ0E7QUFDQTs7QUFFQSxhQUFhLFNBQVM7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9tdXppL2dpdGh1Yi9kZWVyLWZsb3cvYWktZWRpdG9yLXN0YW5kYWxvbmUvbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvbGluay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuUHJvcGVydGllc30gUHJvcGVydGllc1xuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5MaW5rfSBMaW5rXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbmltcG9ydCB7bm9ybWFsaXplVXJpfSBmcm9tICdtaWNyb21hcmstdXRpbC1zYW5pdGl6ZS11cmknXG5cbi8qKlxuICogVHVybiBhbiBtZGFzdCBgbGlua2Agbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtMaW5rfSBub2RlXG4gKiAgIG1kYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7RWxlbWVudH1cbiAqICAgaGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gbGluayhzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge1Byb3BlcnRpZXN9ICovXG4gIGNvbnN0IHByb3BlcnRpZXMgPSB7aHJlZjogbm9ybWFsaXplVXJpKG5vZGUudXJsKX1cblxuICBpZiAobm9kZS50aXRsZSAhPT0gbnVsbCAmJiBub2RlLnRpdGxlICE9PSB1bmRlZmluZWQpIHtcbiAgICBwcm9wZXJ0aWVzLnRpdGxlID0gbm9kZS50aXRsZVxuICB9XG5cbiAgLyoqIEB0eXBlIHtFbGVtZW50fSAqL1xuICBjb25zdCByZXN1bHQgPSB7XG4gICAgdHlwZTogJ2VsZW1lbnQnLFxuICAgIHRhZ05hbWU6ICdhJyxcbiAgICBwcm9wZXJ0aWVzLFxuICAgIGNoaWxkcmVuOiBzdGF0ZS5hbGwobm9kZSlcbiAgfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list-item.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/list-item.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   listItem: () => (/* binding */ listItem)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Parents} Parents\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `listItem` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ListItem} node\n *   mdast node.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */\nfunction listItem(state, node, parent) {\n  const results = state.all(node)\n  const loose = parent ? listLoose(parent) : listItemLoose(node)\n  /** @type {Properties} */\n  const properties = {}\n  /** @type {Array<ElementContent>} */\n  const children = []\n\n  if (typeof node.checked === 'boolean') {\n    const head = results[0]\n    /** @type {Element} */\n    let paragraph\n\n    if (head && head.type === 'element' && head.tagName === 'p') {\n      paragraph = head\n    } else {\n      paragraph = {type: 'element', tagName: 'p', properties: {}, children: []}\n      results.unshift(paragraph)\n    }\n\n    if (paragraph.children.length > 0) {\n      paragraph.children.unshift({type: 'text', value: ' '})\n    }\n\n    paragraph.children.unshift({\n      type: 'element',\n      tagName: 'input',\n      properties: {type: 'checkbox', checked: node.checked, disabled: true},\n      children: []\n    })\n\n    // According to github-markdown-css, this class hides bullet.\n    // See: <https://github.com/sindresorhus/github-markdown-css>.\n    properties.className = ['task-list-item']\n  }\n\n  let index = -1\n\n  while (++index < results.length) {\n    const child = results[index]\n\n    // Add eols before nodes, except if this is a loose, first paragraph.\n    if (\n      loose ||\n      index !== 0 ||\n      child.type !== 'element' ||\n      child.tagName !== 'p'\n    ) {\n      children.push({type: 'text', value: '\\n'})\n    }\n\n    if (child.type === 'element' && child.tagName === 'p' && !loose) {\n      children.push(...child.children)\n    } else {\n      children.push(child)\n    }\n  }\n\n  const tail = results[results.length - 1]\n\n  // Add a final eol.\n  if (tail && (loose || tail.type !== 'element' || tail.tagName !== 'p')) {\n    children.push({type: 'text', value: '\\n'})\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'li', properties, children}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n\n/**\n * @param {Parents} node\n * @return {Boolean}\n */\nfunction listLoose(node) {\n  let loose = false\n  if (node.type === 'list') {\n    loose = node.spread || false\n    const children = node.children\n    let index = -1\n\n    while (!loose && ++index < children.length) {\n      loose = listItemLoose(children[index])\n    }\n  }\n\n  return loose\n}\n\n/**\n * @param {ListItem} node\n * @return {Boolean}\n */\nfunction listItemLoose(node) {\n  const spread = node.spread\n\n  return spread === null || spread === undefined\n    ? node.children.length > 1\n    : spread\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list-item.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/list.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   list: () => (/* binding */ list)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').List} List\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `list` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {List} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction list(state, node) {\n  /** @type {Properties} */\n  const properties = {}\n  const results = state.all(node)\n  let index = -1\n\n  if (typeof node.start === 'number' && node.start !== 1) {\n    properties.start = node.start\n  }\n\n  // Like GitHub, add a class for custom styling.\n  while (++index < results.length) {\n    const child = results[index]\n\n    if (\n      child.type === 'element' &&\n      child.tagName === 'li' &&\n      child.properties &&\n      Array.isArray(child.properties.className) &&\n      child.properties.className.includes('task-list-item')\n    ) {\n      properties.className = ['contains-task-list']\n      break\n    }\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: node.ordered ? 'ol' : 'ul',\n    properties,\n    children: state.wrap(results, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/paragraph.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/paragraph.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   paragraph: () => (/* binding */ paragraph)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `paragraph` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Paragraph} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction paragraph(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'p',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9wYXJhZ3JhcGguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSx3QkFBd0I7QUFDckMsYUFBYSwyQkFBMkI7QUFDeEMsYUFBYSw2QkFBNkI7QUFDMUM7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxXQUFXLFdBQVc7QUFDdEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1AsYUFBYSxTQUFTO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9tdXppL2dpdGh1Yi9kZWVyLWZsb3cvYWktZWRpdG9yLXN0YW5kYWxvbmUvbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvcGFyYWdyYXBoLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuUGFyYWdyYXBofSBQYXJhZ3JhcGhcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYHBhcmFncmFwaGAgbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtQYXJhZ3JhcGh9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwYXJhZ3JhcGgoc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtFbGVtZW50fSAqL1xuICBjb25zdCByZXN1bHQgPSB7XG4gICAgdHlwZTogJ2VsZW1lbnQnLFxuICAgIHRhZ05hbWU6ICdwJyxcbiAgICBwcm9wZXJ0aWVzOiB7fSxcbiAgICBjaGlsZHJlbjogc3RhdGUuYWxsKG5vZGUpXG4gIH1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/paragraph.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/root.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/root.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: () => (/* binding */ root)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Parents} HastParents\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('mdast').Root} MdastRoot\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `root` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastRoot} node\n *   mdast node.\n * @returns {HastParents}\n *   hast node.\n */\nfunction root(state, node) {\n  /** @type {HastRoot} */\n  const result = {type: 'root', children: state.wrap(state.all(node))}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9yb290LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsd0JBQXdCO0FBQ3JDLGFBQWEscUJBQXFCO0FBQ2xDLGFBQWEsc0JBQXNCO0FBQ25DLGFBQWEsNkJBQTZCO0FBQzFDOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsV0FBVyxXQUFXO0FBQ3RCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQLGFBQWEsVUFBVTtBQUN2QixrQkFBa0I7QUFDbEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9tdXppL2dpdGh1Yi9kZWVyLWZsb3cvYWktZWRpdG9yLXN0YW5kYWxvbmUvbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvcm9vdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5QYXJlbnRzfSBIYXN0UGFyZW50c1xuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLlJvb3R9IEhhc3RSb290XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLlJvb3R9IE1kYXN0Um9vdFxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vc3RhdGUuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG4vLyBNYWtlIFZTIENvZGUgc2hvdyByZWZlcmVuY2VzIHRvIHRoZSBhYm92ZSB0eXBlcy5cbicnXG5cbi8qKlxuICogVHVybiBhbiBtZGFzdCBgcm9vdGAgbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtNZGFzdFJvb3R9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtIYXN0UGFyZW50c31cbiAqICAgaGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gcm9vdChzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge0hhc3RSb290fSAqL1xuICBjb25zdCByZXN1bHQgPSB7dHlwZTogJ3Jvb3QnLCBjaGlsZHJlbjogc3RhdGUud3JhcChzdGF0ZS5hbGwobm9kZSkpfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/root.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/strong.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/strong.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strong: () => (/* binding */ strong)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Strong} Strong\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `strong` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Strong} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction strong(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'strong',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9zdHJvbmcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYSx3QkFBd0I7QUFDckMsYUFBYSx3QkFBd0I7QUFDckMsYUFBYSw2QkFBNkI7QUFDMUM7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1AsYUFBYSxTQUFTO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvaG9tZS9tdXppL2dpdGh1Yi9kZWVyLWZsb3cvYWktZWRpdG9yLXN0YW5kYWxvbmUvbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvc3Ryb25nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuU3Ryb25nfSBTdHJvbmdcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYHN0cm9uZ2Agbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtTdHJvbmd9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBzdHJvbmcoc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtFbGVtZW50fSAqL1xuICBjb25zdCByZXN1bHQgPSB7XG4gICAgdHlwZTogJ2VsZW1lbnQnLFxuICAgIHRhZ05hbWU6ICdzdHJvbmcnLFxuICAgIHByb3BlcnRpZXM6IHt9LFxuICAgIGNoaWxkcmVuOiBzdGF0ZS5hbGwobm9kZSlcbiAgfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/strong.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-cell.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/table-cell.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tableCell: () => (/* binding */ tableCell)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').TableCell} TableCell\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `tableCell` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableCell} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction tableCell(state, node) {\n  // Note: this function is normally not called: see `table-row` for how rows\n  // and their cells are compiled.\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'td', // Assume body cell.\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy90YWJsZS1jZWxsLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGFBQWEsd0JBQXdCO0FBQ3JDLGFBQWEsMkJBQTJCO0FBQ3hDLGFBQWEsNkJBQTZCO0FBQzFDOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsV0FBVyxXQUFXO0FBQ3RCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSxhQUFhLFNBQVM7QUFDdEI7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL211emkvZ2l0aHViL2RlZXItZmxvdy9haS1lZGl0b3Itc3RhbmRhbG9uZS9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy90YWJsZS1jZWxsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuVGFibGVDZWxsfSBUYWJsZUNlbGxcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYHRhYmxlQ2VsbGAgbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtUYWJsZUNlbGx9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0YWJsZUNlbGwoc3RhdGUsIG5vZGUpIHtcbiAgLy8gTm90ZTogdGhpcyBmdW5jdGlvbiBpcyBub3JtYWxseSBub3QgY2FsbGVkOiBzZWUgYHRhYmxlLXJvd2AgZm9yIGhvdyByb3dzXG4gIC8vIGFuZCB0aGVpciBjZWxscyBhcmUgY29tcGlsZWQuXG4gIC8qKiBAdHlwZSB7RWxlbWVudH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge1xuICAgIHR5cGU6ICdlbGVtZW50JyxcbiAgICB0YWdOYW1lOiAndGQnLCAvLyBBc3N1bWUgYm9keSBjZWxsLlxuICAgIHByb3BlcnRpZXM6IHt9LFxuICAgIGNoaWxkcmVuOiBzdGF0ZS5hbGwobm9kZSlcbiAgfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-cell.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-row.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/table-row.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tableRow: () => (/* binding */ tableRow)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Parents} Parents\n * @typedef {import('mdast').TableRow} TableRow\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `tableRow` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableRow} node\n *   mdast node.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */\nfunction tableRow(state, node, parent) {\n  const siblings = parent ? parent.children : undefined\n  // Generate a body row when without parent.\n  const rowIndex = siblings ? siblings.indexOf(node) : 1\n  const tagName = rowIndex === 0 ? 'th' : 'td'\n  // To do: option to use `style`?\n  const align = parent && parent.type === 'table' ? parent.align : undefined\n  const length = align ? align.length : node.children.length\n  let cellIndex = -1\n  /** @type {Array<ElementContent>} */\n  const cells = []\n\n  while (++cellIndex < length) {\n    // Note: can also be undefined.\n    const cell = node.children[cellIndex]\n    /** @type {Properties} */\n    const properties = {}\n    const alignValue = align ? align[cellIndex] : undefined\n\n    if (alignValue) {\n      properties.align = alignValue\n    }\n\n    /** @type {Element} */\n    let result = {type: 'element', tagName, properties, children: []}\n\n    if (cell) {\n      result.children = state.all(cell)\n      state.patch(cell, result)\n      result = state.applyData(cell, result)\n    }\n\n    cells.push(result)\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'tr',\n    properties: {},\n    children: state.wrap(cells, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-row.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/table.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   table: () => (/* binding */ table)\n/* harmony export */ });\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-position */ \"(ssr)/./node_modules/unist-util-position/lib/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Table} Table\n * @typedef {import('../state.js').State} State\n */\n\n\n\n/**\n * Turn an mdast `table` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Table} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction table(state, node) {\n  const rows = state.all(node)\n  const firstRow = rows.shift()\n  /** @type {Array<Element>} */\n  const tableContent = []\n\n  if (firstRow) {\n    /** @type {Element} */\n    const head = {\n      type: 'element',\n      tagName: 'thead',\n      properties: {},\n      children: state.wrap([firstRow], true)\n    }\n    state.patch(node.children[0], head)\n    tableContent.push(head)\n  }\n\n  if (rows.length > 0) {\n    /** @type {Element} */\n    const body = {\n      type: 'element',\n      tagName: 'tbody',\n      properties: {},\n      children: state.wrap(rows, true)\n    }\n\n    const start = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_0__.pointStart)(node.children[1])\n    const end = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_0__.pointEnd)(node.children[node.children.length - 1])\n    if (start && end) body.position = {start, end}\n    tableContent.push(body)\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'table',\n    properties: {},\n    children: state.wrap(tableContent, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/text.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/text.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var trim_lines__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! trim-lines */ \"(ssr)/./node_modules/trim-lines/index.js\");\n/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').Text} HastText\n * @typedef {import('mdast').Text} MdastText\n * @typedef {import('../state.js').State} State\n */\n\n\n\n/**\n * Turn an mdast `text` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastText} node\n *   mdast node.\n * @returns {HastElement | HastText}\n *   hast node.\n */\nfunction text(state, node) {\n  /** @type {HastText} */\n  const result = {type: 'text', value: (0,trim_lines__WEBPACK_IMPORTED_MODULE_0__.trimLines)(String(node.value))}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy90ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLHdCQUF3QjtBQUNyQyxhQUFhLHFCQUFxQjtBQUNsQyxhQUFhLHNCQUFzQjtBQUNuQyxhQUFhLDZCQUE2QjtBQUMxQzs7QUFFb0M7O0FBRXBDO0FBQ0E7QUFDQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsV0FBVztBQUN0QjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUCxhQUFhLFVBQVU7QUFDdkIsa0JBQWtCLHFCQUFxQixxREFBUztBQUNoRDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL211emkvZ2l0aHViL2RlZXItZmxvdy9haS1lZGl0b3Itc3RhbmRhbG9uZS9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy90ZXh0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEhhc3RFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuVGV4dH0gSGFzdFRleHRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuVGV4dH0gTWRhc3RUZXh0XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbmltcG9ydCB7dHJpbUxpbmVzfSBmcm9tICd0cmltLWxpbmVzJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYHRleHRgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7TWRhc3RUZXh0fSBub2RlXG4gKiAgIG1kYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7SGFzdEVsZW1lbnQgfCBIYXN0VGV4dH1cbiAqICAgaGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gdGV4dChzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge0hhc3RUZXh0fSAqL1xuICBjb25zdCByZXN1bHQgPSB7dHlwZTogJ3RleHQnLCB2YWx1ZTogdHJpbUxpbmVzKFN0cmluZyhub2RlLnZhbHVlKSl9XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHN0YXRlLmFwcGx5RGF0YShub2RlLCByZXN1bHQpXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   thematicBreak: () => (/* binding */ thematicBreak)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').ThematicBreak} ThematicBreak\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `thematicBreak` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ThematicBreak} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nfunction thematicBreak(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'hr',\n    properties: {},\n    children: []\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy90aGVtYXRpYy1icmVhay5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLHdCQUF3QjtBQUNyQyxhQUFhLCtCQUErQjtBQUM1QyxhQUFhLDZCQUE2QjtBQUMxQzs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsZUFBZTtBQUMxQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUCxhQUFhLFNBQVM7QUFDdEI7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9ob21lL211emkvZ2l0aHViL2RlZXItZmxvdy9haS1lZGl0b3Itc3RhbmRhbG9uZS9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy90aGVtYXRpYy1icmVhay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLlRoZW1hdGljQnJlYWt9IFRoZW1hdGljQnJlYWtcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYHRoZW1hdGljQnJlYWtgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7VGhlbWF0aWNCcmVha30gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0VsZW1lbnR9XG4gKiAgIGhhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHRoZW1hdGljQnJlYWsoc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtFbGVtZW50fSAqL1xuICBjb25zdCByZXN1bHQgPSB7XG4gICAgdHlwZTogJ2VsZW1lbnQnLFxuICAgIHRhZ05hbWU6ICdocicsXG4gICAgcHJvcGVydGllczoge30sXG4gICAgY2hpbGRyZW46IFtdXG4gIH1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toHast: () => (/* binding */ toHast)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var _footer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./footer.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/footer.js\");\n/* harmony import */ var _state_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./state.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/state.js\");\n/**\n * @typedef {import('hast').Nodes} HastNodes\n * @typedef {import('mdast').Nodes} MdastNodes\n * @typedef {import('./state.js').Options} Options\n */\n\n\n\n\n\n/**\n * Transform mdast to hast.\n *\n * ##### Notes\n *\n * ###### HTML\n *\n * Raw HTML is available in mdast as `html` nodes and can be embedded in hast\n * as semistandard `raw` nodes.\n * Most utilities ignore `raw` nodes but two notable ones don’t:\n *\n * *   `hast-util-to-html` also has an option `allowDangerousHtml` which will\n *     output the raw HTML.\n *     This is typically discouraged as noted by the option name but is useful\n *     if you completely trust authors\n * *   `hast-util-raw` can handle the raw embedded HTML strings by parsing them\n *     into standard hast nodes (`element`, `text`, etc).\n *     This is a heavy task as it needs a full HTML parser, but it is the only\n *     way to support untrusted content\n *\n * ###### Footnotes\n *\n * Many options supported here relate to footnotes.\n * Footnotes are not specified by CommonMark, which we follow by default.\n * They are supported by GitHub, so footnotes can be enabled in markdown with\n * `mdast-util-gfm`.\n *\n * The options `footnoteBackLabel` and `footnoteLabel` define natural language\n * that explains footnotes, which is hidden for sighted users but shown to\n * assistive technology.\n * When your page is not in English, you must define translated values.\n *\n * Back references use ARIA attributes, but the section label itself uses a\n * heading that is hidden with an `sr-only` class.\n * To show it to sighted users, define different attributes in\n * `footnoteLabelProperties`.\n *\n * ###### Clobbering\n *\n * Footnotes introduces a problem, as it links footnote calls to footnote\n * definitions on the page through `id` attributes generated from user content,\n * which results in DOM clobbering.\n *\n * DOM clobbering is this:\n *\n * ```html\n * <p id=x></p>\n * <script>alert(x) // `x` now refers to the DOM `p#x` element</script>\n * ```\n *\n * Elements by their ID are made available by browsers on the `window` object,\n * which is a security risk.\n * Using a prefix solves this problem.\n *\n * More information on how to handle clobbering and the prefix is explained in\n * Example: headings (DOM clobbering) in `rehype-sanitize`.\n *\n * ###### Unknown nodes\n *\n * Unknown nodes are nodes with a type that isn’t in `handlers` or `passThrough`.\n * The default behavior for unknown nodes is:\n *\n * *   when the node has a `value` (and doesn’t have `data.hName`,\n *     `data.hProperties`, or `data.hChildren`, see later), create a hast `text`\n *     node\n * *   otherwise, create a `<div>` element (which could be changed with\n *     `data.hName`), with its children mapped from mdast to hast as well\n *\n * This behavior can be changed by passing an `unknownHandler`.\n *\n * @param {MdastNodes} tree\n *   mdast tree.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {HastNodes}\n *   hast tree.\n */\nfunction toHast(tree, options) {\n  const state = (0,_state_js__WEBPACK_IMPORTED_MODULE_0__.createState)(tree, options)\n  const node = state.one(tree, undefined)\n  const foot = (0,_footer_js__WEBPACK_IMPORTED_MODULE_1__.footer)(state)\n  /** @type {HastNodes} */\n  const result = Array.isArray(node)\n    ? {type: 'root', children: node}\n    : node || {type: 'root', children: []}\n\n  if (foot) {\n    // If there’s a footer, there were definitions, meaning block\n    // content.\n    // So `result` is a parent node.\n    (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)('children' in result)\n    result.children.push({type: 'text', value: '\\n'}, foot)\n  }\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/revert.js":
/*!*******************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/revert.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   revert: () => (/* binding */ revert)\n/* harmony export */ });\n/**\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('mdast').Nodes} Nodes\n * @typedef {import('mdast').Reference} Reference\n *\n * @typedef {import('./state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Return the content of a reference without definition as plain text.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Extract<Nodes, Reference>} node\n *   Reference node (image, link).\n * @returns {Array<ElementContent>}\n *   hast content.\n */\nfunction revert(state, node) {\n  const subtype = node.referenceType\n  let suffix = ']'\n\n  if (subtype === 'collapsed') {\n    suffix += '[]'\n  } else if (subtype === 'full') {\n    suffix += '[' + (node.label || node.identifier) + ']'\n  }\n\n  if (node.type === 'imageReference') {\n    return [{type: 'text', value: '![' + node.alt + suffix}]\n  }\n\n  const contents = state.all(node)\n  const head = contents[0]\n\n  if (head && head.type === 'text') {\n    head.value = '[' + head.value\n  } else {\n    contents.unshift({type: 'text', value: '['})\n  }\n\n  const tail = contents[contents.length - 1]\n\n  if (tail && tail.type === 'text') {\n    tail.value += suffix\n  } else {\n    contents.push({type: 'text', value: suffix})\n  }\n\n  return contents\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/revert.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/state.js":
/*!******************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/state.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createState: () => (/* binding */ createState),\n/* harmony export */   wrap: () => (/* binding */ wrap)\n/* harmony export */ });\n/* harmony import */ var _ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ungap/structured-clone */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! unist-util-position */ \"(ssr)/./node_modules/unist-util-position/lib/index.js\");\n/* harmony import */ var _handlers_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./handlers/index.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/index.js\");\n/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').ElementContent} HastElementContent\n * @typedef {import('hast').Nodes} HastNodes\n * @typedef {import('hast').Properties} HastProperties\n * @typedef {import('hast').RootContent} HastRootContent\n * @typedef {import('hast').Text} HastText\n *\n * @typedef {import('mdast').Definition} MdastDefinition\n * @typedef {import('mdast').FootnoteDefinition} MdastFootnoteDefinition\n * @typedef {import('mdast').Nodes} MdastNodes\n * @typedef {import('mdast').Parents} MdastParents\n *\n * @typedef {import('vfile').VFile} VFile\n *\n * @typedef {import('./footer.js').FootnoteBackContentTemplate} FootnoteBackContentTemplate\n * @typedef {import('./footer.js').FootnoteBackLabelTemplate} FootnoteBackLabelTemplate\n */\n\n/**\n * @callback Handler\n *   Handle a node.\n * @param {State} state\n *   Info passed around.\n * @param {any} node\n *   mdast node to handle.\n * @param {MdastParents | undefined} parent\n *   Parent of `node`.\n * @returns {Array<HastElementContent> | HastElementContent | undefined}\n *   hast node.\n *\n * @typedef {Partial<Record<MdastNodes['type'], Handler>>} Handlers\n *   Handle nodes.\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [allowDangerousHtml=false]\n *   Whether to persist raw HTML in markdown in the hast tree (default:\n *   `false`).\n * @property {string | null | undefined} [clobberPrefix='user-content-']\n *   Prefix to use before the `id` property on footnotes to prevent them from\n *   *clobbering* (default: `'user-content-'`).\n *\n *   Pass `''` for trusted markdown and when you are careful with\n *   polyfilling.\n *   You could pass a different prefix.\n *\n *   DOM clobbering is this:\n *\n *   ```html\n *   <p id=\"x\"></p>\n *   <script>alert(x) // `x` now refers to the `p#x` DOM element</script>\n *   ```\n *\n *   The above example shows that elements are made available by browsers, by\n *   their ID, on the `window` object.\n *   This is a security risk because you might be expecting some other variable\n *   at that place.\n *   It can also break polyfills.\n *   Using a prefix solves these problems.\n * @property {VFile | null | undefined} [file]\n *   Corresponding virtual file representing the input document (optional).\n * @property {FootnoteBackContentTemplate | string | null | undefined} [footnoteBackContent]\n *   Content of the backreference back to references (default: `defaultFootnoteBackContent`).\n *\n *   The default value is:\n *\n *   ```js\n *   function defaultFootnoteBackContent(_, rereferenceIndex) {\n *     const result = [{type: 'text', value: '↩'}]\n *\n *     if (rereferenceIndex > 1) {\n *       result.push({\n *         type: 'element',\n *         tagName: 'sup',\n *         properties: {},\n *         children: [{type: 'text', value: String(rereferenceIndex)}]\n *       })\n *     }\n *\n *     return result\n *   }\n *   ```\n *\n *   This content is used in the `a` element of each backreference (the `↩`\n *   links).\n * @property {FootnoteBackLabelTemplate | string | null | undefined} [footnoteBackLabel]\n *   Label to describe the backreference back to references (default:\n *   `defaultFootnoteBackLabel`).\n *\n *   The default value is:\n *\n *   ```js\n *   function defaultFootnoteBackLabel(referenceIndex, rereferenceIndex) {\n *    return (\n *      'Back to reference ' +\n *      (referenceIndex + 1) +\n *      (rereferenceIndex > 1 ? '-' + rereferenceIndex : '')\n *    )\n *   }\n *   ```\n *\n *   Change it when the markdown is not in English.\n *\n *   This label is used in the `ariaLabel` property on each backreference\n *   (the `↩` links).\n *   It affects users of assistive technology.\n * @property {string | null | undefined} [footnoteLabel='Footnotes']\n *   Textual label to use for the footnotes section (default: `'Footnotes'`).\n *\n *   Change it when the markdown is not in English.\n *\n *   This label is typically hidden visually (assuming a `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass different properties with the `footnoteLabelProperties` option.\n * @property {HastProperties | null | undefined} [footnoteLabelProperties={className: ['sr-only']}]\n *   Properties to use on the footnote label (default: `{className:\n *   ['sr-only']}`).\n *\n *   Change it to show the label and add other properties.\n *\n *   This label is typically hidden visually (assuming an `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass an empty string.\n *   You can also add different properties.\n *\n *   > **Note**: `id: 'footnote-label'` is always added, because footnote\n *   > calls use it with `aria-describedby` to provide an accessible label.\n * @property {string | null | undefined} [footnoteLabelTagName='h2']\n *   HTML tag name to use for the footnote label element (default: `'h2'`).\n *\n *   Change it to match your document structure.\n *\n *   This label is typically hidden visually (assuming a `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass different properties with the `footnoteLabelProperties` option.\n * @property {Handlers | null | undefined} [handlers]\n *   Extra handlers for nodes (optional).\n * @property {Array<MdastNodes['type']> | null | undefined} [passThrough]\n *   List of custom mdast node types to pass through (keep) in hast (note that\n *   the node itself is passed, but eventual children are transformed)\n *   (optional).\n * @property {Handler | null | undefined} [unknownHandler]\n *   Handler for all unknown nodes (optional).\n *\n * @typedef State\n *   Info passed around.\n * @property {(node: MdastNodes) => Array<HastElementContent>} all\n *   Transform the children of an mdast parent to hast.\n * @property {<Type extends HastNodes>(from: MdastNodes, to: Type) => HastElement | Type} applyData\n *   Honor the `data` of `from`, and generate an element instead of `node`.\n * @property {Map<string, MdastDefinition>} definitionById\n *   Definitions by their identifier.\n * @property {Map<string, MdastFootnoteDefinition>} footnoteById\n *   Footnote definitions by their identifier.\n * @property {Map<string, number>} footnoteCounts\n *   Counts for how often the same footnote was called.\n * @property {Array<string>} footnoteOrder\n *   Identifiers of order when footnote calls first appear in tree order.\n * @property {Handlers} handlers\n *   Applied handlers.\n * @property {(node: MdastNodes, parent: MdastParents | undefined) => Array<HastElementContent> | HastElementContent | undefined} one\n *   Transform an mdast node to hast.\n * @property {Options} options\n *   Configuration.\n * @property {(from: MdastNodes, node: HastNodes) => undefined} patch\n *   Copy a node’s positional info.\n * @property {<Type extends HastRootContent>(nodes: Array<Type>, loose?: boolean | undefined) => Array<HastText | Type>} wrap\n *   Wrap `nodes` with line endings between each node, adds initial/final line endings when `loose`.\n */\n\n\n\n\n\n\nconst own = {}.hasOwnProperty\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Create `state` from an mdast tree.\n *\n * @param {MdastNodes} tree\n *   mdast node to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {State}\n *   `state` function.\n */\nfunction createState(tree, options) {\n  const settings = options || emptyOptions\n  /** @type {Map<string, MdastDefinition>} */\n  const definitionById = new Map()\n  /** @type {Map<string, MdastFootnoteDefinition>} */\n  const footnoteById = new Map()\n  /** @type {Map<string, number>} */\n  const footnoteCounts = new Map()\n  /** @type {Handlers} */\n  // @ts-expect-error: the root handler returns a root.\n  // Hard to type.\n  const handlers = {..._handlers_index_js__WEBPACK_IMPORTED_MODULE_0__.handlers, ...settings.handlers}\n\n  /** @type {State} */\n  const state = {\n    all,\n    applyData,\n    definitionById,\n    footnoteById,\n    footnoteCounts,\n    footnoteOrder: [],\n    handlers,\n    one,\n    options: settings,\n    patch,\n    wrap\n  }\n\n  ;(0,unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.visit)(tree, function (node) {\n    if (node.type === 'definition' || node.type === 'footnoteDefinition') {\n      const map = node.type === 'definition' ? definitionById : footnoteById\n      const id = String(node.identifier).toUpperCase()\n\n      // Mimick CM behavior of link definitions.\n      // See: <https://github.com/syntax-tree/mdast-util-definitions/blob/9032189/lib/index.js#L20-L21>.\n      if (!map.has(id)) {\n        // @ts-expect-error: node type matches map.\n        map.set(id, node)\n      }\n    }\n  })\n\n  return state\n\n  /**\n   * Transform an mdast node into a hast node.\n   *\n   * @param {MdastNodes} node\n   *   mdast node.\n   * @param {MdastParents | undefined} [parent]\n   *   Parent of `node`.\n   * @returns {Array<HastElementContent> | HastElementContent | undefined}\n   *   Resulting hast node.\n   */\n  function one(node, parent) {\n    const type = node.type\n    const handle = state.handlers[type]\n\n    if (own.call(state.handlers, type) && handle) {\n      return handle(state, node, parent)\n    }\n\n    if (state.options.passThrough && state.options.passThrough.includes(type)) {\n      if ('children' in node) {\n        const {children, ...shallow} = node\n        const result = (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(shallow)\n        // @ts-expect-error: TS doesn’t understand…\n        result.children = state.all(node)\n        // @ts-expect-error: TS doesn’t understand…\n        return result\n      }\n\n      // @ts-expect-error: it’s custom.\n      return (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(node)\n    }\n\n    const unknown = state.options.unknownHandler || defaultUnknownHandler\n\n    return unknown(state, node, parent)\n  }\n\n  /**\n   * Transform the children of an mdast node into hast nodes.\n   *\n   * @param {MdastNodes} parent\n   *   mdast node to compile\n   * @returns {Array<HastElementContent>}\n   *   Resulting hast nodes.\n   */\n  function all(parent) {\n    /** @type {Array<HastElementContent>} */\n    const values = []\n\n    if ('children' in parent) {\n      const nodes = parent.children\n      let index = -1\n      while (++index < nodes.length) {\n        const result = state.one(nodes[index], parent)\n\n        // To do: see if we van clean this? Can we merge texts?\n        if (result) {\n          if (index && nodes[index - 1].type === 'break') {\n            if (!Array.isArray(result) && result.type === 'text') {\n              result.value = trimMarkdownSpaceStart(result.value)\n            }\n\n            if (!Array.isArray(result) && result.type === 'element') {\n              const head = result.children[0]\n\n              if (head && head.type === 'text') {\n                head.value = trimMarkdownSpaceStart(head.value)\n              }\n            }\n          }\n\n          if (Array.isArray(result)) {\n            values.push(...result)\n          } else {\n            values.push(result)\n          }\n        }\n      }\n    }\n\n    return values\n  }\n}\n\n/**\n * Copy a node’s positional info.\n *\n * @param {MdastNodes} from\n *   mdast node to copy from.\n * @param {HastNodes} to\n *   hast node to copy into.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(from, to) {\n  if (from.position) to.position = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_3__.position)(from)\n}\n\n/**\n * Honor the `data` of `from` and maybe generate an element instead of `to`.\n *\n * @template {HastNodes} Type\n *   Node type.\n * @param {MdastNodes} from\n *   mdast node to use data from.\n * @param {Type} to\n *   hast node to change.\n * @returns {HastElement | Type}\n *   Nothing.\n */\nfunction applyData(from, to) {\n  /** @type {HastElement | Type} */\n  let result = to\n\n  // Handle `data.hName`, `data.hProperties, `data.hChildren`.\n  if (from && from.data) {\n    const hName = from.data.hName\n    const hChildren = from.data.hChildren\n    const hProperties = from.data.hProperties\n\n    if (typeof hName === 'string') {\n      // Transforming the node resulted in an element with a different name\n      // than wanted:\n      if (result.type === 'element') {\n        result.tagName = hName\n      }\n      // Transforming the node resulted in a non-element, which happens for\n      // raw, text, and root nodes (unless custom handlers are passed).\n      // The intent of `hName` is to create an element, but likely also to keep\n      // the content around (otherwise: pass `hChildren`).\n      else {\n        /** @type {Array<HastElementContent>} */\n        // @ts-expect-error: assume no doctypes in `root`.\n        const children = 'children' in result ? result.children : [result]\n        result = {type: 'element', tagName: hName, properties: {}, children}\n      }\n    }\n\n    if (result.type === 'element' && hProperties) {\n      Object.assign(result.properties, (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(hProperties))\n    }\n\n    if (\n      'children' in result &&\n      result.children &&\n      hChildren !== null &&\n      hChildren !== undefined\n    ) {\n      result.children = hChildren\n    }\n  }\n\n  return result\n}\n\n/**\n * Transform an unknown node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} node\n *   Unknown mdast node.\n * @returns {HastElement | HastText}\n *   Resulting hast node.\n */\nfunction defaultUnknownHandler(state, node) {\n  const data = node.data || {}\n  /** @type {HastElement | HastText} */\n  const result =\n    'value' in node &&\n    !(own.call(data, 'hProperties') || own.call(data, 'hChildren'))\n      ? {type: 'text', value: node.value}\n      : {\n          type: 'element',\n          tagName: 'div',\n          properties: {},\n          children: state.all(node)\n        }\n\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n\n/**\n * Wrap `nodes` with line endings between each node.\n *\n * @template {HastRootContent} Type\n *   Node type.\n * @param {Array<Type>} nodes\n *   List of nodes to wrap.\n * @param {boolean | undefined} [loose=false]\n *   Whether to add line endings at start and end (default: `false`).\n * @returns {Array<HastText | Type>}\n *   Wrapped nodes.\n */\nfunction wrap(nodes, loose) {\n  /** @type {Array<HastText | Type>} */\n  const result = []\n  let index = -1\n\n  if (loose) {\n    result.push({type: 'text', value: '\\n'})\n  }\n\n  while (++index < nodes.length) {\n    if (index) result.push({type: 'text', value: '\\n'})\n    result.push(nodes[index])\n  }\n\n  if (loose && nodes.length > 0) {\n    result.push({type: 'text', value: '\\n'})\n  }\n\n  return result\n}\n\n/**\n * Trim spaces and tabs at the start of `value`.\n *\n * @param {string} value\n *   Value to trim.\n * @returns {string}\n *   Result.\n */\nfunction trimMarkdownSpaceStart(value) {\n  let index = 0\n  let code = value.charCodeAt(index)\n\n  while (code === 9 || code === 32) {\n    index++\n    code = value.charCodeAt(index)\n  }\n\n  return value.slice(index)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/state.js\n");

/***/ })

};
;