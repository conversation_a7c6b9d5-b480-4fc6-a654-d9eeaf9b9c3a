"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tunnel-rat@0.1.2_@types+react@18.3.23_react@18.3.1";
exports.ids = ["vendor-chunks/tunnel-rat@0.1.2_@types+react@18.3.23_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/tunnel-rat@0.1.2_@types+react@18.3.23_react@18.3.1/node_modules/tunnel-rat/dist/index.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/tunnel-rat@0.1.2_@types+react@18.3.23_react@18.3.1/node_modules/tunnel-rat/dist/index.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ tunnel)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/.pnpm/zustand@4.5.7_@types+react@18.3.23_react@18.3.1/node_modules/zustand/esm/index.mjs\");\n\n\n\nvar _window$document, _window$navigator;\n/**\n * An SSR-friendly useLayoutEffect.\n *\n * React currently throws a warning when using useLayoutEffect on the server.\n * To get around it, we can conditionally useEffect on the server (no-op) and\n * useLayoutEffect elsewhere.\n *\n * @see https://github.com/facebook/react/issues/14927\n */\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' && ((_window$document = window.document) != null && _window$document.createElement || ((_window$navigator = window.navigator) == null ? void 0 : _window$navigator.product) === 'ReactNative') ? (react__WEBPACK_IMPORTED_MODULE_0___default().useLayoutEffect) : (react__WEBPACK_IMPORTED_MODULE_0___default().useEffect);\n\nfunction tunnel() {\n  const useStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)(set => ({\n    current: new Array(),\n    version: 0,\n    set\n  }));\n  return {\n    In: ({\n      children\n    }) => {\n      const set = useStore(state => state.set);\n      const version = useStore(state => state.version);\n      /* When this component mounts, we increase the store's version number.\n      This will cause all existing rats to re-render (just like if the Out component\n      were mapping items to a list.) The re-rendering will cause the final \n      order of rendered components to match what the user is expecting. */\n\n      useIsomorphicLayoutEffect(() => {\n        set(state => ({\n          version: state.version + 1\n        }));\n      }, []);\n      /* Any time the children _or_ the store's version number change, insert\n      the specified React children into the list of rats. */\n\n      useIsomorphicLayoutEffect(() => {\n        set(({\n          current\n        }) => ({\n          current: [...current, children]\n        }));\n        return () => set(({\n          current\n        }) => ({\n          current: current.filter(c => c !== children)\n        }));\n      }, [children, version]);\n      return null;\n    },\n    Out: () => {\n      const current = useStore(state => state.current);\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, current);\n    }\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/tunnel-rat@0.1.2_@types+react@18.3.23_react@18.3.1/node_modules/tunnel-rat/dist/index.js\n");

/***/ })

};
;