"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+starter-kit@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+starter-kit@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+starter-kit@2.26.1/node_modules/@tiptap/starter-kit/dist/index.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+starter-kit@2.26.1/node_modules/@tiptap/starter-kit/dist/index.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StarterKit: () => (/* binding */ StarterKit),\n/* harmony export */   \"default\": () => (/* binding */ StarterKit)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_extension_blockquote__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/extension-blockquote */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-blockquote@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-blockquote/dist/index.js\");\n/* harmony import */ var _tiptap_extension_bold__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/extension-bold */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-bold@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-bold/dist/index.js\");\n/* harmony import */ var _tiptap_extension_bullet_list__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/extension-bullet-list */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-bullet-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-bullet-list/dist/index.js\");\n/* harmony import */ var _tiptap_extension_code__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tiptap/extension-code */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-code@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-code/dist/index.js\");\n/* harmony import */ var _tiptap_extension_code_block__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tiptap/extension-code-block */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-code-block/dist/index.js\");\n/* harmony import */ var _tiptap_extension_document__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tiptap/extension-document */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-document@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-document/dist/index.js\");\n/* harmony import */ var _tiptap_extension_dropcursor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tiptap/extension-dropcursor */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-dropcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-dropcursor/dist/index.js\");\n/* harmony import */ var _tiptap_extension_gapcursor__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tiptap/extension-gapcursor */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-gapcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-gapcursor/dist/index.js\");\n/* harmony import */ var _tiptap_extension_hard_break__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tiptap/extension-hard-break */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-hard-break@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-hard-break/dist/index.js\");\n/* harmony import */ var _tiptap_extension_heading__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tiptap/extension-heading */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-heading@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-heading/dist/index.js\");\n/* harmony import */ var _tiptap_extension_history__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @tiptap/extension-history */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-history@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-history/dist/index.js\");\n/* harmony import */ var _tiptap_extension_horizontal_rule__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tiptap/extension-horizontal-rule */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-horizontal-rule@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-horizontal-rule/dist/index.js\");\n/* harmony import */ var _tiptap_extension_italic__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tiptap/extension-italic */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-italic@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-italic/dist/index.js\");\n/* harmony import */ var _tiptap_extension_list_item__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tiptap/extension-list-item */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-list-item@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-list-item/dist/index.js\");\n/* harmony import */ var _tiptap_extension_ordered_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tiptap/extension-ordered-list */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-ordered-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-ordered-list/dist/index.js\");\n/* harmony import */ var _tiptap_extension_paragraph__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @tiptap/extension-paragraph */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-paragraph@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-paragraph/dist/index.js\");\n/* harmony import */ var _tiptap_extension_strike__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @tiptap/extension-strike */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-strike@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-strike/dist/index.js\");\n/* harmony import */ var _tiptap_extension_text__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @tiptap/extension-text */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-text@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-text/dist/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * The starter kit is a collection of essential editor extensions.\n *\n * It’s a good starting point for building your own editor.\n */\nconst StarterKit = _tiptap_core__WEBPACK_IMPORTED_MODULE_18__.Extension.create({\n    name: 'starterKit',\n    addExtensions() {\n        const extensions = [];\n        if (this.options.bold !== false) {\n            extensions.push(_tiptap_extension_bold__WEBPACK_IMPORTED_MODULE_1__.Bold.configure(this.options.bold));\n        }\n        if (this.options.blockquote !== false) {\n            extensions.push(_tiptap_extension_blockquote__WEBPACK_IMPORTED_MODULE_0__.Blockquote.configure(this.options.blockquote));\n        }\n        if (this.options.bulletList !== false) {\n            extensions.push(_tiptap_extension_bullet_list__WEBPACK_IMPORTED_MODULE_2__.BulletList.configure(this.options.bulletList));\n        }\n        if (this.options.code !== false) {\n            extensions.push(_tiptap_extension_code__WEBPACK_IMPORTED_MODULE_3__.Code.configure(this.options.code));\n        }\n        if (this.options.codeBlock !== false) {\n            extensions.push(_tiptap_extension_code_block__WEBPACK_IMPORTED_MODULE_4__.CodeBlock.configure(this.options.codeBlock));\n        }\n        if (this.options.document !== false) {\n            extensions.push(_tiptap_extension_document__WEBPACK_IMPORTED_MODULE_5__.Document.configure(this.options.document));\n        }\n        if (this.options.dropcursor !== false) {\n            extensions.push(_tiptap_extension_dropcursor__WEBPACK_IMPORTED_MODULE_6__.Dropcursor.configure(this.options.dropcursor));\n        }\n        if (this.options.gapcursor !== false) {\n            extensions.push(_tiptap_extension_gapcursor__WEBPACK_IMPORTED_MODULE_7__.Gapcursor.configure(this.options.gapcursor));\n        }\n        if (this.options.hardBreak !== false) {\n            extensions.push(_tiptap_extension_hard_break__WEBPACK_IMPORTED_MODULE_8__.HardBreak.configure(this.options.hardBreak));\n        }\n        if (this.options.heading !== false) {\n            extensions.push(_tiptap_extension_heading__WEBPACK_IMPORTED_MODULE_9__.Heading.configure(this.options.heading));\n        }\n        if (this.options.history !== false) {\n            extensions.push(_tiptap_extension_history__WEBPACK_IMPORTED_MODULE_10__.History.configure(this.options.history));\n        }\n        if (this.options.horizontalRule !== false) {\n            extensions.push(_tiptap_extension_horizontal_rule__WEBPACK_IMPORTED_MODULE_11__.HorizontalRule.configure(this.options.horizontalRule));\n        }\n        if (this.options.italic !== false) {\n            extensions.push(_tiptap_extension_italic__WEBPACK_IMPORTED_MODULE_12__.Italic.configure(this.options.italic));\n        }\n        if (this.options.listItem !== false) {\n            extensions.push(_tiptap_extension_list_item__WEBPACK_IMPORTED_MODULE_13__.ListItem.configure(this.options.listItem));\n        }\n        if (this.options.orderedList !== false) {\n            extensions.push(_tiptap_extension_ordered_list__WEBPACK_IMPORTED_MODULE_14__.OrderedList.configure(this.options.orderedList));\n        }\n        if (this.options.paragraph !== false) {\n            extensions.push(_tiptap_extension_paragraph__WEBPACK_IMPORTED_MODULE_15__.Paragraph.configure(this.options.paragraph));\n        }\n        if (this.options.strike !== false) {\n            extensions.push(_tiptap_extension_strike__WEBPACK_IMPORTED_MODULE_16__.Strike.configure(this.options.strike));\n        }\n        if (this.options.text !== false) {\n            extensions.push(_tiptap_extension_text__WEBPACK_IMPORTED_MODULE_17__.Text.configure(this.options.text));\n        }\n        return extensions;\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+starter-kit@2.26.1/node_modules/@tiptap/starter-kit/dist/index.js\n");

/***/ })

};
;