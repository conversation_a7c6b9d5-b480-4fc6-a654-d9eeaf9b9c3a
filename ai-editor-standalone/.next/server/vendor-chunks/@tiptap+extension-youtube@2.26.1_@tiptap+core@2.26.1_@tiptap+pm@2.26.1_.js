"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-youtube@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-youtube@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-youtube@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-youtube/dist/index.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-youtube@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-youtube/dist/index.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Youtube: () => (/* binding */ Youtube),\n/* harmony export */   \"default\": () => (/* binding */ Youtube)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\nconst YOUTUBE_REGEX = /^((?:https?:)?\\/\\/)?((?:www|m|music)\\.)?((?:youtube\\.com|youtu\\.be|youtube-nocookie\\.com))(\\/(?:[\\w-]+\\?v=|embed\\/|v\\/)?)([\\w-]+)(\\S+)?$/;\nconst YOUTUBE_REGEX_GLOBAL = /^((?:https?:)?\\/\\/)?((?:www|m|music)\\.)?((?:youtube\\.com|youtu\\.be|youtube-nocookie\\.com))(\\/(?:[\\w-]+\\?v=|embed\\/|v\\/)?)([\\w-]+)(\\S+)?$/g;\nconst isValidYoutubeUrl = (url) => {\n    return url.match(YOUTUBE_REGEX);\n};\nconst getYoutubeEmbedUrl = (nocookie, isPlaylist) => {\n    if (isPlaylist) {\n        return 'https://www.youtube-nocookie.com/embed/videoseries?list=';\n    }\n    return nocookie ? 'https://www.youtube-nocookie.com/embed/' : 'https://www.youtube.com/embed/';\n};\nconst getYoutubeVideoOrPlaylistId = (url) => {\n    if (url.searchParams.has('v')) {\n        return { id: url.searchParams.get('v') };\n    }\n    if (url.hostname === 'youtu.be'\n        || url.pathname.includes('shorts')\n        || url.pathname.includes('live')) {\n        return { id: url.pathname.split('/').pop() };\n    }\n    if (url.searchParams.has('list')) {\n        return { id: url.searchParams.get('list'), isPlaylist: true };\n    }\n    return null;\n};\nconst getEmbedUrlFromYoutubeUrl = (options) => {\n    var _a;\n    const { url, allowFullscreen, autoplay, ccLanguage, ccLoadPolicy, controls, disableKBcontrols, enableIFrameApi, endTime, interfaceLanguage, ivLoadPolicy, loop, modestBranding, nocookie, origin, playlist, progressBarColor, startAt, rel, } = options;\n    if (!isValidYoutubeUrl(url)) {\n        return null;\n    }\n    // if is already an embed url, return it\n    if (url.includes('/embed/')) {\n        return url;\n    }\n    const urlObject = new URL(url);\n    const { id, isPlaylist } = (_a = getYoutubeVideoOrPlaylistId(urlObject)) !== null && _a !== void 0 ? _a : {};\n    if (!id) {\n        return null;\n    }\n    const embedUrl = new URL(`${getYoutubeEmbedUrl(nocookie, isPlaylist)}${id}`);\n    if (urlObject.searchParams.has('t')) {\n        embedUrl.searchParams.set('start', urlObject.searchParams.get('t').replaceAll('s', ''));\n    }\n    if (allowFullscreen === false) {\n        embedUrl.searchParams.set('fs', '0');\n    }\n    if (autoplay) {\n        embedUrl.searchParams.set('autoplay', '1');\n    }\n    if (ccLanguage) {\n        embedUrl.searchParams.set('cc_lang_pref', ccLanguage);\n    }\n    if (ccLoadPolicy) {\n        embedUrl.searchParams.set('cc_load_policy', '1');\n    }\n    if (!controls) {\n        embedUrl.searchParams.set('controls', '0');\n    }\n    if (disableKBcontrols) {\n        embedUrl.searchParams.set('disablekb', '1');\n    }\n    if (enableIFrameApi) {\n        embedUrl.searchParams.set('enablejsapi', '1');\n    }\n    if (endTime) {\n        embedUrl.searchParams.set('end', endTime.toString());\n    }\n    if (interfaceLanguage) {\n        embedUrl.searchParams.set('hl', interfaceLanguage);\n    }\n    if (ivLoadPolicy) {\n        embedUrl.searchParams.set('iv_load_policy', ivLoadPolicy.toString());\n    }\n    if (loop) {\n        embedUrl.searchParams.set('loop', '1');\n    }\n    if (modestBranding) {\n        embedUrl.searchParams.set('modestbranding', '1');\n    }\n    if (origin) {\n        embedUrl.searchParams.set('origin', origin);\n    }\n    if (playlist) {\n        embedUrl.searchParams.set('playlist', playlist);\n    }\n    if (startAt) {\n        embedUrl.searchParams.set('start', startAt.toString());\n    }\n    if (progressBarColor) {\n        embedUrl.searchParams.set('color', progressBarColor);\n    }\n    if (rel !== undefined) {\n        embedUrl.searchParams.set('rel', rel.toString());\n    }\n    return embedUrl.toString();\n};\n\n/**\n * This extension adds support for youtube videos.\n * @see https://www.tiptap.dev/api/nodes/youtube\n */\nconst Youtube = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Node.create({\n    name: 'youtube',\n    addOptions() {\n        return {\n            addPasteHandler: true,\n            allowFullscreen: true,\n            autoplay: false,\n            ccLanguage: undefined,\n            ccLoadPolicy: undefined,\n            controls: true,\n            disableKBcontrols: false,\n            enableIFrameApi: false,\n            endTime: 0,\n            height: 480,\n            interfaceLanguage: undefined,\n            ivLoadPolicy: 0,\n            loop: false,\n            modestBranding: false,\n            HTMLAttributes: {},\n            inline: false,\n            nocookie: false,\n            origin: '',\n            playlist: '',\n            progressBarColor: undefined,\n            width: 640,\n            rel: 1,\n        };\n    },\n    inline() {\n        return this.options.inline;\n    },\n    group() {\n        return this.options.inline ? 'inline' : 'block';\n    },\n    draggable: true,\n    addAttributes() {\n        return {\n            src: {\n                default: null,\n            },\n            start: {\n                default: 0,\n            },\n            width: {\n                default: this.options.width,\n            },\n            height: {\n                default: this.options.height,\n            },\n        };\n    },\n    parseHTML() {\n        return [\n            {\n                tag: 'div[data-youtube-video] iframe',\n            },\n        ];\n    },\n    addCommands() {\n        return {\n            setYoutubeVideo: (options) => ({ commands }) => {\n                if (!isValidYoutubeUrl(options.src)) {\n                    return false;\n                }\n                return commands.insertContent({\n                    type: this.name,\n                    attrs: options,\n                });\n            },\n        };\n    },\n    addPasteRules() {\n        if (!this.options.addPasteHandler) {\n            return [];\n        }\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.nodePasteRule)({\n                find: YOUTUBE_REGEX_GLOBAL,\n                type: this.type,\n                getAttributes: match => {\n                    return { src: match.input };\n                },\n            }),\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        const embedUrl = getEmbedUrlFromYoutubeUrl({\n            url: HTMLAttributes.src,\n            allowFullscreen: this.options.allowFullscreen,\n            autoplay: this.options.autoplay,\n            ccLanguage: this.options.ccLanguage,\n            ccLoadPolicy: this.options.ccLoadPolicy,\n            controls: this.options.controls,\n            disableKBcontrols: this.options.disableKBcontrols,\n            enableIFrameApi: this.options.enableIFrameApi,\n            endTime: this.options.endTime,\n            interfaceLanguage: this.options.interfaceLanguage,\n            ivLoadPolicy: this.options.ivLoadPolicy,\n            loop: this.options.loop,\n            modestBranding: this.options.modestBranding,\n            nocookie: this.options.nocookie,\n            origin: this.options.origin,\n            playlist: this.options.playlist,\n            progressBarColor: this.options.progressBarColor,\n            startAt: HTMLAttributes.start || 0,\n            rel: this.options.rel,\n        });\n        HTMLAttributes.src = embedUrl;\n        return [\n            'div',\n            { 'data-youtube-video': '' },\n            [\n                'iframe',\n                (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, {\n                    width: this.options.width,\n                    height: this.options.height,\n                    allowfullscreen: this.options.allowFullscreen,\n                    autoplay: this.options.autoplay,\n                    ccLanguage: this.options.ccLanguage,\n                    ccLoadPolicy: this.options.ccLoadPolicy,\n                    disableKBcontrols: this.options.disableKBcontrols,\n                    enableIFrameApi: this.options.enableIFrameApi,\n                    endTime: this.options.endTime,\n                    interfaceLanguage: this.options.interfaceLanguage,\n                    ivLoadPolicy: this.options.ivLoadPolicy,\n                    loop: this.options.loop,\n                    modestBranding: this.options.modestBranding,\n                    origin: this.options.origin,\n                    playlist: this.options.playlist,\n                    progressBarColor: this.options.progressBarColor,\n                    rel: this.options.rel,\n                }, HTMLAttributes),\n            ],\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-youtube@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-youtube/dist/index.js\n");

/***/ })

};
;