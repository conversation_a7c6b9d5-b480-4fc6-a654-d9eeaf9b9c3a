"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@egjs+list-differ@1.0.1";
exports.ids = ["vendor-chunks/@egjs+list-differ@1.0.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@egjs+list-differ@1.0.1/node_modules/@egjs/list-differ/dist/list-differ.esm.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@egjs+list-differ@1.0.1/node_modules/@egjs/list-differ/dist/list-differ.esm.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   diff: () => (/* binding */ diff)\n/* harmony export */ });\n/*\nCopyright (c) 2019-present NAVER Corp.\nname: @egjs/list-differ\nlicense: MIT\nauthor: NAVER Corp.\nrepository: https://github.com/naver/egjs-list-differ\nversion: 1.0.1\n*/\n/*\negjs-list-differ\nCopyright (c) 2019-present NAVER Corp.\nMIT license\n*/\nvar PolyMap =\n/*#__PURE__*/\nfunction () {\n  function PolyMap() {\n    this.keys = [];\n    this.values = [];\n  }\n\n  var __proto = PolyMap.prototype;\n\n  __proto.get = function (key) {\n    return this.values[this.keys.indexOf(key)];\n  };\n\n  __proto.set = function (key, value) {\n    var keys = this.keys;\n    var values = this.values;\n    var prevIndex = keys.indexOf(key);\n    var index = prevIndex === -1 ? keys.length : prevIndex;\n    keys[index] = key;\n    values[index] = value;\n  };\n\n  return PolyMap;\n}();\n\n/*\negjs-list-differ\nCopyright (c) 2019-present NAVER Corp.\nMIT license\n*/\nvar HashMap =\n/*#__PURE__*/\nfunction () {\n  function HashMap() {\n    this.object = {};\n  }\n\n  var __proto = HashMap.prototype;\n\n  __proto.get = function (key) {\n    return this.object[key];\n  };\n\n  __proto.set = function (key, value) {\n    this.object[key] = value;\n  };\n\n  return HashMap;\n}();\n\n/*\negjs-list-differ\nCopyright (c) 2019-present NAVER Corp.\nMIT license\n*/\nvar SUPPORT_MAP = typeof Map === \"function\";\n\n/*\negjs-list-differ\nCopyright (c) 2019-present NAVER Corp.\nMIT license\n*/\nvar Link =\n/*#__PURE__*/\nfunction () {\n  function Link() {}\n\n  var __proto = Link.prototype;\n\n  __proto.connect = function (prevLink, nextLink) {\n    this.prev = prevLink;\n    this.next = nextLink;\n    prevLink && (prevLink.next = this);\n    nextLink && (nextLink.prev = this);\n  };\n\n  __proto.disconnect = function () {\n    // In double linked list, diconnect the interconnected relationship.\n    var prevLink = this.prev;\n    var nextLink = this.next;\n    prevLink && (prevLink.next = nextLink);\n    nextLink && (nextLink.prev = prevLink);\n  };\n\n  __proto.getIndex = function () {\n    var link = this;\n    var index = -1;\n\n    while (link) {\n      link = link.prev;\n      ++index;\n    }\n\n    return index;\n  };\n\n  return Link;\n}();\n\n/*\negjs-list-differ\nCopyright (c) 2019-present NAVER Corp.\nMIT license\n*/\n\nfunction orderChanged(changed, fixed) {\n  // It is roughly in the order of these examples.\n  // 4, 6, 0, 2, 1, 3, 5, 7\n  var fromLinks = []; // 0, 1, 2, 3, 4, 5, 6, 7\n\n  var toLinks = [];\n  changed.forEach(function (_a) {\n    var from = _a[0],\n        to = _a[1];\n    var link = new Link();\n    fromLinks[from] = link;\n    toLinks[to] = link;\n  }); // `fromLinks` are connected to each other by double linked list.\n\n  fromLinks.forEach(function (link, i) {\n    link.connect(fromLinks[i - 1]);\n  });\n  return changed.filter(function (_, i) {\n    return !fixed[i];\n  }).map(function (_a, i) {\n    var from = _a[0],\n        to = _a[1];\n\n    if (from === to) {\n      return [0, 0];\n    }\n\n    var fromLink = fromLinks[from];\n    var toLink = toLinks[to - 1];\n    var fromIndex = fromLink.getIndex(); // Disconnect the link connected to `fromLink`.\n\n    fromLink.disconnect(); // Connect `fromLink` to the right of `toLink`.\n\n    if (!toLink) {\n      fromLink.connect(undefined, fromLinks[0]);\n    } else {\n      fromLink.connect(toLink, toLink.next);\n    }\n\n    var toIndex = fromLink.getIndex();\n    return [fromIndex, toIndex];\n  });\n}\n\nvar Result =\n/*#__PURE__*/\nfunction () {\n  function Result(prevList, list, added, removed, changed, maintained, changedBeforeAdded, fixed) {\n    this.prevList = prevList;\n    this.list = list;\n    this.added = added;\n    this.removed = removed;\n    this.changed = changed;\n    this.maintained = maintained;\n    this.changedBeforeAdded = changedBeforeAdded;\n    this.fixed = fixed;\n  }\n\n  var __proto = Result.prototype;\n  Object.defineProperty(__proto, \"ordered\", {\n    get: function () {\n      if (!this.cacheOrdered) {\n        this.caculateOrdered();\n      }\n\n      return this.cacheOrdered;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(__proto, \"pureChanged\", {\n    get: function () {\n      if (!this.cachePureChanged) {\n        this.caculateOrdered();\n      }\n\n      return this.cachePureChanged;\n    },\n    enumerable: true,\n    configurable: true\n  });\n\n  __proto.caculateOrdered = function () {\n    var ordered = orderChanged(this.changedBeforeAdded, this.fixed);\n    var changed = this.changed;\n    var pureChanged = [];\n    this.cacheOrdered = ordered.filter(function (_a, i) {\n      var from = _a[0],\n          to = _a[1];\n      var _b = changed[i],\n          fromBefore = _b[0],\n          toBefore = _b[1];\n\n      if (from !== to) {\n        pureChanged.push([fromBefore, toBefore]);\n        return true;\n      }\n    });\n    this.cachePureChanged = pureChanged;\n  };\n\n  return Result;\n}();\n\n/**\n *\n * @memberof eg.ListDiffer\n * @static\n * @function\n * @param - Previous List <ko> 이전 목록 </ko>\n * @param - List to Update <ko> 업데이트 할 목록 </ko>\n * @param - This callback function returns the key of the item. <ko> 아이템의 키를 반환하는 콜백 함수입니다.</ko>\n * @return - Returns the diff between `prevList` and `list` <ko> `prevList`와 `list`의 다른 점을 반환한다.</ko>\n * @example\n * import { diff } from \"@egjs/list-differ\";\n * // script => eg.ListDiffer.diff\n * const result = diff([0, 1, 2, 3, 4, 5], [7, 8, 0, 4, 3, 6, 2, 1], e => e);\n * // List before update\n * // [1, 2, 3, 4, 5]\n * console.log(result.prevList);\n * // Updated list\n * // [4, 3, 6, 2, 1]\n * console.log(result.list);\n * // Index array of values added to `list`\n * // [0, 1, 5]\n * console.log(result.added);\n * // Index array of values removed in `prevList`\n * // [5]\n * console.log(result.removed);\n * // An array of index pairs of `prevList` and `list` with different indexes from `prevList` and `list`\n * // [[0, 2], [4, 3], [3, 4], [2, 6], [1, 7]]\n * console.log(result.changed);\n * // The subset of `changed` and an array of index pairs that moved data directly. Indicate an array of absolute index pairs of `ordered`.(Formatted by: Array<[index of prevList, index of list]>)\n * // [[4, 3], [3, 4], [2, 6]]\n * console.log(result.pureChanged);\n * // An array of index pairs to be `ordered` that can synchronize `list` before adding data. (Formatted by: Array<[prevIndex, nextIndex]>)\n * // [[4, 1], [4, 2], [4, 3]]\n * console.log(result.ordered);\n * // An array of index pairs of `prevList` and `list` that have not been added/removed so data is preserved\n * // [[0, 2], [4, 3], [3, 4], [2, 6], [1, 7]]\n * console.log(result.maintained);\n */\n\nfunction diff(prevList, list, findKeyCallback) {\n  var mapClass = SUPPORT_MAP ? Map : findKeyCallback ? HashMap : PolyMap;\n\n  var callback = findKeyCallback || function (e) {\n    return e;\n  };\n\n  var added = [];\n  var removed = [];\n  var maintained = [];\n  var prevKeys = prevList.map(callback);\n  var keys = list.map(callback);\n  var prevKeyMap = new mapClass();\n  var keyMap = new mapClass();\n  var changedBeforeAdded = [];\n  var fixed = [];\n  var removedMap = {};\n  var changed = [];\n  var addedCount = 0;\n  var removedCount = 0; // Add prevKeys and keys to the hashmap.\n\n  prevKeys.forEach(function (key, prevListIndex) {\n    prevKeyMap.set(key, prevListIndex);\n  });\n  keys.forEach(function (key, listIndex) {\n    keyMap.set(key, listIndex);\n  }); // Compare `prevKeys` and `keys` and add them to `removed` if they are not in `keys`.\n\n  prevKeys.forEach(function (key, prevListIndex) {\n    var listIndex = keyMap.get(key); // In prevList, but not in list, it is removed.\n\n    if (typeof listIndex === \"undefined\") {\n      ++removedCount;\n      removed.push(prevListIndex);\n    } else {\n      removedMap[listIndex] = removedCount;\n    }\n  }); // Compare `prevKeys` and `keys` and add them to `added` if they are not in `prevKeys`.\n\n  keys.forEach(function (key, listIndex) {\n    var prevListIndex = prevKeyMap.get(key); // In list, but not in prevList, it is added.\n\n    if (typeof prevListIndex === \"undefined\") {\n      added.push(listIndex);\n      ++addedCount;\n    } else {\n      maintained.push([prevListIndex, listIndex]);\n      removedCount = removedMap[listIndex] || 0;\n      changedBeforeAdded.push([prevListIndex - removedCount, listIndex - addedCount]);\n      fixed.push(listIndex === prevListIndex);\n\n      if (prevListIndex !== listIndex) {\n        changed.push([prevListIndex, listIndex]);\n      }\n    }\n  }); // Sort by ascending order of 'to(list's index).\n\n  removed.reverse();\n  return new Result(prevList, list, added, removed, changed, maintained, changedBeforeAdded, fixed);\n}\n\n/**\n * A module that checks diff when values are added, removed, or changed in an array.\n * @ko 배열 또는 오브젝트에서 값이 추가되거나 삭제되거나 순서가 변경사항을 체크하는 모듈입니다.\n * @memberof eg\n */\n\nvar ListDiffer =\n/*#__PURE__*/\nfunction () {\n  /**\n   * @param - Initializing Data Array. <ko> 초기 설정할 데이터 배열.</ko>\n   * @param - This callback function returns the key of the item. <ko> 아이템의 키를 반환하는 콜백 함수입니다.</ko>\n   * @example\n   * import ListDiffer from \"@egjs/list-differ\";\n   * // script => eg.ListDiffer\n   * const differ = new ListDiffer([0, 1, 2, 3, 4, 5], e => e);\n   * const result = differ.update([7, 8, 0, 4, 3, 6, 2, 1]);\n   * // List before update\n   * // [1, 2, 3, 4, 5]\n   * console.log(result.prevList);\n   * // Updated list\n   * // [4, 3, 6, 2, 1]\n   * console.log(result.list);\n   * // Index array of values added to `list`.\n   * // [0, 1, 5]\n   * console.log(result.added);\n   * // Index array of values removed in `prevList`.\n   * // [5]\n   * console.log(result.removed);\n   * // An array of index pairs of `prevList` and `list` with different indexes from `prevList` and `list`.\n   * // [[0, 2], [4, 3], [3, 4], [2, 6], [1, 7]]\n   * console.log(result.changed);\n   * // The subset of `changed` and an array of index pairs that moved data directly. Indicate an array of absolute index pairs of `ordered`.(Formatted by: Array<[index of prevList, index of list]>)\n   * // [[4, 3], [3, 4], [2, 6]]\n   * console.log(result.pureChanged);\n   * // An array of index pairs to be `ordered` that can synchronize `list` before adding data. (Formatted by: Array<[prevIndex, nextIndex]>)\n   * // [[4, 1], [4, 2], [4, 3]]\n   * console.log(result.ordered);\n   * // An array of index pairs of `prevList` and `list` that have not been added/removed so data is preserved.\n   * // [[0, 2], [4, 3], [3, 4], [2, 6], [1, 7]]\n   * console.log(result.maintained);\n   */\n  function ListDiffer(list, findKeyCallback) {\n    if (list === void 0) {\n      list = [];\n    }\n\n    this.findKeyCallback = findKeyCallback;\n    this.list = [].slice.call(list);\n  }\n  /**\n   * Update list.\n   * @ko 리스트를 업데이트를 합니다.\n   * @param - List to update <ko> 업데이트할 리스트 </ko>\n   * @return - Returns the results of an update from `prevList` to `list`.<ko> `prevList`에서 `list`로 업데이트한 결과를 반환한다. </ko>\n   */\n\n\n  var __proto = ListDiffer.prototype;\n\n  __proto.update = function (list) {\n    var newData = [].slice.call(list);\n    var result = diff(this.list, newData, this.findKeyCallback);\n    this.list = newData;\n    return result;\n  };\n\n  return ListDiffer;\n}();\n\n/*\negjs-list-differ\nCopyright (c) 2019-present NAVER Corp.\nMIT license\n*/\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ListDiffer);\n\n//# sourceMappingURL=list-differ.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@egjs+list-differ@1.0.1/node_modules/@egjs/list-differ/dist/list-differ.esm.js\n");

/***/ })

};
;