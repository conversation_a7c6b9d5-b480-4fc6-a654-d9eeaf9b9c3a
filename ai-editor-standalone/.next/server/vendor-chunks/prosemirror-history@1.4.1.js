"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-history@1.4.1";
exports.ids = ["vendor-chunks/prosemirror-history@1.4.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/prosemirror-history@1.4.1/node_modules/prosemirror-history/dist/index.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/prosemirror-history@1.4.1/node_modules/prosemirror-history/dist/index.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closeHistory: () => (/* binding */ closeHistory),\n/* harmony export */   history: () => (/* binding */ history),\n/* harmony export */   redo: () => (/* binding */ redo),\n/* harmony export */   redoDepth: () => (/* binding */ redoDepth),\n/* harmony export */   redoNoScroll: () => (/* binding */ redoNoScroll),\n/* harmony export */   undo: () => (/* binding */ undo),\n/* harmony export */   undoDepth: () => (/* binding */ undoDepth),\n/* harmony export */   undoNoScroll: () => (/* binding */ undoNoScroll)\n/* harmony export */ });\n/* harmony import */ var rope_sequence__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rope-sequence */ \"(ssr)/./node_modules/.pnpm/rope-sequence@1.3.4/node_modules/rope-sequence/dist/index.js\");\n/* harmony import */ var prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-transform */ \"(ssr)/./node_modules/.pnpm/prosemirror-transform@1.10.4/node_modules/prosemirror-transform/dist/index.js\");\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/./node_modules/.pnpm/prosemirror-state@1.4.3/node_modules/prosemirror-state/dist/index.js\");\n\n\n\n\n// ProseMirror's history isn't simply a way to roll back to a previous\n// state, because ProseMirror supports applying changes without adding\n// them to the history (for example during collaboration).\n//\n// To this end, each 'Branch' (one for the undo history and one for\n// the redo history) keeps an array of 'Items', which can optionally\n// hold a step (an actual undoable change), and always hold a position\n// map (which is needed to move changes below them to apply to the\n// current document).\n//\n// An item that has both a step and a selection bookmark is the start\n// of an 'event' — a group of changes that will be undone or redone at\n// once. (It stores only the bookmark, since that way we don't have to\n// provide a document until the selection is actually applied, which\n// is useful when compressing.)\n// Used to schedule history compression\nconst max_empty_items = 500;\nclass Branch {\n    constructor(items, eventCount) {\n        this.items = items;\n        this.eventCount = eventCount;\n    }\n    // Pop the latest event off the branch's history and apply it\n    // to a document transform.\n    popEvent(state, preserveItems) {\n        if (this.eventCount == 0)\n            return null;\n        let end = this.items.length;\n        for (;; end--) {\n            let next = this.items.get(end - 1);\n            if (next.selection) {\n                --end;\n                break;\n            }\n        }\n        let remap, mapFrom;\n        if (preserveItems) {\n            remap = this.remapping(end, this.items.length);\n            mapFrom = remap.maps.length;\n        }\n        let transform = state.tr;\n        let selection, remaining;\n        let addAfter = [], addBefore = [];\n        this.items.forEach((item, i) => {\n            if (!item.step) {\n                if (!remap) {\n                    remap = this.remapping(end, i + 1);\n                    mapFrom = remap.maps.length;\n                }\n                mapFrom--;\n                addBefore.push(item);\n                return;\n            }\n            if (remap) {\n                addBefore.push(new Item(item.map));\n                let step = item.step.map(remap.slice(mapFrom)), map;\n                if (step && transform.maybeStep(step).doc) {\n                    map = transform.mapping.maps[transform.mapping.maps.length - 1];\n                    addAfter.push(new Item(map, undefined, undefined, addAfter.length + addBefore.length));\n                }\n                mapFrom--;\n                if (map)\n                    remap.appendMap(map, mapFrom);\n            }\n            else {\n                transform.maybeStep(item.step);\n            }\n            if (item.selection) {\n                selection = remap ? item.selection.map(remap.slice(mapFrom)) : item.selection;\n                remaining = new Branch(this.items.slice(0, end).append(addBefore.reverse().concat(addAfter)), this.eventCount - 1);\n                return false;\n            }\n        }, this.items.length, 0);\n        return { remaining: remaining, transform, selection: selection };\n    }\n    // Create a new branch with the given transform added.\n    addTransform(transform, selection, histOptions, preserveItems) {\n        let newItems = [], eventCount = this.eventCount;\n        let oldItems = this.items, lastItem = !preserveItems && oldItems.length ? oldItems.get(oldItems.length - 1) : null;\n        for (let i = 0; i < transform.steps.length; i++) {\n            let step = transform.steps[i].invert(transform.docs[i]);\n            let item = new Item(transform.mapping.maps[i], step, selection), merged;\n            if (merged = lastItem && lastItem.merge(item)) {\n                item = merged;\n                if (i)\n                    newItems.pop();\n                else\n                    oldItems = oldItems.slice(0, oldItems.length - 1);\n            }\n            newItems.push(item);\n            if (selection) {\n                eventCount++;\n                selection = undefined;\n            }\n            if (!preserveItems)\n                lastItem = item;\n        }\n        let overflow = eventCount - histOptions.depth;\n        if (overflow > DEPTH_OVERFLOW) {\n            oldItems = cutOffEvents(oldItems, overflow);\n            eventCount -= overflow;\n        }\n        return new Branch(oldItems.append(newItems), eventCount);\n    }\n    remapping(from, to) {\n        let maps = new prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.Mapping;\n        this.items.forEach((item, i) => {\n            let mirrorPos = item.mirrorOffset != null && i - item.mirrorOffset >= from\n                ? maps.maps.length - item.mirrorOffset : undefined;\n            maps.appendMap(item.map, mirrorPos);\n        }, from, to);\n        return maps;\n    }\n    addMaps(array) {\n        if (this.eventCount == 0)\n            return this;\n        return new Branch(this.items.append(array.map(map => new Item(map))), this.eventCount);\n    }\n    // When the collab module receives remote changes, the history has\n    // to know about those, so that it can adjust the steps that were\n    // rebased on top of the remote changes, and include the position\n    // maps for the remote changes in its array of items.\n    rebased(rebasedTransform, rebasedCount) {\n        if (!this.eventCount)\n            return this;\n        let rebasedItems = [], start = Math.max(0, this.items.length - rebasedCount);\n        let mapping = rebasedTransform.mapping;\n        let newUntil = rebasedTransform.steps.length;\n        let eventCount = this.eventCount;\n        this.items.forEach(item => { if (item.selection)\n            eventCount--; }, start);\n        let iRebased = rebasedCount;\n        this.items.forEach(item => {\n            let pos = mapping.getMirror(--iRebased);\n            if (pos == null)\n                return;\n            newUntil = Math.min(newUntil, pos);\n            let map = mapping.maps[pos];\n            if (item.step) {\n                let step = rebasedTransform.steps[pos].invert(rebasedTransform.docs[pos]);\n                let selection = item.selection && item.selection.map(mapping.slice(iRebased + 1, pos));\n                if (selection)\n                    eventCount++;\n                rebasedItems.push(new Item(map, step, selection));\n            }\n            else {\n                rebasedItems.push(new Item(map));\n            }\n        }, start);\n        let newMaps = [];\n        for (let i = rebasedCount; i < newUntil; i++)\n            newMaps.push(new Item(mapping.maps[i]));\n        let items = this.items.slice(0, start).append(newMaps).append(rebasedItems);\n        let branch = new Branch(items, eventCount);\n        if (branch.emptyItemCount() > max_empty_items)\n            branch = branch.compress(this.items.length - rebasedItems.length);\n        return branch;\n    }\n    emptyItemCount() {\n        let count = 0;\n        this.items.forEach(item => { if (!item.step)\n            count++; });\n        return count;\n    }\n    // Compressing a branch means rewriting it to push the air (map-only\n    // items) out. During collaboration, these naturally accumulate\n    // because each remote change adds one. The `upto` argument is used\n    // to ensure that only the items below a given level are compressed,\n    // because `rebased` relies on a clean, untouched set of items in\n    // order to associate old items with rebased steps.\n    compress(upto = this.items.length) {\n        let remap = this.remapping(0, upto), mapFrom = remap.maps.length;\n        let items = [], events = 0;\n        this.items.forEach((item, i) => {\n            if (i >= upto) {\n                items.push(item);\n                if (item.selection)\n                    events++;\n            }\n            else if (item.step) {\n                let step = item.step.map(remap.slice(mapFrom)), map = step && step.getMap();\n                mapFrom--;\n                if (map)\n                    remap.appendMap(map, mapFrom);\n                if (step) {\n                    let selection = item.selection && item.selection.map(remap.slice(mapFrom));\n                    if (selection)\n                        events++;\n                    let newItem = new Item(map.invert(), step, selection), merged, last = items.length - 1;\n                    if (merged = items.length && items[last].merge(newItem))\n                        items[last] = merged;\n                    else\n                        items.push(newItem);\n                }\n            }\n            else if (item.map) {\n                mapFrom--;\n            }\n        }, this.items.length, 0);\n        return new Branch(rope_sequence__WEBPACK_IMPORTED_MODULE_0__[\"default\"].from(items.reverse()), events);\n    }\n}\nBranch.empty = new Branch(rope_sequence__WEBPACK_IMPORTED_MODULE_0__[\"default\"].empty, 0);\nfunction cutOffEvents(items, n) {\n    let cutPoint;\n    items.forEach((item, i) => {\n        if (item.selection && (n-- == 0)) {\n            cutPoint = i;\n            return false;\n        }\n    });\n    return items.slice(cutPoint);\n}\nclass Item {\n    constructor(\n    // The (forward) step map for this item.\n    map, \n    // The inverted step\n    step, \n    // If this is non-null, this item is the start of a group, and\n    // this selection is the starting selection for the group (the one\n    // that was active before the first step was applied)\n    selection, \n    // If this item is the inverse of a previous mapping on the stack,\n    // this points at the inverse's offset\n    mirrorOffset) {\n        this.map = map;\n        this.step = step;\n        this.selection = selection;\n        this.mirrorOffset = mirrorOffset;\n    }\n    merge(other) {\n        if (this.step && other.step && !other.selection) {\n            let step = other.step.merge(this.step);\n            if (step)\n                return new Item(step.getMap().invert(), step, this.selection);\n        }\n    }\n}\n// The value of the state field that tracks undo/redo history for that\n// state. Will be stored in the plugin state when the history plugin\n// is active.\nclass HistoryState {\n    constructor(done, undone, prevRanges, prevTime, prevComposition) {\n        this.done = done;\n        this.undone = undone;\n        this.prevRanges = prevRanges;\n        this.prevTime = prevTime;\n        this.prevComposition = prevComposition;\n    }\n}\nconst DEPTH_OVERFLOW = 20;\n// Record a transformation in undo history.\nfunction applyTransaction(history, state, tr, options) {\n    let historyTr = tr.getMeta(historyKey), rebased;\n    if (historyTr)\n        return historyTr.historyState;\n    if (tr.getMeta(closeHistoryKey))\n        history = new HistoryState(history.done, history.undone, null, 0, -1);\n    let appended = tr.getMeta(\"appendedTransaction\");\n    if (tr.steps.length == 0) {\n        return history;\n    }\n    else if (appended && appended.getMeta(historyKey)) {\n        if (appended.getMeta(historyKey).redo)\n            return new HistoryState(history.done.addTransform(tr, undefined, options, mustPreserveItems(state)), history.undone, rangesFor(tr.mapping.maps), history.prevTime, history.prevComposition);\n        else\n            return new HistoryState(history.done, history.undone.addTransform(tr, undefined, options, mustPreserveItems(state)), null, history.prevTime, history.prevComposition);\n    }\n    else if (tr.getMeta(\"addToHistory\") !== false && !(appended && appended.getMeta(\"addToHistory\") === false)) {\n        // Group transforms that occur in quick succession into one event.\n        let composition = tr.getMeta(\"composition\");\n        let newGroup = history.prevTime == 0 ||\n            (!appended && history.prevComposition != composition &&\n                (history.prevTime < (tr.time || 0) - options.newGroupDelay || !isAdjacentTo(tr, history.prevRanges)));\n        let prevRanges = appended ? mapRanges(history.prevRanges, tr.mapping) : rangesFor(tr.mapping.maps);\n        return new HistoryState(history.done.addTransform(tr, newGroup ? state.selection.getBookmark() : undefined, options, mustPreserveItems(state)), Branch.empty, prevRanges, tr.time, composition == null ? history.prevComposition : composition);\n    }\n    else if (rebased = tr.getMeta(\"rebased\")) {\n        // Used by the collab module to tell the history that some of its\n        // content has been rebased.\n        return new HistoryState(history.done.rebased(tr, rebased), history.undone.rebased(tr, rebased), mapRanges(history.prevRanges, tr.mapping), history.prevTime, history.prevComposition);\n    }\n    else {\n        return new HistoryState(history.done.addMaps(tr.mapping.maps), history.undone.addMaps(tr.mapping.maps), mapRanges(history.prevRanges, tr.mapping), history.prevTime, history.prevComposition);\n    }\n}\nfunction isAdjacentTo(transform, prevRanges) {\n    if (!prevRanges)\n        return false;\n    if (!transform.docChanged)\n        return true;\n    let adjacent = false;\n    transform.mapping.maps[0].forEach((start, end) => {\n        for (let i = 0; i < prevRanges.length; i += 2)\n            if (start <= prevRanges[i + 1] && end >= prevRanges[i])\n                adjacent = true;\n    });\n    return adjacent;\n}\nfunction rangesFor(maps) {\n    let result = [];\n    for (let i = maps.length - 1; i >= 0 && result.length == 0; i--)\n        maps[i].forEach((_from, _to, from, to) => result.push(from, to));\n    return result;\n}\nfunction mapRanges(ranges, mapping) {\n    if (!ranges)\n        return null;\n    let result = [];\n    for (let i = 0; i < ranges.length; i += 2) {\n        let from = mapping.map(ranges[i], 1), to = mapping.map(ranges[i + 1], -1);\n        if (from <= to)\n            result.push(from, to);\n    }\n    return result;\n}\n// Apply the latest event from one branch to the document and shift the event\n// onto the other branch.\nfunction histTransaction(history, state, redo) {\n    let preserveItems = mustPreserveItems(state);\n    let histOptions = historyKey.get(state).spec.config;\n    let pop = (redo ? history.undone : history.done).popEvent(state, preserveItems);\n    if (!pop)\n        return null;\n    let selection = pop.selection.resolve(pop.transform.doc);\n    let added = (redo ? history.done : history.undone).addTransform(pop.transform, state.selection.getBookmark(), histOptions, preserveItems);\n    let newHist = new HistoryState(redo ? added : pop.remaining, redo ? pop.remaining : added, null, 0, -1);\n    return pop.transform.setSelection(selection).setMeta(historyKey, { redo, historyState: newHist });\n}\nlet cachedPreserveItems = false, cachedPreserveItemsPlugins = null;\n// Check whether any plugin in the given state has a\n// `historyPreserveItems` property in its spec, in which case we must\n// preserve steps exactly as they came in, so that they can be\n// rebased.\nfunction mustPreserveItems(state) {\n    let plugins = state.plugins;\n    if (cachedPreserveItemsPlugins != plugins) {\n        cachedPreserveItems = false;\n        cachedPreserveItemsPlugins = plugins;\n        for (let i = 0; i < plugins.length; i++)\n            if (plugins[i].spec.historyPreserveItems) {\n                cachedPreserveItems = true;\n                break;\n            }\n    }\n    return cachedPreserveItems;\n}\n/**\nSet a flag on the given transaction that will prevent further steps\nfrom being appended to an existing history event (so that they\nrequire a separate undo command to undo).\n*/\nfunction closeHistory(tr) {\n    return tr.setMeta(closeHistoryKey, true);\n}\nconst historyKey = new prosemirror_state__WEBPACK_IMPORTED_MODULE_2__.PluginKey(\"history\");\nconst closeHistoryKey = new prosemirror_state__WEBPACK_IMPORTED_MODULE_2__.PluginKey(\"closeHistory\");\n/**\nReturns a plugin that enables the undo history for an editor. The\nplugin will track undo and redo stacks, which can be used with the\n[`undo`](https://prosemirror.net/docs/ref/#history.undo) and [`redo`](https://prosemirror.net/docs/ref/#history.redo) commands.\n\nYou can set an `\"addToHistory\"` [metadata\nproperty](https://prosemirror.net/docs/ref/#state.Transaction.setMeta) of `false` on a transaction\nto prevent it from being rolled back by undo.\n*/\nfunction history(config = {}) {\n    config = { depth: config.depth || 100,\n        newGroupDelay: config.newGroupDelay || 500 };\n    return new prosemirror_state__WEBPACK_IMPORTED_MODULE_2__.Plugin({\n        key: historyKey,\n        state: {\n            init() {\n                return new HistoryState(Branch.empty, Branch.empty, null, 0, -1);\n            },\n            apply(tr, hist, state) {\n                return applyTransaction(hist, state, tr, config);\n            }\n        },\n        config,\n        props: {\n            handleDOMEvents: {\n                beforeinput(view, e) {\n                    let inputType = e.inputType;\n                    let command = inputType == \"historyUndo\" ? undo : inputType == \"historyRedo\" ? redo : null;\n                    if (!command)\n                        return false;\n                    e.preventDefault();\n                    return command(view.state, view.dispatch);\n                }\n            }\n        }\n    });\n}\nfunction buildCommand(redo, scroll) {\n    return (state, dispatch) => {\n        let hist = historyKey.getState(state);\n        if (!hist || (redo ? hist.undone : hist.done).eventCount == 0)\n            return false;\n        if (dispatch) {\n            let tr = histTransaction(hist, state, redo);\n            if (tr)\n                dispatch(scroll ? tr.scrollIntoView() : tr);\n        }\n        return true;\n    };\n}\n/**\nA command function that undoes the last change, if any.\n*/\nconst undo = buildCommand(false, true);\n/**\nA command function that redoes the last undone change, if any.\n*/\nconst redo = buildCommand(true, true);\n/**\nA command function that undoes the last change. Don't scroll the\nselection into view.\n*/\nconst undoNoScroll = buildCommand(false, false);\n/**\nA command function that redoes the last undone change. Don't\nscroll the selection into view.\n*/\nconst redoNoScroll = buildCommand(true, false);\n/**\nThe amount of undoable events available in a given state.\n*/\nfunction undoDepth(state) {\n    let hist = historyKey.getState(state);\n    return hist ? hist.done.eventCount : 0;\n}\n/**\nThe amount of redoable events available in a given editor state.\n*/\nfunction redoDepth(state) {\n    let hist = historyKey.getState(state);\n    return hist ? hist.undone.eventCount : 0;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcHJvc2VtaXJyb3ItaGlzdG9yeUAxLjQuMS9ub2RlX21vZHVsZXMvcHJvc2VtaXJyb3ItaGlzdG9yeS9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQXlDO0FBQ087QUFDTTs7QUFFdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsNEJBQTRCO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLDBEQUFPO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQztBQUNyQywyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLG1DQUFtQyxjQUFjO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQztBQUNyQyxzQkFBc0I7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULDBCQUEwQixxREFBWTtBQUN0QztBQUNBO0FBQ0EsMEJBQTBCLHFEQUFZO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsdUJBQXVCO0FBQy9DO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0MsOEJBQThCO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLG1CQUFtQjtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUVBQXVFLDZCQUE2QjtBQUNwRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLG9CQUFvQjtBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsd0RBQVM7QUFDaEMsNEJBQTRCLHdEQUFTO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCO0FBQzVCLGVBQWU7QUFDZjtBQUNBLGVBQWUscURBQU07QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUUrRiIsInNvdXJjZXMiOlsiL2hvbWUvbXV6aS9naXRodWIvZGVlci1mbG93L2FpLWVkaXRvci1zdGFuZGFsb25lL25vZGVfbW9kdWxlcy8ucG5wbS9wcm9zZW1pcnJvci1oaXN0b3J5QDEuNC4xL25vZGVfbW9kdWxlcy9wcm9zZW1pcnJvci1oaXN0b3J5L2Rpc3QvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJvcGVTZXF1ZW5jZSBmcm9tICdyb3BlLXNlcXVlbmNlJztcbmltcG9ydCB7IE1hcHBpbmcgfSBmcm9tICdwcm9zZW1pcnJvci10cmFuc2Zvcm0nO1xuaW1wb3J0IHsgUGx1Z2luS2V5LCBQbHVnaW4gfSBmcm9tICdwcm9zZW1pcnJvci1zdGF0ZSc7XG5cbi8vIFByb3NlTWlycm9yJ3MgaGlzdG9yeSBpc24ndCBzaW1wbHkgYSB3YXkgdG8gcm9sbCBiYWNrIHRvIGEgcHJldmlvdXNcbi8vIHN0YXRlLCBiZWNhdXNlIFByb3NlTWlycm9yIHN1cHBvcnRzIGFwcGx5aW5nIGNoYW5nZXMgd2l0aG91dCBhZGRpbmdcbi8vIHRoZW0gdG8gdGhlIGhpc3RvcnkgKGZvciBleGFtcGxlIGR1cmluZyBjb2xsYWJvcmF0aW9uKS5cbi8vXG4vLyBUbyB0aGlzIGVuZCwgZWFjaCAnQnJhbmNoJyAob25lIGZvciB0aGUgdW5kbyBoaXN0b3J5IGFuZCBvbmUgZm9yXG4vLyB0aGUgcmVkbyBoaXN0b3J5KSBrZWVwcyBhbiBhcnJheSBvZiAnSXRlbXMnLCB3aGljaCBjYW4gb3B0aW9uYWxseVxuLy8gaG9sZCBhIHN0ZXAgKGFuIGFjdHVhbCB1bmRvYWJsZSBjaGFuZ2UpLCBhbmQgYWx3YXlzIGhvbGQgYSBwb3NpdGlvblxuLy8gbWFwICh3aGljaCBpcyBuZWVkZWQgdG8gbW92ZSBjaGFuZ2VzIGJlbG93IHRoZW0gdG8gYXBwbHkgdG8gdGhlXG4vLyBjdXJyZW50IGRvY3VtZW50KS5cbi8vXG4vLyBBbiBpdGVtIHRoYXQgaGFzIGJvdGggYSBzdGVwIGFuZCBhIHNlbGVjdGlvbiBib29rbWFyayBpcyB0aGUgc3RhcnRcbi8vIG9mIGFuICdldmVudCcg4oCUIGEgZ3JvdXAgb2YgY2hhbmdlcyB0aGF0IHdpbGwgYmUgdW5kb25lIG9yIHJlZG9uZSBhdFxuLy8gb25jZS4gKEl0IHN0b3JlcyBvbmx5IHRoZSBib29rbWFyaywgc2luY2UgdGhhdCB3YXkgd2UgZG9uJ3QgaGF2ZSB0b1xuLy8gcHJvdmlkZSBhIGRvY3VtZW50IHVudGlsIHRoZSBzZWxlY3Rpb24gaXMgYWN0dWFsbHkgYXBwbGllZCwgd2hpY2hcbi8vIGlzIHVzZWZ1bCB3aGVuIGNvbXByZXNzaW5nLilcbi8vIFVzZWQgdG8gc2NoZWR1bGUgaGlzdG9yeSBjb21wcmVzc2lvblxuY29uc3QgbWF4X2VtcHR5X2l0ZW1zID0gNTAwO1xuY2xhc3MgQnJhbmNoIHtcbiAgICBjb25zdHJ1Y3RvcihpdGVtcywgZXZlbnRDb3VudCkge1xuICAgICAgICB0aGlzLml0ZW1zID0gaXRlbXM7XG4gICAgICAgIHRoaXMuZXZlbnRDb3VudCA9IGV2ZW50Q291bnQ7XG4gICAgfVxuICAgIC8vIFBvcCB0aGUgbGF0ZXN0IGV2ZW50IG9mZiB0aGUgYnJhbmNoJ3MgaGlzdG9yeSBhbmQgYXBwbHkgaXRcbiAgICAvLyB0byBhIGRvY3VtZW50IHRyYW5zZm9ybS5cbiAgICBwb3BFdmVudChzdGF0ZSwgcHJlc2VydmVJdGVtcykge1xuICAgICAgICBpZiAodGhpcy5ldmVudENvdW50ID09IDApXG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgbGV0IGVuZCA9IHRoaXMuaXRlbXMubGVuZ3RoO1xuICAgICAgICBmb3IgKDs7IGVuZC0tKSB7XG4gICAgICAgICAgICBsZXQgbmV4dCA9IHRoaXMuaXRlbXMuZ2V0KGVuZCAtIDEpO1xuICAgICAgICAgICAgaWYgKG5leHQuc2VsZWN0aW9uKSB7XG4gICAgICAgICAgICAgICAgLS1lbmQ7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgbGV0IHJlbWFwLCBtYXBGcm9tO1xuICAgICAgICBpZiAocHJlc2VydmVJdGVtcykge1xuICAgICAgICAgICAgcmVtYXAgPSB0aGlzLnJlbWFwcGluZyhlbmQsIHRoaXMuaXRlbXMubGVuZ3RoKTtcbiAgICAgICAgICAgIG1hcEZyb20gPSByZW1hcC5tYXBzLmxlbmd0aDtcbiAgICAgICAgfVxuICAgICAgICBsZXQgdHJhbnNmb3JtID0gc3RhdGUudHI7XG4gICAgICAgIGxldCBzZWxlY3Rpb24sIHJlbWFpbmluZztcbiAgICAgICAgbGV0IGFkZEFmdGVyID0gW10sIGFkZEJlZm9yZSA9IFtdO1xuICAgICAgICB0aGlzLml0ZW1zLmZvckVhY2goKGl0ZW0sIGkpID0+IHtcbiAgICAgICAgICAgIGlmICghaXRlbS5zdGVwKSB7XG4gICAgICAgICAgICAgICAgaWYgKCFyZW1hcCkge1xuICAgICAgICAgICAgICAgICAgICByZW1hcCA9IHRoaXMucmVtYXBwaW5nKGVuZCwgaSArIDEpO1xuICAgICAgICAgICAgICAgICAgICBtYXBGcm9tID0gcmVtYXAubWFwcy5sZW5ndGg7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIG1hcEZyb20tLTtcbiAgICAgICAgICAgICAgICBhZGRCZWZvcmUucHVzaChpdGVtKTtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAocmVtYXApIHtcbiAgICAgICAgICAgICAgICBhZGRCZWZvcmUucHVzaChuZXcgSXRlbShpdGVtLm1hcCkpO1xuICAgICAgICAgICAgICAgIGxldCBzdGVwID0gaXRlbS5zdGVwLm1hcChyZW1hcC5zbGljZShtYXBGcm9tKSksIG1hcDtcbiAgICAgICAgICAgICAgICBpZiAoc3RlcCAmJiB0cmFuc2Zvcm0ubWF5YmVTdGVwKHN0ZXApLmRvYykge1xuICAgICAgICAgICAgICAgICAgICBtYXAgPSB0cmFuc2Zvcm0ubWFwcGluZy5tYXBzW3RyYW5zZm9ybS5tYXBwaW5nLm1hcHMubGVuZ3RoIC0gMV07XG4gICAgICAgICAgICAgICAgICAgIGFkZEFmdGVyLnB1c2gobmV3IEl0ZW0obWFwLCB1bmRlZmluZWQsIHVuZGVmaW5lZCwgYWRkQWZ0ZXIubGVuZ3RoICsgYWRkQmVmb3JlLmxlbmd0aCkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBtYXBGcm9tLS07XG4gICAgICAgICAgICAgICAgaWYgKG1hcClcbiAgICAgICAgICAgICAgICAgICAgcmVtYXAuYXBwZW5kTWFwKG1hcCwgbWFwRnJvbSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICB0cmFuc2Zvcm0ubWF5YmVTdGVwKGl0ZW0uc3RlcCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoaXRlbS5zZWxlY3Rpb24pIHtcbiAgICAgICAgICAgICAgICBzZWxlY3Rpb24gPSByZW1hcCA/IGl0ZW0uc2VsZWN0aW9uLm1hcChyZW1hcC5zbGljZShtYXBGcm9tKSkgOiBpdGVtLnNlbGVjdGlvbjtcbiAgICAgICAgICAgICAgICByZW1haW5pbmcgPSBuZXcgQnJhbmNoKHRoaXMuaXRlbXMuc2xpY2UoMCwgZW5kKS5hcHBlbmQoYWRkQmVmb3JlLnJldmVyc2UoKS5jb25jYXQoYWRkQWZ0ZXIpKSwgdGhpcy5ldmVudENvdW50IC0gMSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LCB0aGlzLml0ZW1zLmxlbmd0aCwgMCk7XG4gICAgICAgIHJldHVybiB7IHJlbWFpbmluZzogcmVtYWluaW5nLCB0cmFuc2Zvcm0sIHNlbGVjdGlvbjogc2VsZWN0aW9uIH07XG4gICAgfVxuICAgIC8vIENyZWF0ZSBhIG5ldyBicmFuY2ggd2l0aCB0aGUgZ2l2ZW4gdHJhbnNmb3JtIGFkZGVkLlxuICAgIGFkZFRyYW5zZm9ybSh0cmFuc2Zvcm0sIHNlbGVjdGlvbiwgaGlzdE9wdGlvbnMsIHByZXNlcnZlSXRlbXMpIHtcbiAgICAgICAgbGV0IG5ld0l0ZW1zID0gW10sIGV2ZW50Q291bnQgPSB0aGlzLmV2ZW50Q291bnQ7XG4gICAgICAgIGxldCBvbGRJdGVtcyA9IHRoaXMuaXRlbXMsIGxhc3RJdGVtID0gIXByZXNlcnZlSXRlbXMgJiYgb2xkSXRlbXMubGVuZ3RoID8gb2xkSXRlbXMuZ2V0KG9sZEl0ZW1zLmxlbmd0aCAtIDEpIDogbnVsbDtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0cmFuc2Zvcm0uc3RlcHMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgIGxldCBzdGVwID0gdHJhbnNmb3JtLnN0ZXBzW2ldLmludmVydCh0cmFuc2Zvcm0uZG9jc1tpXSk7XG4gICAgICAgICAgICBsZXQgaXRlbSA9IG5ldyBJdGVtKHRyYW5zZm9ybS5tYXBwaW5nLm1hcHNbaV0sIHN0ZXAsIHNlbGVjdGlvbiksIG1lcmdlZDtcbiAgICAgICAgICAgIGlmIChtZXJnZWQgPSBsYXN0SXRlbSAmJiBsYXN0SXRlbS5tZXJnZShpdGVtKSkge1xuICAgICAgICAgICAgICAgIGl0ZW0gPSBtZXJnZWQ7XG4gICAgICAgICAgICAgICAgaWYgKGkpXG4gICAgICAgICAgICAgICAgICAgIG5ld0l0ZW1zLnBvcCgpO1xuICAgICAgICAgICAgICAgIGVsc2VcbiAgICAgICAgICAgICAgICAgICAgb2xkSXRlbXMgPSBvbGRJdGVtcy5zbGljZSgwLCBvbGRJdGVtcy5sZW5ndGggLSAxKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIG5ld0l0ZW1zLnB1c2goaXRlbSk7XG4gICAgICAgICAgICBpZiAoc2VsZWN0aW9uKSB7XG4gICAgICAgICAgICAgICAgZXZlbnRDb3VudCsrO1xuICAgICAgICAgICAgICAgIHNlbGVjdGlvbiA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghcHJlc2VydmVJdGVtcylcbiAgICAgICAgICAgICAgICBsYXN0SXRlbSA9IGl0ZW07XG4gICAgICAgIH1cbiAgICAgICAgbGV0IG92ZXJmbG93ID0gZXZlbnRDb3VudCAtIGhpc3RPcHRpb25zLmRlcHRoO1xuICAgICAgICBpZiAob3ZlcmZsb3cgPiBERVBUSF9PVkVSRkxPVykge1xuICAgICAgICAgICAgb2xkSXRlbXMgPSBjdXRPZmZFdmVudHMob2xkSXRlbXMsIG92ZXJmbG93KTtcbiAgICAgICAgICAgIGV2ZW50Q291bnQgLT0gb3ZlcmZsb3c7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG5ldyBCcmFuY2gob2xkSXRlbXMuYXBwZW5kKG5ld0l0ZW1zKSwgZXZlbnRDb3VudCk7XG4gICAgfVxuICAgIHJlbWFwcGluZyhmcm9tLCB0bykge1xuICAgICAgICBsZXQgbWFwcyA9IG5ldyBNYXBwaW5nO1xuICAgICAgICB0aGlzLml0ZW1zLmZvckVhY2goKGl0ZW0sIGkpID0+IHtcbiAgICAgICAgICAgIGxldCBtaXJyb3JQb3MgPSBpdGVtLm1pcnJvck9mZnNldCAhPSBudWxsICYmIGkgLSBpdGVtLm1pcnJvck9mZnNldCA+PSBmcm9tXG4gICAgICAgICAgICAgICAgPyBtYXBzLm1hcHMubGVuZ3RoIC0gaXRlbS5taXJyb3JPZmZzZXQgOiB1bmRlZmluZWQ7XG4gICAgICAgICAgICBtYXBzLmFwcGVuZE1hcChpdGVtLm1hcCwgbWlycm9yUG9zKTtcbiAgICAgICAgfSwgZnJvbSwgdG8pO1xuICAgICAgICByZXR1cm4gbWFwcztcbiAgICB9XG4gICAgYWRkTWFwcyhhcnJheSkge1xuICAgICAgICBpZiAodGhpcy5ldmVudENvdW50ID09IDApXG4gICAgICAgICAgICByZXR1cm4gdGhpcztcbiAgICAgICAgcmV0dXJuIG5ldyBCcmFuY2godGhpcy5pdGVtcy5hcHBlbmQoYXJyYXkubWFwKG1hcCA9PiBuZXcgSXRlbShtYXApKSksIHRoaXMuZXZlbnRDb3VudCk7XG4gICAgfVxuICAgIC8vIFdoZW4gdGhlIGNvbGxhYiBtb2R1bGUgcmVjZWl2ZXMgcmVtb3RlIGNoYW5nZXMsIHRoZSBoaXN0b3J5IGhhc1xuICAgIC8vIHRvIGtub3cgYWJvdXQgdGhvc2UsIHNvIHRoYXQgaXQgY2FuIGFkanVzdCB0aGUgc3RlcHMgdGhhdCB3ZXJlXG4gICAgLy8gcmViYXNlZCBvbiB0b3Agb2YgdGhlIHJlbW90ZSBjaGFuZ2VzLCBhbmQgaW5jbHVkZSB0aGUgcG9zaXRpb25cbiAgICAvLyBtYXBzIGZvciB0aGUgcmVtb3RlIGNoYW5nZXMgaW4gaXRzIGFycmF5IG9mIGl0ZW1zLlxuICAgIHJlYmFzZWQocmViYXNlZFRyYW5zZm9ybSwgcmViYXNlZENvdW50KSB7XG4gICAgICAgIGlmICghdGhpcy5ldmVudENvdW50KVxuICAgICAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgICAgIGxldCByZWJhc2VkSXRlbXMgPSBbXSwgc3RhcnQgPSBNYXRoLm1heCgwLCB0aGlzLml0ZW1zLmxlbmd0aCAtIHJlYmFzZWRDb3VudCk7XG4gICAgICAgIGxldCBtYXBwaW5nID0gcmViYXNlZFRyYW5zZm9ybS5tYXBwaW5nO1xuICAgICAgICBsZXQgbmV3VW50aWwgPSByZWJhc2VkVHJhbnNmb3JtLnN0ZXBzLmxlbmd0aDtcbiAgICAgICAgbGV0IGV2ZW50Q291bnQgPSB0aGlzLmV2ZW50Q291bnQ7XG4gICAgICAgIHRoaXMuaXRlbXMuZm9yRWFjaChpdGVtID0+IHsgaWYgKGl0ZW0uc2VsZWN0aW9uKVxuICAgICAgICAgICAgZXZlbnRDb3VudC0tOyB9LCBzdGFydCk7XG4gICAgICAgIGxldCBpUmViYXNlZCA9IHJlYmFzZWRDb3VudDtcbiAgICAgICAgdGhpcy5pdGVtcy5mb3JFYWNoKGl0ZW0gPT4ge1xuICAgICAgICAgICAgbGV0IHBvcyA9IG1hcHBpbmcuZ2V0TWlycm9yKC0taVJlYmFzZWQpO1xuICAgICAgICAgICAgaWYgKHBvcyA9PSBudWxsKVxuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIG5ld1VudGlsID0gTWF0aC5taW4obmV3VW50aWwsIHBvcyk7XG4gICAgICAgICAgICBsZXQgbWFwID0gbWFwcGluZy5tYXBzW3Bvc107XG4gICAgICAgICAgICBpZiAoaXRlbS5zdGVwKSB7XG4gICAgICAgICAgICAgICAgbGV0IHN0ZXAgPSByZWJhc2VkVHJhbnNmb3JtLnN0ZXBzW3Bvc10uaW52ZXJ0KHJlYmFzZWRUcmFuc2Zvcm0uZG9jc1twb3NdKTtcbiAgICAgICAgICAgICAgICBsZXQgc2VsZWN0aW9uID0gaXRlbS5zZWxlY3Rpb24gJiYgaXRlbS5zZWxlY3Rpb24ubWFwKG1hcHBpbmcuc2xpY2UoaVJlYmFzZWQgKyAxLCBwb3MpKTtcbiAgICAgICAgICAgICAgICBpZiAoc2VsZWN0aW9uKVxuICAgICAgICAgICAgICAgICAgICBldmVudENvdW50Kys7XG4gICAgICAgICAgICAgICAgcmViYXNlZEl0ZW1zLnB1c2gobmV3IEl0ZW0obWFwLCBzdGVwLCBzZWxlY3Rpb24pKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIHJlYmFzZWRJdGVtcy5wdXNoKG5ldyBJdGVtKG1hcCkpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LCBzdGFydCk7XG4gICAgICAgIGxldCBuZXdNYXBzID0gW107XG4gICAgICAgIGZvciAobGV0IGkgPSByZWJhc2VkQ291bnQ7IGkgPCBuZXdVbnRpbDsgaSsrKVxuICAgICAgICAgICAgbmV3TWFwcy5wdXNoKG5ldyBJdGVtKG1hcHBpbmcubWFwc1tpXSkpO1xuICAgICAgICBsZXQgaXRlbXMgPSB0aGlzLml0ZW1zLnNsaWNlKDAsIHN0YXJ0KS5hcHBlbmQobmV3TWFwcykuYXBwZW5kKHJlYmFzZWRJdGVtcyk7XG4gICAgICAgIGxldCBicmFuY2ggPSBuZXcgQnJhbmNoKGl0ZW1zLCBldmVudENvdW50KTtcbiAgICAgICAgaWYgKGJyYW5jaC5lbXB0eUl0ZW1Db3VudCgpID4gbWF4X2VtcHR5X2l0ZW1zKVxuICAgICAgICAgICAgYnJhbmNoID0gYnJhbmNoLmNvbXByZXNzKHRoaXMuaXRlbXMubGVuZ3RoIC0gcmViYXNlZEl0ZW1zLmxlbmd0aCk7XG4gICAgICAgIHJldHVybiBicmFuY2g7XG4gICAgfVxuICAgIGVtcHR5SXRlbUNvdW50KCkge1xuICAgICAgICBsZXQgY291bnQgPSAwO1xuICAgICAgICB0aGlzLml0ZW1zLmZvckVhY2goaXRlbSA9PiB7IGlmICghaXRlbS5zdGVwKVxuICAgICAgICAgICAgY291bnQrKzsgfSk7XG4gICAgICAgIHJldHVybiBjb3VudDtcbiAgICB9XG4gICAgLy8gQ29tcHJlc3NpbmcgYSBicmFuY2ggbWVhbnMgcmV3cml0aW5nIGl0IHRvIHB1c2ggdGhlIGFpciAobWFwLW9ubHlcbiAgICAvLyBpdGVtcykgb3V0LiBEdXJpbmcgY29sbGFib3JhdGlvbiwgdGhlc2UgbmF0dXJhbGx5IGFjY3VtdWxhdGVcbiAgICAvLyBiZWNhdXNlIGVhY2ggcmVtb3RlIGNoYW5nZSBhZGRzIG9uZS4gVGhlIGB1cHRvYCBhcmd1bWVudCBpcyB1c2VkXG4gICAgLy8gdG8gZW5zdXJlIHRoYXQgb25seSB0aGUgaXRlbXMgYmVsb3cgYSBnaXZlbiBsZXZlbCBhcmUgY29tcHJlc3NlZCxcbiAgICAvLyBiZWNhdXNlIGByZWJhc2VkYCByZWxpZXMgb24gYSBjbGVhbiwgdW50b3VjaGVkIHNldCBvZiBpdGVtcyBpblxuICAgIC8vIG9yZGVyIHRvIGFzc29jaWF0ZSBvbGQgaXRlbXMgd2l0aCByZWJhc2VkIHN0ZXBzLlxuICAgIGNvbXByZXNzKHVwdG8gPSB0aGlzLml0ZW1zLmxlbmd0aCkge1xuICAgICAgICBsZXQgcmVtYXAgPSB0aGlzLnJlbWFwcGluZygwLCB1cHRvKSwgbWFwRnJvbSA9IHJlbWFwLm1hcHMubGVuZ3RoO1xuICAgICAgICBsZXQgaXRlbXMgPSBbXSwgZXZlbnRzID0gMDtcbiAgICAgICAgdGhpcy5pdGVtcy5mb3JFYWNoKChpdGVtLCBpKSA9PiB7XG4gICAgICAgICAgICBpZiAoaSA+PSB1cHRvKSB7XG4gICAgICAgICAgICAgICAgaXRlbXMucHVzaChpdGVtKTtcbiAgICAgICAgICAgICAgICBpZiAoaXRlbS5zZWxlY3Rpb24pXG4gICAgICAgICAgICAgICAgICAgIGV2ZW50cysrO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSBpZiAoaXRlbS5zdGVwKSB7XG4gICAgICAgICAgICAgICAgbGV0IHN0ZXAgPSBpdGVtLnN0ZXAubWFwKHJlbWFwLnNsaWNlKG1hcEZyb20pKSwgbWFwID0gc3RlcCAmJiBzdGVwLmdldE1hcCgpO1xuICAgICAgICAgICAgICAgIG1hcEZyb20tLTtcbiAgICAgICAgICAgICAgICBpZiAobWFwKVxuICAgICAgICAgICAgICAgICAgICByZW1hcC5hcHBlbmRNYXAobWFwLCBtYXBGcm9tKTtcbiAgICAgICAgICAgICAgICBpZiAoc3RlcCkge1xuICAgICAgICAgICAgICAgICAgICBsZXQgc2VsZWN0aW9uID0gaXRlbS5zZWxlY3Rpb24gJiYgaXRlbS5zZWxlY3Rpb24ubWFwKHJlbWFwLnNsaWNlKG1hcEZyb20pKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHNlbGVjdGlvbilcbiAgICAgICAgICAgICAgICAgICAgICAgIGV2ZW50cysrO1xuICAgICAgICAgICAgICAgICAgICBsZXQgbmV3SXRlbSA9IG5ldyBJdGVtKG1hcC5pbnZlcnQoKSwgc3RlcCwgc2VsZWN0aW9uKSwgbWVyZ2VkLCBsYXN0ID0gaXRlbXMubGVuZ3RoIC0gMTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKG1lcmdlZCA9IGl0ZW1zLmxlbmd0aCAmJiBpdGVtc1tsYXN0XS5tZXJnZShuZXdJdGVtKSlcbiAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW1zW2xhc3RdID0gbWVyZ2VkO1xuICAgICAgICAgICAgICAgICAgICBlbHNlXG4gICAgICAgICAgICAgICAgICAgICAgICBpdGVtcy5wdXNoKG5ld0l0ZW0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKGl0ZW0ubWFwKSB7XG4gICAgICAgICAgICAgICAgbWFwRnJvbS0tO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LCB0aGlzLml0ZW1zLmxlbmd0aCwgMCk7XG4gICAgICAgIHJldHVybiBuZXcgQnJhbmNoKFJvcGVTZXF1ZW5jZS5mcm9tKGl0ZW1zLnJldmVyc2UoKSksIGV2ZW50cyk7XG4gICAgfVxufVxuQnJhbmNoLmVtcHR5ID0gbmV3IEJyYW5jaChSb3BlU2VxdWVuY2UuZW1wdHksIDApO1xuZnVuY3Rpb24gY3V0T2ZmRXZlbnRzKGl0ZW1zLCBuKSB7XG4gICAgbGV0IGN1dFBvaW50O1xuICAgIGl0ZW1zLmZvckVhY2goKGl0ZW0sIGkpID0+IHtcbiAgICAgICAgaWYgKGl0ZW0uc2VsZWN0aW9uICYmIChuLS0gPT0gMCkpIHtcbiAgICAgICAgICAgIGN1dFBvaW50ID0gaTtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgIH0pO1xuICAgIHJldHVybiBpdGVtcy5zbGljZShjdXRQb2ludCk7XG59XG5jbGFzcyBJdGVtIHtcbiAgICBjb25zdHJ1Y3RvcihcbiAgICAvLyBUaGUgKGZvcndhcmQpIHN0ZXAgbWFwIGZvciB0aGlzIGl0ZW0uXG4gICAgbWFwLCBcbiAgICAvLyBUaGUgaW52ZXJ0ZWQgc3RlcFxuICAgIHN0ZXAsIFxuICAgIC8vIElmIHRoaXMgaXMgbm9uLW51bGwsIHRoaXMgaXRlbSBpcyB0aGUgc3RhcnQgb2YgYSBncm91cCwgYW5kXG4gICAgLy8gdGhpcyBzZWxlY3Rpb24gaXMgdGhlIHN0YXJ0aW5nIHNlbGVjdGlvbiBmb3IgdGhlIGdyb3VwICh0aGUgb25lXG4gICAgLy8gdGhhdCB3YXMgYWN0aXZlIGJlZm9yZSB0aGUgZmlyc3Qgc3RlcCB3YXMgYXBwbGllZClcbiAgICBzZWxlY3Rpb24sIFxuICAgIC8vIElmIHRoaXMgaXRlbSBpcyB0aGUgaW52ZXJzZSBvZiBhIHByZXZpb3VzIG1hcHBpbmcgb24gdGhlIHN0YWNrLFxuICAgIC8vIHRoaXMgcG9pbnRzIGF0IHRoZSBpbnZlcnNlJ3Mgb2Zmc2V0XG4gICAgbWlycm9yT2Zmc2V0KSB7XG4gICAgICAgIHRoaXMubWFwID0gbWFwO1xuICAgICAgICB0aGlzLnN0ZXAgPSBzdGVwO1xuICAgICAgICB0aGlzLnNlbGVjdGlvbiA9IHNlbGVjdGlvbjtcbiAgICAgICAgdGhpcy5taXJyb3JPZmZzZXQgPSBtaXJyb3JPZmZzZXQ7XG4gICAgfVxuICAgIG1lcmdlKG90aGVyKSB7XG4gICAgICAgIGlmICh0aGlzLnN0ZXAgJiYgb3RoZXIuc3RlcCAmJiAhb3RoZXIuc2VsZWN0aW9uKSB7XG4gICAgICAgICAgICBsZXQgc3RlcCA9IG90aGVyLnN0ZXAubWVyZ2UodGhpcy5zdGVwKTtcbiAgICAgICAgICAgIGlmIChzdGVwKVxuICAgICAgICAgICAgICAgIHJldHVybiBuZXcgSXRlbShzdGVwLmdldE1hcCgpLmludmVydCgpLCBzdGVwLCB0aGlzLnNlbGVjdGlvbik7XG4gICAgICAgIH1cbiAgICB9XG59XG4vLyBUaGUgdmFsdWUgb2YgdGhlIHN0YXRlIGZpZWxkIHRoYXQgdHJhY2tzIHVuZG8vcmVkbyBoaXN0b3J5IGZvciB0aGF0XG4vLyBzdGF0ZS4gV2lsbCBiZSBzdG9yZWQgaW4gdGhlIHBsdWdpbiBzdGF0ZSB3aGVuIHRoZSBoaXN0b3J5IHBsdWdpblxuLy8gaXMgYWN0aXZlLlxuY2xhc3MgSGlzdG9yeVN0YXRlIHtcbiAgICBjb25zdHJ1Y3Rvcihkb25lLCB1bmRvbmUsIHByZXZSYW5nZXMsIHByZXZUaW1lLCBwcmV2Q29tcG9zaXRpb24pIHtcbiAgICAgICAgdGhpcy5kb25lID0gZG9uZTtcbiAgICAgICAgdGhpcy51bmRvbmUgPSB1bmRvbmU7XG4gICAgICAgIHRoaXMucHJldlJhbmdlcyA9IHByZXZSYW5nZXM7XG4gICAgICAgIHRoaXMucHJldlRpbWUgPSBwcmV2VGltZTtcbiAgICAgICAgdGhpcy5wcmV2Q29tcG9zaXRpb24gPSBwcmV2Q29tcG9zaXRpb247XG4gICAgfVxufVxuY29uc3QgREVQVEhfT1ZFUkZMT1cgPSAyMDtcbi8vIFJlY29yZCBhIHRyYW5zZm9ybWF0aW9uIGluIHVuZG8gaGlzdG9yeS5cbmZ1bmN0aW9uIGFwcGx5VHJhbnNhY3Rpb24oaGlzdG9yeSwgc3RhdGUsIHRyLCBvcHRpb25zKSB7XG4gICAgbGV0IGhpc3RvcnlUciA9IHRyLmdldE1ldGEoaGlzdG9yeUtleSksIHJlYmFzZWQ7XG4gICAgaWYgKGhpc3RvcnlUcilcbiAgICAgICAgcmV0dXJuIGhpc3RvcnlUci5oaXN0b3J5U3RhdGU7XG4gICAgaWYgKHRyLmdldE1ldGEoY2xvc2VIaXN0b3J5S2V5KSlcbiAgICAgICAgaGlzdG9yeSA9IG5ldyBIaXN0b3J5U3RhdGUoaGlzdG9yeS5kb25lLCBoaXN0b3J5LnVuZG9uZSwgbnVsbCwgMCwgLTEpO1xuICAgIGxldCBhcHBlbmRlZCA9IHRyLmdldE1ldGEoXCJhcHBlbmRlZFRyYW5zYWN0aW9uXCIpO1xuICAgIGlmICh0ci5zdGVwcy5sZW5ndGggPT0gMCkge1xuICAgICAgICByZXR1cm4gaGlzdG9yeTtcbiAgICB9XG4gICAgZWxzZSBpZiAoYXBwZW5kZWQgJiYgYXBwZW5kZWQuZ2V0TWV0YShoaXN0b3J5S2V5KSkge1xuICAgICAgICBpZiAoYXBwZW5kZWQuZ2V0TWV0YShoaXN0b3J5S2V5KS5yZWRvKVxuICAgICAgICAgICAgcmV0dXJuIG5ldyBIaXN0b3J5U3RhdGUoaGlzdG9yeS5kb25lLmFkZFRyYW5zZm9ybSh0ciwgdW5kZWZpbmVkLCBvcHRpb25zLCBtdXN0UHJlc2VydmVJdGVtcyhzdGF0ZSkpLCBoaXN0b3J5LnVuZG9uZSwgcmFuZ2VzRm9yKHRyLm1hcHBpbmcubWFwcyksIGhpc3RvcnkucHJldlRpbWUsIGhpc3RvcnkucHJldkNvbXBvc2l0aW9uKTtcbiAgICAgICAgZWxzZVxuICAgICAgICAgICAgcmV0dXJuIG5ldyBIaXN0b3J5U3RhdGUoaGlzdG9yeS5kb25lLCBoaXN0b3J5LnVuZG9uZS5hZGRUcmFuc2Zvcm0odHIsIHVuZGVmaW5lZCwgb3B0aW9ucywgbXVzdFByZXNlcnZlSXRlbXMoc3RhdGUpKSwgbnVsbCwgaGlzdG9yeS5wcmV2VGltZSwgaGlzdG9yeS5wcmV2Q29tcG9zaXRpb24pO1xuICAgIH1cbiAgICBlbHNlIGlmICh0ci5nZXRNZXRhKFwiYWRkVG9IaXN0b3J5XCIpICE9PSBmYWxzZSAmJiAhKGFwcGVuZGVkICYmIGFwcGVuZGVkLmdldE1ldGEoXCJhZGRUb0hpc3RvcnlcIikgPT09IGZhbHNlKSkge1xuICAgICAgICAvLyBHcm91cCB0cmFuc2Zvcm1zIHRoYXQgb2NjdXIgaW4gcXVpY2sgc3VjY2Vzc2lvbiBpbnRvIG9uZSBldmVudC5cbiAgICAgICAgbGV0IGNvbXBvc2l0aW9uID0gdHIuZ2V0TWV0YShcImNvbXBvc2l0aW9uXCIpO1xuICAgICAgICBsZXQgbmV3R3JvdXAgPSBoaXN0b3J5LnByZXZUaW1lID09IDAgfHxcbiAgICAgICAgICAgICghYXBwZW5kZWQgJiYgaGlzdG9yeS5wcmV2Q29tcG9zaXRpb24gIT0gY29tcG9zaXRpb24gJiZcbiAgICAgICAgICAgICAgICAoaGlzdG9yeS5wcmV2VGltZSA8ICh0ci50aW1lIHx8IDApIC0gb3B0aW9ucy5uZXdHcm91cERlbGF5IHx8ICFpc0FkamFjZW50VG8odHIsIGhpc3RvcnkucHJldlJhbmdlcykpKTtcbiAgICAgICAgbGV0IHByZXZSYW5nZXMgPSBhcHBlbmRlZCA/IG1hcFJhbmdlcyhoaXN0b3J5LnByZXZSYW5nZXMsIHRyLm1hcHBpbmcpIDogcmFuZ2VzRm9yKHRyLm1hcHBpbmcubWFwcyk7XG4gICAgICAgIHJldHVybiBuZXcgSGlzdG9yeVN0YXRlKGhpc3RvcnkuZG9uZS5hZGRUcmFuc2Zvcm0odHIsIG5ld0dyb3VwID8gc3RhdGUuc2VsZWN0aW9uLmdldEJvb2ttYXJrKCkgOiB1bmRlZmluZWQsIG9wdGlvbnMsIG11c3RQcmVzZXJ2ZUl0ZW1zKHN0YXRlKSksIEJyYW5jaC5lbXB0eSwgcHJldlJhbmdlcywgdHIudGltZSwgY29tcG9zaXRpb24gPT0gbnVsbCA/IGhpc3RvcnkucHJldkNvbXBvc2l0aW9uIDogY29tcG9zaXRpb24pO1xuICAgIH1cbiAgICBlbHNlIGlmIChyZWJhc2VkID0gdHIuZ2V0TWV0YShcInJlYmFzZWRcIikpIHtcbiAgICAgICAgLy8gVXNlZCBieSB0aGUgY29sbGFiIG1vZHVsZSB0byB0ZWxsIHRoZSBoaXN0b3J5IHRoYXQgc29tZSBvZiBpdHNcbiAgICAgICAgLy8gY29udGVudCBoYXMgYmVlbiByZWJhc2VkLlxuICAgICAgICByZXR1cm4gbmV3IEhpc3RvcnlTdGF0ZShoaXN0b3J5LmRvbmUucmViYXNlZCh0ciwgcmViYXNlZCksIGhpc3RvcnkudW5kb25lLnJlYmFzZWQodHIsIHJlYmFzZWQpLCBtYXBSYW5nZXMoaGlzdG9yeS5wcmV2UmFuZ2VzLCB0ci5tYXBwaW5nKSwgaGlzdG9yeS5wcmV2VGltZSwgaGlzdG9yeS5wcmV2Q29tcG9zaXRpb24pO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIG5ldyBIaXN0b3J5U3RhdGUoaGlzdG9yeS5kb25lLmFkZE1hcHModHIubWFwcGluZy5tYXBzKSwgaGlzdG9yeS51bmRvbmUuYWRkTWFwcyh0ci5tYXBwaW5nLm1hcHMpLCBtYXBSYW5nZXMoaGlzdG9yeS5wcmV2UmFuZ2VzLCB0ci5tYXBwaW5nKSwgaGlzdG9yeS5wcmV2VGltZSwgaGlzdG9yeS5wcmV2Q29tcG9zaXRpb24pO1xuICAgIH1cbn1cbmZ1bmN0aW9uIGlzQWRqYWNlbnRUbyh0cmFuc2Zvcm0sIHByZXZSYW5nZXMpIHtcbiAgICBpZiAoIXByZXZSYW5nZXMpXG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICBpZiAoIXRyYW5zZm9ybS5kb2NDaGFuZ2VkKVxuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICBsZXQgYWRqYWNlbnQgPSBmYWxzZTtcbiAgICB0cmFuc2Zvcm0ubWFwcGluZy5tYXBzWzBdLmZvckVhY2goKHN0YXJ0LCBlbmQpID0+IHtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBwcmV2UmFuZ2VzLmxlbmd0aDsgaSArPSAyKVxuICAgICAgICAgICAgaWYgKHN0YXJ0IDw9IHByZXZSYW5nZXNbaSArIDFdICYmIGVuZCA+PSBwcmV2UmFuZ2VzW2ldKVxuICAgICAgICAgICAgICAgIGFkamFjZW50ID0gdHJ1ZTtcbiAgICB9KTtcbiAgICByZXR1cm4gYWRqYWNlbnQ7XG59XG5mdW5jdGlvbiByYW5nZXNGb3IobWFwcykge1xuICAgIGxldCByZXN1bHQgPSBbXTtcbiAgICBmb3IgKGxldCBpID0gbWFwcy5sZW5ndGggLSAxOyBpID49IDAgJiYgcmVzdWx0Lmxlbmd0aCA9PSAwOyBpLS0pXG4gICAgICAgIG1hcHNbaV0uZm9yRWFjaCgoX2Zyb20sIF90bywgZnJvbSwgdG8pID0+IHJlc3VsdC5wdXNoKGZyb20sIHRvKSk7XG4gICAgcmV0dXJuIHJlc3VsdDtcbn1cbmZ1bmN0aW9uIG1hcFJhbmdlcyhyYW5nZXMsIG1hcHBpbmcpIHtcbiAgICBpZiAoIXJhbmdlcylcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgbGV0IHJlc3VsdCA9IFtdO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcmFuZ2VzLmxlbmd0aDsgaSArPSAyKSB7XG4gICAgICAgIGxldCBmcm9tID0gbWFwcGluZy5tYXAocmFuZ2VzW2ldLCAxKSwgdG8gPSBtYXBwaW5nLm1hcChyYW5nZXNbaSArIDFdLCAtMSk7XG4gICAgICAgIGlmIChmcm9tIDw9IHRvKVxuICAgICAgICAgICAgcmVzdWx0LnB1c2goZnJvbSwgdG8pO1xuICAgIH1cbiAgICByZXR1cm4gcmVzdWx0O1xufVxuLy8gQXBwbHkgdGhlIGxhdGVzdCBldmVudCBmcm9tIG9uZSBicmFuY2ggdG8gdGhlIGRvY3VtZW50IGFuZCBzaGlmdCB0aGUgZXZlbnRcbi8vIG9udG8gdGhlIG90aGVyIGJyYW5jaC5cbmZ1bmN0aW9uIGhpc3RUcmFuc2FjdGlvbihoaXN0b3J5LCBzdGF0ZSwgcmVkbykge1xuICAgIGxldCBwcmVzZXJ2ZUl0ZW1zID0gbXVzdFByZXNlcnZlSXRlbXMoc3RhdGUpO1xuICAgIGxldCBoaXN0T3B0aW9ucyA9IGhpc3RvcnlLZXkuZ2V0KHN0YXRlKS5zcGVjLmNvbmZpZztcbiAgICBsZXQgcG9wID0gKHJlZG8gPyBoaXN0b3J5LnVuZG9uZSA6IGhpc3RvcnkuZG9uZSkucG9wRXZlbnQoc3RhdGUsIHByZXNlcnZlSXRlbXMpO1xuICAgIGlmICghcG9wKVxuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICBsZXQgc2VsZWN0aW9uID0gcG9wLnNlbGVjdGlvbi5yZXNvbHZlKHBvcC50cmFuc2Zvcm0uZG9jKTtcbiAgICBsZXQgYWRkZWQgPSAocmVkbyA/IGhpc3RvcnkuZG9uZSA6IGhpc3RvcnkudW5kb25lKS5hZGRUcmFuc2Zvcm0ocG9wLnRyYW5zZm9ybSwgc3RhdGUuc2VsZWN0aW9uLmdldEJvb2ttYXJrKCksIGhpc3RPcHRpb25zLCBwcmVzZXJ2ZUl0ZW1zKTtcbiAgICBsZXQgbmV3SGlzdCA9IG5ldyBIaXN0b3J5U3RhdGUocmVkbyA/IGFkZGVkIDogcG9wLnJlbWFpbmluZywgcmVkbyA/IHBvcC5yZW1haW5pbmcgOiBhZGRlZCwgbnVsbCwgMCwgLTEpO1xuICAgIHJldHVybiBwb3AudHJhbnNmb3JtLnNldFNlbGVjdGlvbihzZWxlY3Rpb24pLnNldE1ldGEoaGlzdG9yeUtleSwgeyByZWRvLCBoaXN0b3J5U3RhdGU6IG5ld0hpc3QgfSk7XG59XG5sZXQgY2FjaGVkUHJlc2VydmVJdGVtcyA9IGZhbHNlLCBjYWNoZWRQcmVzZXJ2ZUl0ZW1zUGx1Z2lucyA9IG51bGw7XG4vLyBDaGVjayB3aGV0aGVyIGFueSBwbHVnaW4gaW4gdGhlIGdpdmVuIHN0YXRlIGhhcyBhXG4vLyBgaGlzdG9yeVByZXNlcnZlSXRlbXNgIHByb3BlcnR5IGluIGl0cyBzcGVjLCBpbiB3aGljaCBjYXNlIHdlIG11c3Rcbi8vIHByZXNlcnZlIHN0ZXBzIGV4YWN0bHkgYXMgdGhleSBjYW1lIGluLCBzbyB0aGF0IHRoZXkgY2FuIGJlXG4vLyByZWJhc2VkLlxuZnVuY3Rpb24gbXVzdFByZXNlcnZlSXRlbXMoc3RhdGUpIHtcbiAgICBsZXQgcGx1Z2lucyA9IHN0YXRlLnBsdWdpbnM7XG4gICAgaWYgKGNhY2hlZFByZXNlcnZlSXRlbXNQbHVnaW5zICE9IHBsdWdpbnMpIHtcbiAgICAgICAgY2FjaGVkUHJlc2VydmVJdGVtcyA9IGZhbHNlO1xuICAgICAgICBjYWNoZWRQcmVzZXJ2ZUl0ZW1zUGx1Z2lucyA9IHBsdWdpbnM7XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcGx1Z2lucy5sZW5ndGg7IGkrKylcbiAgICAgICAgICAgIGlmIChwbHVnaW5zW2ldLnNwZWMuaGlzdG9yeVByZXNlcnZlSXRlbXMpIHtcbiAgICAgICAgICAgICAgICBjYWNoZWRQcmVzZXJ2ZUl0ZW1zID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGNhY2hlZFByZXNlcnZlSXRlbXM7XG59XG4vKipcblNldCBhIGZsYWcgb24gdGhlIGdpdmVuIHRyYW5zYWN0aW9uIHRoYXQgd2lsbCBwcmV2ZW50IGZ1cnRoZXIgc3RlcHNcbmZyb20gYmVpbmcgYXBwZW5kZWQgdG8gYW4gZXhpc3RpbmcgaGlzdG9yeSBldmVudCAoc28gdGhhdCB0aGV5XG5yZXF1aXJlIGEgc2VwYXJhdGUgdW5kbyBjb21tYW5kIHRvIHVuZG8pLlxuKi9cbmZ1bmN0aW9uIGNsb3NlSGlzdG9yeSh0cikge1xuICAgIHJldHVybiB0ci5zZXRNZXRhKGNsb3NlSGlzdG9yeUtleSwgdHJ1ZSk7XG59XG5jb25zdCBoaXN0b3J5S2V5ID0gbmV3IFBsdWdpbktleShcImhpc3RvcnlcIik7XG5jb25zdCBjbG9zZUhpc3RvcnlLZXkgPSBuZXcgUGx1Z2luS2V5KFwiY2xvc2VIaXN0b3J5XCIpO1xuLyoqXG5SZXR1cm5zIGEgcGx1Z2luIHRoYXQgZW5hYmxlcyB0aGUgdW5kbyBoaXN0b3J5IGZvciBhbiBlZGl0b3IuIFRoZVxucGx1Z2luIHdpbGwgdHJhY2sgdW5kbyBhbmQgcmVkbyBzdGFja3MsIHdoaWNoIGNhbiBiZSB1c2VkIHdpdGggdGhlXG5bYHVuZG9gXShodHRwczovL3Byb3NlbWlycm9yLm5ldC9kb2NzL3JlZi8jaGlzdG9yeS51bmRvKSBhbmQgW2ByZWRvYF0oaHR0cHM6Ly9wcm9zZW1pcnJvci5uZXQvZG9jcy9yZWYvI2hpc3RvcnkucmVkbykgY29tbWFuZHMuXG5cbllvdSBjYW4gc2V0IGFuIGBcImFkZFRvSGlzdG9yeVwiYCBbbWV0YWRhdGFcbnByb3BlcnR5XShodHRwczovL3Byb3NlbWlycm9yLm5ldC9kb2NzL3JlZi8jc3RhdGUuVHJhbnNhY3Rpb24uc2V0TWV0YSkgb2YgYGZhbHNlYCBvbiBhIHRyYW5zYWN0aW9uXG50byBwcmV2ZW50IGl0IGZyb20gYmVpbmcgcm9sbGVkIGJhY2sgYnkgdW5kby5cbiovXG5mdW5jdGlvbiBoaXN0b3J5KGNvbmZpZyA9IHt9KSB7XG4gICAgY29uZmlnID0geyBkZXB0aDogY29uZmlnLmRlcHRoIHx8IDEwMCxcbiAgICAgICAgbmV3R3JvdXBEZWxheTogY29uZmlnLm5ld0dyb3VwRGVsYXkgfHwgNTAwIH07XG4gICAgcmV0dXJuIG5ldyBQbHVnaW4oe1xuICAgICAgICBrZXk6IGhpc3RvcnlLZXksXG4gICAgICAgIHN0YXRlOiB7XG4gICAgICAgICAgICBpbml0KCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBuZXcgSGlzdG9yeVN0YXRlKEJyYW5jaC5lbXB0eSwgQnJhbmNoLmVtcHR5LCBudWxsLCAwLCAtMSk7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgYXBwbHkodHIsIGhpc3QsIHN0YXRlKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGFwcGx5VHJhbnNhY3Rpb24oaGlzdCwgc3RhdGUsIHRyLCBjb25maWcpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBjb25maWcsXG4gICAgICAgIHByb3BzOiB7XG4gICAgICAgICAgICBoYW5kbGVET01FdmVudHM6IHtcbiAgICAgICAgICAgICAgICBiZWZvcmVpbnB1dCh2aWV3LCBlKSB7XG4gICAgICAgICAgICAgICAgICAgIGxldCBpbnB1dFR5cGUgPSBlLmlucHV0VHlwZTtcbiAgICAgICAgICAgICAgICAgICAgbGV0IGNvbW1hbmQgPSBpbnB1dFR5cGUgPT0gXCJoaXN0b3J5VW5kb1wiID8gdW5kbyA6IGlucHV0VHlwZSA9PSBcImhpc3RvcnlSZWRvXCIgPyByZWRvIDogbnVsbDtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFjb21tYW5kKVxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBjb21tYW5kKHZpZXcuc3RhdGUsIHZpZXcuZGlzcGF0Y2gpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH0pO1xufVxuZnVuY3Rpb24gYnVpbGRDb21tYW5kKHJlZG8sIHNjcm9sbCkge1xuICAgIHJldHVybiAoc3RhdGUsIGRpc3BhdGNoKSA9PiB7XG4gICAgICAgIGxldCBoaXN0ID0gaGlzdG9yeUtleS5nZXRTdGF0ZShzdGF0ZSk7XG4gICAgICAgIGlmICghaGlzdCB8fCAocmVkbyA/IGhpc3QudW5kb25lIDogaGlzdC5kb25lKS5ldmVudENvdW50ID09IDApXG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIGlmIChkaXNwYXRjaCkge1xuICAgICAgICAgICAgbGV0IHRyID0gaGlzdFRyYW5zYWN0aW9uKGhpc3QsIHN0YXRlLCByZWRvKTtcbiAgICAgICAgICAgIGlmICh0cilcbiAgICAgICAgICAgICAgICBkaXNwYXRjaChzY3JvbGwgPyB0ci5zY3JvbGxJbnRvVmlldygpIDogdHIpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH07XG59XG4vKipcbkEgY29tbWFuZCBmdW5jdGlvbiB0aGF0IHVuZG9lcyB0aGUgbGFzdCBjaGFuZ2UsIGlmIGFueS5cbiovXG5jb25zdCB1bmRvID0gYnVpbGRDb21tYW5kKGZhbHNlLCB0cnVlKTtcbi8qKlxuQSBjb21tYW5kIGZ1bmN0aW9uIHRoYXQgcmVkb2VzIHRoZSBsYXN0IHVuZG9uZSBjaGFuZ2UsIGlmIGFueS5cbiovXG5jb25zdCByZWRvID0gYnVpbGRDb21tYW5kKHRydWUsIHRydWUpO1xuLyoqXG5BIGNvbW1hbmQgZnVuY3Rpb24gdGhhdCB1bmRvZXMgdGhlIGxhc3QgY2hhbmdlLiBEb24ndCBzY3JvbGwgdGhlXG5zZWxlY3Rpb24gaW50byB2aWV3LlxuKi9cbmNvbnN0IHVuZG9Ob1Njcm9sbCA9IGJ1aWxkQ29tbWFuZChmYWxzZSwgZmFsc2UpO1xuLyoqXG5BIGNvbW1hbmQgZnVuY3Rpb24gdGhhdCByZWRvZXMgdGhlIGxhc3QgdW5kb25lIGNoYW5nZS4gRG9uJ3RcbnNjcm9sbCB0aGUgc2VsZWN0aW9uIGludG8gdmlldy5cbiovXG5jb25zdCByZWRvTm9TY3JvbGwgPSBidWlsZENvbW1hbmQodHJ1ZSwgZmFsc2UpO1xuLyoqXG5UaGUgYW1vdW50IG9mIHVuZG9hYmxlIGV2ZW50cyBhdmFpbGFibGUgaW4gYSBnaXZlbiBzdGF0ZS5cbiovXG5mdW5jdGlvbiB1bmRvRGVwdGgoc3RhdGUpIHtcbiAgICBsZXQgaGlzdCA9IGhpc3RvcnlLZXkuZ2V0U3RhdGUoc3RhdGUpO1xuICAgIHJldHVybiBoaXN0ID8gaGlzdC5kb25lLmV2ZW50Q291bnQgOiAwO1xufVxuLyoqXG5UaGUgYW1vdW50IG9mIHJlZG9hYmxlIGV2ZW50cyBhdmFpbGFibGUgaW4gYSBnaXZlbiBlZGl0b3Igc3RhdGUuXG4qL1xuZnVuY3Rpb24gcmVkb0RlcHRoKHN0YXRlKSB7XG4gICAgbGV0IGhpc3QgPSBoaXN0b3J5S2V5LmdldFN0YXRlKHN0YXRlKTtcbiAgICByZXR1cm4gaGlzdCA/IGhpc3QudW5kb25lLmV2ZW50Q291bnQgOiAwO1xufVxuXG5leHBvcnQgeyBjbG9zZUhpc3RvcnksIGhpc3RvcnksIHJlZG8sIHJlZG9EZXB0aCwgcmVkb05vU2Nyb2xsLCB1bmRvLCB1bmRvRGVwdGgsIHVuZG9Ob1Njcm9sbCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/prosemirror-history@1.4.1/node_modules/prosemirror-history/dist/index.js\n");

/***/ })

};
;