"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tunnel-rat";
exports.ids = ["vendor-chunks/tunnel-rat"];
exports.modules = {

/***/ "(ssr)/./node_modules/tunnel-rat/dist/index.js":
/*!***********************************************!*\
  !*** ./node_modules/tunnel-rat/dist/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ tunnel)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n\n\n\nvar _window$document, _window$navigator;\n/**\n * An SSR-friendly useLayoutEffect.\n *\n * React currently throws a warning when using useLayoutEffect on the server.\n * To get around it, we can conditionally useEffect on the server (no-op) and\n * useLayoutEffect elsewhere.\n *\n * @see https://github.com/facebook/react/issues/14927\n */\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' && ((_window$document = window.document) != null && _window$document.createElement || ((_window$navigator = window.navigator) == null ? void 0 : _window$navigator.product) === 'ReactNative') ? (react__WEBPACK_IMPORTED_MODULE_0___default().useLayoutEffect) : (react__WEBPACK_IMPORTED_MODULE_0___default().useEffect);\n\nfunction tunnel() {\n  const useStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)(set => ({\n    current: new Array(),\n    version: 0,\n    set\n  }));\n  return {\n    In: ({\n      children\n    }) => {\n      const set = useStore(state => state.set);\n      const version = useStore(state => state.version);\n      /* When this component mounts, we increase the store's version number.\n      This will cause all existing rats to re-render (just like if the Out component\n      were mapping items to a list.) The re-rendering will cause the final \n      order of rendered components to match what the user is expecting. */\n\n      useIsomorphicLayoutEffect(() => {\n        set(state => ({\n          version: state.version + 1\n        }));\n      }, []);\n      /* Any time the children _or_ the store's version number change, insert\n      the specified React children into the list of rats. */\n\n      useIsomorphicLayoutEffect(() => {\n        set(({\n          current\n        }) => ({\n          current: [...current, children]\n        }));\n        return () => set(({\n          current\n        }) => ({\n          current: current.filter(c => c !== children)\n        }));\n      }, [children, version]);\n      return null;\n    },\n    Out: () => {\n      const current = useStore(state => state.current);\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, current);\n    }\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tunnel-rat/dist/index.js\n");

/***/ })

};
;