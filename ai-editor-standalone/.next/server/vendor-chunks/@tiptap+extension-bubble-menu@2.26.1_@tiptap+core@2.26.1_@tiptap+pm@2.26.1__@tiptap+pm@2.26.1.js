"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-bubble-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-bubble-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-bubble-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-bubble-menu/dist/index.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-bubble-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-bubble-menu/dist/index.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BubbleMenu: () => (/* binding */ BubbleMenu),\n/* harmony export */   BubbleMenuPlugin: () => (/* binding */ BubbleMenuPlugin),\n/* harmony export */   BubbleMenuView: () => (/* binding */ BubbleMenuView),\n/* harmony export */   \"default\": () => (/* binding */ BubbleMenu)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/state/dist/index.js\");\n/* harmony import */ var tippy_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tippy.js */ \"(ssr)/./node_modules/.pnpm/tippy.js@6.3.7/node_modules/tippy.js/dist/tippy.esm.js\");\n\n\n\n\nclass BubbleMenuView {\n    constructor({ editor, element, view, tippyOptions = {}, updateDelay = 250, shouldShow, }) {\n        this.preventHide = false;\n        this.shouldShow = ({ view, state, from, to, }) => {\n            const { doc, selection } = state;\n            const { empty } = selection;\n            // Sometime check for `empty` is not enough.\n            // Doubleclick an empty paragraph returns a node size of 2.\n            // So we check also for an empty text size.\n            const isEmptyTextBlock = !doc.textBetween(from, to).length && (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.isTextSelection)(state.selection);\n            // When clicking on a element inside the bubble menu the editor \"blur\" event\n            // is called and the bubble menu item is focussed. In this case we should\n            // consider the menu as part of the editor and keep showing the menu\n            const isChildOfMenu = this.element.contains(document.activeElement);\n            const hasEditorFocus = view.hasFocus() || isChildOfMenu;\n            if (!hasEditorFocus || empty || isEmptyTextBlock || !this.editor.isEditable) {\n                return false;\n            }\n            return true;\n        };\n        this.mousedownHandler = () => {\n            this.preventHide = true;\n        };\n        this.dragstartHandler = () => {\n            this.hide();\n        };\n        this.focusHandler = () => {\n            // we use `setTimeout` to make sure `selection` is already updated\n            setTimeout(() => this.update(this.editor.view));\n        };\n        this.blurHandler = ({ event }) => {\n            var _a;\n            if (this.preventHide) {\n                this.preventHide = false;\n                return;\n            }\n            if ((event === null || event === void 0 ? void 0 : event.relatedTarget) && ((_a = this.element.parentNode) === null || _a === void 0 ? void 0 : _a.contains(event.relatedTarget))) {\n                return;\n            }\n            if ((event === null || event === void 0 ? void 0 : event.relatedTarget) === this.editor.view.dom) {\n                return;\n            }\n            this.hide();\n        };\n        this.tippyBlurHandler = (event) => {\n            this.blurHandler({ event });\n        };\n        this.handleDebouncedUpdate = (view, oldState) => {\n            const selectionChanged = !(oldState === null || oldState === void 0 ? void 0 : oldState.selection.eq(view.state.selection));\n            const docChanged = !(oldState === null || oldState === void 0 ? void 0 : oldState.doc.eq(view.state.doc));\n            if (!selectionChanged && !docChanged) {\n                return;\n            }\n            if (this.updateDebounceTimer) {\n                clearTimeout(this.updateDebounceTimer);\n            }\n            this.updateDebounceTimer = window.setTimeout(() => {\n                this.updateHandler(view, selectionChanged, docChanged, oldState);\n            }, this.updateDelay);\n        };\n        this.updateHandler = (view, selectionChanged, docChanged, oldState) => {\n            var _a, _b, _c;\n            const { state, composing } = view;\n            const { selection } = state;\n            const isSame = !selectionChanged && !docChanged;\n            if (composing || isSame) {\n                return;\n            }\n            this.createTooltip();\n            // support for CellSelections\n            const { ranges } = selection;\n            const from = Math.min(...ranges.map(range => range.$from.pos));\n            const to = Math.max(...ranges.map(range => range.$to.pos));\n            const shouldShow = (_a = this.shouldShow) === null || _a === void 0 ? void 0 : _a.call(this, {\n                editor: this.editor,\n                element: this.element,\n                view,\n                state,\n                oldState,\n                from,\n                to,\n            });\n            if (!shouldShow) {\n                this.hide();\n                return;\n            }\n            (_b = this.tippy) === null || _b === void 0 ? void 0 : _b.setProps({\n                getReferenceClientRect: ((_c = this.tippyOptions) === null || _c === void 0 ? void 0 : _c.getReferenceClientRect)\n                    || (() => {\n                        if ((0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.isNodeSelection)(state.selection)) {\n                            let node = view.nodeDOM(from);\n                            if (node) {\n                                const nodeViewWrapper = node.dataset.nodeViewWrapper ? node : node.querySelector('[data-node-view-wrapper]');\n                                if (nodeViewWrapper) {\n                                    node = nodeViewWrapper.firstChild;\n                                }\n                                if (node) {\n                                    return node.getBoundingClientRect();\n                                }\n                            }\n                        }\n                        return (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.posToDOMRect)(view, from, to);\n                    }),\n            });\n            this.show();\n        };\n        this.editor = editor;\n        this.element = element;\n        this.view = view;\n        this.updateDelay = updateDelay;\n        if (shouldShow) {\n            this.shouldShow = shouldShow;\n        }\n        this.element.addEventListener('mousedown', this.mousedownHandler, { capture: true });\n        this.view.dom.addEventListener('dragstart', this.dragstartHandler);\n        this.editor.on('focus', this.focusHandler);\n        this.editor.on('blur', this.blurHandler);\n        this.tippyOptions = tippyOptions;\n        // Detaches menu content from its current parent\n        this.element.remove();\n        this.element.style.visibility = 'visible';\n    }\n    createTooltip() {\n        const { element: editorElement } = this.editor.options;\n        const editorIsAttached = !!editorElement.parentElement;\n        this.element.tabIndex = 0;\n        if (this.tippy || !editorIsAttached) {\n            return;\n        }\n        this.tippy = (0,tippy_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(editorElement, {\n            duration: 0,\n            getReferenceClientRect: null,\n            content: this.element,\n            interactive: true,\n            trigger: 'manual',\n            placement: 'top',\n            hideOnClick: 'toggle',\n            ...this.tippyOptions,\n        });\n        // maybe we have to hide tippy on its own blur event as well\n        if (this.tippy.popper.firstChild) {\n            this.tippy.popper.firstChild.addEventListener('blur', this.tippyBlurHandler);\n        }\n    }\n    update(view, oldState) {\n        const { state } = view;\n        const hasValidSelection = state.selection.from !== state.selection.to;\n        if (this.updateDelay > 0 && hasValidSelection) {\n            this.handleDebouncedUpdate(view, oldState);\n            return;\n        }\n        const selectionChanged = !(oldState === null || oldState === void 0 ? void 0 : oldState.selection.eq(view.state.selection));\n        const docChanged = !(oldState === null || oldState === void 0 ? void 0 : oldState.doc.eq(view.state.doc));\n        this.updateHandler(view, selectionChanged, docChanged, oldState);\n    }\n    show() {\n        var _a;\n        (_a = this.tippy) === null || _a === void 0 ? void 0 : _a.show();\n    }\n    hide() {\n        var _a;\n        (_a = this.tippy) === null || _a === void 0 ? void 0 : _a.hide();\n    }\n    destroy() {\n        var _a, _b;\n        if ((_a = this.tippy) === null || _a === void 0 ? void 0 : _a.popper.firstChild) {\n            this.tippy.popper.firstChild.removeEventListener('blur', this.tippyBlurHandler);\n        }\n        (_b = this.tippy) === null || _b === void 0 ? void 0 : _b.destroy();\n        this.element.removeEventListener('mousedown', this.mousedownHandler, { capture: true });\n        this.view.dom.removeEventListener('dragstart', this.dragstartHandler);\n        this.editor.off('focus', this.focusHandler);\n        this.editor.off('blur', this.blurHandler);\n    }\n}\nconst BubbleMenuPlugin = (options) => {\n    return new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n        key: typeof options.pluginKey === 'string' ? new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey(options.pluginKey) : options.pluginKey,\n        view: view => new BubbleMenuView({ view, ...options }),\n    });\n};\n\n/**\n * This extension allows you to create a bubble menu.\n * @see https://tiptap.dev/api/extensions/bubble-menu\n */\nconst BubbleMenu = _tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Extension.create({\n    name: 'bubbleMenu',\n    addOptions() {\n        return {\n            element: null,\n            tippyOptions: {},\n            pluginKey: 'bubbleMenu',\n            updateDelay: undefined,\n            shouldShow: null,\n        };\n    },\n    addProseMirrorPlugins() {\n        if (!this.options.element) {\n            return [];\n        }\n        return [\n            BubbleMenuPlugin({\n                pluginKey: this.options.pluginKey,\n                editor: this.editor,\n                element: this.options.element,\n                tippyOptions: this.options.tippyOptions,\n                updateDelay: this.options.updateDelay,\n                shouldShow: this.options.shouldShow,\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-bubble-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-bubble-menu/dist/index.js\n");

/***/ })

};
;