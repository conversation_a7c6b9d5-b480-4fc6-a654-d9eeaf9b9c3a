/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/markdown-it-task-lists@2.1.1";
exports.ids = ["vendor-chunks/markdown-it-task-lists@2.1.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/markdown-it-task-lists@2.1.1/node_modules/markdown-it-task-lists/index.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/markdown-it-task-lists@2.1.1/node_modules/markdown-it-task-lists/index.js ***!
  \******************************************************************************************************/
/***/ ((module) => {

eval("// Markdown-it plugin to render GitHub-style task lists; see\n//\n// https://github.com/blog/1375-task-lists-in-gfm-issues-pulls-comments\n// https://github.com/blog/1825-task-lists-in-all-markdown-documents\n\nvar disableCheckboxes = true;\nvar useLabelWrapper = false;\nvar useLabelAfter = false;\n\nmodule.exports = function(md, options) {\n\tif (options) {\n\t\tdisableCheckboxes = !options.enabled;\n\t\tuseLabelWrapper = !!options.label;\n\t\tuseLabelAfter = !!options.labelAfter;\n\t}\n\n\tmd.core.ruler.after('inline', 'github-task-lists', function(state) {\n\t\tvar tokens = state.tokens;\n\t\tfor (var i = 2; i < tokens.length; i++) {\n\t\t\tif (isTodoItem(tokens, i)) {\n\t\t\t\ttodoify(tokens[i], state.Token);\n\t\t\t\tattrSet(tokens[i-2], 'class', 'task-list-item' + (!disableCheckboxes ? ' enabled' : ''));\n\t\t\t\tattrSet(tokens[parentToken(tokens, i-2)], 'class', 'contains-task-list');\n\t\t\t}\n\t\t}\n\t});\n};\n\nfunction attrSet(token, name, value) {\n\tvar index = token.attrIndex(name);\n\tvar attr = [name, value];\n\n\tif (index < 0) {\n\t\ttoken.attrPush(attr);\n\t} else {\n\t\ttoken.attrs[index] = attr;\n\t}\n}\n\nfunction parentToken(tokens, index) {\n\tvar targetLevel = tokens[index].level - 1;\n\tfor (var i = index - 1; i >= 0; i--) {\n\t\tif (tokens[i].level === targetLevel) {\n\t\t\treturn i;\n\t\t}\n\t}\n\treturn -1;\n}\n\nfunction isTodoItem(tokens, index) {\n\treturn isInline(tokens[index]) &&\n\t       isParagraph(tokens[index - 1]) &&\n\t       isListItem(tokens[index - 2]) &&\n\t       startsWithTodoMarkdown(tokens[index]);\n}\n\nfunction todoify(token, TokenConstructor) {\n\ttoken.children.unshift(makeCheckbox(token, TokenConstructor));\n\ttoken.children[1].content = token.children[1].content.slice(3);\n\ttoken.content = token.content.slice(3);\n\n\tif (useLabelWrapper) {\n\t\tif (useLabelAfter) {\n\t\t\ttoken.children.pop();\n\n\t\t\t// Use large random number as id property of the checkbox.\n\t\t\tvar id = 'task-item-' + Math.ceil(Math.random() * (10000 * 1000) - 1000);\n\t\t\ttoken.children[0].content = token.children[0].content.slice(0, -1) + ' id=\"' + id + '\">';\n\t\t\ttoken.children.push(afterLabel(token.content, id, TokenConstructor));\n\t\t} else {\n\t\t\ttoken.children.unshift(beginLabel(TokenConstructor));\n\t\t\ttoken.children.push(endLabel(TokenConstructor));\n\t\t}\n\t}\n}\n\nfunction makeCheckbox(token, TokenConstructor) {\n\tvar checkbox = new TokenConstructor('html_inline', '', 0);\n\tvar disabledAttr = disableCheckboxes ? ' disabled=\"\" ' : '';\n\tif (token.content.indexOf('[ ] ') === 0) {\n\t\tcheckbox.content = '<input class=\"task-list-item-checkbox\"' + disabledAttr + 'type=\"checkbox\">';\n\t} else if (token.content.indexOf('[x] ') === 0 || token.content.indexOf('[X] ') === 0) {\n\t\tcheckbox.content = '<input class=\"task-list-item-checkbox\" checked=\"\"' + disabledAttr + 'type=\"checkbox\">';\n\t}\n\treturn checkbox;\n}\n\n// these next two functions are kind of hacky; probably should really be a\n// true block-level token with .tag=='label'\nfunction beginLabel(TokenConstructor) {\n\tvar token = new TokenConstructor('html_inline', '', 0);\n\ttoken.content = '<label>';\n\treturn token;\n}\n\nfunction endLabel(TokenConstructor) {\n\tvar token = new TokenConstructor('html_inline', '', 0);\n\ttoken.content = '</label>';\n\treturn token;\n}\n\nfunction afterLabel(content, id, TokenConstructor) {\n\tvar token = new TokenConstructor('html_inline', '', 0);\n\ttoken.content = '<label class=\"task-list-item-label\" for=\"' + id + '\">' + content + '</label>';\n\ttoken.attrs = [{for: id}];\n\treturn token;\n}\n\nfunction isInline(token) { return token.type === 'inline'; }\nfunction isParagraph(token) { return token.type === 'paragraph_open'; }\nfunction isListItem(token) { return token.type === 'list_item_open'; }\n\nfunction startsWithTodoMarkdown(token) {\n\t// leading whitespace in a list item is already trimmed off by markdown-it\n\treturn token.content.indexOf('[ ] ') === 0 || token.content.indexOf('[x] ') === 0 || token.content.indexOf('[X] ') === 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/markdown-it-task-lists@2.1.1/node_modules/markdown-it-task-lists/index.js\n");

/***/ })

};
;