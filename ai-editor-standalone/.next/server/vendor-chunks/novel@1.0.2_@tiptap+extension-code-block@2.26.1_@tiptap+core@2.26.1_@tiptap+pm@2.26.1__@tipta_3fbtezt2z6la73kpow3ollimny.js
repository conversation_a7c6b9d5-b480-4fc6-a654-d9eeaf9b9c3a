"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/novel@1.0.2_@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tipta_3fbtezt2z6la73kpow3ollimny";
exports.ids = ["vendor-chunks/novel@1.0.2_@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tipta_3fbtezt2z6la73kpow3ollimny"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/novel@1.0.2_@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tipta_3fbtezt2z6la73kpow3ollimny/node_modules/novel/dist/index.js":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/novel@1.0.2_@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tipta_3fbtezt2z6la73kpow3ollimny/node_modules/novel/dist/index.js ***!
  \**************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIHighlight: () => (/* binding */ oe),\n/* harmony export */   CharacterCount: () => (/* reexport safe */ _tiptap_extension_character_count__WEBPACK_IMPORTED_MODULE_17__[\"default\"]),\n/* harmony export */   CodeBlockLowlight: () => (/* reexport safe */ _tiptap_extension_code_block_lowlight__WEBPACK_IMPORTED_MODULE_18__[\"default\"]),\n/* harmony export */   Color: () => (/* reexport safe */ _tiptap_extension_color__WEBPACK_IMPORTED_MODULE_4__.Color),\n/* harmony export */   Command: () => (/* binding */ me),\n/* harmony export */   CustomKeymap: () => (/* binding */ z),\n/* harmony export */   EditorBubble: () => (/* binding */ P),\n/* harmony export */   EditorBubbleItem: () => (/* binding */ w),\n/* harmony export */   EditorCommand: () => (/* binding */ T),\n/* harmony export */   EditorCommandEmpty: () => (/* binding */ B),\n/* harmony export */   EditorCommandItem: () => (/* binding */ S),\n/* harmony export */   EditorCommandList: () => (/* binding */ O),\n/* harmony export */   EditorContent: () => (/* binding */ I),\n/* harmony export */   EditorRoot: () => (/* binding */ U),\n/* harmony export */   GlobalDragHandle: () => (/* reexport safe */ tiptap_extension_global_drag_handle__WEBPACK_IMPORTED_MODULE_20__[\"default\"]),\n/* harmony export */   HighlightExtension: () => (/* binding */ ve),\n/* harmony export */   HorizontalRule: () => (/* binding */ He),\n/* harmony export */   ImageResizer: () => (/* binding */ K),\n/* harmony export */   InputRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_1__.InputRule),\n/* harmony export */   Mathematics: () => (/* binding */ F),\n/* harmony export */   Placeholder: () => (/* binding */ Ae),\n/* harmony export */   StarterKit: () => (/* reexport safe */ _tiptap_starter_kit__WEBPACK_IMPORTED_MODULE_14__[\"default\"]),\n/* harmony export */   TaskItem: () => (/* reexport safe */ _tiptap_extension_task_item__WEBPACK_IMPORTED_MODULE_10__.TaskItem),\n/* harmony export */   TaskList: () => (/* reexport safe */ _tiptap_extension_task_list__WEBPACK_IMPORTED_MODULE_11__.TaskList),\n/* harmony export */   TextStyle: () => (/* reexport safe */ _tiptap_extension_text_style__WEBPACK_IMPORTED_MODULE_12__[\"default\"]),\n/* harmony export */   TiptapImage: () => (/* reexport safe */ _tiptap_extension_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   TiptapLink: () => (/* reexport safe */ _tiptap_extension_link__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   TiptapUnderline: () => (/* reexport safe */ _tiptap_extension_underline__WEBPACK_IMPORTED_MODULE_13__[\"default\"]),\n/* harmony export */   Twitter: () => (/* binding */ $),\n/* harmony export */   UpdatedImage: () => (/* binding */ V),\n/* harmony export */   UploadImagesPlugin: () => (/* binding */ _),\n/* harmony export */   Youtube: () => (/* reexport safe */ _tiptap_extension_youtube__WEBPACK_IMPORTED_MODULE_19__[\"default\"]),\n/* harmony export */   addAIHighlight: () => (/* binding */ ne),\n/* harmony export */   createImageUpload: () => (/* binding */ q),\n/* harmony export */   createSuggestionItems: () => (/* binding */ pe),\n/* harmony export */   getAllContent: () => (/* binding */ ze),\n/* harmony export */   getPrevText: () => (/* binding */ Be),\n/* harmony export */   getUrlFromString: () => (/* binding */ De),\n/* harmony export */   handleCommandNavigation: () => (/* binding */ ce),\n/* harmony export */   handleImageDrop: () => (/* binding */ J),\n/* harmony export */   handleImagePaste: () => (/* binding */ G),\n/* harmony export */   isValidUrl: () => (/* binding */ X),\n/* harmony export */   queryAtom: () => (/* binding */ E),\n/* harmony export */   rangeAtom: () => (/* binding */ u),\n/* harmony export */   removeAIHighlight: () => (/* binding */ re),\n/* harmony export */   renderItems: () => (/* binding */ le),\n/* harmony export */   useEditor: () => (/* reexport safe */ _tiptap_react__WEBPACK_IMPORTED_MODULE_0__.useCurrentEditor)\n/* harmony export */ });\n/* harmony import */ var _tiptap_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/react */ \"(ssr)/./node_modules/.pnpm/@tiptap+react@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1_react-dom@18.3._q3aad5pwjtroq2r7i35exxu3a4/node_modules/@tiptap/react/dist/index.js\");\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! jotai */ \"(ssr)/./node_modules/.pnpm/jotai@2.12.5_@types+react@18.3.23_react@18.3.1/node_modules/jotai/esm/index.mjs\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! jotai */ \"(ssr)/./node_modules/.pnpm/jotai@2.12.5_@types+react@18.3.23_react@18.3.1/node_modules/jotai/esm/vanilla.mjs\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! jotai */ \"(ssr)/./node_modules/.pnpm/jotai@2.12.5_@types+react@18.3.23_react@18.3.1/node_modules/jotai/esm/react.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var tunnel_rat__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! tunnel-rat */ \"(ssr)/./node_modules/.pnpm/tunnel-rat@0.1.2_@types+react@18.3.23_react@18.3.1/node_modules/tunnel-rat/dist/index.js\");\n/* harmony import */ var cmdk__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! cmdk */ \"(ssr)/./node_modules/.pnpm/cmdk@1.1.1_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@18.3.23_react-dom@18.3._ruqjqq7hqrjul6i4m4h32ddhae/node_modules/cmdk/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _tiptap_extension_color__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tiptap/extension-color */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-color@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+extension-text-_ochc23jjcl3wq5k6zoczrgb3ku/node_modules/@tiptap/extension-color/dist/index.js\");\n/* harmony import */ var _tiptap_extension_highlight__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tiptap/extension-highlight */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-highlight@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-highlight/dist/index.js\");\n/* harmony import */ var _tiptap_extension_horizontal_rule__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tiptap/extension-horizontal-rule */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-horizontal-rule@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-horizontal-rule/dist/index.js\");\n/* harmony import */ var _tiptap_extension_image__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tiptap/extension-image */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-image@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-image/dist/index.js\");\n/* harmony import */ var _tiptap_extension_link__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tiptap/extension-link */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-link@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-link/dist/index.js\");\n/* harmony import */ var _tiptap_extension_placeholder__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tiptap/extension-placeholder */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-placeholder@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-placeholder/dist/index.js\");\n/* harmony import */ var _tiptap_extension_task_item__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @tiptap/extension-task-item */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-task-item@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-task-item/dist/index.js\");\n/* harmony import */ var _tiptap_extension_task_list__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tiptap/extension-task-list */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-task-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-task-list/dist/index.js\");\n/* harmony import */ var _tiptap_extension_text_style__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tiptap/extension-text-style */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-text-style@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-text-style/dist/index.js\");\n/* harmony import */ var _tiptap_extension_underline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tiptap/extension-underline */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-underline@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-underline/dist/index.js\");\n/* harmony import */ var _tiptap_starter_kit__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tiptap/starter-kit */ \"(ssr)/./node_modules/.pnpm/@tiptap+starter-kit@2.26.1/node_modules/@tiptap/starter-kit/dist/index.js\");\n/* harmony import */ var react_moveable__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! react-moveable */ \"(ssr)/./node_modules/.pnpm/react-moveable@0.56.0/node_modules/react-moveable/dist/moveable.esm.js\");\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/state/dist/index.js\");\n/* harmony import */ var katex__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! katex */ \"(ssr)/./node_modules/.pnpm/katex@0.16.22/node_modules/katex/dist/katex.mjs\");\n/* harmony import */ var react_tweet__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! react-tweet */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-tweet/dist/swr.js\");\n/* harmony import */ var _tiptap_extension_character_count__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @tiptap/extension-character-count */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-character-count@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-character-count/dist/index.js\");\n/* harmony import */ var _tiptap_extension_code_block_lowlight__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tiptap/extension-code-block-lowlight */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-code-block-lowlight@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+e_sns5t5ltdqvqcmb5tff5texts4/node_modules/@tiptap/extension-code-block-lowlight/dist/index.js\");\n/* harmony import */ var _tiptap_extension_youtube__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @tiptap/extension-youtube */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-youtube@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-youtube/dist/index.js\");\n/* harmony import */ var tiptap_extension_global_drag_handle__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! tiptap-extension-global-drag-handle */ \"(ssr)/./node_modules/.pnpm/tiptap-extension-global-drag-handle@0.1.18/node_modules/tiptap-extension-global-drag-handle/dist/index.js\");\n/* harmony import */ var _tiptap_suggestion__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @tiptap/suggestion */ \"(ssr)/./node_modules/.pnpm/@tiptap+suggestion@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/suggestion/dist/index.js\");\n/* harmony import */ var tippy_js__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! tippy.js */ \"(ssr)/./node_modules/.pnpm/tippy.js@6.3.7/node_modules/tippy.js/dist/tippy.esm.js\");\n/* harmony import */ var _tiptap_pm_view__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @tiptap/pm/view */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/view/dist/index.js\");\n/* harmony import */ var _tiptap_pm_model__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @tiptap/pm/model */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/model/dist/index.js\");\nvar M=Object.defineProperty;var Z=Object.getOwnPropertyDescriptor;var j=Object.getOwnPropertyNames;var tt=Object.prototype.hasOwnProperty;var et=(t,e)=>{for(var o in e)M(t,o,{get:e[o],enumerable:true});},H=(t,e,o,r)=>{if(e&&typeof e==\"object\"||typeof e==\"function\")for(let n of j(e))!tt.call(t,n)&&n!==o&&M(t,n,{get:()=>e[n],enumerable:!(r=Z(e,n))||r.enumerable});return t},y=(t,e,o)=>(H(t,e,\"default\"),o);var l={};et(l,{novelStore:()=>p});y(l,jotai__WEBPACK_IMPORTED_MODULE_24__);var p=(0,jotai__WEBPACK_IMPORTED_MODULE_25__.createStore)();var E=(0,jotai__WEBPACK_IMPORTED_MODULE_25__.atom)(\"\"),u=(0,jotai__WEBPACK_IMPORTED_MODULE_25__.atom)(null);var b=(0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)({}),k=({query:t,range:e})=>{let o=(0,jotai__WEBPACK_IMPORTED_MODULE_26__.useSetAtom)(E,{store:p}),r=(0,jotai__WEBPACK_IMPORTED_MODULE_26__.useSetAtom)(u,{store:p});return (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{o(t);},[t,o]),(0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{r(e);},[e,r]),(0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{let n=[\"ArrowUp\",\"ArrowDown\",\"Enter\"],i=a=>{if(n.includes(a.key)){a.preventDefault();let s=document.querySelector(\"#slash-command\");return s&&s.dispatchEvent(new KeyboardEvent(\"keydown\",{key:a.key,cancelable:true,bubbles:true})),false}};return document.addEventListener(\"keydown\",i),()=>{document.removeEventListener(\"keydown\",i);}},[]),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(b.Consumer,{children:n=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(n.Out,{})})},T=(0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(({children:t,className:e,...o},r)=>{let[n,i]=(0,jotai__WEBPACK_IMPORTED_MODULE_26__.useAtom)(E);return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(b.Consumer,{children:a=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(a.In,{children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(cmdk__WEBPACK_IMPORTED_MODULE_27__.Command,{ref:r,onKeyDown:s=>{s.stopPropagation();},id:\"slash-command\",className:e,...o,children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(cmdk__WEBPACK_IMPORTED_MODULE_27__.Command.Input,{value:n,onValueChange:i,style:{display:\"none\"}}),t]})})})}),O=cmdk__WEBPACK_IMPORTED_MODULE_27__.Command.List;T.displayName=\"EditorCommand\";var U=({children:t})=>{let e=(0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)((0,tunnel_rat__WEBPACK_IMPORTED_MODULE_28__[\"default\"])()).current;return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(jotai__WEBPACK_IMPORTED_MODULE_26__.Provider,{store:p,children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(b.Provider,{value:e,children:t})})},I=(0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(({className:t,children:e,initialContent:o,...r},n)=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\",{ref:n,className:t,children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tiptap_react__WEBPACK_IMPORTED_MODULE_0__.EditorProvider,{...r,content:o,children:e})}));I.displayName=\"EditorContent\";var P=(0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(({children:t,tippyOptions:e,...o},r)=>{let{editor:n}=(0,_tiptap_react__WEBPACK_IMPORTED_MODULE_0__.useCurrentEditor)(),i=(0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);(0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{!i.current||!e?.placement||(i.current.setProps({placement:e.placement}),i.current.popperInstance?.update());},[e?.placement]);let a=(0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({shouldShow:({editor:d,state:m})=>{let{selection:h}=m,{empty:c}=h;return !(!d.isEditable||d.isActive(\"image\")||c||(0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.isNodeSelection)(h))},tippyOptions:{onCreate:d=>{i.current=d,i.current.popper.firstChild?.addEventListener(\"blur\",m=>{m.preventDefault(),m.stopImmediatePropagation();});},moveTransition:\"transform 0.15s ease-out\",...e},editor:n,...o}),[o,e]);return n?(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\",{ref:r,children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tiptap_react__WEBPACK_IMPORTED_MODULE_0__.BubbleMenu,{...a,children:t})}):null});P.displayName=\"EditorBubble\";var w=(0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(({children:t,asChild:e,onSelect:o,...r},n)=>{let{editor:i}=(0,_tiptap_react__WEBPACK_IMPORTED_MODULE_0__.useCurrentEditor)(),a=e?_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_29__.Slot:\"div\";return i?(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(a,{ref:n,...r,onClick:()=>o?.(i),children:t}):null});w.displayName=\"EditorBubbleItem\";var S=(0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(({children:t,onCommand:e,...o},r)=>{let{editor:n}=(0,_tiptap_react__WEBPACK_IMPORTED_MODULE_0__.useCurrentEditor)(),i=(0,jotai__WEBPACK_IMPORTED_MODULE_26__.useAtomValue)(u);return !n||!i?null:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(cmdk__WEBPACK_IMPORTED_MODULE_27__.CommandItem,{ref:r,...o,onSelect:()=>e({editor:n,range:i}),children:t})});S.displayName=\"EditorCommandItem\";var B=cmdk__WEBPACK_IMPORTED_MODULE_27__.CommandEmpty;var Mt=_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Extension.create({name:\"CustomKeymap\",addCommands(){return {selectTextWithinNodeBoundaries:()=>({editor:t,commands:e})=>{let{state:o}=t,{tr:r}=o,n=r.selection.$from.start(),i=r.selection.$to.end();return e.setTextSelection({from:n,to:i})}}},addKeyboardShortcuts(){return {\"Mod-a\":({editor:t})=>{let{state:e}=t,{tr:o}=e,r=o.selection.from,n=o.selection.to,i=o.selection.$from.start(),a=o.selection.$to.end();return r>i||n<a?(t.chain().selectTextWithinNodeBoundaries().run(),true):false}}}}),z=Mt;var K=()=>{let{editor:t}=(0,_tiptap_react__WEBPACK_IMPORTED_MODULE_0__.useCurrentEditor)();if(!t?.isActive(\"image\"))return null;let e=()=>{let o=document.querySelector(\".ProseMirror-selectednode\");if(o){let r=t.state.selection,n=t.commands.setImage;n({src:o.src,width:Number(o.style.width.replace(\"px\",\"\")),height:Number(o.style.height.replace(\"px\",\"\"))}),t.commands.setNodeSelection(r.from);}};return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(react_moveable__WEBPACK_IMPORTED_MODULE_30__[\"default\"],{target:document.querySelector(\".ProseMirror-selectednode\"),container:null,origin:false,edge:false,throttleDrag:0,keepRatio:true,resizable:true,throttleResize:0,onResize:({target:o,width:r,height:n,delta:i})=>{i[0]&&(o.style.width=`${r}px`),i[1]&&(o.style.height=`${n}px`);},onResizeEnd:()=>{e();},scalable:true,throttleScale:0,renderDirections:[\"w\",\"e\"],onScale:({target:o,transform:r})=>{o.style.transform=r;}})};var F=_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Node.create({name:\"math\",inline:true,group:\"inline\",atom:true,selectable:true,marks:\"\",addAttributes(){return {latex:\"\"}},addOptions(){return {shouldRender:(t,e)=>{let o=t.doc.resolve(e);return o.parent.isTextblock?o.parent.type.name!==\"codeBlock\":false},katexOptions:{throwOnError:false},HTMLAttributes:{}}},addCommands(){return {setLatex:({latex:t})=>({chain:e,state:o})=>{if(!t)return  false;let{from:r,to:n,$anchor:i}=o.selection;return this.options.shouldRender(o,i.pos)?e().insertContentAt({from:r,to:n},{type:\"math\",attrs:{latex:t}}).setTextSelection({from:r,to:r+1}).run():false},unsetLatex:()=>({editor:t,state:e,chain:o})=>{let r=t.getAttributes(this.name).latex;if(typeof r!=\"string\")return  false;let{from:n,to:i}=e.selection;return o().command(({tr:a})=>(a.insertText(r,n,i),true)).setTextSelection({from:n,to:n+r.length}).run()}}},parseHTML(){return [{tag:`span[data-type=\"${this.name}\"]`}]},renderHTML({node:t,HTMLAttributes:e}){let o=t.attrs.latex??\"\";return [\"span\",(0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.mergeAttributes)(e,{\"data-type\":this.name}),o]},renderText({node:t}){return t.attrs.latex??\"\"},addNodeView(){return ({node:t,HTMLAttributes:e,getPos:o,editor:r})=>{let n=document.createElement(\"span\"),i=t.attrs.latex??\"\";return Object.entries(this.options.HTMLAttributes).forEach(([a,s])=>{n.setAttribute(a,s);}),Object.entries(e).forEach(([a,s])=>{n.setAttribute(a,s);}),n.addEventListener(\"click\",a=>{if(r.isEditable&&typeof o==\"function\"){let s=o(),d=t.nodeSize;r.commands.setTextSelection({from:s,to:s+d});}}),n.contentEditable=\"false\",n.innerHTML=katex__WEBPACK_IMPORTED_MODULE_16__[\"default\"].renderToString(i,this.options.katexOptions),{dom:n}}}});var Wt=/(https?:\\/\\/)?(www\\.)?x\\.com\\/([a-zA-Z0-9_]{1,15})(\\/status\\/(\\d+))?(\\/\\S*)?/g,_t=/^https?:\\/\\/(www\\.)?x\\.com\\/([a-zA-Z0-9_]{1,15})(\\/status\\/(\\d+))?(\\/\\S*)?$/,qt=t=>t.match(_t),Gt=({node:t})=>{let o=t?.attrs?.src?.split(\"/\").pop();return o?(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tiptap_react__WEBPACK_IMPORTED_MODULE_0__.NodeViewWrapper,{children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\",{\"data-twitter\":\"\",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(react_tweet__WEBPACK_IMPORTED_MODULE_31__.Tweet,{id:o})})}):null},$=_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Node.create({name:\"twitter\",addOptions(){return {addPasteHandler:true,HTMLAttributes:{},inline:false,origin:\"\"}},addNodeView(){return (0,_tiptap_react__WEBPACK_IMPORTED_MODULE_0__.ReactNodeViewRenderer)(Gt,{attrs:this.options.HTMLAttributes})},inline(){return this.options.inline},group(){return this.options.inline?\"inline\":\"block\"},draggable:true,addAttributes(){return {src:{default:null}}},parseHTML(){return [{tag:\"div[data-twitter]\"}]},addCommands(){return {setTweet:t=>({commands:e})=>qt(t.src)?e.insertContent({type:this.name,attrs:t}):false}},addPasteRules(){return this.options.addPasteHandler?[(0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.nodePasteRule)({find:Wt,type:this.type,getAttributes:t=>({src:t.input})})]:[]},renderHTML({HTMLAttributes:t}){return [\"div\",(0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.mergeAttributes)({\"data-twitter\":\"\"},t)]}});var Xt=_tiptap_extension_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"].extend({name:\"image\",addAttributes(){return {...this.parent?.(),width:{default:null},height:{default:null}}}}),V=Xt;var te=/(?:^|\\s)((?:==)((?:[^~=]+))(?:==))$/,ee=/(?:^|\\s)((?:==)((?:[^~=]+))(?:==))/g,oe=_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Mark.create({name:\"ai-highlight\",addOptions(){return {HTMLAttributes:{}}},addAttributes(){return {color:{default:null,parseHTML:t=>t.getAttribute(\"data-color\")||t.style.backgroundColor,renderHTML:t=>t.color?{\"data-color\":t.color,style:`background-color: ${t.color}; color: inherit`}:{}}}},parseHTML(){return [{tag:\"mark\"}]},renderHTML({HTMLAttributes:t}){return [\"mark\",(0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.mergeAttributes)(this.options.HTMLAttributes,t),0]},addCommands(){return {setAIHighlight:t=>({commands:e})=>e.setMark(this.name,t),toggleAIHighlight:t=>({commands:e})=>e.toggleMark(this.name,t),unsetAIHighlight:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return {\"Mod-Shift-h\":()=>this.editor.commands.toggleAIHighlight()}},addInputRules(){return [(0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.markInputRule)({find:te,type:this.type})]},addPasteRules(){return [(0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.markPasteRule)({find:ee,type:this.type})]}}),re=t=>{let e=t.state.tr;e.removeMark(0,t.state.doc.nodeSize-2,t.state.schema.marks[\"ai-highlight\"]),t.view.dispatch(e);},ne=(t,e)=>{t.chain().setAIHighlight({color:e??\"#c1ecf970\"}).run();};var me=_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Extension.create({name:\"slash-command\",addOptions(){return {suggestion:{char:\"/\",command:({editor:t,range:e,props:o})=>{o.command({editor:t,range:e});}}}},addProseMirrorPlugins(){return [(0,_tiptap_suggestion__WEBPACK_IMPORTED_MODULE_21__[\"default\"])({editor:this.editor,...this.options.suggestion})]}}),le=t=>{let e=null,o=null;return {onStart:r=>{e=new _tiptap_react__WEBPACK_IMPORTED_MODULE_0__.ReactRenderer(k,{props:r,editor:r.editor});let{selection:n}=r.editor.state;if(n.$from.node(n.$from.depth).type.name===\"codeBlock\")return  false;o=(0,tippy_js__WEBPACK_IMPORTED_MODULE_32__[\"default\"])(\"body\",{getReferenceClientRect:r.clientRect,appendTo:()=>t?t.current:document.body,content:e.element,showOnCreate:true,interactive:true,trigger:\"manual\",placement:\"bottom-start\"});},onUpdate:r=>{e?.updateProps(r),o?.[0]?.setProps({getReferenceClientRect:r.clientRect});},onKeyDown:r=>r.event.key===\"Escape\"?(o?.[0]?.hide(),true):e?.ref?.onKeyDown(r),onExit:()=>{o?.[0]?.destroy(),e?.destroy();}}},pe=t=>t,ce=t=>{if([\"ArrowUp\",\"ArrowDown\",\"Enter\"].includes(t.key)&&document.querySelector(\"#slash-command\"))return  true};var Ae=_tiptap_extension_placeholder__WEBPACK_IMPORTED_MODULE_9__[\"default\"].configure({placeholder:({node:t})=>t.type.name===\"heading\"?`Heading ${t.attrs.level}`:\"Press '/' for commands\",includeChildren:true}),ve=_tiptap_extension_highlight__WEBPACK_IMPORTED_MODULE_5__[\"default\"].configure({multicolor:true}),He=_tiptap_extension_horizontal_rule__WEBPACK_IMPORTED_MODULE_6__[\"default\"].extend({addInputRules(){return [new _tiptap_core__WEBPACK_IMPORTED_MODULE_1__.InputRule({find:/^(?:---|—-|___\\s|\\*\\*\\*\\s)$/u,handler:({state:t,range:e})=>{let o={},{tr:r}=t,n=e.from,i=e.to;r.insert(n-1,this.type.create(o)).delete(r.mapping.map(n),r.mapping.map(i));}})]}});var g=new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_15__.PluginKey(\"upload-image\"),_=({imageClass:t})=>new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_15__.Plugin({key:g,state:{init(){return _tiptap_pm_view__WEBPACK_IMPORTED_MODULE_22__.DecorationSet.empty},apply(e,o){o=o.map(e.mapping,e.doc);let r=e.getMeta(this);if(r?.add){let{id:n,pos:i,src:a}=r.add,s=document.createElement(\"div\");s.setAttribute(\"class\",\"img-placeholder\");let d=document.createElement(\"img\");d.setAttribute(\"class\",t),d.src=a,s.appendChild(d);let m=_tiptap_pm_view__WEBPACK_IMPORTED_MODULE_22__.Decoration.widget(i+1,s,{id:n});o=o.add(e.doc,[m]);}else r?.remove&&(o=o.remove(o.find(undefined,undefined,n=>n.id==r.remove.id)));return o}},props:{decorations(e){return this.getState(e)}}});function Oe(t,e){let r=g.getState(t).find(undefined,undefined,n=>n.id==e);return r.length?r[0]?.from:null}var q=({validateFn:t,onUpload:e})=>(o,r,n)=>{if(!t?.(o))return;let a={},s=r.state.tr;s.selection.empty||s.deleteSelection();let d=new FileReader;d.readAsDataURL(o),d.onload=()=>{s.setMeta(g,{add:{id:a,pos:n,src:d.result}}),r.dispatch(s);},e(o).then(m=>{let{schema:h}=r.state,c=Oe(r.state,a);if(c==null)return;let Y=typeof m==\"object\"?d.result:m,v=h.nodes.image?.create({src:Y});if(!v)return;let Q=r.state.tr.replaceWith(c,c,v).setMeta(g,{remove:{id:a}});r.dispatch(Q);},()=>{let m=r.state.tr.delete(n,n).setMeta(g,{remove:{id:a}});r.dispatch(m);});},G=(t,e,o)=>{if(e.clipboardData?.files.length){e.preventDefault();let[r]=Array.from(e.clipboardData.files),n=t.state.selection.from;return r&&o(r,t,n),true}return  false},J=(t,e,o,r)=>{if(!o&&e.dataTransfer?.files.length){e.preventDefault();let[n]=Array.from(e.dataTransfer.files),i=t.posAtCoords({left:e.clientX,top:e.clientY});return n&&r(n,t,i?.pos??-1),true}return  false};function X(t){try{return new URL(t),!0}catch{return  false}}function De(t){if(X(t))return t;try{if(t.includes(\".\")&&!t.includes(\" \"))return new URL(`https://${t}`).toString()}catch{return null}}var Be=(t,e)=>{let o=[];t.state.doc.forEach((i,a)=>a>=e?false:(o.push(i),true));let r=_tiptap_pm_model__WEBPACK_IMPORTED_MODULE_23__.Fragment.fromArray(o),n=t.state.doc.copy(r);return t.storage.markdown.serializer.serialize(n)},ze=t=>{let e=t.state.doc.content,o=t.state.doc.copy(e);return t.storage.markdown.serializer.serialize(o)};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/novel@1.0.2_@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tipta_3fbtezt2z6la73kpow3ollimny/node_modules/novel/dist/index.js\n");

/***/ })

};
;