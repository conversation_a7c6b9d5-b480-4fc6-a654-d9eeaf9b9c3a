"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-history@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-history@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-history@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-history/dist/index.js":
/*!***************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-history@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-history/dist/index.js ***!
  \***************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   History: () => (/* binding */ History),\n/* harmony export */   \"default\": () => (/* binding */ History)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_history__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/history */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/history/dist/index.js\");\n\n\n\n/**\n * This extension allows you to undo and redo recent changes.\n * @see https://www.tiptap.dev/api/extensions/history\n *\n * **Important**: If the `@tiptap/extension-collaboration` package is used, make sure to remove\n * the `history` extension, as it is not compatible with the `collaboration` extension.\n *\n * `@tiptap/extension-collaboration` uses its own history implementation.\n */\nconst History = _tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Extension.create({\n    name: 'history',\n    addOptions() {\n        return {\n            depth: 100,\n            newGroupDelay: 500,\n        };\n    },\n    addCommands() {\n        return {\n            undo: () => ({ state, dispatch }) => {\n                return (0,_tiptap_pm_history__WEBPACK_IMPORTED_MODULE_0__.undo)(state, dispatch);\n            },\n            redo: () => ({ state, dispatch }) => {\n                return (0,_tiptap_pm_history__WEBPACK_IMPORTED_MODULE_0__.redo)(state, dispatch);\n            },\n        };\n    },\n    addProseMirrorPlugins() {\n        return [\n            (0,_tiptap_pm_history__WEBPACK_IMPORTED_MODULE_0__.history)(this.options),\n        ];\n    },\n    addKeyboardShortcuts() {\n        return {\n            'Mod-z': () => this.editor.commands.undo(),\n            'Shift-Mod-z': () => this.editor.commands.redo(),\n            'Mod-y': () => this.editor.commands.redo(),\n            // Russian keyboard layouts\n            'Mod-я': () => this.editor.commands.undo(),\n            'Shift-Mod-я': () => this.editor.commands.redo(),\n        };\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-history@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-history/dist/index.js\n");

/***/ })

};
;