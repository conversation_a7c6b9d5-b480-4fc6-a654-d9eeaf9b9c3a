"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-task-item@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-task-item@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-task-item@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-task-item/dist/index.js":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-task-item@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-task-item/dist/index.js ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TaskItem: () => (/* binding */ TaskItem),\n/* harmony export */   \"default\": () => (/* binding */ TaskItem),\n/* harmony export */   inputRegex: () => (/* binding */ inputRegex)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n/**\n * Matches a task item to a - [ ] on input.\n */\nconst inputRegex = /^\\s*(\\[([( |x])?\\])\\s$/;\n/**\n * This extension allows you to create task items.\n * @see https://www.tiptap.dev/api/nodes/task-item\n */\nconst TaskItem = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Node.create({\n    name: 'taskItem',\n    addOptions() {\n        return {\n            nested: false,\n            HTMLAttributes: {},\n            taskListTypeName: 'taskList',\n            a11y: undefined,\n        };\n    },\n    content() {\n        return this.options.nested ? 'paragraph block*' : 'paragraph+';\n    },\n    defining: true,\n    addAttributes() {\n        return {\n            checked: {\n                default: false,\n                keepOnSplit: false,\n                parseHTML: element => {\n                    const dataChecked = element.getAttribute('data-checked');\n                    return dataChecked === '' || dataChecked === 'true';\n                },\n                renderHTML: attributes => ({\n                    'data-checked': attributes.checked,\n                }),\n            },\n        };\n    },\n    parseHTML() {\n        return [\n            {\n                tag: `li[data-type=\"${this.name}\"]`,\n                priority: 51,\n            },\n        ];\n    },\n    renderHTML({ node, HTMLAttributes }) {\n        return [\n            'li',\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes, {\n                'data-type': this.name,\n            }),\n            [\n                'label',\n                [\n                    'input',\n                    {\n                        type: 'checkbox',\n                        checked: node.attrs.checked ? 'checked' : null,\n                    },\n                ],\n                ['span'],\n            ],\n            ['div', 0],\n        ];\n    },\n    addKeyboardShortcuts() {\n        const shortcuts = {\n            Enter: () => this.editor.commands.splitListItem(this.name),\n            'Shift-Tab': () => this.editor.commands.liftListItem(this.name),\n        };\n        if (!this.options.nested) {\n            return shortcuts;\n        }\n        return {\n            ...shortcuts,\n            Tab: () => this.editor.commands.sinkListItem(this.name),\n        };\n    },\n    addNodeView() {\n        return ({ node, HTMLAttributes, getPos, editor, }) => {\n            const listItem = document.createElement('li');\n            const checkboxWrapper = document.createElement('label');\n            const checkboxStyler = document.createElement('span');\n            const checkbox = document.createElement('input');\n            const content = document.createElement('div');\n            const updateA11Y = () => {\n                var _a, _b;\n                checkbox.ariaLabel = ((_b = (_a = this.options.a11y) === null || _a === void 0 ? void 0 : _a.checkboxLabel) === null || _b === void 0 ? void 0 : _b.call(_a, node, checkbox.checked))\n                    || `Task item checkbox for ${node.textContent || 'empty task item'}`;\n            };\n            updateA11Y();\n            checkboxWrapper.contentEditable = 'false';\n            checkbox.type = 'checkbox';\n            checkbox.addEventListener('mousedown', event => event.preventDefault());\n            checkbox.addEventListener('change', event => {\n                // if the editor isn’t editable and we don't have a handler for\n                // readonly checks we have to undo the latest change\n                if (!editor.isEditable && !this.options.onReadOnlyChecked) {\n                    checkbox.checked = !checkbox.checked;\n                    return;\n                }\n                const { checked } = event.target;\n                if (editor.isEditable && typeof getPos === 'function') {\n                    editor\n                        .chain()\n                        .focus(undefined, { scrollIntoView: false })\n                        .command(({ tr }) => {\n                        const position = getPos();\n                        if (typeof position !== 'number') {\n                            return false;\n                        }\n                        const currentNode = tr.doc.nodeAt(position);\n                        tr.setNodeMarkup(position, undefined, {\n                            ...currentNode === null || currentNode === void 0 ? void 0 : currentNode.attrs,\n                            checked,\n                        });\n                        return true;\n                    })\n                        .run();\n                }\n                if (!editor.isEditable && this.options.onReadOnlyChecked) {\n                    // Reset state if onReadOnlyChecked returns false\n                    if (!this.options.onReadOnlyChecked(node, checked)) {\n                        checkbox.checked = !checkbox.checked;\n                    }\n                }\n            });\n            Object.entries(this.options.HTMLAttributes).forEach(([key, value]) => {\n                listItem.setAttribute(key, value);\n            });\n            listItem.dataset.checked = node.attrs.checked;\n            checkbox.checked = node.attrs.checked;\n            checkboxWrapper.append(checkbox, checkboxStyler);\n            listItem.append(checkboxWrapper, content);\n            Object.entries(HTMLAttributes).forEach(([key, value]) => {\n                listItem.setAttribute(key, value);\n            });\n            return {\n                dom: listItem,\n                contentDOM: content,\n                update: updatedNode => {\n                    if (updatedNode.type !== this.type) {\n                        return false;\n                    }\n                    listItem.dataset.checked = updatedNode.attrs.checked;\n                    checkbox.checked = updatedNode.attrs.checked;\n                    updateA11Y();\n                    return true;\n                },\n            };\n        };\n    },\n    addInputRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.wrappingInputRule)({\n                find: inputRegex,\n                type: this.type,\n                getAttributes: match => ({\n                    checked: match[match.length - 1] === 'x',\n                }),\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHRpcHRhcCtleHRlbnNpb24tdGFzay1pdGVtQDIuMjYuMV9AdGlwdGFwK2NvcmVAMi4yNi4xX0B0aXB0YXArcG1AMi4yNi4xX19AdGlwdGFwK3BtQDIuMjYuMS9ub2RlX21vZHVsZXMvQHRpcHRhcC9leHRlbnNpb24tdGFzay1pdGVtL2Rpc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF3RTs7QUFFeEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQiw4Q0FBSTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QjtBQUM5QjtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2I7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLFVBQVU7QUFDaEQ7QUFDQSxhQUFhO0FBQ2I7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHNCQUFzQjtBQUN2QztBQUNBO0FBQ0EsWUFBWSw2REFBZTtBQUMzQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLGtCQUFrQix1Q0FBdUM7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlEQUFpRCxzQ0FBc0M7QUFDdkY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLFVBQVU7QUFDbEM7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLHVCQUF1QjtBQUNuRSxvQ0FBb0MsSUFBSTtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZLCtEQUFpQjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2I7QUFDQSxLQUFLO0FBQ0wsQ0FBQzs7QUFFb0Q7QUFDckQiLCJzb3VyY2VzIjpbIi9ob21lL211emkvZ2l0aHViL2RlZXItZmxvdy9haS1lZGl0b3Itc3RhbmRhbG9uZS9ub2RlX21vZHVsZXMvLnBucG0vQHRpcHRhcCtleHRlbnNpb24tdGFzay1pdGVtQDIuMjYuMV9AdGlwdGFwK2NvcmVAMi4yNi4xX0B0aXB0YXArcG1AMi4yNi4xX19AdGlwdGFwK3BtQDIuMjYuMS9ub2RlX21vZHVsZXMvQHRpcHRhcC9leHRlbnNpb24tdGFzay1pdGVtL2Rpc3QvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTm9kZSwgbWVyZ2VBdHRyaWJ1dGVzLCB3cmFwcGluZ0lucHV0UnVsZSB9IGZyb20gJ0B0aXB0YXAvY29yZSc7XG5cbi8qKlxuICogTWF0Y2hlcyBhIHRhc2sgaXRlbSB0byBhIC0gWyBdIG9uIGlucHV0LlxuICovXG5jb25zdCBpbnB1dFJlZ2V4ID0gL15cXHMqKFxcWyhbKCB8eF0pP1xcXSlcXHMkLztcbi8qKlxuICogVGhpcyBleHRlbnNpb24gYWxsb3dzIHlvdSB0byBjcmVhdGUgdGFzayBpdGVtcy5cbiAqIEBzZWUgaHR0cHM6Ly93d3cudGlwdGFwLmRldi9hcGkvbm9kZXMvdGFzay1pdGVtXG4gKi9cbmNvbnN0IFRhc2tJdGVtID0gTm9kZS5jcmVhdGUoe1xuICAgIG5hbWU6ICd0YXNrSXRlbScsXG4gICAgYWRkT3B0aW9ucygpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIG5lc3RlZDogZmFsc2UsXG4gICAgICAgICAgICBIVE1MQXR0cmlidXRlczoge30sXG4gICAgICAgICAgICB0YXNrTGlzdFR5cGVOYW1lOiAndGFza0xpc3QnLFxuICAgICAgICAgICAgYTExeTogdW5kZWZpbmVkLFxuICAgICAgICB9O1xuICAgIH0sXG4gICAgY29udGVudCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMub3B0aW9ucy5uZXN0ZWQgPyAncGFyYWdyYXBoIGJsb2NrKicgOiAncGFyYWdyYXBoKyc7XG4gICAgfSxcbiAgICBkZWZpbmluZzogdHJ1ZSxcbiAgICBhZGRBdHRyaWJ1dGVzKCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgY2hlY2tlZDoge1xuICAgICAgICAgICAgICAgIGRlZmF1bHQ6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGtlZXBPblNwbGl0OiBmYWxzZSxcbiAgICAgICAgICAgICAgICBwYXJzZUhUTUw6IGVsZW1lbnQgPT4ge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBkYXRhQ2hlY2tlZCA9IGVsZW1lbnQuZ2V0QXR0cmlidXRlKCdkYXRhLWNoZWNrZWQnKTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGRhdGFDaGVja2VkID09PSAnJyB8fCBkYXRhQ2hlY2tlZCA9PT0gJ3RydWUnO1xuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgcmVuZGVySFRNTDogYXR0cmlidXRlcyA9PiAoe1xuICAgICAgICAgICAgICAgICAgICAnZGF0YS1jaGVja2VkJzogYXR0cmlidXRlcy5jaGVja2VkLFxuICAgICAgICAgICAgICAgIH0pLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgfTtcbiAgICB9LFxuICAgIHBhcnNlSFRNTCgpIHtcbiAgICAgICAgcmV0dXJuIFtcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICB0YWc6IGBsaVtkYXRhLXR5cGU9XCIke3RoaXMubmFtZX1cIl1gLFxuICAgICAgICAgICAgICAgIHByaW9yaXR5OiA1MSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgIF07XG4gICAgfSxcbiAgICByZW5kZXJIVE1MKHsgbm9kZSwgSFRNTEF0dHJpYnV0ZXMgfSkge1xuICAgICAgICByZXR1cm4gW1xuICAgICAgICAgICAgJ2xpJyxcbiAgICAgICAgICAgIG1lcmdlQXR0cmlidXRlcyh0aGlzLm9wdGlvbnMuSFRNTEF0dHJpYnV0ZXMsIEhUTUxBdHRyaWJ1dGVzLCB7XG4gICAgICAgICAgICAgICAgJ2RhdGEtdHlwZSc6IHRoaXMubmFtZSxcbiAgICAgICAgICAgIH0pLFxuICAgICAgICAgICAgW1xuICAgICAgICAgICAgICAgICdsYWJlbCcsXG4gICAgICAgICAgICAgICAgW1xuICAgICAgICAgICAgICAgICAgICAnaW5wdXQnLFxuICAgICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAnY2hlY2tib3gnLFxuICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZDogbm9kZS5hdHRycy5jaGVja2VkID8gJ2NoZWNrZWQnIDogbnVsbCxcbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICBdLFxuICAgICAgICAgICAgICAgIFsnc3BhbiddLFxuICAgICAgICAgICAgXSxcbiAgICAgICAgICAgIFsnZGl2JywgMF0sXG4gICAgICAgIF07XG4gICAgfSxcbiAgICBhZGRLZXlib2FyZFNob3J0Y3V0cygpIHtcbiAgICAgICAgY29uc3Qgc2hvcnRjdXRzID0ge1xuICAgICAgICAgICAgRW50ZXI6ICgpID0+IHRoaXMuZWRpdG9yLmNvbW1hbmRzLnNwbGl0TGlzdEl0ZW0odGhpcy5uYW1lKSxcbiAgICAgICAgICAgICdTaGlmdC1UYWInOiAoKSA9PiB0aGlzLmVkaXRvci5jb21tYW5kcy5saWZ0TGlzdEl0ZW0odGhpcy5uYW1lKSxcbiAgICAgICAgfTtcbiAgICAgICAgaWYgKCF0aGlzLm9wdGlvbnMubmVzdGVkKSB7XG4gICAgICAgICAgICByZXR1cm4gc2hvcnRjdXRzO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi5zaG9ydGN1dHMsXG4gICAgICAgICAgICBUYWI6ICgpID0+IHRoaXMuZWRpdG9yLmNvbW1hbmRzLnNpbmtMaXN0SXRlbSh0aGlzLm5hbWUpLFxuICAgICAgICB9O1xuICAgIH0sXG4gICAgYWRkTm9kZVZpZXcoKSB7XG4gICAgICAgIHJldHVybiAoeyBub2RlLCBIVE1MQXR0cmlidXRlcywgZ2V0UG9zLCBlZGl0b3IsIH0pID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGxpc3RJdGVtID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnbGknKTtcbiAgICAgICAgICAgIGNvbnN0IGNoZWNrYm94V3JhcHBlciA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2xhYmVsJyk7XG4gICAgICAgICAgICBjb25zdCBjaGVja2JveFN0eWxlciA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ3NwYW4nKTtcbiAgICAgICAgICAgIGNvbnN0IGNoZWNrYm94ID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnaW5wdXQnKTtcbiAgICAgICAgICAgIGNvbnN0IGNvbnRlbnQgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdkaXYnKTtcbiAgICAgICAgICAgIGNvbnN0IHVwZGF0ZUExMVkgPSAoKSA9PiB7XG4gICAgICAgICAgICAgICAgdmFyIF9hLCBfYjtcbiAgICAgICAgICAgICAgICBjaGVja2JveC5hcmlhTGFiZWwgPSAoKF9iID0gKF9hID0gdGhpcy5vcHRpb25zLmExMXkpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5jaGVja2JveExhYmVsKSA9PT0gbnVsbCB8fCBfYiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2IuY2FsbChfYSwgbm9kZSwgY2hlY2tib3guY2hlY2tlZCkpXG4gICAgICAgICAgICAgICAgICAgIHx8IGBUYXNrIGl0ZW0gY2hlY2tib3ggZm9yICR7bm9kZS50ZXh0Q29udGVudCB8fCAnZW1wdHkgdGFzayBpdGVtJ31gO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIHVwZGF0ZUExMVkoKTtcbiAgICAgICAgICAgIGNoZWNrYm94V3JhcHBlci5jb250ZW50RWRpdGFibGUgPSAnZmFsc2UnO1xuICAgICAgICAgICAgY2hlY2tib3gudHlwZSA9ICdjaGVja2JveCc7XG4gICAgICAgICAgICBjaGVja2JveC5hZGRFdmVudExpc3RlbmVyKCdtb3VzZWRvd24nLCBldmVudCA9PiBldmVudC5wcmV2ZW50RGVmYXVsdCgpKTtcbiAgICAgICAgICAgIGNoZWNrYm94LmFkZEV2ZW50TGlzdGVuZXIoJ2NoYW5nZScsIGV2ZW50ID0+IHtcbiAgICAgICAgICAgICAgICAvLyBpZiB0aGUgZWRpdG9yIGlzbuKAmXQgZWRpdGFibGUgYW5kIHdlIGRvbid0IGhhdmUgYSBoYW5kbGVyIGZvclxuICAgICAgICAgICAgICAgIC8vIHJlYWRvbmx5IGNoZWNrcyB3ZSBoYXZlIHRvIHVuZG8gdGhlIGxhdGVzdCBjaGFuZ2VcbiAgICAgICAgICAgICAgICBpZiAoIWVkaXRvci5pc0VkaXRhYmxlICYmICF0aGlzLm9wdGlvbnMub25SZWFkT25seUNoZWNrZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgY2hlY2tib3guY2hlY2tlZCA9ICFjaGVja2JveC5jaGVja2VkO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IHsgY2hlY2tlZCB9ID0gZXZlbnQudGFyZ2V0O1xuICAgICAgICAgICAgICAgIGlmIChlZGl0b3IuaXNFZGl0YWJsZSAmJiB0eXBlb2YgZ2V0UG9zID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICAgICAgICAgICAgIGVkaXRvclxuICAgICAgICAgICAgICAgICAgICAgICAgLmNoYWluKClcbiAgICAgICAgICAgICAgICAgICAgICAgIC5mb2N1cyh1bmRlZmluZWQsIHsgc2Nyb2xsSW50b1ZpZXc6IGZhbHNlIH0pXG4gICAgICAgICAgICAgICAgICAgICAgICAuY29tbWFuZCgoeyB0ciB9KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBwb3NpdGlvbiA9IGdldFBvcygpO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHR5cGVvZiBwb3NpdGlvbiAhPT0gJ251bWJlcicpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjdXJyZW50Tm9kZSA9IHRyLmRvYy5ub2RlQXQocG9zaXRpb24pO1xuICAgICAgICAgICAgICAgICAgICAgICAgdHIuc2V0Tm9kZU1hcmt1cChwb3NpdGlvbiwgdW5kZWZpbmVkLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uY3VycmVudE5vZGUgPT09IG51bGwgfHwgY3VycmVudE5vZGUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGN1cnJlbnROb2RlLmF0dHJzLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQsXG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgICAgICAgICAgLnJ1bigpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoIWVkaXRvci5pc0VkaXRhYmxlICYmIHRoaXMub3B0aW9ucy5vblJlYWRPbmx5Q2hlY2tlZCkge1xuICAgICAgICAgICAgICAgICAgICAvLyBSZXNldCBzdGF0ZSBpZiBvblJlYWRPbmx5Q2hlY2tlZCByZXR1cm5zIGZhbHNlXG4gICAgICAgICAgICAgICAgICAgIGlmICghdGhpcy5vcHRpb25zLm9uUmVhZE9ubHlDaGVja2VkKG5vZGUsIGNoZWNrZWQpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjaGVja2JveC5jaGVja2VkID0gIWNoZWNrYm94LmNoZWNrZWQ7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIE9iamVjdC5lbnRyaWVzKHRoaXMub3B0aW9ucy5IVE1MQXR0cmlidXRlcykuZm9yRWFjaCgoW2tleSwgdmFsdWVdKSA9PiB7XG4gICAgICAgICAgICAgICAgbGlzdEl0ZW0uc2V0QXR0cmlidXRlKGtleSwgdmFsdWUpO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBsaXN0SXRlbS5kYXRhc2V0LmNoZWNrZWQgPSBub2RlLmF0dHJzLmNoZWNrZWQ7XG4gICAgICAgICAgICBjaGVja2JveC5jaGVja2VkID0gbm9kZS5hdHRycy5jaGVja2VkO1xuICAgICAgICAgICAgY2hlY2tib3hXcmFwcGVyLmFwcGVuZChjaGVja2JveCwgY2hlY2tib3hTdHlsZXIpO1xuICAgICAgICAgICAgbGlzdEl0ZW0uYXBwZW5kKGNoZWNrYm94V3JhcHBlciwgY29udGVudCk7XG4gICAgICAgICAgICBPYmplY3QuZW50cmllcyhIVE1MQXR0cmlidXRlcykuZm9yRWFjaCgoW2tleSwgdmFsdWVdKSA9PiB7XG4gICAgICAgICAgICAgICAgbGlzdEl0ZW0uc2V0QXR0cmlidXRlKGtleSwgdmFsdWUpO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIGRvbTogbGlzdEl0ZW0sXG4gICAgICAgICAgICAgICAgY29udGVudERPTTogY29udGVudCxcbiAgICAgICAgICAgICAgICB1cGRhdGU6IHVwZGF0ZWROb2RlID0+IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHVwZGF0ZWROb2RlLnR5cGUgIT09IHRoaXMudHlwZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGxpc3RJdGVtLmRhdGFzZXQuY2hlY2tlZCA9IHVwZGF0ZWROb2RlLmF0dHJzLmNoZWNrZWQ7XG4gICAgICAgICAgICAgICAgICAgIGNoZWNrYm94LmNoZWNrZWQgPSB1cGRhdGVkTm9kZS5hdHRycy5jaGVja2VkO1xuICAgICAgICAgICAgICAgICAgICB1cGRhdGVBMTFZKCk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9O1xuICAgICAgICB9O1xuICAgIH0sXG4gICAgYWRkSW5wdXRSdWxlcygpIHtcbiAgICAgICAgcmV0dXJuIFtcbiAgICAgICAgICAgIHdyYXBwaW5nSW5wdXRSdWxlKHtcbiAgICAgICAgICAgICAgICBmaW5kOiBpbnB1dFJlZ2V4LFxuICAgICAgICAgICAgICAgIHR5cGU6IHRoaXMudHlwZSxcbiAgICAgICAgICAgICAgICBnZXRBdHRyaWJ1dGVzOiBtYXRjaCA9PiAoe1xuICAgICAgICAgICAgICAgICAgICBjaGVja2VkOiBtYXRjaFttYXRjaC5sZW5ndGggLSAxXSA9PT0gJ3gnLFxuICAgICAgICAgICAgICAgIH0pLFxuICAgICAgICAgICAgfSksXG4gICAgICAgIF07XG4gICAgfSxcbn0pO1xuXG5leHBvcnQgeyBUYXNrSXRlbSwgVGFza0l0ZW0gYXMgZGVmYXVsdCwgaW5wdXRSZWdleCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-task-item@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-task-item/dist/index.js\n");

/***/ })

};
;