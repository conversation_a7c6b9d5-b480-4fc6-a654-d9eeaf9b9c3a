"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@scena+matrix@1.1.1";
exports.ids = ["vendor-chunks/@scena+matrix@1.1.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@scena+matrix@1.1.1/node_modules/@scena/matrix/dist/matrix.esm.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/.pnpm/@scena+matrix@1.1.1/node_modules/@scena/matrix/dist/matrix.esm.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculate: () => (/* binding */ calculate),\n/* harmony export */   convertCSStoMatrix: () => (/* binding */ convertCSStoMatrix),\n/* harmony export */   convertDimension: () => (/* binding */ convertDimension),\n/* harmony export */   convertMatrixtoCSS: () => (/* binding */ convertMatrixtoCSS),\n/* harmony export */   convertPositionMatrix: () => (/* binding */ convertPositionMatrix),\n/* harmony export */   createIdentityMatrix: () => (/* binding */ createIdentityMatrix),\n/* harmony export */   createOriginMatrix: () => (/* binding */ createOriginMatrix),\n/* harmony export */   createRotateMatrix: () => (/* binding */ createRotateMatrix),\n/* harmony export */   createScaleMatrix: () => (/* binding */ createScaleMatrix),\n/* harmony export */   createWarpMatrix: () => (/* binding */ createWarpMatrix),\n/* harmony export */   fromTranslation: () => (/* binding */ fromTranslation),\n/* harmony export */   getCenter: () => (/* binding */ getCenter),\n/* harmony export */   getOrigin: () => (/* binding */ getOrigin),\n/* harmony export */   ignoreDimension: () => (/* binding */ ignoreDimension),\n/* harmony export */   invert: () => (/* binding */ invert),\n/* harmony export */   matrix3d: () => (/* binding */ matrix3d),\n/* harmony export */   minus: () => (/* binding */ minus),\n/* harmony export */   multiplies: () => (/* binding */ multiplies),\n/* harmony export */   multiply: () => (/* binding */ multiply),\n/* harmony export */   plus: () => (/* binding */ plus),\n/* harmony export */   rotate: () => (/* binding */ rotate),\n/* harmony export */   rotateX3d: () => (/* binding */ rotateX3d),\n/* harmony export */   rotateY3d: () => (/* binding */ rotateY3d),\n/* harmony export */   rotateZ3d: () => (/* binding */ rotateZ3d),\n/* harmony export */   scale3d: () => (/* binding */ scale3d),\n/* harmony export */   translate3d: () => (/* binding */ translate3d),\n/* harmony export */   transpose: () => (/* binding */ transpose)\n/* harmony export */ });\n/* harmony import */ var _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @daybrush/utils */ \"(ssr)/./node_modules/.pnpm/@daybrush+utils@1.13.0/node_modules/@daybrush/utils/dist/utils.esm.js\");\n/*\nCopyright (c) 2020 Daybrush\nname: @scena/matrix\nlicense: MIT\nauthor: Daybrush\nrepository: git+https://github.com/daybrush/matrix\nversion: 1.1.1\n*/\n\n\nfunction add(matrix, inverseMatrix, startIndex, fromIndex, n, k) {\n  for (var i = 0; i < n; ++i) {\n    var x = startIndex + i * n;\n    var fromX = fromIndex + i * n;\n    matrix[x] += matrix[fromX] * k;\n    inverseMatrix[x] += inverseMatrix[fromX] * k;\n  }\n}\n\nfunction swap(matrix, inverseMatrix, startIndex, fromIndex, n) {\n  for (var i = 0; i < n; ++i) {\n    var x = startIndex + i * n;\n    var fromX = fromIndex + i * n;\n    var v = matrix[x];\n    var iv = inverseMatrix[x];\n    matrix[x] = matrix[fromX];\n    matrix[fromX] = v;\n    inverseMatrix[x] = inverseMatrix[fromX];\n    inverseMatrix[fromX] = iv;\n  }\n}\n\nfunction divide(matrix, inverseMatrix, startIndex, n, k) {\n  for (var i = 0; i < n; ++i) {\n    var x = startIndex + i * n;\n    matrix[x] /= k;\n    inverseMatrix[x] /= k;\n  }\n}\n/**\n *\n * @namespace Matrix\n */\n\n/**\n * @memberof Matrix\n */\n\n\nfunction ignoreDimension(matrix, m, n) {\n  if (n === void 0) {\n    n = Math.sqrt(matrix.length);\n  }\n\n  var newMatrix = matrix.slice();\n\n  for (var i = 0; i < n; ++i) {\n    newMatrix[i * n + m - 1] = 0;\n    newMatrix[(m - 1) * n + i] = 0;\n  }\n\n  newMatrix[(m - 1) * (n + 1)] = 1;\n  return newMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction invert(matrix, n) {\n  if (n === void 0) {\n    n = Math.sqrt(matrix.length);\n  }\n\n  var newMatrix = matrix.slice();\n  var inverseMatrix = createIdentityMatrix(n);\n\n  for (var i = 0; i < n; ++i) {\n    // diagonal\n    var identityIndex = n * i + i;\n\n    if (!(0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.throttle)(newMatrix[identityIndex], _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.TINY_NUM)) {\n      // newMatrix[identityIndex] = 0;\n      for (var j = i + 1; j < n; ++j) {\n        if (newMatrix[n * i + j]) {\n          swap(newMatrix, inverseMatrix, i, j, n);\n          break;\n        }\n      }\n    }\n\n    if (!(0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.throttle)(newMatrix[identityIndex], _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.TINY_NUM)) {\n      // no inverse matrix\n      return [];\n    }\n\n    divide(newMatrix, inverseMatrix, i, n, newMatrix[identityIndex]);\n\n    for (var j = 0; j < n; ++j) {\n      var targetStartIndex = j;\n      var targetIndex = j + i * n;\n      var target = newMatrix[targetIndex];\n\n      if (!(0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.throttle)(target, _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.TINY_NUM) || i === j) {\n        continue;\n      }\n\n      add(newMatrix, inverseMatrix, targetStartIndex, i, n, -target);\n    }\n  }\n\n  return inverseMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction transpose(matrix, n) {\n  if (n === void 0) {\n    n = Math.sqrt(matrix.length);\n  }\n\n  var newMatrix = [];\n\n  for (var i = 0; i < n; ++i) {\n    for (var j = 0; j < n; ++j) {\n      newMatrix[j * n + i] = matrix[n * i + j];\n    }\n  }\n\n  return newMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction getOrigin(matrix, n) {\n  if (n === void 0) {\n    n = Math.sqrt(matrix.length);\n  }\n\n  var originMatrix = [];\n  var w = matrix[n * n - 1];\n\n  for (var i = 0; i < n - 1; ++i) {\n    originMatrix[i] = matrix[n * (n - 1) + i] / w;\n  }\n\n  originMatrix[n - 1] = 0;\n  return originMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction fromTranslation(pos, n) {\n  var newMatrix = createIdentityMatrix(n);\n\n  for (var i = 0; i < n - 1; ++i) {\n    newMatrix[n * (n - 1) + i] = pos[i] || 0;\n  }\n\n  return newMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction convertPositionMatrix(matrix, n) {\n  var newMatrix = matrix.slice();\n\n  for (var i = matrix.length; i < n - 1; ++i) {\n    newMatrix[i] = 0;\n  }\n\n  newMatrix[n - 1] = 1;\n  return newMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction convertDimension(matrix, n, m) {\n  if (n === void 0) {\n    n = Math.sqrt(matrix.length);\n  } // n < m\n\n\n  if (n === m) {\n    return matrix;\n  }\n\n  var newMatrix = createIdentityMatrix(m);\n  var length = Math.min(n, m);\n\n  for (var i = 0; i < length - 1; ++i) {\n    for (var j = 0; j < length - 1; ++j) {\n      newMatrix[i * m + j] = matrix[i * n + j];\n    }\n\n    newMatrix[(i + 1) * m - 1] = matrix[(i + 1) * n - 1];\n    newMatrix[(m - 1) * m + i] = matrix[(n - 1) * n + i];\n  }\n\n  newMatrix[m * m - 1] = matrix[n * n - 1];\n  return newMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction multiplies(n) {\n  var matrixes = [];\n\n  for (var _i = 1; _i < arguments.length; _i++) {\n    matrixes[_i - 1] = arguments[_i];\n  }\n\n  var m = createIdentityMatrix(n);\n  matrixes.forEach(function (matrix) {\n    m = multiply(m, matrix, n);\n  });\n  return m;\n}\n/**\n * @memberof Matrix\n */\n\nfunction multiply(matrix, matrix2, n) {\n  if (n === void 0) {\n    n = Math.sqrt(matrix.length);\n  }\n\n  var newMatrix = []; // 1 y: n\n  // 1 x: m\n  // 2 x: m\n  // 2 y: k\n  // n * m X m * k\n\n  var m = matrix.length / n;\n  var k = matrix2.length / m;\n\n  if (!m) {\n    return matrix2;\n  } else if (!k) {\n    return matrix;\n  }\n\n  for (var i = 0; i < n; ++i) {\n    for (var j = 0; j < k; ++j) {\n      newMatrix[j * n + i] = 0;\n\n      for (var l = 0; l < m; ++l) {\n        // m1 x: m(l), y: n(i)\n        // m2 x: k(j):  y: m(l)\n        // nw x: n(i), y: k(j)\n        newMatrix[j * n + i] += matrix[l * n + i] * matrix2[j * m + l];\n      }\n    }\n  } // n * k\n\n\n  return newMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction plus(pos1, pos2) {\n  var length = Math.min(pos1.length, pos2.length);\n  var nextPos = pos1.slice();\n\n  for (var i = 0; i < length; ++i) {\n    nextPos[i] = nextPos[i] + pos2[i];\n  }\n\n  return nextPos;\n}\n/**\n * @memberof Matrix\n */\n\nfunction minus(pos1, pos2) {\n  var length = Math.min(pos1.length, pos2.length);\n  var nextPos = pos1.slice();\n\n  for (var i = 0; i < length; ++i) {\n    nextPos[i] = nextPos[i] - pos2[i];\n  }\n\n  return nextPos;\n}\n/**\n * @memberof Matrix\n */\n\nfunction convertCSStoMatrix(a, is2d) {\n  if (is2d === void 0) {\n    is2d = a.length === 6;\n  }\n\n  if (is2d) {\n    return [a[0], a[1], 0, a[2], a[3], 0, a[4], a[5], 1];\n  }\n\n  return a;\n}\n/**\n * @memberof Matrix\n */\n\nfunction convertMatrixtoCSS(a, is2d) {\n  if (is2d === void 0) {\n    is2d = a.length === 9;\n  }\n\n  if (is2d) {\n    return [a[0], a[1], a[3], a[4], a[6], a[7]];\n  }\n\n  return a;\n}\n/**\n * @memberof Matrix\n */\n\nfunction calculate(matrix, matrix2, n) {\n  if (n === void 0) {\n    n = matrix2.length;\n  }\n\n  var result = multiply(matrix, matrix2, n);\n  var k = result[n - 1];\n  return result.map(function (v) {\n    return v / k;\n  });\n}\n/**\n * @memberof Matrix\n */\n\nfunction rotateX3d(matrix, rad) {\n  return multiply(matrix, [1, 0, 0, 0, 0, Math.cos(rad), Math.sin(rad), 0, 0, -Math.sin(rad), Math.cos(rad), 0, 0, 0, 0, 1], 4);\n}\n/**\n * @memberof Matrix\n */\n\nfunction rotateY3d(matrix, rad) {\n  return multiply(matrix, [Math.cos(rad), 0, -Math.sin(rad), 0, 0, 1, 0, 0, Math.sin(rad), 0, Math.cos(rad), 0, 0, 0, 0, 1], 4);\n}\n/**\n * @memberof Matrix\n */\n\nfunction rotateZ3d(matrix, rad) {\n  return multiply(matrix, createRotateMatrix(rad, 4));\n}\n/**\n * @memberof Matrix\n */\n\nfunction scale3d(matrix, _a) {\n  var _b = _a[0],\n      sx = _b === void 0 ? 1 : _b,\n      _c = _a[1],\n      sy = _c === void 0 ? 1 : _c,\n      _d = _a[2],\n      sz = _d === void 0 ? 1 : _d;\n  return multiply(matrix, [sx, 0, 0, 0, 0, sy, 0, 0, 0, 0, sz, 0, 0, 0, 0, 1], 4);\n}\n/**\n * @memberof Matrix\n */\n\nfunction rotate(pos, rad) {\n  return calculate(createRotateMatrix(rad, 3), convertPositionMatrix(pos, 3));\n}\n/**\n * @memberof Matrix\n */\n\nfunction translate3d(matrix, _a) {\n  var _b = _a[0],\n      tx = _b === void 0 ? 0 : _b,\n      _c = _a[1],\n      ty = _c === void 0 ? 0 : _c,\n      _d = _a[2],\n      tz = _d === void 0 ? 0 : _d;\n  return multiply(matrix, [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, tx, ty, tz, 1], 4);\n}\n/**\n * @memberof Matrix\n */\n\nfunction matrix3d(matrix1, matrix2) {\n  return multiply(matrix1, matrix2, 4);\n}\n/**\n * @memberof Matrix\n */\n\nfunction createRotateMatrix(rad, n) {\n  var cos = Math.cos(rad);\n  var sin = Math.sin(rad);\n  var m = createIdentityMatrix(n); // cos -sin\n  // sin cos\n\n  m[0] = cos;\n  m[1] = sin;\n  m[n] = -sin;\n  m[n + 1] = cos;\n  return m;\n}\n/**\n * @memberof Matrix\n */\n\nfunction createIdentityMatrix(n) {\n  var length = n * n;\n  var matrix = [];\n\n  for (var i = 0; i < length; ++i) {\n    matrix[i] = i % (n + 1) ? 0 : 1;\n  }\n\n  return matrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction createScaleMatrix(scale, n) {\n  var m = createIdentityMatrix(n);\n  var length = Math.min(scale.length, n - 1);\n\n  for (var i = 0; i < length; ++i) {\n    m[(n + 1) * i] = scale[i];\n  }\n\n  return m;\n}\n/**\n * @memberof Matrix\n */\n\nfunction createOriginMatrix(origin, n) {\n  var m = createIdentityMatrix(n);\n  var length = Math.min(origin.length, n - 1);\n\n  for (var i = 0; i < length; ++i) {\n    m[n * (n - 1) + i] = origin[i];\n  }\n\n  return m;\n}\n/**\n * @memberof Matrix\n */\n\nfunction createWarpMatrix(pos0, pos1, pos2, pos3, nextPos0, nextPos1, nextPos2, nextPos3) {\n  var x0 = pos0[0],\n      y0 = pos0[1];\n  var x1 = pos1[0],\n      y1 = pos1[1];\n  var x2 = pos2[0],\n      y2 = pos2[1];\n  var x3 = pos3[0],\n      y3 = pos3[1];\n  var u0 = nextPos0[0],\n      v0 = nextPos0[1];\n  var u1 = nextPos1[0],\n      v1 = nextPos1[1];\n  var u2 = nextPos2[0],\n      v2 = nextPos2[1];\n  var u3 = nextPos3[0],\n      v3 = nextPos3[1];\n  var matrix = [x0, 0, x1, 0, x2, 0, x3, 0, y0, 0, y1, 0, y2, 0, y3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, x0, 0, x1, 0, x2, 0, x3, 0, y0, 0, y1, 0, y2, 0, y3, 0, 1, 0, 1, 0, 1, 0, 1, -u0 * x0, -v0 * x0, -u1 * x1, -v1 * x1, -u2 * x2, -v2 * x2, -u3 * x3, -v3 * x3, -u0 * y0, -v0 * y0, -u1 * y1, -v1 * y1, -u2 * y2, -v2 * y2, -u3 * y3, -v3 * y3];\n  var inverseMatrix = invert(matrix, 8);\n\n  if (!inverseMatrix.length) {\n    return [];\n  }\n\n  var h = multiply(inverseMatrix, [u0, v0, u1, v1, u2, v2, u3, v3], 8);\n  h[8] = 1;\n  return convertDimension(transpose(h), 3, 4);\n}\n/**\n * @memberof Matrix\n */\n\nfunction getCenter(points) {\n  return [0, 1].map(function (i) {\n    return (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.average)(points.map(function (pos) {\n      return pos[i];\n    }));\n  });\n}\n\n\n//# sourceMappingURL=matrix.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@scena+matrix@1.1.1/node_modules/@scena/matrix/dist/matrix.esm.js\n");

/***/ })

};
;