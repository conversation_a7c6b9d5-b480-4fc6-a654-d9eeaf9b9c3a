"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-hard-break@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-hard-break@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-hard-break@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-hard-break/dist/index.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-hard-break@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-hard-break/dist/index.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HardBreak: () => (/* binding */ HardBreak),\n/* harmony export */   \"default\": () => (/* binding */ HardBreak)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n/**\n * This extension allows you to insert hard breaks.\n * @see https://www.tiptap.dev/api/nodes/hard-break\n */\nconst HardBreak = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Node.create({\n    name: 'hardBreak',\n    addOptions() {\n        return {\n            keepMarks: true,\n            HTMLAttributes: {},\n        };\n    },\n    inline: true,\n    group: 'inline',\n    selectable: false,\n    linebreakReplacement: true,\n    parseHTML() {\n        return [\n            { tag: 'br' },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['br', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes)];\n    },\n    renderText() {\n        return '\\n';\n    },\n    addCommands() {\n        return {\n            setHardBreak: () => ({ commands, chain, state, editor, }) => {\n                return commands.first([\n                    () => commands.exitCode(),\n                    () => commands.command(() => {\n                        const { selection, storedMarks } = state;\n                        if (selection.$from.parent.type.spec.isolating) {\n                            return false;\n                        }\n                        const { keepMarks } = this.options;\n                        const { splittableMarks } = editor.extensionManager;\n                        const marks = storedMarks\n                            || (selection.$to.parentOffset && selection.$from.marks());\n                        return chain()\n                            .insertContent({ type: this.name })\n                            .command(({ tr, dispatch }) => {\n                            if (dispatch && marks && keepMarks) {\n                                const filteredMarks = marks\n                                    .filter(mark => splittableMarks.includes(mark.type.name));\n                                tr.ensureMarks(filteredMarks);\n                            }\n                            return true;\n                        })\n                            .run();\n                    }),\n                ]);\n            },\n        };\n    },\n    addKeyboardShortcuts() {\n        return {\n            'Mod-Enter': () => this.editor.commands.setHardBreak(),\n            'Shift-Enter': () => this.editor.commands.setHardBreak(),\n        };\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-hard-break@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-hard-break/dist/index.js\n");

/***/ })

};
;