"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/css-to-mat@1.1.1";
exports.ids = ["vendor-chunks/css-to-mat@1.1.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/css-to-mat@1.1.1/node_modules/css-to-mat/dist/css-to-mat.esm.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/css-to-mat@1.1.1/node_modules/css-to-mat/dist/css-to-mat.esm.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateMatrixDist: () => (/* binding */ calculateMatrixDist),\n/* harmony export */   createMatrix: () => (/* binding */ createMatrix),\n/* harmony export */   getDistElementMatrix: () => (/* binding */ getDistElementMatrix),\n/* harmony export */   getElementMatrix: () => (/* binding */ getElementMatrix),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parseMat: () => (/* binding */ parseMat),\n/* harmony export */   toMat: () => (/* binding */ toMat)\n/* harmony export */ });\n/* harmony import */ var _daybrush_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @daybrush/utils */ \"(ssr)/./node_modules/.pnpm/@daybrush+utils@1.13.0/node_modules/@daybrush/utils/dist/utils.esm.js\");\n/* harmony import */ var _scena_matrix__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @scena/matrix */ \"(ssr)/./node_modules/.pnpm/@scena+matrix@1.1.1/node_modules/@scena/matrix/dist/matrix.esm.js\");\n/*\nCopyright (c) 2019 Daybrush\nname: css-to-mat\nlicense: MIT\nauthor: Daybrush\nrepository: git+https://github.com/daybrush/css-to-mat.git\nversion: 1.1.0\n*/\n\n\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nfunction createMatrix() {\n    return [\n        1, 0, 0, 0,\n        0, 1, 0, 0,\n        0, 0, 1, 0,\n        0, 0, 0, 1,\n    ];\n}\nfunction parseMat(transform, size) {\n    if (size === void 0) { size = 0; }\n    return toMat(parse(transform, size));\n}\nfunction getElementMatrix(el) {\n    return parseMat(getComputedStyle(el).transform);\n}\nfunction calculateMatrixDist(matrix, pos) {\n    var res = (0,_scena_matrix__WEBPACK_IMPORTED_MODULE_0__.calculate)(matrix, [pos[0], pos[1] || 0, pos[2] || 0, 1], 4);\n    var w = res[3] || 1;\n    return [\n        res[0] / w,\n        res[1] / w,\n        res[2] / w,\n    ];\n}\nfunction getDistElementMatrix(el, container) {\n    if (container === void 0) { container = document.body; }\n    var target = el;\n    var matrix = createMatrix();\n    while (target) {\n        var transform = getComputedStyle(target).transform;\n        matrix = (0,_scena_matrix__WEBPACK_IMPORTED_MODULE_0__.matrix3d)(parseMat(transform), matrix);\n        if (target === container) {\n            break;\n        }\n        target = target.parentElement;\n    }\n    matrix = (0,_scena_matrix__WEBPACK_IMPORTED_MODULE_0__.invert)(matrix, 4);\n    matrix[12] = 0;\n    matrix[13] = 0;\n    matrix[14] = 0;\n    return matrix;\n}\nfunction toMat(matrixInfos) {\n    var target = createMatrix();\n    matrixInfos.forEach(function (info) {\n        var matrixFunction = info.matrixFunction, functionValue = info.functionValue;\n        if (!matrixFunction) {\n            return;\n        }\n        target = matrixFunction(target, functionValue);\n    });\n    return target;\n}\nfunction parse(transform, size) {\n    if (size === void 0) { size = 0; }\n    var transforms = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_1__.isArray)(transform) ? transform : (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_1__.splitSpace)(transform);\n    return transforms.map(function (t) {\n        var _a = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_1__.splitBracket)(t), name = _a.prefix, value = _a.value;\n        var matrixFunction = null;\n        var functionName = name;\n        var functionValue = \"\";\n        if (name === \"translate\" || name === \"translateX\" || name === \"translate3d\") {\n            var nextSize_1 = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_1__.isObject)(size) ? __assign(__assign({}, size), { \"o%\": size[\"%\"] }) : {\n                \"%\": size,\n                \"o%\": size,\n            };\n            var _b = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_1__.splitComma)(value).map(function (v, i) {\n                if (i === 0 && \"x%\" in nextSize_1) {\n                    nextSize_1[\"%\"] = size[\"x%\"];\n                }\n                else if (i === 1 && \"y%\" in nextSize_1) {\n                    nextSize_1[\"%\"] = size[\"y%\"];\n                }\n                else {\n                    nextSize_1[\"%\"] = size[\"o%\"];\n                }\n                return (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_1__.convertUnitSize)(v, nextSize_1);\n            }), posX = _b[0], _c = _b[1], posY = _c === void 0 ? 0 : _c, _d = _b[2], posZ = _d === void 0 ? 0 : _d;\n            matrixFunction = _scena_matrix__WEBPACK_IMPORTED_MODULE_0__.translate3d;\n            functionValue = [posX, posY, posZ];\n        }\n        else if (name === \"translateY\") {\n            var nextSize = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_1__.isObject)(size) ? __assign({ \"%\": size[\"y%\"] }, size) : {\n                \"%\": size,\n            };\n            var posY = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_1__.convertUnitSize)(value, nextSize);\n            matrixFunction = _scena_matrix__WEBPACK_IMPORTED_MODULE_0__.translate3d;\n            functionValue = [0, posY, 0];\n        }\n        else if (name === \"translateZ\") {\n            var posZ = parseFloat(value);\n            matrixFunction = _scena_matrix__WEBPACK_IMPORTED_MODULE_0__.translate3d;\n            functionValue = [0, 0, posZ];\n        }\n        else if (name === \"scale\" || name === \"scale3d\") {\n            var _e = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_1__.splitComma)(value).map(function (v) { return parseFloat(v); }), sx = _e[0], _f = _e[1], sy = _f === void 0 ? sx : _f, _g = _e[2], sz = _g === void 0 ? 1 : _g;\n            matrixFunction = _scena_matrix__WEBPACK_IMPORTED_MODULE_0__.scale3d;\n            functionValue = [sx, sy, sz];\n        }\n        else if (name === \"scaleX\") {\n            var sx = parseFloat(value);\n            matrixFunction = _scena_matrix__WEBPACK_IMPORTED_MODULE_0__.scale3d;\n            functionValue = [sx, 1, 1];\n        }\n        else if (name === \"scaleY\") {\n            var sy = parseFloat(value);\n            matrixFunction = _scena_matrix__WEBPACK_IMPORTED_MODULE_0__.scale3d;\n            functionValue = [1, sy, 1];\n        }\n        else if (name === \"scaleZ\") {\n            var sz = parseFloat(value);\n            matrixFunction = _scena_matrix__WEBPACK_IMPORTED_MODULE_0__.scale3d;\n            functionValue = [1, 1, sz];\n        }\n        else if (name === \"rotate\" || name === \"rotateZ\" || name === \"rotateX\" || name === \"rotateY\") {\n            var _h = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_1__.splitUnit)(value), unit = _h.unit, unitValue = _h.value;\n            var rad = unit === \"rad\" ? unitValue : unitValue * Math.PI / 180;\n            if (name === \"rotate\" || name === \"rotateZ\") {\n                functionName = \"rotateZ\";\n                matrixFunction = _scena_matrix__WEBPACK_IMPORTED_MODULE_0__.rotateZ3d;\n            }\n            else if (name === \"rotateX\") {\n                matrixFunction = _scena_matrix__WEBPACK_IMPORTED_MODULE_0__.rotateX3d;\n            }\n            else if (name === \"rotateY\") {\n                matrixFunction = _scena_matrix__WEBPACK_IMPORTED_MODULE_0__.rotateY3d;\n            }\n            functionValue = rad;\n        }\n        else if (name === \"matrix3d\") {\n            matrixFunction = _scena_matrix__WEBPACK_IMPORTED_MODULE_0__.matrix3d;\n            functionValue = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_1__.splitComma)(value).map(function (v) { return parseFloat(v); });\n        }\n        else if (name === \"matrix\") {\n            var m = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_1__.splitComma)(value).map(function (v) { return parseFloat(v); });\n            matrixFunction = _scena_matrix__WEBPACK_IMPORTED_MODULE_0__.matrix3d;\n            functionValue = [\n                m[0], m[1], 0, 0,\n                m[2], m[3], 0, 0,\n                0, 0, 1, 0,\n                m[4], m[5], 0, 1,\n            ];\n        }\n        else {\n            functionName = \"\";\n        }\n        return {\n            name: name,\n            functionName: functionName,\n            value: value,\n            matrixFunction: matrixFunction,\n            functionValue: functionValue,\n        };\n    });\n}\n\n\n//# sourceMappingURL=css-to-mat.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/css-to-mat@1.1.1/node_modules/css-to-mat/dist/css-to-mat.esm.js\n");

/***/ })

};
;