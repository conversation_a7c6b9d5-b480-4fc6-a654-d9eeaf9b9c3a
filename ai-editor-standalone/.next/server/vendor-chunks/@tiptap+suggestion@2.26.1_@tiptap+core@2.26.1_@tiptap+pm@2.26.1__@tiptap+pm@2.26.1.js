"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+suggestion@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+suggestion@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+suggestion@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/suggestion/dist/index.js":
/*!*************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+suggestion@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/suggestion/dist/index.js ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Suggestion: () => (/* binding */ Suggestion),\n/* harmony export */   SuggestionPluginKey: () => (/* binding */ SuggestionPluginKey),\n/* harmony export */   \"default\": () => (/* binding */ Suggestion),\n/* harmony export */   findSuggestionMatch: () => (/* binding */ findSuggestionMatch)\n/* harmony export */ });\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/state/dist/index.js\");\n/* harmony import */ var _tiptap_pm_view__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/pm/view */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/view/dist/index.js\");\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n\n\nfunction findSuggestionMatch(config) {\n    var _a;\n    const { char, allowSpaces: allowSpacesOption, allowToIncludeChar, allowedPrefixes, startOfLine, $position, } = config;\n    const allowSpaces = allowSpacesOption && !allowToIncludeChar;\n    const escapedChar = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.escapeForRegEx)(char);\n    const suffix = new RegExp(`\\\\s${escapedChar}$`);\n    const prefix = startOfLine ? '^' : '';\n    const finalEscapedChar = allowToIncludeChar ? '' : escapedChar;\n    const regexp = allowSpaces\n        ? new RegExp(`${prefix}${escapedChar}.*?(?=\\\\s${finalEscapedChar}|$)`, 'gm')\n        : new RegExp(`${prefix}(?:^)?${escapedChar}[^\\\\s${finalEscapedChar}]*`, 'gm');\n    const text = ((_a = $position.nodeBefore) === null || _a === void 0 ? void 0 : _a.isText) && $position.nodeBefore.text;\n    if (!text) {\n        return null;\n    }\n    const textFrom = $position.pos - text.length;\n    const match = Array.from(text.matchAll(regexp)).pop();\n    if (!match || match.input === undefined || match.index === undefined) {\n        return null;\n    }\n    // JavaScript doesn't have lookbehinds. This hacks a check that first character\n    // is a space or the start of the line\n    const matchPrefix = match.input.slice(Math.max(0, match.index - 1), match.index);\n    const matchPrefixIsAllowed = new RegExp(`^[${allowedPrefixes === null || allowedPrefixes === void 0 ? void 0 : allowedPrefixes.join('')}\\0]?$`).test(matchPrefix);\n    if (allowedPrefixes !== null && !matchPrefixIsAllowed) {\n        return null;\n    }\n    // The absolute position of the match in the document\n    const from = textFrom + match.index;\n    let to = from + match[0].length;\n    // Edge case handling; if spaces are allowed and we're directly in between\n    // two triggers\n    if (allowSpaces && suffix.test(text.slice(to - 1, to + 1))) {\n        match[0] += ' ';\n        to += 1;\n    }\n    // If the $position is located within the matched substring, return that range\n    if (from < $position.pos && to >= $position.pos) {\n        return {\n            range: {\n                from,\n                to,\n            },\n            query: match[0].slice(char.length),\n            text: match[0],\n        };\n    }\n    return null;\n}\n\nconst SuggestionPluginKey = new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey('suggestion');\n/**\n * This utility allows you to create suggestions.\n * @see https://tiptap.dev/api/utilities/suggestion\n */\nfunction Suggestion({ pluginKey = SuggestionPluginKey, editor, char = '@', allowSpaces = false, allowToIncludeChar = false, allowedPrefixes = [' '], startOfLine = false, decorationTag = 'span', decorationClass = 'suggestion', decorationContent = '', decorationEmptyClass = 'is-empty', command = () => null, items = () => [], render = () => ({}), allow = () => true, findSuggestionMatch: findSuggestionMatch$1 = findSuggestionMatch, }) {\n    let props;\n    const renderer = render === null || render === void 0 ? void 0 : render();\n    const plugin = new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n        key: pluginKey,\n        view() {\n            return {\n                update: async (view, prevState) => {\n                    var _a, _b, _c, _d, _e, _f, _g;\n                    const prev = (_a = this.key) === null || _a === void 0 ? void 0 : _a.getState(prevState);\n                    const next = (_b = this.key) === null || _b === void 0 ? void 0 : _b.getState(view.state);\n                    // See how the state changed\n                    const moved = prev.active && next.active && prev.range.from !== next.range.from;\n                    const started = !prev.active && next.active;\n                    const stopped = prev.active && !next.active;\n                    const changed = !started && !stopped && prev.query !== next.query;\n                    const handleStart = started || (moved && changed);\n                    const handleChange = changed || moved;\n                    const handleExit = stopped || (moved && changed);\n                    // Cancel when suggestion isn't active\n                    if (!handleStart && !handleChange && !handleExit) {\n                        return;\n                    }\n                    const state = handleExit && !handleStart ? prev : next;\n                    const decorationNode = view.dom.querySelector(`[data-decoration-id=\"${state.decorationId}\"]`);\n                    props = {\n                        editor,\n                        range: state.range,\n                        query: state.query,\n                        text: state.text,\n                        items: [],\n                        command: commandProps => {\n                            return command({\n                                editor,\n                                range: state.range,\n                                props: commandProps,\n                            });\n                        },\n                        decorationNode,\n                        // virtual node for popper.js or tippy.js\n                        // this can be used for building popups without a DOM node\n                        clientRect: decorationNode\n                            ? () => {\n                                var _a;\n                                // because of `items` can be asynchrounous we’ll search for the current decoration node\n                                const { decorationId } = (_a = this.key) === null || _a === void 0 ? void 0 : _a.getState(editor.state); // eslint-disable-line\n                                const currentDecorationNode = view.dom.querySelector(`[data-decoration-id=\"${decorationId}\"]`);\n                                return (currentDecorationNode === null || currentDecorationNode === void 0 ? void 0 : currentDecorationNode.getBoundingClientRect()) || null;\n                            }\n                            : null,\n                    };\n                    if (handleStart) {\n                        (_c = renderer === null || renderer === void 0 ? void 0 : renderer.onBeforeStart) === null || _c === void 0 ? void 0 : _c.call(renderer, props);\n                    }\n                    if (handleChange) {\n                        (_d = renderer === null || renderer === void 0 ? void 0 : renderer.onBeforeUpdate) === null || _d === void 0 ? void 0 : _d.call(renderer, props);\n                    }\n                    if (handleChange || handleStart) {\n                        props.items = await items({\n                            editor,\n                            query: state.query,\n                        });\n                    }\n                    if (handleExit) {\n                        (_e = renderer === null || renderer === void 0 ? void 0 : renderer.onExit) === null || _e === void 0 ? void 0 : _e.call(renderer, props);\n                    }\n                    if (handleChange) {\n                        (_f = renderer === null || renderer === void 0 ? void 0 : renderer.onUpdate) === null || _f === void 0 ? void 0 : _f.call(renderer, props);\n                    }\n                    if (handleStart) {\n                        (_g = renderer === null || renderer === void 0 ? void 0 : renderer.onStart) === null || _g === void 0 ? void 0 : _g.call(renderer, props);\n                    }\n                },\n                destroy: () => {\n                    var _a;\n                    if (!props) {\n                        return;\n                    }\n                    (_a = renderer === null || renderer === void 0 ? void 0 : renderer.onExit) === null || _a === void 0 ? void 0 : _a.call(renderer, props);\n                },\n            };\n        },\n        state: {\n            // Initialize the plugin's internal state.\n            init() {\n                const state = {\n                    active: false,\n                    range: {\n                        from: 0,\n                        to: 0,\n                    },\n                    query: null,\n                    text: null,\n                    composing: false,\n                };\n                return state;\n            },\n            // Apply changes to the plugin state from a view transaction.\n            apply(transaction, prev, _oldState, state) {\n                const { isEditable } = editor;\n                const { composing } = editor.view;\n                const { selection } = transaction;\n                const { empty, from } = selection;\n                const next = { ...prev };\n                next.composing = composing;\n                // We can only be suggesting if the view is editable, and:\n                //   * there is no selection, or\n                //   * a composition is active (see: https://github.com/ueberdosis/tiptap/issues/1449)\n                if (isEditable && (empty || editor.view.composing)) {\n                    // Reset active state if we just left the previous suggestion range\n                    if ((from < prev.range.from || from > prev.range.to)\n                        && !composing\n                        && !prev.composing) {\n                        next.active = false;\n                    }\n                    // Try to match against where our cursor currently is\n                    const match = findSuggestionMatch$1({\n                        char,\n                        allowSpaces,\n                        allowToIncludeChar,\n                        allowedPrefixes,\n                        startOfLine,\n                        $position: selection.$from,\n                    });\n                    const decorationId = `id_${Math.floor(Math.random() * 0xffffffff)}`;\n                    // If we found a match, update the current state to show it\n                    if (match\n                        && allow({\n                            editor,\n                            state,\n                            range: match.range,\n                            isActive: prev.active,\n                        })) {\n                        next.active = true;\n                        next.decorationId = prev.decorationId\n                            ? prev.decorationId\n                            : decorationId;\n                        next.range = match.range;\n                        next.query = match.query;\n                        next.text = match.text;\n                    }\n                    else {\n                        next.active = false;\n                    }\n                }\n                else {\n                    next.active = false;\n                }\n                // Make sure to empty the range if suggestion is inactive\n                if (!next.active) {\n                    next.decorationId = null;\n                    next.range = { from: 0, to: 0 };\n                    next.query = null;\n                    next.text = null;\n                }\n                return next;\n            },\n        },\n        props: {\n            // Call the keydown hook if suggestion is active.\n            handleKeyDown(view, event) {\n                var _a;\n                const { active, range } = plugin.getState(view.state);\n                if (!active) {\n                    return false;\n                }\n                return ((_a = renderer === null || renderer === void 0 ? void 0 : renderer.onKeyDown) === null || _a === void 0 ? void 0 : _a.call(renderer, { view, event, range })) || false;\n            },\n            // Setup decorator on the currently active suggestion.\n            decorations(state) {\n                const { active, range, decorationId, query, } = plugin.getState(state);\n                if (!active) {\n                    return null;\n                }\n                const isEmpty = !(query === null || query === void 0 ? void 0 : query.length);\n                const classNames = [decorationClass];\n                if (isEmpty) {\n                    classNames.push(decorationEmptyClass);\n                }\n                return _tiptap_pm_view__WEBPACK_IMPORTED_MODULE_1__.DecorationSet.create(state.doc, [\n                    _tiptap_pm_view__WEBPACK_IMPORTED_MODULE_1__.Decoration.inline(range.from, range.to, {\n                        nodeName: decorationTag,\n                        class: classNames.join(' '),\n                        'data-decoration-id': decorationId,\n                        'data-decoration-content': decorationContent,\n                    }),\n                ]);\n            },\n        },\n    });\n    return plugin;\n}\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+suggestion@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/suggestion/dist/index.js\n");

/***/ })

};
;