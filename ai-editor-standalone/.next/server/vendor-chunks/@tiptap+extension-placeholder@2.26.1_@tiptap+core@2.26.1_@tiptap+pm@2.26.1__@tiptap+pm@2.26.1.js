"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-placeholder@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-placeholder@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-placeholder@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-placeholder/dist/index.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-placeholder@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-placeholder/dist/index.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Placeholder: () => (/* binding */ Placeholder),\n/* harmony export */   \"default\": () => (/* binding */ Placeholder)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/state/dist/index.js\");\n/* harmony import */ var _tiptap_pm_view__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/pm/view */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/view/dist/index.js\");\n\n\n\n\n/**\n * This extension allows you to add a placeholder to your editor.\n * A placeholder is a text that appears when the editor or a node is empty.\n * @see https://www.tiptap.dev/api/extensions/placeholder\n */\nconst Placeholder = _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.Extension.create({\n    name: 'placeholder',\n    addOptions() {\n        return {\n            emptyEditorClass: 'is-editor-empty',\n            emptyNodeClass: 'is-empty',\n            placeholder: 'Write something …',\n            showOnlyWhenEditable: true,\n            showOnlyCurrent: true,\n            includeChildren: false,\n        };\n    },\n    addProseMirrorPlugins() {\n        return [\n            new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n                key: new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey('placeholder'),\n                props: {\n                    decorations: ({ doc, selection }) => {\n                        const active = this.editor.isEditable || !this.options.showOnlyWhenEditable;\n                        const { anchor } = selection;\n                        const decorations = [];\n                        if (!active) {\n                            return null;\n                        }\n                        const isEmptyDoc = this.editor.isEmpty;\n                        doc.descendants((node, pos) => {\n                            const hasAnchor = anchor >= pos && anchor <= pos + node.nodeSize;\n                            const isEmpty = !node.isLeaf && (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isNodeEmpty)(node);\n                            if ((hasAnchor || !this.options.showOnlyCurrent) && isEmpty) {\n                                const classes = [this.options.emptyNodeClass];\n                                if (isEmptyDoc) {\n                                    classes.push(this.options.emptyEditorClass);\n                                }\n                                const decoration = _tiptap_pm_view__WEBPACK_IMPORTED_MODULE_1__.Decoration.node(pos, pos + node.nodeSize, {\n                                    class: classes.join(' '),\n                                    'data-placeholder': typeof this.options.placeholder === 'function'\n                                        ? this.options.placeholder({\n                                            editor: this.editor,\n                                            node,\n                                            pos,\n                                            hasAnchor,\n                                        })\n                                        : this.options.placeholder,\n                                });\n                                decorations.push(decoration);\n                            }\n                            return this.options.includeChildren;\n                        });\n                        return _tiptap_pm_view__WEBPACK_IMPORTED_MODULE_1__.DecorationSet.create(doc, decorations);\n                    },\n                },\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHRpcHRhcCtleHRlbnNpb24tcGxhY2Vob2xkZXJAMi4yNi4xX0B0aXB0YXArY29yZUAyLjI2LjFfQHRpcHRhcCtwbUAyLjI2LjFfX0B0aXB0YXArcG1AMi4yNi4xL25vZGVfbW9kdWxlcy9AdGlwdGFwL2V4dGVuc2lvbi1wbGFjZWhvbGRlci9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXNEO0FBQ0Q7QUFDTzs7QUFFNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixtREFBUztBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsZ0JBQWdCLG9EQUFNO0FBQ3RCLHlCQUF5Qix1REFBUztBQUNsQztBQUNBLG9DQUFvQyxnQkFBZ0I7QUFDcEQ7QUFDQSxnQ0FBZ0MsU0FBUztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDREQUE0RCx5REFBVztBQUN2RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbURBQW1ELHVEQUFVO0FBQzdEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUNBQXlDO0FBQ3pDO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QiwrQkFBK0IsMERBQWE7QUFDNUMscUJBQXFCO0FBQ3JCLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2I7QUFDQSxLQUFLO0FBQ0wsQ0FBQzs7QUFFOEM7QUFDL0MiLCJzb3VyY2VzIjpbIi9ob21lL211emkvZ2l0aHViL2RlZXItZmxvdy9haS1lZGl0b3Itc3RhbmRhbG9uZS9ub2RlX21vZHVsZXMvLnBucG0vQHRpcHRhcCtleHRlbnNpb24tcGxhY2Vob2xkZXJAMi4yNi4xX0B0aXB0YXArY29yZUAyLjI2LjFfQHRpcHRhcCtwbUAyLjI2LjFfX0B0aXB0YXArcG1AMi4yNi4xL25vZGVfbW9kdWxlcy9AdGlwdGFwL2V4dGVuc2lvbi1wbGFjZWhvbGRlci9kaXN0L2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEV4dGVuc2lvbiwgaXNOb2RlRW1wdHkgfSBmcm9tICdAdGlwdGFwL2NvcmUnO1xuaW1wb3J0IHsgUGx1Z2luLCBQbHVnaW5LZXkgfSBmcm9tICdAdGlwdGFwL3BtL3N0YXRlJztcbmltcG9ydCB7IERlY29yYXRpb24sIERlY29yYXRpb25TZXQgfSBmcm9tICdAdGlwdGFwL3BtL3ZpZXcnO1xuXG4vKipcbiAqIFRoaXMgZXh0ZW5zaW9uIGFsbG93cyB5b3UgdG8gYWRkIGEgcGxhY2Vob2xkZXIgdG8geW91ciBlZGl0b3IuXG4gKiBBIHBsYWNlaG9sZGVyIGlzIGEgdGV4dCB0aGF0IGFwcGVhcnMgd2hlbiB0aGUgZWRpdG9yIG9yIGEgbm9kZSBpcyBlbXB0eS5cbiAqIEBzZWUgaHR0cHM6Ly93d3cudGlwdGFwLmRldi9hcGkvZXh0ZW5zaW9ucy9wbGFjZWhvbGRlclxuICovXG5jb25zdCBQbGFjZWhvbGRlciA9IEV4dGVuc2lvbi5jcmVhdGUoe1xuICAgIG5hbWU6ICdwbGFjZWhvbGRlcicsXG4gICAgYWRkT3B0aW9ucygpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGVtcHR5RWRpdG9yQ2xhc3M6ICdpcy1lZGl0b3ItZW1wdHknLFxuICAgICAgICAgICAgZW1wdHlOb2RlQ2xhc3M6ICdpcy1lbXB0eScsXG4gICAgICAgICAgICBwbGFjZWhvbGRlcjogJ1dyaXRlIHNvbWV0aGluZyDigKYnLFxuICAgICAgICAgICAgc2hvd09ubHlXaGVuRWRpdGFibGU6IHRydWUsXG4gICAgICAgICAgICBzaG93T25seUN1cnJlbnQ6IHRydWUsXG4gICAgICAgICAgICBpbmNsdWRlQ2hpbGRyZW46IGZhbHNlLFxuICAgICAgICB9O1xuICAgIH0sXG4gICAgYWRkUHJvc2VNaXJyb3JQbHVnaW5zKCkge1xuICAgICAgICByZXR1cm4gW1xuICAgICAgICAgICAgbmV3IFBsdWdpbih7XG4gICAgICAgICAgICAgICAga2V5OiBuZXcgUGx1Z2luS2V5KCdwbGFjZWhvbGRlcicpLFxuICAgICAgICAgICAgICAgIHByb3BzOiB7XG4gICAgICAgICAgICAgICAgICAgIGRlY29yYXRpb25zOiAoeyBkb2MsIHNlbGVjdGlvbiB9KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBhY3RpdmUgPSB0aGlzLmVkaXRvci5pc0VkaXRhYmxlIHx8ICF0aGlzLm9wdGlvbnMuc2hvd09ubHlXaGVuRWRpdGFibGU7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB7IGFuY2hvciB9ID0gc2VsZWN0aW9uO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZGVjb3JhdGlvbnMgPSBbXTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghYWN0aXZlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBpc0VtcHR5RG9jID0gdGhpcy5lZGl0b3IuaXNFbXB0eTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGRvYy5kZXNjZW5kYW50cygobm9kZSwgcG9zKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgaGFzQW5jaG9yID0gYW5jaG9yID49IHBvcyAmJiBhbmNob3IgPD0gcG9zICsgbm9kZS5ub2RlU2l6ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBpc0VtcHR5ID0gIW5vZGUuaXNMZWFmICYmIGlzTm9kZUVtcHR5KG5vZGUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICgoaGFzQW5jaG9yIHx8ICF0aGlzLm9wdGlvbnMuc2hvd09ubHlDdXJyZW50KSAmJiBpc0VtcHR5KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGNsYXNzZXMgPSBbdGhpcy5vcHRpb25zLmVtcHR5Tm9kZUNsYXNzXTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGlzRW1wdHlEb2MpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzZXMucHVzaCh0aGlzLm9wdGlvbnMuZW1wdHlFZGl0b3JDbGFzcyk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZGVjb3JhdGlvbiA9IERlY29yYXRpb24ubm9kZShwb3MsIHBvcyArIG5vZGUubm9kZVNpemUsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzOiBjbGFzc2VzLmpvaW4oJyAnKSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdkYXRhLXBsYWNlaG9sZGVyJzogdHlwZW9mIHRoaXMub3B0aW9ucy5wbGFjZWhvbGRlciA9PT0gJ2Z1bmN0aW9uJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gdGhpcy5vcHRpb25zLnBsYWNlaG9sZGVyKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZWRpdG9yOiB0aGlzLmVkaXRvcixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbm9kZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcG9zLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYXNBbmNob3IsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHRoaXMub3B0aW9ucy5wbGFjZWhvbGRlcixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlY29yYXRpb25zLnB1c2goZGVjb3JhdGlvbik7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLm9wdGlvbnMuaW5jbHVkZUNoaWxkcmVuO1xuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gRGVjb3JhdGlvblNldC5jcmVhdGUoZG9jLCBkZWNvcmF0aW9ucyk7XG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIH0pLFxuICAgICAgICBdO1xuICAgIH0sXG59KTtcblxuZXhwb3J0IHsgUGxhY2Vob2xkZXIsIFBsYWNlaG9sZGVyIGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-placeholder@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-placeholder/dist/index.js\n");

/***/ })

};
;