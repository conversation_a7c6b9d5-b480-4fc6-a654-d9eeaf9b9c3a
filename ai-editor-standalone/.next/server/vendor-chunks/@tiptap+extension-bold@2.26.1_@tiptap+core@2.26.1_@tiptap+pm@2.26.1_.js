"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-bold@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-bold@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-bold@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-bold/dist/index.js":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-bold@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-bold/dist/index.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bold: () => (/* binding */ Bold),\n/* harmony export */   \"default\": () => (/* binding */ Bold),\n/* harmony export */   starInputRegex: () => (/* binding */ starInputRegex),\n/* harmony export */   starPasteRegex: () => (/* binding */ starPasteRegex),\n/* harmony export */   underscoreInputRegex: () => (/* binding */ underscoreInputRegex),\n/* harmony export */   underscorePasteRegex: () => (/* binding */ underscorePasteRegex)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n/**\n * Matches bold text via `**` as input.\n */\nconst starInputRegex = /(?:^|\\s)(\\*\\*(?!\\s+\\*\\*)((?:[^*]+))\\*\\*(?!\\s+\\*\\*))$/;\n/**\n * Matches bold text via `**` while pasting.\n */\nconst starPasteRegex = /(?:^|\\s)(\\*\\*(?!\\s+\\*\\*)((?:[^*]+))\\*\\*(?!\\s+\\*\\*))/g;\n/**\n * Matches bold text via `__` as input.\n */\nconst underscoreInputRegex = /(?:^|\\s)(__(?!\\s+__)((?:[^_]+))__(?!\\s+__))$/;\n/**\n * Matches bold text via `__` while pasting.\n */\nconst underscorePasteRegex = /(?:^|\\s)(__(?!\\s+__)((?:[^_]+))__(?!\\s+__))/g;\n/**\n * This extension allows you to mark text as bold.\n * @see https://tiptap.dev/api/marks/bold\n */\nconst Bold = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Mark.create({\n    name: 'bold',\n    addOptions() {\n        return {\n            HTMLAttributes: {},\n        };\n    },\n    parseHTML() {\n        return [\n            {\n                tag: 'strong',\n            },\n            {\n                tag: 'b',\n                getAttrs: node => node.style.fontWeight !== 'normal' && null,\n            },\n            {\n                style: 'font-weight=400',\n                clearMark: mark => mark.type.name === this.name,\n            },\n            {\n                style: 'font-weight',\n                getAttrs: value => /^(bold(er)?|[5-9]\\d{2,})$/.test(value) && null,\n            },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['strong', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n    addCommands() {\n        return {\n            setBold: () => ({ commands }) => {\n                return commands.setMark(this.name);\n            },\n            toggleBold: () => ({ commands }) => {\n                return commands.toggleMark(this.name);\n            },\n            unsetBold: () => ({ commands }) => {\n                return commands.unsetMark(this.name);\n            },\n        };\n    },\n    addKeyboardShortcuts() {\n        return {\n            'Mod-b': () => this.editor.commands.toggleBold(),\n            'Mod-B': () => this.editor.commands.toggleBold(),\n        };\n    },\n    addInputRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markInputRule)({\n                find: starInputRegex,\n                type: this.type,\n            }),\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markInputRule)({\n                find: underscoreInputRegex,\n                type: this.type,\n            }),\n        ];\n    },\n    addPasteRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markPasteRule)({\n                find: starPasteRegex,\n                type: this.type,\n            }),\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markPasteRule)({\n                find: underscorePasteRegex,\n                type: this.type,\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-bold@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-bold/dist/index.js\n");

/***/ })

};
;