"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-color@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+extension-text-_ochc23jjcl3wq5k6zoczrgb3ku";
exports.ids = ["vendor-chunks/@tiptap+extension-color@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+extension-text-_ochc23jjcl3wq5k6zoczrgb3ku"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-color@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+extension-text-_ochc23jjcl3wq5k6zoczrgb3ku/node_modules/@tiptap/extension-color/dist/index.js":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-color@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+extension-text-_ochc23jjcl3wq5k6zoczrgb3ku/node_modules/@tiptap/extension-color/dist/index.js ***!
  \********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Color: () => (/* binding */ Color),\n/* harmony export */   \"default\": () => (/* binding */ Color)\n/* harmony export */ });\n/* harmony import */ var _tiptap_extension_text_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/extension-text-style */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-text-style@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-text-style/dist/index.js\");\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n\n/**\n * This extension allows you to color your text.\n * @see https://tiptap.dev/api/extensions/color\n */\nconst Color = _tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Extension.create({\n    name: 'color',\n    addOptions() {\n        return {\n            types: ['textStyle'],\n        };\n    },\n    addGlobalAttributes() {\n        return [\n            {\n                types: this.options.types,\n                attributes: {\n                    color: {\n                        default: null,\n                        parseHTML: element => { var _a; return (_a = element.style.color) === null || _a === void 0 ? void 0 : _a.replace(/['\"]+/g, ''); },\n                        renderHTML: attributes => {\n                            if (!attributes.color) {\n                                return {};\n                            }\n                            return {\n                                style: `color: ${attributes.color}`,\n                            };\n                        },\n                    },\n                },\n            },\n        ];\n    },\n    addCommands() {\n        return {\n            setColor: color => ({ chain }) => {\n                return chain()\n                    .setMark('textStyle', { color })\n                    .run();\n            },\n            unsetColor: () => ({ chain }) => {\n                return chain()\n                    .setMark('textStyle', { color: null })\n                    .removeEmptyTextStyle()\n                    .run();\n            },\n        };\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-color@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+extension-text-_ochc23jjcl3wq5k6zoczrgb3ku/node_modules/@tiptap/extension-color/dist/index.js\n");

/***/ })

};
;