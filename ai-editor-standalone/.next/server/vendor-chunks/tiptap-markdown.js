"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tiptap-markdown";
exports.ids = ["vendor-chunks/tiptap-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/tiptap-markdown/dist/tiptap-markdown.es.js":
/*!*****************************************************************!*\
  !*** ./node_modules/tiptap-markdown/dist/tiptap-markdown.es.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Markdown: () => (/* binding */ Markdown)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! prosemirror-markdown */ \"(ssr)/./node_modules/prosemirror-markdown/dist/index.js\");\n/* harmony import */ var markdown_it__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! markdown-it */ \"(ssr)/./node_modules/markdown-it/index.mjs\");\n/* harmony import */ var _tiptap_pm_model__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/pm/model */ \"(ssr)/./node_modules/@tiptap/pm/model/dist/index.js\");\n/* harmony import */ var markdown_it_task_lists__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! markdown-it-task-lists */ \"(ssr)/./node_modules/markdown-it-task-lists/index.js\");\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/@tiptap/pm/state/dist/index.js\");\nvar __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\n\n\n\n\n\n\nconst MarkdownTightLists = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Extension.create({\n  name: \"markdownTightLists\",\n  addOptions: () => ({\n    tight: true,\n    tightClass: \"tight\",\n    listTypes: [\"bulletList\", \"orderedList\"]\n  }),\n  addGlobalAttributes() {\n    return [{\n      types: this.options.listTypes,\n      attributes: {\n        tight: {\n          default: this.options.tight,\n          parseHTML: (element) => element.getAttribute(\"data-tight\") === \"true\" || !element.querySelector(\"p\"),\n          renderHTML: (attributes) => ({\n            class: attributes.tight ? this.options.tightClass : null,\n            \"data-tight\": attributes.tight ? \"true\" : null\n          })\n        }\n      }\n    }];\n  },\n  addCommands() {\n    var _this = this;\n    return {\n      toggleTight: function() {\n        let tight = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : null;\n        return (_ref) => {\n          let {\n            editor,\n            commands\n          } = _ref;\n          function toggleTight(name) {\n            if (!editor.isActive(name)) {\n              return false;\n            }\n            const attrs = editor.getAttributes(name);\n            return commands.updateAttributes(name, {\n              tight: tight !== null && tight !== void 0 ? tight : !(attrs !== null && attrs !== void 0 && attrs.tight)\n            });\n          }\n          return _this.options.listTypes.some((name) => toggleTight(name));\n        };\n      }\n    };\n  }\n});\nconst md = (0,markdown_it__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\nfunction scanDelims(text, pos) {\n  md.inline.State.prototype.scanDelims.call({\n    src: text,\n    posMax: text.length\n  });\n  const state = new md.inline.State(text, null, null, []);\n  return state.scanDelims(pos, true);\n}\nfunction shiftDelim(text, delim, start, offset) {\n  let res = text.substring(0, start) + text.substring(start + delim.length);\n  res = res.substring(0, start + offset) + delim + res.substring(start + offset);\n  return res;\n}\nfunction trimStart(text, delim, from, to) {\n  let pos = from, res = text;\n  while (pos < to) {\n    if (scanDelims(res, pos).can_open) {\n      break;\n    }\n    res = shiftDelim(res, delim, pos, 1);\n    pos++;\n  }\n  return {\n    text: res,\n    from: pos,\n    to\n  };\n}\nfunction trimEnd(text, delim, from, to) {\n  let pos = to, res = text;\n  while (pos > from) {\n    if (scanDelims(res, pos).can_close) {\n      break;\n    }\n    res = shiftDelim(res, delim, pos, -1);\n    pos--;\n  }\n  return {\n    text: res,\n    from,\n    to: pos\n  };\n}\nfunction trimInline(text, delim, from, to) {\n  let state = {\n    text,\n    from,\n    to\n  };\n  state = trimStart(state.text, delim, state.from, state.to);\n  state = trimEnd(state.text, delim, state.from, state.to);\n  if (state.to - state.from < delim.length + 1) {\n    state.text = state.text.substring(0, state.from) + state.text.substring(state.to + delim.length);\n  }\n  return state.text;\n}\nclass MarkdownSerializerState extends prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.MarkdownSerializerState {\n  constructor(nodes, marks, options) {\n    super(nodes, marks, options !== null && options !== void 0 ? options : {});\n    __publicField(this, \"inTable\", false);\n    this.inlines = [];\n  }\n  render(node, parent, index) {\n    super.render(node, parent, index);\n    const top = this.inlines[this.inlines.length - 1];\n    if (top !== null && top !== void 0 && top.start && top !== null && top !== void 0 && top.end) {\n      const {\n        delimiter,\n        start,\n        end\n      } = this.normalizeInline(top);\n      this.out = trimInline(this.out, delimiter, start, end);\n      this.inlines.pop();\n    }\n  }\n  markString(mark, open, parent, index) {\n    const info = this.marks[mark.type.name];\n    if (info.expelEnclosingWhitespace) {\n      if (open) {\n        this.inlines.push({\n          start: this.out.length,\n          delimiter: info.open\n        });\n      } else {\n        const top = this.inlines.pop();\n        this.inlines.push({\n          ...top,\n          end: this.out.length\n        });\n      }\n    }\n    return super.markString(mark, open, parent, index);\n  }\n  normalizeInline(inline) {\n    let {\n      start,\n      end\n    } = inline;\n    while (this.out.charAt(start).match(/\\s/)) {\n      start++;\n    }\n    return {\n      ...inline,\n      start\n    };\n  }\n}\nconst HTMLMark = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Mark.create({\n  name: \"markdownHTMLMark\",\n  /**\n   * @return {{markdown: MarkdownMarkSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: {\n          open(state, mark) {\n            var _getMarkTags$, _getMarkTags;\n            if (!this.editor.storage.markdown.options.html) {\n              console.warn(`Tiptap Markdown: \"${mark.type.name}\" mark is only available in html mode`);\n              return \"\";\n            }\n            return (_getMarkTags$ = (_getMarkTags = getMarkTags(mark)) === null || _getMarkTags === void 0 ? void 0 : _getMarkTags[0]) !== null && _getMarkTags$ !== void 0 ? _getMarkTags$ : \"\";\n          },\n          close(state, mark) {\n            var _getMarkTags$2, _getMarkTags2;\n            if (!this.editor.storage.markdown.options.html) {\n              return \"\";\n            }\n            return (_getMarkTags$2 = (_getMarkTags2 = getMarkTags(mark)) === null || _getMarkTags2 === void 0 ? void 0 : _getMarkTags2[1]) !== null && _getMarkTags$2 !== void 0 ? _getMarkTags$2 : \"\";\n          }\n        },\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nfunction getMarkTags(mark) {\n  const schema = mark.type.schema;\n  const node = schema.text(\" \", [mark]);\n  const html = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_4__.getHTMLFromFragment)(_tiptap_pm_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(node), schema);\n  const match = html.match(/^(<.*?>) (<\\/.*?>)$/);\n  return match ? [match[1], match[2]] : null;\n}\nfunction elementFromString(value) {\n  const wrappedValue = `<body>${value}</body>`;\n  return new window.DOMParser().parseFromString(wrappedValue, \"text/html\").body;\n}\nfunction escapeHTML(value) {\n  return value === null || value === void 0 ? void 0 : value.replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n}\nfunction extractElement(node) {\n  const parent = node.parentElement;\n  const prepend = parent.cloneNode();\n  while (parent.firstChild && parent.firstChild !== node) {\n    prepend.appendChild(parent.firstChild);\n  }\n  if (prepend.childNodes.length > 0) {\n    parent.parentElement.insertBefore(prepend, parent);\n  }\n  parent.parentElement.insertBefore(node, parent);\n  if (parent.childNodes.length === 0) {\n    parent.remove();\n  }\n}\nfunction unwrapElement(node) {\n  const parent = node.parentNode;\n  while (node.firstChild)\n    parent.insertBefore(node.firstChild, node);\n  parent.removeChild(node);\n}\nconst HTMLNode = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"markdownHTMLNode\",\n  addStorage() {\n    return {\n      markdown: {\n        serialize(state, node, parent) {\n          if (this.editor.storage.markdown.options.html) {\n            state.write(serializeHTML(node, parent));\n          } else {\n            console.warn(`Tiptap Markdown: \"${node.type.name}\" node is only available in html mode`);\n            state.write(`[${node.type.name}]`);\n          }\n          if (node.isBlock) {\n            state.closeBlock(node);\n          }\n        },\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nfunction serializeHTML(node, parent) {\n  const schema = node.type.schema;\n  const html = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_4__.getHTMLFromFragment)(_tiptap_pm_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(node), schema);\n  if (node.isBlock && (parent instanceof _tiptap_pm_model__WEBPACK_IMPORTED_MODULE_1__.Fragment || parent.type.name === schema.topNodeType.name)) {\n    return formatBlock(html);\n  }\n  return html;\n}\nfunction formatBlock(html) {\n  const dom = elementFromString(html);\n  const element = dom.firstElementChild;\n  element.innerHTML = element.innerHTML.trim() ? `\n${element.innerHTML}\n` : `\n`;\n  return element.outerHTML;\n}\nconst Blockquote = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"blockquote\"\n});\nconst Blockquote$1 = Blockquote.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.defaultMarkdownSerializer.nodes.blockquote,\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst BulletList = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"bulletList\"\n});\nconst BulletList$1 = BulletList.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize(state, node) {\n          return state.renderList(node, \"  \", () => (this.editor.storage.markdown.options.bulletListMarker || \"-\") + \" \");\n        },\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst CodeBlock = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"codeBlock\"\n});\nconst CodeBlock$1 = CodeBlock.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize(state, node) {\n          state.write(\"```\" + (node.attrs.language || \"\") + \"\\n\");\n          state.text(node.textContent, false);\n          state.ensureNewLine();\n          state.write(\"```\");\n          state.closeBlock(node);\n        },\n        parse: {\n          setup(markdownit2) {\n            var _this$options$languag;\n            markdownit2.set({\n              langPrefix: (_this$options$languag = this.options.languageClassPrefix) !== null && _this$options$languag !== void 0 ? _this$options$languag : \"language-\"\n            });\n          },\n          updateDOM(element) {\n            element.innerHTML = element.innerHTML.replace(/\\n<\\/code><\\/pre>/g, \"</code></pre>\");\n          }\n        }\n      }\n    };\n  }\n});\nconst HardBreak = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"hardBreak\"\n});\nconst HardBreak$1 = HardBreak.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize(state, node, parent, index) {\n          for (let i = index + 1; i < parent.childCount; i++)\n            if (parent.child(i).type != node.type) {\n              state.write(state.inTable ? HTMLNode.storage.markdown.serialize.call(this, state, node, parent) : \"\\\\\\n\");\n              return;\n            }\n        },\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst Heading = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"heading\"\n});\nconst Heading$1 = Heading.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.defaultMarkdownSerializer.nodes.heading,\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst HorizontalRule = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"horizontalRule\"\n});\nconst HorizontalRule$1 = HorizontalRule.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.defaultMarkdownSerializer.nodes.horizontal_rule,\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst Image = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"image\"\n});\nconst Image$1 = Image.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.defaultMarkdownSerializer.nodes.image,\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst ListItem = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"listItem\"\n});\nconst ListItem$1 = ListItem.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.defaultMarkdownSerializer.nodes.list_item,\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst OrderedList = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"orderedList\"\n});\nfunction findIndexOfAdjacentNode(node, parent, index) {\n  let i = 0;\n  for (; index - i > 0; i++) {\n    if (parent.child(index - i - 1).type.name !== node.type.name) {\n      break;\n    }\n  }\n  return i;\n}\nconst OrderedList$1 = OrderedList.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize(state, node, parent, index) {\n          const start = node.attrs.start || 1;\n          const maxW = String(start + node.childCount - 1).length;\n          const space = state.repeat(\" \", maxW + 2);\n          const adjacentIndex = findIndexOfAdjacentNode(node, parent, index);\n          const separator = adjacentIndex % 2 ? \") \" : \". \";\n          state.renderList(node, space, (i) => {\n            const nStr = String(start + i);\n            return state.repeat(\" \", maxW - nStr.length) + nStr + separator;\n          });\n        },\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst Paragraph = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"paragraph\"\n});\nconst Paragraph$1 = Paragraph.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.defaultMarkdownSerializer.nodes.paragraph,\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nfunction childNodes(node) {\n  var _node$content$content, _node$content;\n  return (_node$content$content = node === null || node === void 0 || (_node$content = node.content) === null || _node$content === void 0 ? void 0 : _node$content.content) !== null && _node$content$content !== void 0 ? _node$content$content : [];\n}\nconst Table = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"table\"\n});\nconst Table$1 = Table.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize(state, node, parent) {\n          if (!isMarkdownSerializable(node)) {\n            HTMLNode.storage.markdown.serialize.call(this, state, node, parent);\n            return;\n          }\n          state.inTable = true;\n          node.forEach((row, p, i) => {\n            state.write(\"| \");\n            row.forEach((col, p2, j) => {\n              if (j) {\n                state.write(\" | \");\n              }\n              const cellContent = col.firstChild;\n              if (cellContent.textContent.trim()) {\n                state.renderInline(cellContent);\n              }\n            });\n            state.write(\" |\");\n            state.ensureNewLine();\n            if (!i) {\n              const delimiterRow = Array.from({\n                length: row.childCount\n              }).map(() => \"---\").join(\" | \");\n              state.write(`| ${delimiterRow} |`);\n              state.ensureNewLine();\n            }\n          });\n          state.closeBlock(node);\n          state.inTable = false;\n        },\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nfunction hasSpan(node) {\n  return node.attrs.colspan > 1 || node.attrs.rowspan > 1;\n}\nfunction isMarkdownSerializable(node) {\n  const rows = childNodes(node);\n  const firstRow = rows[0];\n  const bodyRows = rows.slice(1);\n  if (childNodes(firstRow).some((cell) => cell.type.name !== \"tableHeader\" || hasSpan(cell) || cell.childCount > 1)) {\n    return false;\n  }\n  if (bodyRows.some((row) => childNodes(row).some((cell) => cell.type.name === \"tableHeader\" || hasSpan(cell) || cell.childCount > 1))) {\n    return false;\n  }\n  return true;\n}\nconst TaskItem = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"taskItem\"\n});\nconst TaskItem$1 = TaskItem.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize(state, node) {\n          const check = node.attrs.checked ? \"[x]\" : \"[ ]\";\n          state.write(`${check} `);\n          state.renderContent(node);\n        },\n        parse: {\n          updateDOM(element) {\n            [...element.querySelectorAll(\".task-list-item\")].forEach((item) => {\n              const input = item.querySelector(\"input\");\n              item.setAttribute(\"data-type\", \"taskItem\");\n              if (input) {\n                item.setAttribute(\"data-checked\", input.checked);\n                input.remove();\n              }\n            });\n          }\n        }\n      }\n    };\n  }\n});\nconst TaskList = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"taskList\"\n});\nconst TaskList$1 = TaskList.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: BulletList$1.storage.markdown.serialize,\n        parse: {\n          setup(markdownit2) {\n            markdownit2.use(markdown_it_task_lists__WEBPACK_IMPORTED_MODULE_2__);\n          },\n          updateDOM(element) {\n            [...element.querySelectorAll(\".contains-task-list\")].forEach((list) => {\n              list.setAttribute(\"data-type\", \"taskList\");\n            });\n          }\n        }\n      }\n    };\n  }\n});\nconst Text = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"text\"\n});\nconst Text$1 = Text.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize(state, node) {\n          state.text(escapeHTML(node.text));\n        },\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst Bold = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Mark.create({\n  name: \"bold\"\n});\nconst Bold$1 = Bold.extend({\n  /**\n   * @return {{markdown: MarkdownMarkSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.defaultMarkdownSerializer.marks.strong,\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst Code = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Mark.create({\n  name: \"code\"\n});\nconst Code$1 = Code.extend({\n  /**\n   * @return {{markdown: MarkdownMarkSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.defaultMarkdownSerializer.marks.code,\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst Italic = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Mark.create({\n  name: \"italic\"\n});\nconst Italic$1 = Italic.extend({\n  /**\n   * @return {{markdown: MarkdownMarkSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.defaultMarkdownSerializer.marks.em,\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst Link = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Mark.create({\n  name: \"link\"\n});\nconst Link$1 = Link.extend({\n  /**\n   * @return {{markdown: MarkdownMarkSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.defaultMarkdownSerializer.marks.link,\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst Strike = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Mark.create({\n  name: \"strike\"\n});\nconst Strike$1 = Strike.extend({\n  /**\n   * @return {{markdown: MarkdownMarkSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: {\n          open: \"~~\",\n          close: \"~~\",\n          expelEnclosingWhitespace: true\n        },\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst markdownExtensions = [Blockquote$1, BulletList$1, CodeBlock$1, HardBreak$1, Heading$1, HorizontalRule$1, HTMLNode, Image$1, ListItem$1, OrderedList$1, Paragraph$1, Table$1, TaskItem$1, TaskList$1, Text$1, Bold$1, Code$1, HTMLMark, Italic$1, Link$1, Strike$1];\nfunction getMarkdownSpec(extension) {\n  var _extension$storage, _markdownExtensions$f;\n  const markdownSpec = (_extension$storage = extension.storage) === null || _extension$storage === void 0 ? void 0 : _extension$storage.markdown;\n  const defaultMarkdownSpec = (_markdownExtensions$f = markdownExtensions.find((e) => e.name === extension.name)) === null || _markdownExtensions$f === void 0 ? void 0 : _markdownExtensions$f.storage.markdown;\n  if (markdownSpec || defaultMarkdownSpec) {\n    return {\n      ...defaultMarkdownSpec,\n      ...markdownSpec\n    };\n  }\n  return null;\n}\nclass MarkdownSerializer {\n  constructor(editor) {\n    /**\n     * @type {import('@tiptap/core').Editor}\n     */\n    __publicField(this, \"editor\", null);\n    this.editor = editor;\n  }\n  serialize(content) {\n    const state = new MarkdownSerializerState(this.nodes, this.marks, {\n      hardBreakNodeName: HardBreak$1.name\n    });\n    state.renderContent(content);\n    return state.out;\n  }\n  get nodes() {\n    var _this$editor$extensio;\n    return {\n      ...Object.fromEntries(Object.keys(this.editor.schema.nodes).map((name) => [name, this.serializeNode(HTMLNode)])),\n      ...Object.fromEntries((_this$editor$extensio = this.editor.extensionManager.extensions.filter((extension) => extension.type === \"node\" && this.serializeNode(extension)).map((extension) => [extension.name, this.serializeNode(extension)])) !== null && _this$editor$extensio !== void 0 ? _this$editor$extensio : [])\n    };\n  }\n  get marks() {\n    var _this$editor$extensio2;\n    return {\n      ...Object.fromEntries(Object.keys(this.editor.schema.marks).map((name) => [name, this.serializeMark(HTMLMark)])),\n      ...Object.fromEntries((_this$editor$extensio2 = this.editor.extensionManager.extensions.filter((extension) => extension.type === \"mark\" && this.serializeMark(extension)).map((extension) => [extension.name, this.serializeMark(extension)])) !== null && _this$editor$extensio2 !== void 0 ? _this$editor$extensio2 : [])\n    };\n  }\n  serializeNode(node) {\n    var _getMarkdownSpec;\n    return (_getMarkdownSpec = getMarkdownSpec(node)) === null || _getMarkdownSpec === void 0 || (_getMarkdownSpec = _getMarkdownSpec.serialize) === null || _getMarkdownSpec === void 0 ? void 0 : _getMarkdownSpec.bind({\n      editor: this.editor,\n      options: node.options\n    });\n  }\n  serializeMark(mark) {\n    var _getMarkdownSpec2;\n    const serialize = (_getMarkdownSpec2 = getMarkdownSpec(mark)) === null || _getMarkdownSpec2 === void 0 ? void 0 : _getMarkdownSpec2.serialize;\n    return serialize ? {\n      ...serialize,\n      open: typeof serialize.open === \"function\" ? serialize.open.bind({\n        editor: this.editor,\n        options: mark.options\n      }) : serialize.open,\n      close: typeof serialize.close === \"function\" ? serialize.close.bind({\n        editor: this.editor,\n        options: mark.options\n      }) : serialize.close\n    } : null;\n  }\n}\nclass MarkdownParser {\n  constructor(editor, _ref) {\n    /**\n     * @type {import('@tiptap/core').Editor}\n     */\n    __publicField(this, \"editor\", null);\n    /**\n     * @type {markdownit}\n     */\n    __publicField(this, \"md\", null);\n    let {\n      html,\n      linkify,\n      breaks\n    } = _ref;\n    this.editor = editor;\n    this.md = this.withPatchedRenderer((0,markdown_it__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n      html,\n      linkify,\n      breaks\n    }));\n  }\n  parse(content) {\n    let {\n      inline\n    } = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    if (typeof content === \"string\") {\n      this.editor.extensionManager.extensions.forEach((extension) => {\n        var _getMarkdownSpec;\n        return (_getMarkdownSpec = getMarkdownSpec(extension)) === null || _getMarkdownSpec === void 0 || (_getMarkdownSpec = _getMarkdownSpec.parse) === null || _getMarkdownSpec === void 0 || (_getMarkdownSpec = _getMarkdownSpec.setup) === null || _getMarkdownSpec === void 0 ? void 0 : _getMarkdownSpec.call({\n          editor: this.editor,\n          options: extension.options\n        }, this.md);\n      });\n      const renderedHTML = this.md.render(content);\n      const element = elementFromString(renderedHTML);\n      this.editor.extensionManager.extensions.forEach((extension) => {\n        var _getMarkdownSpec2;\n        return (_getMarkdownSpec2 = getMarkdownSpec(extension)) === null || _getMarkdownSpec2 === void 0 || (_getMarkdownSpec2 = _getMarkdownSpec2.parse) === null || _getMarkdownSpec2 === void 0 || (_getMarkdownSpec2 = _getMarkdownSpec2.updateDOM) === null || _getMarkdownSpec2 === void 0 ? void 0 : _getMarkdownSpec2.call({\n          editor: this.editor,\n          options: extension.options\n        }, element);\n      });\n      this.normalizeDOM(element, {\n        inline,\n        content\n      });\n      return element.innerHTML;\n    }\n    return content;\n  }\n  normalizeDOM(node, _ref2) {\n    let {\n      inline,\n      content\n    } = _ref2;\n    this.normalizeBlocks(node);\n    node.querySelectorAll(\"*\").forEach((el) => {\n      var _el$nextSibling;\n      if (((_el$nextSibling = el.nextSibling) === null || _el$nextSibling === void 0 ? void 0 : _el$nextSibling.nodeType) === Node.TEXT_NODE && !el.closest(\"pre\")) {\n        el.nextSibling.textContent = el.nextSibling.textContent.replace(/^\\n/, \"\");\n      }\n    });\n    if (inline) {\n      this.normalizeInline(node, content);\n    }\n    return node;\n  }\n  normalizeBlocks(node) {\n    const blocks = Object.values(this.editor.schema.nodes).filter((node2) => node2.isBlock);\n    const selector = blocks.map((block) => {\n      var _block$spec$parseDOM;\n      return (_block$spec$parseDOM = block.spec.parseDOM) === null || _block$spec$parseDOM === void 0 ? void 0 : _block$spec$parseDOM.map((spec) => spec.tag);\n    }).flat().filter(Boolean).join(\",\");\n    if (!selector) {\n      return;\n    }\n    [...node.querySelectorAll(selector)].forEach((el) => {\n      if (el.parentElement.matches(\"p\")) {\n        extractElement(el);\n      }\n    });\n  }\n  normalizeInline(node, content) {\n    var _node$firstElementChi;\n    if ((_node$firstElementChi = node.firstElementChild) !== null && _node$firstElementChi !== void 0 && _node$firstElementChi.matches(\"p\")) {\n      var _content$match$, _content$match, _content$match$2, _content$match2;\n      const firstParagraph = node.firstElementChild;\n      const {\n        nextElementSibling\n      } = firstParagraph;\n      const startSpaces = (_content$match$ = (_content$match = content.match(/^\\s+/)) === null || _content$match === void 0 ? void 0 : _content$match[0]) !== null && _content$match$ !== void 0 ? _content$match$ : \"\";\n      const endSpaces = !nextElementSibling ? (_content$match$2 = (_content$match2 = content.match(/\\s+$/)) === null || _content$match2 === void 0 ? void 0 : _content$match2[0]) !== null && _content$match$2 !== void 0 ? _content$match$2 : \"\" : \"\";\n      if (content.match(/^\\n\\n/)) {\n        firstParagraph.innerHTML = `${firstParagraph.innerHTML}${endSpaces}`;\n        return;\n      }\n      unwrapElement(firstParagraph);\n      node.innerHTML = `${startSpaces}${node.innerHTML}${endSpaces}`;\n    }\n  }\n  /**\n   * @param {markdownit} md\n   */\n  withPatchedRenderer(md2) {\n    const withoutNewLine = (renderer) => function() {\n      const rendered = renderer(...arguments);\n      if (rendered === \"\\n\") {\n        return rendered;\n      }\n      if (rendered[rendered.length - 1] === \"\\n\") {\n        return rendered.slice(0, -1);\n      }\n      return rendered;\n    };\n    md2.renderer.rules.hardbreak = withoutNewLine(md2.renderer.rules.hardbreak);\n    md2.renderer.rules.softbreak = withoutNewLine(md2.renderer.rules.softbreak);\n    md2.renderer.rules.fence = withoutNewLine(md2.renderer.rules.fence);\n    md2.renderer.rules.code_block = withoutNewLine(md2.renderer.rules.code_block);\n    md2.renderer.renderToken = withoutNewLine(md2.renderer.renderToken.bind(md2.renderer));\n    return md2;\n  }\n}\nconst MarkdownClipboard = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Extension.create({\n  name: \"markdownClipboard\",\n  addOptions() {\n    return {\n      transformPastedText: false,\n      transformCopiedText: false\n    };\n  },\n  addProseMirrorPlugins() {\n    return [new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_3__.Plugin({\n      key: new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_3__.PluginKey(\"markdownClipboard\"),\n      props: {\n        clipboardTextParser: (text, context, plainText) => {\n          if (plainText || !this.options.transformPastedText) {\n            return null;\n          }\n          const parsed = this.editor.storage.markdown.parser.parse(text, {\n            inline: true\n          });\n          return _tiptap_pm_model__WEBPACK_IMPORTED_MODULE_1__.DOMParser.fromSchema(this.editor.schema).parseSlice(elementFromString(parsed), {\n            preserveWhitespace: true,\n            context\n          });\n        },\n        /**\n         * @param {import('prosemirror-model').Slice} slice\n         */\n        clipboardTextSerializer: (slice) => {\n          if (!this.options.transformCopiedText) {\n            return null;\n          }\n          return this.editor.storage.markdown.serializer.serialize(slice.content);\n        }\n      }\n    })];\n  }\n});\nconst Markdown = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Extension.create({\n  name: \"markdown\",\n  priority: 50,\n  addOptions() {\n    return {\n      html: true,\n      tightLists: true,\n      tightListClass: \"tight\",\n      bulletListMarker: \"-\",\n      linkify: false,\n      breaks: false,\n      transformPastedText: false,\n      transformCopiedText: false\n    };\n  },\n  addCommands() {\n    const commands = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.extensions.Commands.config.addCommands();\n    return {\n      setContent: (content, emitUpdate, parseOptions) => (props) => {\n        return commands.setContent(props.editor.storage.markdown.parser.parse(content), emitUpdate, parseOptions)(props);\n      },\n      insertContentAt: (range, content, options) => (props) => {\n        return commands.insertContentAt(range, props.editor.storage.markdown.parser.parse(content, {\n          inline: true\n        }), options)(props);\n      }\n    };\n  },\n  onBeforeCreate() {\n    this.editor.storage.markdown = {\n      options: {\n        ...this.options\n      },\n      parser: new MarkdownParser(this.editor, this.options),\n      serializer: new MarkdownSerializer(this.editor),\n      getMarkdown: () => {\n        return this.editor.storage.markdown.serializer.serialize(this.editor.state.doc);\n      }\n    };\n    this.editor.options.initialContent = this.editor.options.content;\n    this.editor.options.content = this.editor.storage.markdown.parser.parse(this.editor.options.content);\n  },\n  onCreate() {\n    this.editor.options.content = this.editor.options.initialContent;\n    delete this.editor.options.initialContent;\n  },\n  addStorage() {\n    return {\n      /// storage will be defined in onBeforeCreate() to prevent initial object overriding\n    };\n  },\n  addExtensions() {\n    return [MarkdownTightLists.configure({\n      tight: this.options.tightLists,\n      tightClass: this.options.tightListClass\n    }), MarkdownClipboard.configure({\n      transformPastedText: this.options.transformPastedText,\n      transformCopiedText: this.options.transformCopiedText\n    })];\n  }\n});\n\n//# sourceMappingURL=tiptap-markdown.es.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tiptap-markdown/dist/tiptap-markdown.es.js\n");

/***/ })

};
;