"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-dropcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-dropcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-dropcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-dropcursor/dist/index.js":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-dropcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-dropcursor/dist/index.js ***!
  \*********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dropcursor: () => (/* binding */ Dropcursor),\n/* harmony export */   \"default\": () => (/* binding */ Dropcursor)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_dropcursor__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/dropcursor */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/dropcursor/dist/index.js\");\n\n\n\n/**\n * This extension allows you to add a drop cursor to your editor.\n * A drop cursor is a line that appears when you drag and drop content\n * inbetween nodes.\n * @see https://tiptap.dev/api/extensions/dropcursor\n */\nconst Dropcursor = _tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Extension.create({\n    name: 'dropCursor',\n    addOptions() {\n        return {\n            color: 'currentColor',\n            width: 1,\n            class: undefined,\n        };\n    },\n    addProseMirrorPlugins() {\n        return [\n            (0,_tiptap_pm_dropcursor__WEBPACK_IMPORTED_MODULE_0__.dropCursor)(this.options),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHRpcHRhcCtleHRlbnNpb24tZHJvcGN1cnNvckAyLjI2LjFfQHRpcHRhcCtjb3JlQDIuMjYuMV9AdGlwdGFwK3BtQDIuMjYuMV9fQHRpcHRhcCtwbUAyLjI2LjEvbm9kZV9tb2R1bGVzL0B0aXB0YXAvZXh0ZW5zaW9uLWRyb3BjdXJzb3IvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXlDO0FBQ1U7O0FBRW5EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixtREFBUztBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWSxpRUFBVTtBQUN0QjtBQUNBLEtBQUs7QUFDTCxDQUFDOztBQUU0QztBQUM3QyIsInNvdXJjZXMiOlsiL2hvbWUvbXV6aS9naXRodWIvZGVlci1mbG93L2FpLWVkaXRvci1zdGFuZGFsb25lL25vZGVfbW9kdWxlcy8ucG5wbS9AdGlwdGFwK2V4dGVuc2lvbi1kcm9wY3Vyc29yQDIuMjYuMV9AdGlwdGFwK2NvcmVAMi4yNi4xX0B0aXB0YXArcG1AMi4yNi4xX19AdGlwdGFwK3BtQDIuMjYuMS9ub2RlX21vZHVsZXMvQHRpcHRhcC9leHRlbnNpb24tZHJvcGN1cnNvci9kaXN0L2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEV4dGVuc2lvbiB9IGZyb20gJ0B0aXB0YXAvY29yZSc7XG5pbXBvcnQgeyBkcm9wQ3Vyc29yIH0gZnJvbSAnQHRpcHRhcC9wbS9kcm9wY3Vyc29yJztcblxuLyoqXG4gKiBUaGlzIGV4dGVuc2lvbiBhbGxvd3MgeW91IHRvIGFkZCBhIGRyb3AgY3Vyc29yIHRvIHlvdXIgZWRpdG9yLlxuICogQSBkcm9wIGN1cnNvciBpcyBhIGxpbmUgdGhhdCBhcHBlYXJzIHdoZW4geW91IGRyYWcgYW5kIGRyb3AgY29udGVudFxuICogaW5iZXR3ZWVuIG5vZGVzLlxuICogQHNlZSBodHRwczovL3RpcHRhcC5kZXYvYXBpL2V4dGVuc2lvbnMvZHJvcGN1cnNvclxuICovXG5jb25zdCBEcm9wY3Vyc29yID0gRXh0ZW5zaW9uLmNyZWF0ZSh7XG4gICAgbmFtZTogJ2Ryb3BDdXJzb3InLFxuICAgIGFkZE9wdGlvbnMoKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBjb2xvcjogJ2N1cnJlbnRDb2xvcicsXG4gICAgICAgICAgICB3aWR0aDogMSxcbiAgICAgICAgICAgIGNsYXNzOiB1bmRlZmluZWQsXG4gICAgICAgIH07XG4gICAgfSxcbiAgICBhZGRQcm9zZU1pcnJvclBsdWdpbnMoKSB7XG4gICAgICAgIHJldHVybiBbXG4gICAgICAgICAgICBkcm9wQ3Vyc29yKHRoaXMub3B0aW9ucyksXG4gICAgICAgIF07XG4gICAgfSxcbn0pO1xuXG5leHBvcnQgeyBEcm9wY3Vyc29yLCBEcm9wY3Vyc29yIGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-dropcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-dropcursor/dist/index.js\n");

/***/ })

};
;