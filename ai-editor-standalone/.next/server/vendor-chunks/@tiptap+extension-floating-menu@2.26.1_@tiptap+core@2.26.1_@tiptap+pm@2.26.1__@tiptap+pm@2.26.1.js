"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-floating-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-floating-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-floating-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-floating-menu/dist/index.js":
/*!***************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-floating-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-floating-menu/dist/index.js ***!
  \***************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FloatingMenu: () => (/* binding */ FloatingMenu),\n/* harmony export */   FloatingMenuPlugin: () => (/* binding */ FloatingMenuPlugin),\n/* harmony export */   FloatingMenuView: () => (/* binding */ FloatingMenuView),\n/* harmony export */   \"default\": () => (/* binding */ FloatingMenu)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/state/dist/index.js\");\n/* harmony import */ var tippy_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tippy.js */ \"(ssr)/./node_modules/.pnpm/tippy.js@6.3.7/node_modules/tippy.js/dist/tippy.esm.js\");\n\n\n\n\nclass FloatingMenuView {\n    getTextContent(node) {\n        return (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.getText)(node, { textSerializers: (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.getTextSerializersFromSchema)(this.editor.schema) });\n    }\n    constructor({ editor, element, view, tippyOptions = {}, shouldShow, }) {\n        this.preventHide = false;\n        this.shouldShow = ({ view, state }) => {\n            const { selection } = state;\n            const { $anchor, empty } = selection;\n            const isRootDepth = $anchor.depth === 1;\n            const isEmptyTextBlock = $anchor.parent.isTextblock && !$anchor.parent.type.spec.code && !$anchor.parent.textContent && $anchor.parent.childCount === 0 && !this.getTextContent($anchor.parent);\n            if (!view.hasFocus()\n                || !empty\n                || !isRootDepth\n                || !isEmptyTextBlock\n                || !this.editor.isEditable) {\n                return false;\n            }\n            return true;\n        };\n        this.mousedownHandler = () => {\n            this.preventHide = true;\n        };\n        this.focusHandler = () => {\n            // we use `setTimeout` to make sure `selection` is already updated\n            setTimeout(() => this.update(this.editor.view));\n        };\n        this.blurHandler = ({ event }) => {\n            var _a;\n            if (this.preventHide) {\n                this.preventHide = false;\n                return;\n            }\n            if ((event === null || event === void 0 ? void 0 : event.relatedTarget) && ((_a = this.element.parentNode) === null || _a === void 0 ? void 0 : _a.contains(event.relatedTarget))) {\n                return;\n            }\n            if ((event === null || event === void 0 ? void 0 : event.relatedTarget) === this.editor.view.dom) {\n                return;\n            }\n            this.hide();\n        };\n        this.tippyBlurHandler = (event) => {\n            this.blurHandler({ event });\n        };\n        this.editor = editor;\n        this.element = element;\n        this.view = view;\n        if (shouldShow) {\n            this.shouldShow = shouldShow;\n        }\n        this.element.addEventListener('mousedown', this.mousedownHandler, { capture: true });\n        this.editor.on('focus', this.focusHandler);\n        this.editor.on('blur', this.blurHandler);\n        this.tippyOptions = tippyOptions;\n        // Detaches menu content from its current parent\n        this.element.remove();\n        this.element.style.visibility = 'visible';\n    }\n    createTooltip() {\n        const { element: editorElement } = this.editor.options;\n        const editorIsAttached = !!editorElement.parentElement;\n        this.element.tabIndex = 0;\n        if (this.tippy || !editorIsAttached) {\n            return;\n        }\n        this.tippy = (0,tippy_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(editorElement, {\n            duration: 0,\n            getReferenceClientRect: null,\n            content: this.element,\n            interactive: true,\n            trigger: 'manual',\n            placement: 'right',\n            hideOnClick: 'toggle',\n            ...this.tippyOptions,\n        });\n        // maybe we have to hide tippy on its own blur event as well\n        if (this.tippy.popper.firstChild) {\n            this.tippy.popper.firstChild.addEventListener('blur', this.tippyBlurHandler);\n        }\n    }\n    update(view, oldState) {\n        var _a, _b, _c;\n        const { state } = view;\n        const { doc, selection } = state;\n        const { from, to } = selection;\n        const isSame = oldState && oldState.doc.eq(doc) && oldState.selection.eq(selection);\n        if (isSame) {\n            return;\n        }\n        this.createTooltip();\n        const shouldShow = (_a = this.shouldShow) === null || _a === void 0 ? void 0 : _a.call(this, {\n            editor: this.editor,\n            view,\n            state,\n            oldState,\n        });\n        if (!shouldShow) {\n            this.hide();\n            return;\n        }\n        (_b = this.tippy) === null || _b === void 0 ? void 0 : _b.setProps({\n            getReferenceClientRect: ((_c = this.tippyOptions) === null || _c === void 0 ? void 0 : _c.getReferenceClientRect) || (() => (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.posToDOMRect)(view, from, to)),\n        });\n        this.show();\n    }\n    show() {\n        var _a;\n        (_a = this.tippy) === null || _a === void 0 ? void 0 : _a.show();\n    }\n    hide() {\n        var _a;\n        (_a = this.tippy) === null || _a === void 0 ? void 0 : _a.hide();\n    }\n    destroy() {\n        var _a, _b;\n        if ((_a = this.tippy) === null || _a === void 0 ? void 0 : _a.popper.firstChild) {\n            this.tippy.popper.firstChild.removeEventListener('blur', this.tippyBlurHandler);\n        }\n        (_b = this.tippy) === null || _b === void 0 ? void 0 : _b.destroy();\n        this.element.removeEventListener('mousedown', this.mousedownHandler, { capture: true });\n        this.editor.off('focus', this.focusHandler);\n        this.editor.off('blur', this.blurHandler);\n    }\n}\nconst FloatingMenuPlugin = (options) => {\n    return new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n        key: typeof options.pluginKey === 'string' ? new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey(options.pluginKey) : options.pluginKey,\n        view: view => new FloatingMenuView({ view, ...options }),\n    });\n};\n\n/**\n * This extension allows you to create a floating menu.\n * @see https://tiptap.dev/api/extensions/floating-menu\n */\nconst FloatingMenu = _tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Extension.create({\n    name: 'floatingMenu',\n    addOptions() {\n        return {\n            element: null,\n            tippyOptions: {},\n            pluginKey: 'floatingMenu',\n            shouldShow: null,\n        };\n    },\n    addProseMirrorPlugins() {\n        if (!this.options.element) {\n            return [];\n        }\n        return [\n            FloatingMenuPlugin({\n                pluginKey: this.options.pluginKey,\n                editor: this.editor,\n                element: this.options.element,\n                tippyOptions: this.options.tippyOptions,\n                shouldShow: this.options.shouldShow,\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-floating-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-floating-menu/dist/index.js\n");

/***/ })

};
;