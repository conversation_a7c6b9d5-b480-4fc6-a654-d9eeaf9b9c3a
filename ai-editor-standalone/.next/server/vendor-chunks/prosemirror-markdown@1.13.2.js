"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-markdown@1.13.2";
exports.ids = ["vendor-chunks/prosemirror-markdown@1.13.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/prosemirror-markdown@1.13.2/node_modules/prosemirror-markdown/dist/index.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/prosemirror-markdown@1.13.2/node_modules/prosemirror-markdown/dist/index.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarkdownParser: () => (/* binding */ MarkdownParser),\n/* harmony export */   MarkdownSerializer: () => (/* binding */ MarkdownSerializer),\n/* harmony export */   MarkdownSerializerState: () => (/* binding */ MarkdownSerializerState),\n/* harmony export */   defaultMarkdownParser: () => (/* binding */ defaultMarkdownParser),\n/* harmony export */   defaultMarkdownSerializer: () => (/* binding */ defaultMarkdownSerializer),\n/* harmony export */   schema: () => (/* binding */ schema)\n/* harmony export */ });\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/./node_modules/.pnpm/prosemirror-model@1.25.2/node_modules/prosemirror-model/dist/index.js\");\n/* harmony import */ var markdown_it__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! markdown-it */ \"(ssr)/./node_modules/.pnpm/markdown-it@14.1.0/node_modules/markdown-it/index.mjs\");\n\n\n\n/**\nDocument schema for the data model used by CommonMark.\n*/\nconst schema = new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Schema({\n    nodes: {\n        doc: {\n            content: \"block+\"\n        },\n        paragraph: {\n            content: \"inline*\",\n            group: \"block\",\n            parseDOM: [{ tag: \"p\" }],\n            toDOM() { return [\"p\", 0]; }\n        },\n        blockquote: {\n            content: \"block+\",\n            group: \"block\",\n            parseDOM: [{ tag: \"blockquote\" }],\n            toDOM() { return [\"blockquote\", 0]; }\n        },\n        horizontal_rule: {\n            group: \"block\",\n            parseDOM: [{ tag: \"hr\" }],\n            toDOM() { return [\"div\", [\"hr\"]]; }\n        },\n        heading: {\n            attrs: { level: { default: 1 } },\n            content: \"(text | image)*\",\n            group: \"block\",\n            defining: true,\n            parseDOM: [{ tag: \"h1\", attrs: { level: 1 } },\n                { tag: \"h2\", attrs: { level: 2 } },\n                { tag: \"h3\", attrs: { level: 3 } },\n                { tag: \"h4\", attrs: { level: 4 } },\n                { tag: \"h5\", attrs: { level: 5 } },\n                { tag: \"h6\", attrs: { level: 6 } }],\n            toDOM(node) { return [\"h\" + node.attrs.level, 0]; }\n        },\n        code_block: {\n            content: \"text*\",\n            group: \"block\",\n            code: true,\n            defining: true,\n            marks: \"\",\n            attrs: { params: { default: \"\" } },\n            parseDOM: [{ tag: \"pre\", preserveWhitespace: \"full\", getAttrs: node => ({ params: node.getAttribute(\"data-params\") || \"\" }) }],\n            toDOM(node) { return [\"pre\", node.attrs.params ? { \"data-params\": node.attrs.params } : {}, [\"code\", 0]]; }\n        },\n        ordered_list: {\n            content: \"list_item+\",\n            group: \"block\",\n            attrs: { order: { default: 1 }, tight: { default: false } },\n            parseDOM: [{ tag: \"ol\", getAttrs(dom) {\n                        return { order: dom.hasAttribute(\"start\") ? +dom.getAttribute(\"start\") : 1,\n                            tight: dom.hasAttribute(\"data-tight\") };\n                    } }],\n            toDOM(node) {\n                return [\"ol\", { start: node.attrs.order == 1 ? null : node.attrs.order,\n                        \"data-tight\": node.attrs.tight ? \"true\" : null }, 0];\n            }\n        },\n        bullet_list: {\n            content: \"list_item+\",\n            group: \"block\",\n            attrs: { tight: { default: false } },\n            parseDOM: [{ tag: \"ul\", getAttrs: dom => ({ tight: dom.hasAttribute(\"data-tight\") }) }],\n            toDOM(node) { return [\"ul\", { \"data-tight\": node.attrs.tight ? \"true\" : null }, 0]; }\n        },\n        list_item: {\n            content: \"block+\",\n            defining: true,\n            parseDOM: [{ tag: \"li\" }],\n            toDOM() { return [\"li\", 0]; }\n        },\n        text: {\n            group: \"inline\"\n        },\n        image: {\n            inline: true,\n            attrs: {\n                src: {},\n                alt: { default: null },\n                title: { default: null }\n            },\n            group: \"inline\",\n            draggable: true,\n            parseDOM: [{ tag: \"img[src]\", getAttrs(dom) {\n                        return {\n                            src: dom.getAttribute(\"src\"),\n                            title: dom.getAttribute(\"title\"),\n                            alt: dom.getAttribute(\"alt\")\n                        };\n                    } }],\n            toDOM(node) { return [\"img\", node.attrs]; }\n        },\n        hard_break: {\n            inline: true,\n            group: \"inline\",\n            selectable: false,\n            parseDOM: [{ tag: \"br\" }],\n            toDOM() { return [\"br\"]; }\n        }\n    },\n    marks: {\n        em: {\n            parseDOM: [\n                { tag: \"i\" }, { tag: \"em\" },\n                { style: \"font-style=italic\" },\n                { style: \"font-style=normal\", clearMark: m => m.type.name == \"em\" }\n            ],\n            toDOM() { return [\"em\"]; }\n        },\n        strong: {\n            parseDOM: [\n                { tag: \"strong\" },\n                { tag: \"b\", getAttrs: node => node.style.fontWeight != \"normal\" && null },\n                { style: \"font-weight=400\", clearMark: m => m.type.name == \"strong\" },\n                { style: \"font-weight\", getAttrs: value => /^(bold(er)?|[5-9]\\d{2,})$/.test(value) && null }\n            ],\n            toDOM() { return [\"strong\"]; }\n        },\n        link: {\n            attrs: {\n                href: {},\n                title: { default: null }\n            },\n            inclusive: false,\n            parseDOM: [{ tag: \"a[href]\", getAttrs(dom) {\n                        return { href: dom.getAttribute(\"href\"), title: dom.getAttribute(\"title\") };\n                    } }],\n            toDOM(node) { return [\"a\", node.attrs]; }\n        },\n        code: {\n            code: true,\n            parseDOM: [{ tag: \"code\" }],\n            toDOM() { return [\"code\"]; }\n        }\n    }\n});\n\n// @ts-ignore\nfunction maybeMerge(a, b) {\n    if (a.isText && b.isText && prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Mark.sameSet(a.marks, b.marks))\n        return a.withText(a.text + b.text);\n}\n// Object used to track the context of a running parse.\nclass MarkdownParseState {\n    constructor(schema, tokenHandlers) {\n        this.schema = schema;\n        this.tokenHandlers = tokenHandlers;\n        this.stack = [{ type: schema.topNodeType, attrs: null, content: [], marks: prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Mark.none }];\n    }\n    top() {\n        return this.stack[this.stack.length - 1];\n    }\n    push(elt) {\n        if (this.stack.length)\n            this.top().content.push(elt);\n    }\n    // Adds the given text to the current position in the document,\n    // using the current marks as styling.\n    addText(text) {\n        if (!text)\n            return;\n        let top = this.top(), nodes = top.content, last = nodes[nodes.length - 1];\n        let node = this.schema.text(text, top.marks), merged;\n        if (last && (merged = maybeMerge(last, node)))\n            nodes[nodes.length - 1] = merged;\n        else\n            nodes.push(node);\n    }\n    // Adds the given mark to the set of active marks.\n    openMark(mark) {\n        let top = this.top();\n        top.marks = mark.addToSet(top.marks);\n    }\n    // Removes the given mark from the set of active marks.\n    closeMark(mark) {\n        let top = this.top();\n        top.marks = mark.removeFromSet(top.marks);\n    }\n    parseTokens(toks) {\n        for (let i = 0; i < toks.length; i++) {\n            let tok = toks[i];\n            let handler = this.tokenHandlers[tok.type];\n            if (!handler)\n                throw new Error(\"Token type `\" + tok.type + \"` not supported by Markdown parser\");\n            handler(this, tok, toks, i);\n        }\n    }\n    // Add a node at the current position.\n    addNode(type, attrs, content) {\n        let top = this.top();\n        let node = type.createAndFill(attrs, content, top ? top.marks : []);\n        if (!node)\n            return null;\n        this.push(node);\n        return node;\n    }\n    // Wrap subsequent content in a node of the given type.\n    openNode(type, attrs) {\n        this.stack.push({ type: type, attrs: attrs, content: [], marks: prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Mark.none });\n    }\n    // Close and return the node that is currently on top of the stack.\n    closeNode() {\n        let info = this.stack.pop();\n        return this.addNode(info.type, info.attrs, info.content);\n    }\n}\nfunction attrs(spec, token, tokens, i) {\n    if (spec.getAttrs)\n        return spec.getAttrs(token, tokens, i);\n    // For backwards compatibility when `attrs` is a Function\n    else if (spec.attrs instanceof Function)\n        return spec.attrs(token);\n    else\n        return spec.attrs;\n}\n// Code content is represented as a single token with a `content`\n// property in Markdown-it.\nfunction noCloseToken(spec, type) {\n    return spec.noCloseToken || type == \"code_inline\" || type == \"code_block\" || type == \"fence\";\n}\nfunction withoutTrailingNewline(str) {\n    return str[str.length - 1] == \"\\n\" ? str.slice(0, str.length - 1) : str;\n}\nfunction noOp() { }\nfunction tokenHandlers(schema, tokens) {\n    let handlers = Object.create(null);\n    for (let type in tokens) {\n        let spec = tokens[type];\n        if (spec.block) {\n            let nodeType = schema.nodeType(spec.block);\n            if (noCloseToken(spec, type)) {\n                handlers[type] = (state, tok, tokens, i) => {\n                    state.openNode(nodeType, attrs(spec, tok, tokens, i));\n                    state.addText(withoutTrailingNewline(tok.content));\n                    state.closeNode();\n                };\n            }\n            else {\n                handlers[type + \"_open\"] = (state, tok, tokens, i) => state.openNode(nodeType, attrs(spec, tok, tokens, i));\n                handlers[type + \"_close\"] = state => state.closeNode();\n            }\n        }\n        else if (spec.node) {\n            let nodeType = schema.nodeType(spec.node);\n            handlers[type] = (state, tok, tokens, i) => state.addNode(nodeType, attrs(spec, tok, tokens, i));\n        }\n        else if (spec.mark) {\n            let markType = schema.marks[spec.mark];\n            if (noCloseToken(spec, type)) {\n                handlers[type] = (state, tok, tokens, i) => {\n                    state.openMark(markType.create(attrs(spec, tok, tokens, i)));\n                    state.addText(withoutTrailingNewline(tok.content));\n                    state.closeMark(markType);\n                };\n            }\n            else {\n                handlers[type + \"_open\"] = (state, tok, tokens, i) => state.openMark(markType.create(attrs(spec, tok, tokens, i)));\n                handlers[type + \"_close\"] = state => state.closeMark(markType);\n            }\n        }\n        else if (spec.ignore) {\n            if (noCloseToken(spec, type)) {\n                handlers[type] = noOp;\n            }\n            else {\n                handlers[type + \"_open\"] = noOp;\n                handlers[type + \"_close\"] = noOp;\n            }\n        }\n        else {\n            throw new RangeError(\"Unrecognized parsing spec \" + JSON.stringify(spec));\n        }\n    }\n    handlers.text = (state, tok) => state.addText(tok.content);\n    handlers.inline = (state, tok) => state.parseTokens(tok.children);\n    handlers.softbreak = handlers.softbreak || (state => state.addText(\" \"));\n    return handlers;\n}\n/**\nA configuration of a Markdown parser. Such a parser uses\n[markdown-it](https://github.com/markdown-it/markdown-it) to\ntokenize a file, and then runs the custom rules it is given over\nthe tokens to create a ProseMirror document tree.\n*/\nclass MarkdownParser {\n    /**\n    Create a parser with the given configuration. You can configure\n    the markdown-it parser to parse the dialect you want, and provide\n    a description of the ProseMirror entities those tokens map to in\n    the `tokens` object, which maps token names to descriptions of\n    what to do with them. Such a description is an object, and may\n    have the following properties:\n    */\n    constructor(\n    /**\n    The parser's document schema.\n    */\n    schema, \n    /**\n    This parser's markdown-it tokenizer.\n    */\n    tokenizer, \n    /**\n    The value of the `tokens` object used to construct this\n    parser. Can be useful to copy and modify to base other parsers\n    on.\n    */\n    tokens) {\n        this.schema = schema;\n        this.tokenizer = tokenizer;\n        this.tokens = tokens;\n        this.tokenHandlers = tokenHandlers(schema, tokens);\n    }\n    /**\n    Parse a string as [CommonMark](http://commonmark.org/) markup,\n    and create a ProseMirror document as prescribed by this parser's\n    rules.\n    \n    The second argument, when given, is passed through to the\n    [Markdown\n    parser](https://markdown-it.github.io/markdown-it/#MarkdownIt.parse).\n    */\n    parse(text, markdownEnv = {}) {\n        let state = new MarkdownParseState(this.schema, this.tokenHandlers), doc;\n        state.parseTokens(this.tokenizer.parse(text, markdownEnv));\n        do {\n            doc = state.closeNode();\n        } while (state.stack.length);\n        return doc || this.schema.topNodeType.createAndFill();\n    }\n}\nfunction listIsTight(tokens, i) {\n    while (++i < tokens.length)\n        if (tokens[i].type != \"list_item_open\")\n            return tokens[i].hidden;\n    return false;\n}\n/**\nA parser parsing unextended [CommonMark](http://commonmark.org/),\nwithout inline HTML, and producing a document in the basic schema.\n*/\nconst defaultMarkdownParser = new MarkdownParser(schema, (0,markdown_it__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"commonmark\", { html: false }), {\n    blockquote: { block: \"blockquote\" },\n    paragraph: { block: \"paragraph\" },\n    list_item: { block: \"list_item\" },\n    bullet_list: { block: \"bullet_list\", getAttrs: (_, tokens, i) => ({ tight: listIsTight(tokens, i) }) },\n    ordered_list: { block: \"ordered_list\", getAttrs: (tok, tokens, i) => ({\n            order: +tok.attrGet(\"start\") || 1,\n            tight: listIsTight(tokens, i)\n        }) },\n    heading: { block: \"heading\", getAttrs: tok => ({ level: +tok.tag.slice(1) }) },\n    code_block: { block: \"code_block\", noCloseToken: true },\n    fence: { block: \"code_block\", getAttrs: tok => ({ params: tok.info || \"\" }), noCloseToken: true },\n    hr: { node: \"horizontal_rule\" },\n    image: { node: \"image\", getAttrs: tok => ({\n            src: tok.attrGet(\"src\"),\n            title: tok.attrGet(\"title\") || null,\n            alt: tok.children[0] && tok.children[0].content || null\n        }) },\n    hardbreak: { node: \"hard_break\" },\n    em: { mark: \"em\" },\n    strong: { mark: \"strong\" },\n    link: { mark: \"link\", getAttrs: tok => ({\n            href: tok.attrGet(\"href\"),\n            title: tok.attrGet(\"title\") || null\n        }) },\n    code_inline: { mark: \"code\", noCloseToken: true }\n});\n\nconst blankMark = { open: \"\", close: \"\", mixable: true };\n/**\nA specification for serializing a ProseMirror document as\nMarkdown/CommonMark text.\n*/\nclass MarkdownSerializer {\n    /**\n    Construct a serializer with the given configuration. The `nodes`\n    object should map node names in a given schema to function that\n    take a serializer state and such a node, and serialize the node.\n    */\n    constructor(\n    /**\n    The node serializer functions for this serializer.\n    */\n    nodes, \n    /**\n    The mark serializer info.\n    */\n    marks, options = {}) {\n        this.nodes = nodes;\n        this.marks = marks;\n        this.options = options;\n    }\n    /**\n    Serialize the content of the given node to\n    [CommonMark](http://commonmark.org/).\n    */\n    serialize(content, options = {}) {\n        options = Object.assign({}, this.options, options);\n        let state = new MarkdownSerializerState(this.nodes, this.marks, options);\n        state.renderContent(content);\n        return state.out;\n    }\n}\n/**\nA serializer for the [basic schema](https://prosemirror.net/docs/ref/#schema).\n*/\nconst defaultMarkdownSerializer = new MarkdownSerializer({\n    blockquote(state, node) {\n        state.wrapBlock(\"> \", null, node, () => state.renderContent(node));\n    },\n    code_block(state, node) {\n        // Make sure the front matter fences are longer than any dash sequence within it\n        const backticks = node.textContent.match(/`{3,}/gm);\n        const fence = backticks ? (backticks.sort().slice(-1)[0] + \"`\") : \"```\";\n        state.write(fence + (node.attrs.params || \"\") + \"\\n\");\n        state.text(node.textContent, false);\n        // Add a newline to the current content before adding closing marker\n        state.write(\"\\n\");\n        state.write(fence);\n        state.closeBlock(node);\n    },\n    heading(state, node) {\n        state.write(state.repeat(\"#\", node.attrs.level) + \" \");\n        state.renderInline(node, false);\n        state.closeBlock(node);\n    },\n    horizontal_rule(state, node) {\n        state.write(node.attrs.markup || \"---\");\n        state.closeBlock(node);\n    },\n    bullet_list(state, node) {\n        state.renderList(node, \"  \", () => (node.attrs.bullet || \"*\") + \" \");\n    },\n    ordered_list(state, node) {\n        let start = node.attrs.order || 1;\n        let maxW = String(start + node.childCount - 1).length;\n        let space = state.repeat(\" \", maxW + 2);\n        state.renderList(node, space, i => {\n            let nStr = String(start + i);\n            return state.repeat(\" \", maxW - nStr.length) + nStr + \". \";\n        });\n    },\n    list_item(state, node) {\n        state.renderContent(node);\n    },\n    paragraph(state, node) {\n        state.renderInline(node);\n        state.closeBlock(node);\n    },\n    image(state, node) {\n        state.write(\"![\" + state.esc(node.attrs.alt || \"\") + \"](\" + node.attrs.src.replace(/[\\(\\)]/g, \"\\\\$&\") +\n            (node.attrs.title ? ' \"' + node.attrs.title.replace(/\"/g, '\\\\\"') + '\"' : \"\") + \")\");\n    },\n    hard_break(state, node, parent, index) {\n        for (let i = index + 1; i < parent.childCount; i++)\n            if (parent.child(i).type != node.type) {\n                state.write(\"\\\\\\n\");\n                return;\n            }\n    },\n    text(state, node) {\n        state.text(node.text, !state.inAutolink);\n    }\n}, {\n    em: { open: \"*\", close: \"*\", mixable: true, expelEnclosingWhitespace: true },\n    strong: { open: \"**\", close: \"**\", mixable: true, expelEnclosingWhitespace: true },\n    link: {\n        open(state, mark, parent, index) {\n            state.inAutolink = isPlainURL(mark, parent, index);\n            return state.inAutolink ? \"<\" : \"[\";\n        },\n        close(state, mark, parent, index) {\n            let { inAutolink } = state;\n            state.inAutolink = undefined;\n            return inAutolink ? \">\"\n                : \"](\" + mark.attrs.href.replace(/[\\(\\)\"]/g, \"\\\\$&\") + (mark.attrs.title ? ` \"${mark.attrs.title.replace(/\"/g, '\\\\\"')}\"` : \"\") + \")\";\n        },\n        mixable: true\n    },\n    code: { open(_state, _mark, parent, index) { return backticksFor(parent.child(index), -1); },\n        close(_state, _mark, parent, index) { return backticksFor(parent.child(index - 1), 1); },\n        escape: false }\n});\nfunction backticksFor(node, side) {\n    let ticks = /`+/g, m, len = 0;\n    if (node.isText)\n        while (m = ticks.exec(node.text))\n            len = Math.max(len, m[0].length);\n    let result = len > 0 && side > 0 ? \" `\" : \"`\";\n    for (let i = 0; i < len; i++)\n        result += \"`\";\n    if (len > 0 && side < 0)\n        result += \" \";\n    return result;\n}\nfunction isPlainURL(link, parent, index) {\n    if (link.attrs.title || !/^\\w+:/.test(link.attrs.href))\n        return false;\n    let content = parent.child(index);\n    if (!content.isText || content.text != link.attrs.href || content.marks[content.marks.length - 1] != link)\n        return false;\n    return index == parent.childCount - 1 || !link.isInSet(parent.child(index + 1).marks);\n}\n/**\nThis is an object used to track state and expose\nmethods related to markdown serialization. Instances are passed to\nnode and mark serialization methods (see `toMarkdown`).\n*/\nclass MarkdownSerializerState {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    nodes, \n    /**\n    @internal\n    */\n    marks, \n    /**\n    The options passed to the serializer.\n    */\n    options) {\n        this.nodes = nodes;\n        this.marks = marks;\n        this.options = options;\n        /**\n        @internal\n        */\n        this.delim = \"\";\n        /**\n        @internal\n        */\n        this.out = \"\";\n        /**\n        @internal\n        */\n        this.closed = null;\n        /**\n        @internal\n        */\n        this.inAutolink = undefined;\n        /**\n        @internal\n        */\n        this.atBlockStart = false;\n        /**\n        @internal\n        */\n        this.inTightList = false;\n        if (typeof this.options.tightLists == \"undefined\")\n            this.options.tightLists = false;\n        if (typeof this.options.hardBreakNodeName == \"undefined\")\n            this.options.hardBreakNodeName = \"hard_break\";\n    }\n    /**\n    @internal\n    */\n    flushClose(size = 2) {\n        if (this.closed) {\n            if (!this.atBlank())\n                this.out += \"\\n\";\n            if (size > 1) {\n                let delimMin = this.delim;\n                let trim = /\\s+$/.exec(delimMin);\n                if (trim)\n                    delimMin = delimMin.slice(0, delimMin.length - trim[0].length);\n                for (let i = 1; i < size; i++)\n                    this.out += delimMin + \"\\n\";\n            }\n            this.closed = null;\n        }\n    }\n    /**\n    @internal\n    */\n    getMark(name) {\n        let info = this.marks[name];\n        if (!info) {\n            if (this.options.strict !== false)\n                throw new Error(`Mark type \\`${name}\\` not supported by Markdown renderer`);\n            info = blankMark;\n        }\n        return info;\n    }\n    /**\n    Render a block, prefixing each line with `delim`, and the first\n    line in `firstDelim`. `node` should be the node that is closed at\n    the end of the block, and `f` is a function that renders the\n    content of the block.\n    */\n    wrapBlock(delim, firstDelim, node, f) {\n        let old = this.delim;\n        this.write(firstDelim != null ? firstDelim : delim);\n        this.delim += delim;\n        f();\n        this.delim = old;\n        this.closeBlock(node);\n    }\n    /**\n    @internal\n    */\n    atBlank() {\n        return /(^|\\n)$/.test(this.out);\n    }\n    /**\n    Ensure the current content ends with a newline.\n    */\n    ensureNewLine() {\n        if (!this.atBlank())\n            this.out += \"\\n\";\n    }\n    /**\n    Prepare the state for writing output (closing closed paragraphs,\n    adding delimiters, and so on), and then optionally add content\n    (unescaped) to the output.\n    */\n    write(content) {\n        this.flushClose();\n        if (this.delim && this.atBlank())\n            this.out += this.delim;\n        if (content)\n            this.out += content;\n    }\n    /**\n    Close the block for the given node.\n    */\n    closeBlock(node) {\n        this.closed = node;\n    }\n    /**\n    Add the given text to the document. When escape is not `false`,\n    it will be escaped.\n    */\n    text(text, escape = true) {\n        let lines = text.split(\"\\n\");\n        for (let i = 0; i < lines.length; i++) {\n            this.write();\n            // Escape exclamation marks in front of links\n            if (!escape && lines[i][0] == \"[\" && /(^|[^\\\\])\\!$/.test(this.out))\n                this.out = this.out.slice(0, this.out.length - 1) + \"\\\\!\";\n            this.out += escape ? this.esc(lines[i], this.atBlockStart) : lines[i];\n            if (i != lines.length - 1)\n                this.out += \"\\n\";\n        }\n    }\n    /**\n    Render the given node as a block.\n    */\n    render(node, parent, index) {\n        if (this.nodes[node.type.name]) {\n            this.nodes[node.type.name](this, node, parent, index);\n        }\n        else {\n            if (this.options.strict !== false) {\n                throw new Error(\"Token type `\" + node.type.name + \"` not supported by Markdown renderer\");\n            }\n            else if (!node.type.isLeaf) {\n                if (node.type.inlineContent)\n                    this.renderInline(node);\n                else\n                    this.renderContent(node);\n                if (node.isBlock)\n                    this.closeBlock(node);\n            }\n        }\n    }\n    /**\n    Render the contents of `parent` as block nodes.\n    */\n    renderContent(parent) {\n        parent.forEach((node, _, i) => this.render(node, parent, i));\n    }\n    /**\n    Render the contents of `parent` as inline content.\n    */\n    renderInline(parent, fromBlockStart = true) {\n        this.atBlockStart = fromBlockStart;\n        let active = [], trailing = \"\";\n        let progress = (node, offset, index) => {\n            let marks = node ? node.marks : [];\n            // Remove marks from `hard_break` that are the last node inside\n            // that mark to prevent parser edge cases with new lines just\n            // before closing marks.\n            if (node && node.type.name === this.options.hardBreakNodeName)\n                marks = marks.filter(m => {\n                    if (index + 1 == parent.childCount)\n                        return false;\n                    let next = parent.child(index + 1);\n                    return m.isInSet(next.marks) && (!next.isText || /\\S/.test(next.text));\n                });\n            let leading = trailing;\n            trailing = \"\";\n            // If whitespace has to be expelled from the node, adjust\n            // leading and trailing accordingly.\n            if (node && node.isText && marks.some(mark => {\n                let info = this.getMark(mark.type.name);\n                return info && info.expelEnclosingWhitespace && !mark.isInSet(active);\n            })) {\n                let [_, lead, rest] = /^(\\s*)(.*)$/m.exec(node.text);\n                if (lead) {\n                    leading += lead;\n                    node = rest ? node.withText(rest) : null;\n                    if (!node)\n                        marks = active;\n                }\n            }\n            if (node && node.isText && marks.some(mark => {\n                let info = this.getMark(mark.type.name);\n                return info && info.expelEnclosingWhitespace &&\n                    (index == parent.childCount - 1 || !mark.isInSet(parent.child(index + 1).marks));\n            })) {\n                let [_, rest, trail] = /^(.*?)(\\s*)$/m.exec(node.text);\n                if (trail) {\n                    trailing = trail;\n                    node = rest ? node.withText(rest) : null;\n                    if (!node)\n                        marks = active;\n                }\n            }\n            let inner = marks.length ? marks[marks.length - 1] : null;\n            let noEsc = inner && this.getMark(inner.type.name).escape === false;\n            let len = marks.length - (noEsc ? 1 : 0);\n            // Try to reorder 'mixable' marks, such as em and strong, which\n            // in Markdown may be opened and closed in different order, so\n            // that order of the marks for the token matches the order in\n            // active.\n            outer: for (let i = 0; i < len; i++) {\n                let mark = marks[i];\n                if (!this.getMark(mark.type.name).mixable)\n                    break;\n                for (let j = 0; j < active.length; j++) {\n                    let other = active[j];\n                    if (!this.getMark(other.type.name).mixable)\n                        break;\n                    if (mark.eq(other)) {\n                        if (i > j)\n                            marks = marks.slice(0, j).concat(mark).concat(marks.slice(j, i)).concat(marks.slice(i + 1, len));\n                        else if (j > i)\n                            marks = marks.slice(0, i).concat(marks.slice(i + 1, j)).concat(mark).concat(marks.slice(j, len));\n                        continue outer;\n                    }\n                }\n            }\n            // Find the prefix of the mark set that didn't change\n            let keep = 0;\n            while (keep < Math.min(active.length, len) && marks[keep].eq(active[keep]))\n                ++keep;\n            // Close the marks that need to be closed\n            while (keep < active.length)\n                this.text(this.markString(active.pop(), false, parent, index), false);\n            // Output any previously expelled trailing whitespace outside the marks\n            if (leading)\n                this.text(leading);\n            // Open the marks that need to be opened\n            if (node) {\n                while (active.length < len) {\n                    let add = marks[active.length];\n                    active.push(add);\n                    this.text(this.markString(add, true, parent, index), false);\n                    this.atBlockStart = false;\n                }\n                // Render the node. Special case code marks, since their content\n                // may not be escaped.\n                if (noEsc && node.isText)\n                    this.text(this.markString(inner, true, parent, index) + node.text +\n                        this.markString(inner, false, parent, index + 1), false);\n                else\n                    this.render(node, parent, index);\n                this.atBlockStart = false;\n            }\n            // After the first non-empty text node is rendered, the end of output\n            // is no longer at block start.\n            //\n            // FIXME: If a non-text node writes something to the output for this\n            // block, the end of output is also no longer at block start. But how\n            // can we detect that?\n            if ((node === null || node === void 0 ? void 0 : node.isText) && node.nodeSize > 0) {\n                this.atBlockStart = false;\n            }\n        };\n        parent.forEach(progress);\n        progress(null, 0, parent.childCount);\n        this.atBlockStart = false;\n    }\n    /**\n    Render a node's content as a list. `delim` should be the extra\n    indentation added to all lines except the first in an item,\n    `firstDelim` is a function going from an item index to a\n    delimiter for the first line of the item.\n    */\n    renderList(node, delim, firstDelim) {\n        if (this.closed && this.closed.type == node.type)\n            this.flushClose(3);\n        else if (this.inTightList)\n            this.flushClose(1);\n        let isTight = typeof node.attrs.tight != \"undefined\" ? node.attrs.tight : this.options.tightLists;\n        let prevTight = this.inTightList;\n        this.inTightList = isTight;\n        node.forEach((child, _, i) => {\n            if (i && isTight)\n                this.flushClose(1);\n            this.wrapBlock(delim, firstDelim(i), node, () => this.render(child, node, i));\n        });\n        this.inTightList = prevTight;\n    }\n    /**\n    Escape the given string so that it can safely appear in Markdown\n    content. If `startOfLine` is true, also escape characters that\n    have special meaning only at the start of the line.\n    */\n    esc(str, startOfLine = false) {\n        str = str.replace(/[`*\\\\~\\[\\]_]/g, (m, i) => m == \"_\" && i > 0 && i + 1 < str.length && str[i - 1].match(/\\w/) && str[i + 1].match(/\\w/) ? m : \"\\\\\" + m);\n        if (startOfLine)\n            str = str.replace(/^(\\+[ ]|[\\-*>])/, \"\\\\$&\").replace(/^(\\s*)(#{1,6})(\\s|$)/, '$1\\\\$2$3').replace(/^(\\s*\\d+)\\.\\s/, \"$1\\\\. \");\n        if (this.options.escapeExtraCharacters)\n            str = str.replace(this.options.escapeExtraCharacters, \"\\\\$&\");\n        return str;\n    }\n    /**\n    @internal\n    */\n    quote(str) {\n        let wrap = str.indexOf('\"') == -1 ? '\"\"' : str.indexOf(\"'\") == -1 ? \"''\" : \"()\";\n        return wrap[0] + str + wrap[1];\n    }\n    /**\n    Repeat the given string `n` times.\n    */\n    repeat(str, n) {\n        let out = \"\";\n        for (let i = 0; i < n; i++)\n            out += str;\n        return out;\n    }\n    /**\n    Get the markdown string for a given opening or closing mark.\n    */\n    markString(mark, open, parent, index) {\n        let info = this.getMark(mark.type.name);\n        let value = open ? info.open : info.close;\n        return typeof value == \"string\" ? value : value(this, mark, parent, index);\n    }\n    /**\n    Get leading and trailing whitespace from a string. Values of\n    leading or trailing property of the return object will be undefined\n    if there is no match.\n    */\n    getEnclosingWhitespace(text) {\n        return {\n            leading: (text.match(/^(\\s+)/) || [undefined])[0],\n            trailing: (text.match(/(\\s+)$/) || [undefined])[0]\n        };\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/prosemirror-markdown@1.13.2/node_modules/prosemirror-markdown/dist/index.js\n");

/***/ })

};
;