"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-ordered-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-ordered-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-ordered-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-ordered-list/dist/index.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-ordered-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-ordered-list/dist/index.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrderedList: () => (/* binding */ OrderedList),\n/* harmony export */   \"default\": () => (/* binding */ OrderedList),\n/* harmony export */   inputRegex: () => (/* binding */ inputRegex)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\nconst ListItemName = 'listItem';\nconst TextStyleName = 'textStyle';\n/**\n * Matches an ordered list to a 1. on input (or any number followed by a dot).\n */\nconst inputRegex = /^(\\d+)\\.\\s$/;\n/**\n * This extension allows you to create ordered lists.\n * This requires the ListItem extension\n * @see https://www.tiptap.dev/api/nodes/ordered-list\n * @see https://www.tiptap.dev/api/nodes/list-item\n */\nconst OrderedList = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Node.create({\n    name: 'orderedList',\n    addOptions() {\n        return {\n            itemTypeName: 'listItem',\n            HTMLAttributes: {},\n            keepMarks: false,\n            keepAttributes: false,\n        };\n    },\n    group: 'block list',\n    content() {\n        return `${this.options.itemTypeName}+`;\n    },\n    addAttributes() {\n        return {\n            start: {\n                default: 1,\n                parseHTML: element => {\n                    return element.hasAttribute('start')\n                        ? parseInt(element.getAttribute('start') || '', 10)\n                        : 1;\n                },\n            },\n            type: {\n                default: null,\n                parseHTML: element => element.getAttribute('type'),\n            },\n        };\n    },\n    parseHTML() {\n        return [\n            {\n                tag: 'ol',\n            },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        const { start, ...attributesWithoutStart } = HTMLAttributes;\n        return start === 1\n            ? ['ol', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, attributesWithoutStart), 0]\n            : ['ol', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n    addCommands() {\n        return {\n            toggleOrderedList: () => ({ commands, chain }) => {\n                if (this.options.keepAttributes) {\n                    return chain().toggleList(this.name, this.options.itemTypeName, this.options.keepMarks).updateAttributes(ListItemName, this.editor.getAttributes(TextStyleName)).run();\n                }\n                return commands.toggleList(this.name, this.options.itemTypeName, this.options.keepMarks);\n            },\n        };\n    },\n    addKeyboardShortcuts() {\n        return {\n            'Mod-Shift-7': () => this.editor.commands.toggleOrderedList(),\n        };\n    },\n    addInputRules() {\n        let inputRule = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.wrappingInputRule)({\n            find: inputRegex,\n            type: this.type,\n            getAttributes: match => ({ start: +match[1] }),\n            joinPredicate: (match, node) => node.childCount + node.attrs.start === +match[1],\n        });\n        if (this.options.keepMarks || this.options.keepAttributes) {\n            inputRule = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.wrappingInputRule)({\n                find: inputRegex,\n                type: this.type,\n                keepMarks: this.options.keepMarks,\n                keepAttributes: this.options.keepAttributes,\n                getAttributes: match => ({ start: +match[1], ...this.editor.getAttributes(TextStyleName) }),\n                joinPredicate: (match, node) => node.childCount + node.attrs.start === +match[1],\n                editor: this.editor,\n            });\n        }\n        return [\n            inputRule,\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-ordered-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-ordered-list/dist/index.js\n");

/***/ })

};
;