"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@scena";
exports.ids = ["vendor-chunks/@scena"];
exports.modules = {

/***/ "(ssr)/./node_modules/@scena/dragscroll/dist/dragscroll.esm.js":
/*!***************************************************************!*\
  !*** ./node_modules/@scena/dragscroll/dist/dragscroll.esm.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _scena_event_emitter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @scena/event-emitter */ \"(ssr)/./node_modules/@scena/event-emitter/dist/event-emitter.esm.js\");\n/* harmony import */ var _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @daybrush/utils */ \"(ssr)/./node_modules/@daybrush/utils/dist/utils.esm.js\");\n/*\nCopyright (c) 2019 Daybrush\nname: @scena/dragscroll\nlicense: MIT\nauthor: Daybrush\nrepository: git+https://github.com/daybrush/dragscroll.git\nversion: 1.4.0\n*/\n\n\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\n/* global Reflect, Promise */\n\nvar extendStatics = function (d, b) {\n  extendStatics = Object.setPrototypeOf || {\n    __proto__: []\n  } instanceof Array && function (d, b) {\n    d.__proto__ = b;\n  } || function (d, b) {\n    for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n  };\n  return extendStatics(d, b);\n};\nfunction __extends(d, b) {\n  extendStatics(d, b);\n  function __() {\n    this.constructor = d;\n  }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\nvar __assign = function () {\n  __assign = Object.assign || function __assign(t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\n\nfunction getDefaultScrollPosition(e) {\n  var container = e.container;\n  if (container === document.body) {\n    return [container.scrollLeft || document.documentElement.scrollLeft, container.scrollTop || document.documentElement.scrollTop];\n  }\n  return [container.scrollLeft, container.scrollTop];\n}\nfunction checkDefaultScrollEvent(container, callback) {\n  container.addEventListener(\"scroll\", callback);\n  return function () {\n    container.removeEventListener(\"scroll\", callback);\n  };\n}\nfunction getContainerElement(container) {\n  if (!container) {\n    return null;\n  } else if ((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.isString)(container)) {\n    return document.querySelector(container);\n  }\n  if ((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.isFunction)(container)) {\n    return container();\n  } else if (container instanceof Element) {\n    return container;\n  } else if (\"current\" in container) {\n    return container.current;\n  } else if (\"value\" in container) {\n    return container.value;\n  }\n}\n/**\n * @sort 1\n */\nvar DragScroll = /*#__PURE__*/function (_super) {\n  __extends(DragScroll, _super);\n  function DragScroll() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this._startRect = null;\n    _this._startPos = [];\n    _this._prevTime = 0;\n    _this._timer = 0;\n    _this._prevScrollPos = [0, 0];\n    _this._isWait = false;\n    _this._flag = false;\n    _this._currentOptions = null;\n    _this._lock = false;\n    _this._unregister = null;\n    _this._onScroll = function () {\n      var options = _this._currentOptions;\n      if (_this._lock || !options) {\n        return;\n      }\n      _this.emit(\"scrollDrag\", {\n        next: function (inputEvent) {\n          _this.checkScroll({\n            container: options.container,\n            inputEvent: inputEvent\n          });\n        }\n      });\n    };\n    return _this;\n  }\n  /**\n   */\n  var __proto = DragScroll.prototype;\n  __proto.dragStart = function (e, options) {\n    var container = getContainerElement(options.container);\n    if (!container) {\n      this._flag = false;\n      return;\n    }\n    var top = 0;\n    var left = 0;\n    var width = 0;\n    var height = 0;\n    if (container === document.body) {\n      width = window.innerWidth;\n      height = window.innerHeight;\n    } else {\n      var rect = container.getBoundingClientRect();\n      top = rect.top;\n      left = rect.left;\n      width = rect.width;\n      height = rect.height;\n    }\n    this._flag = true;\n    this._startPos = [e.clientX, e.clientY];\n    this._startRect = {\n      top: top,\n      left: left,\n      width: width,\n      height: height\n    };\n    this._prevScrollPos = this._getScrollPosition([0, 0], options);\n    this._currentOptions = options;\n    this._registerScrollEvent(options);\n  };\n  __proto.drag = function (e, options) {\n    clearTimeout(this._timer);\n    if (!this._flag) {\n      return;\n    }\n    var clientX = e.clientX,\n      clientY = e.clientY;\n    var _a = options.threshold,\n      threshold = _a === void 0 ? 0 : _a;\n    var _b = this,\n      _startRect = _b._startRect,\n      _startPos = _b._startPos;\n    this._currentOptions = options;\n    var direction = [0, 0];\n    if (_startRect.top > clientY - threshold) {\n      if (_startPos[1] > _startRect.top || clientY < _startPos[1]) {\n        direction[1] = -1;\n      }\n    } else if (_startRect.top + _startRect.height < clientY + threshold) {\n      if (_startPos[1] < _startRect.top + _startRect.height || clientY > _startPos[1]) {\n        direction[1] = 1;\n      }\n    }\n    if (_startRect.left > clientX - threshold) {\n      if (_startPos[0] > _startRect.left || clientX < _startPos[0]) {\n        direction[0] = -1;\n      }\n    } else if (_startRect.left + _startRect.width < clientX + threshold) {\n      if (_startPos[0] < _startRect.left + _startRect.width || clientX > _startPos[0]) {\n        direction[0] = 1;\n      }\n    }\n    if (!direction[0] && !direction[1]) {\n      return false;\n    }\n    return this._continueDrag(__assign(__assign({}, options), {\n      direction: direction,\n      inputEvent: e,\n      isDrag: true\n    }));\n  };\n  /**\n   */\n  __proto.checkScroll = function (options) {\n    var _this = this;\n    if (this._isWait) {\n      return false;\n    }\n    var _a = options.prevScrollPos,\n      prevScrollPos = _a === void 0 ? this._prevScrollPos : _a,\n      direction = options.direction,\n      _b = options.throttleTime,\n      throttleTime = _b === void 0 ? 0 : _b,\n      inputEvent = options.inputEvent,\n      isDrag = options.isDrag;\n    var nextScrollPos = this._getScrollPosition(direction || [0, 0], options);\n    var offsetX = nextScrollPos[0] - prevScrollPos[0];\n    var offsetY = nextScrollPos[1] - prevScrollPos[1];\n    var nextDirection = direction || [offsetX ? Math.abs(offsetX) / offsetX : 0, offsetY ? Math.abs(offsetY) / offsetY : 0];\n    this._prevScrollPos = nextScrollPos;\n    this._lock = false;\n    if (!offsetX && !offsetY) {\n      return false;\n    }\n    /**\n     * @event DragScroll#move\n     */\n    this.emit(\"move\", {\n      offsetX: nextDirection[0] ? offsetX : 0,\n      offsetY: nextDirection[1] ? offsetY : 0,\n      inputEvent: inputEvent\n    });\n    if (throttleTime && isDrag) {\n      clearTimeout(this._timer);\n      this._timer = window.setTimeout(function () {\n        _this._continueDrag(options);\n      }, throttleTime);\n    }\n    return true;\n  };\n  /**\n   *\n   */\n  __proto.dragEnd = function () {\n    this._flag = false;\n    this._lock = false;\n    clearTimeout(this._timer);\n    this._unregisterScrollEvent();\n  };\n  __proto._getScrollPosition = function (direction, options) {\n    var container = options.container,\n      _a = options.getScrollPosition,\n      getScrollPosition = _a === void 0 ? getDefaultScrollPosition : _a;\n    return getScrollPosition({\n      container: getContainerElement(container),\n      direction: direction\n    });\n  };\n  __proto._continueDrag = function (options) {\n    var _this = this;\n    var _a;\n    var container = options.container,\n      direction = options.direction,\n      throttleTime = options.throttleTime,\n      useScroll = options.useScroll,\n      isDrag = options.isDrag,\n      inputEvent = options.inputEvent;\n    if (!this._flag || isDrag && this._isWait) {\n      return;\n    }\n    var nowTime = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.now)();\n    var distTime = Math.max(throttleTime + this._prevTime - nowTime, 0);\n    if (distTime > 0) {\n      clearTimeout(this._timer);\n      this._timer = window.setTimeout(function () {\n        _this._continueDrag(options);\n      }, distTime);\n      return false;\n    }\n    this._prevTime = nowTime;\n    var prevScrollPos = this._getScrollPosition(direction, options);\n    this._prevScrollPos = prevScrollPos;\n    if (isDrag) {\n      this._isWait = true;\n    }\n    // unregister native scroll event\n    if (!useScroll) {\n      this._lock = true;\n    }\n    var param = {\n      container: getContainerElement(container),\n      direction: direction,\n      inputEvent: inputEvent\n    };\n    (_a = options.requestScroll) === null || _a === void 0 ? void 0 : _a.call(options, param);\n    /**\n     * @event DragScroll#scroll\n     */\n    this.emit(\"scroll\", param);\n    this._isWait = false;\n    return useScroll || this.checkScroll(__assign(__assign({}, options), {\n      prevScrollPos: prevScrollPos,\n      direction: direction,\n      inputEvent: inputEvent\n    }));\n  };\n  __proto._registerScrollEvent = function (options) {\n    this._unregisterScrollEvent();\n    var checkScrollEvent = options.checkScrollEvent;\n    if (!checkScrollEvent) {\n      return;\n    }\n    var callback = checkScrollEvent === true ? checkDefaultScrollEvent : checkScrollEvent;\n    var container = getContainerElement(options.container);\n    if (checkScrollEvent === true && (container === document.body || container === document.documentElement)) {\n      this._unregister = checkDefaultScrollEvent(window, this._onScroll);\n    } else {\n      this._unregister = callback(container, this._onScroll);\n    }\n  };\n  __proto._unregisterScrollEvent = function () {\n    var _a;\n    (_a = this._unregister) === null || _a === void 0 ? void 0 : _a.call(this);\n    this._unregister = null;\n  };\n  return DragScroll;\n}(_scena_event_emitter__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DragScroll);\n//# sourceMappingURL=dragscroll.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@scena/dragscroll/dist/dragscroll.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@scena/event-emitter/dist/event-emitter.esm.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@scena/event-emitter/dist/event-emitter.esm.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @daybrush/utils */ \"(ssr)/./node_modules/@daybrush/utils/dist/utils.esm.js\");\n/*\nCopyright (c) 2019 Daybrush\nname: @scena/event-emitter\nlicense: MIT\nauthor: Daybrush\nrepository: git+https://github.com/daybrush/gesture.git\nversion: 1.0.5\n*/\n\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\nvar __assign = function () {\n  __assign = Object.assign || function __assign(t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n\n    return t;\n  };\n\n  return __assign.apply(this, arguments);\n};\nfunction __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n\n  for (var r = Array(s), k = 0, i = 0; i < il; i++) for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++) r[k] = a[j];\n\n  return r;\n}\n\n/**\n * Implement EventEmitter on object or component.\n */\n\nvar EventEmitter =\n/*#__PURE__*/\nfunction () {\n  function EventEmitter() {\n    this._events = {};\n  }\n  /**\n   * Add a listener to the registered event.\n   * @param - Name of the event to be added\n   * @param - listener function of the event to be added\n   * @example\n   * import EventEmitter from \"@scena/event-emitter\";\n   * cosnt emitter = new EventEmitter();\n   *\n   * // Add listener in \"a\" event\n   * emitter.on(\"a\", () => {\n   * });\n   * // Add listeners\n   * emitter.on({\n   *  a: () => {},\n   *  b: () => {},\n   * });\n   */\n\n\n  var __proto = EventEmitter.prototype;\n\n  __proto.on = function (eventName, listener) {\n    if ((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.isObject)(eventName)) {\n      for (var name in eventName) {\n        this.on(name, eventName[name]);\n      }\n    } else {\n      this._addEvent(eventName, listener, {});\n    }\n\n    return this;\n  };\n  /**\n   * Remove listeners registered in the event target.\n   * @param - Name of the event to be removed\n   * @param - listener function of the event to be removed\n   * @example\n   * import EventEmitter from \"@scena/event-emitter\";\n   * cosnt emitter = new EventEmitter();\n   *\n   * // Remove all listeners.\n   * emitter.off();\n   *\n   * // Remove all listeners in \"A\" event.\n   * emitter.off(\"a\");\n   *\n   *\n   * // Remove \"listener\" listener in \"a\" event.\n   * emitter.off(\"a\", listener);\n   */\n\n\n  __proto.off = function (eventName, listener) {\n    if (!eventName) {\n      this._events = {};\n    } else if ((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.isObject)(eventName)) {\n      for (var name in eventName) {\n        this.off(name);\n      }\n    } else if (!listener) {\n      this._events[eventName] = [];\n    } else {\n      var events = this._events[eventName];\n\n      if (events) {\n        var index = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.findIndex)(events, function (e) {\n          return e.listener === listener;\n        });\n\n        if (index > -1) {\n          events.splice(index, 1);\n        }\n      }\n    }\n\n    return this;\n  };\n  /**\n   * Add a disposable listener and Use promise to the registered event.\n   * @param - Name of the event to be added\n   * @param - disposable listener function of the event to be added\n   * @example\n   * import EventEmitter from \"@scena/event-emitter\";\n   * cosnt emitter = new EventEmitter();\n   *\n   * // Add a disposable listener in \"a\" event\n   * emitter.once(\"a\", () => {\n   * });\n   *\n   * // Use Promise\n   * emitter.once(\"a\").then(e => {\n   * });\n   */\n\n\n  __proto.once = function (eventName, listener) {\n    var _this = this;\n\n    if (listener) {\n      this._addEvent(eventName, listener, {\n        once: true\n      });\n    }\n\n    return new Promise(function (resolve) {\n      _this._addEvent(eventName, resolve, {\n        once: true\n      });\n    });\n  };\n  /**\n   * Fires an event to call listeners.\n   * @param - Event name\n   * @param - Event parameter\n   * @return If false, stop the event.\n   * @example\n   *\n   * import EventEmitter from \"@scena/event-emitter\";\n   *\n   *\n   * const emitter = new EventEmitter();\n   *\n   * emitter.on(\"a\", e => {\n   * });\n   *\n   *\n   * emitter.emit(\"a\", {\n   *   a: 1,\n   * });\n   */\n\n\n  __proto.emit = function (eventName, param) {\n    var _this = this;\n\n    if (param === void 0) {\n      param = {};\n    }\n\n    var events = this._events[eventName];\n\n    if (!eventName || !events) {\n      return true;\n    }\n\n    var isStop = false;\n    param.eventType = eventName;\n\n    param.stop = function () {\n      isStop = true;\n    };\n\n    param.currentTarget = this;\n\n    __spreadArrays(events).forEach(function (info) {\n      info.listener(param);\n\n      if (info.once) {\n        _this.off(eventName, info.listener);\n      }\n    });\n\n    return !isStop;\n  };\n  /**\n   * Fires an event to call listeners.\n   * @param - Event name\n   * @param - Event parameter\n   * @return If false, stop the event.\n   * @example\n   *\n   * import EventEmitter from \"@scena/event-emitter\";\n   *\n   *\n   * const emitter = new EventEmitter();\n   *\n   * emitter.on(\"a\", e => {\n   * });\n   *\n   *\n   * emitter.emit(\"a\", {\n   *   a: 1,\n   * });\n   */\n\n  /**\n  * Fires an event to call listeners.\n  * @param - Event name\n  * @param - Event parameter\n  * @return If false, stop the event.\n  * @example\n  *\n  * import EventEmitter from \"@scena/event-emitter\";\n  *\n  *\n  * const emitter = new EventEmitter();\n  *\n  * emitter.on(\"a\", e => {\n  * });\n  *\n  * // emit\n  * emitter.trigger(\"a\", {\n  *   a: 1,\n  * });\n  */\n\n\n  __proto.trigger = function (eventName, param) {\n    if (param === void 0) {\n      param = {};\n    }\n\n    return this.emit(eventName, param);\n  };\n\n  __proto._addEvent = function (eventName, listener, options) {\n    var events = this._events;\n    events[eventName] = events[eventName] || [];\n    var listeners = events[eventName];\n    listeners.push(__assign({\n      listener: listener\n    }, options));\n  };\n\n  return EventEmitter;\n}();\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EventEmitter);\n//# sourceMappingURL=event-emitter.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@scena/event-emitter/dist/event-emitter.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@scena/matrix/dist/matrix.esm.js":
/*!*******************************************************!*\
  !*** ./node_modules/@scena/matrix/dist/matrix.esm.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculate: () => (/* binding */ calculate),\n/* harmony export */   convertCSStoMatrix: () => (/* binding */ convertCSStoMatrix),\n/* harmony export */   convertDimension: () => (/* binding */ convertDimension),\n/* harmony export */   convertMatrixtoCSS: () => (/* binding */ convertMatrixtoCSS),\n/* harmony export */   convertPositionMatrix: () => (/* binding */ convertPositionMatrix),\n/* harmony export */   createIdentityMatrix: () => (/* binding */ createIdentityMatrix),\n/* harmony export */   createOriginMatrix: () => (/* binding */ createOriginMatrix),\n/* harmony export */   createRotateMatrix: () => (/* binding */ createRotateMatrix),\n/* harmony export */   createScaleMatrix: () => (/* binding */ createScaleMatrix),\n/* harmony export */   createWarpMatrix: () => (/* binding */ createWarpMatrix),\n/* harmony export */   fromTranslation: () => (/* binding */ fromTranslation),\n/* harmony export */   getCenter: () => (/* binding */ getCenter),\n/* harmony export */   getOrigin: () => (/* binding */ getOrigin),\n/* harmony export */   ignoreDimension: () => (/* binding */ ignoreDimension),\n/* harmony export */   invert: () => (/* binding */ invert),\n/* harmony export */   matrix3d: () => (/* binding */ matrix3d),\n/* harmony export */   minus: () => (/* binding */ minus),\n/* harmony export */   multiplies: () => (/* binding */ multiplies),\n/* harmony export */   multiply: () => (/* binding */ multiply),\n/* harmony export */   plus: () => (/* binding */ plus),\n/* harmony export */   rotate: () => (/* binding */ rotate),\n/* harmony export */   rotateX3d: () => (/* binding */ rotateX3d),\n/* harmony export */   rotateY3d: () => (/* binding */ rotateY3d),\n/* harmony export */   rotateZ3d: () => (/* binding */ rotateZ3d),\n/* harmony export */   scale3d: () => (/* binding */ scale3d),\n/* harmony export */   translate3d: () => (/* binding */ translate3d),\n/* harmony export */   transpose: () => (/* binding */ transpose)\n/* harmony export */ });\n/* harmony import */ var _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @daybrush/utils */ \"(ssr)/./node_modules/@daybrush/utils/dist/utils.esm.js\");\n/*\nCopyright (c) 2020 Daybrush\nname: @scena/matrix\nlicense: MIT\nauthor: Daybrush\nrepository: git+https://github.com/daybrush/matrix\nversion: 1.1.1\n*/\n\n\nfunction add(matrix, inverseMatrix, startIndex, fromIndex, n, k) {\n  for (var i = 0; i < n; ++i) {\n    var x = startIndex + i * n;\n    var fromX = fromIndex + i * n;\n    matrix[x] += matrix[fromX] * k;\n    inverseMatrix[x] += inverseMatrix[fromX] * k;\n  }\n}\n\nfunction swap(matrix, inverseMatrix, startIndex, fromIndex, n) {\n  for (var i = 0; i < n; ++i) {\n    var x = startIndex + i * n;\n    var fromX = fromIndex + i * n;\n    var v = matrix[x];\n    var iv = inverseMatrix[x];\n    matrix[x] = matrix[fromX];\n    matrix[fromX] = v;\n    inverseMatrix[x] = inverseMatrix[fromX];\n    inverseMatrix[fromX] = iv;\n  }\n}\n\nfunction divide(matrix, inverseMatrix, startIndex, n, k) {\n  for (var i = 0; i < n; ++i) {\n    var x = startIndex + i * n;\n    matrix[x] /= k;\n    inverseMatrix[x] /= k;\n  }\n}\n/**\n *\n * @namespace Matrix\n */\n\n/**\n * @memberof Matrix\n */\n\n\nfunction ignoreDimension(matrix, m, n) {\n  if (n === void 0) {\n    n = Math.sqrt(matrix.length);\n  }\n\n  var newMatrix = matrix.slice();\n\n  for (var i = 0; i < n; ++i) {\n    newMatrix[i * n + m - 1] = 0;\n    newMatrix[(m - 1) * n + i] = 0;\n  }\n\n  newMatrix[(m - 1) * (n + 1)] = 1;\n  return newMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction invert(matrix, n) {\n  if (n === void 0) {\n    n = Math.sqrt(matrix.length);\n  }\n\n  var newMatrix = matrix.slice();\n  var inverseMatrix = createIdentityMatrix(n);\n\n  for (var i = 0; i < n; ++i) {\n    // diagonal\n    var identityIndex = n * i + i;\n\n    if (!(0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.throttle)(newMatrix[identityIndex], _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.TINY_NUM)) {\n      // newMatrix[identityIndex] = 0;\n      for (var j = i + 1; j < n; ++j) {\n        if (newMatrix[n * i + j]) {\n          swap(newMatrix, inverseMatrix, i, j, n);\n          break;\n        }\n      }\n    }\n\n    if (!(0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.throttle)(newMatrix[identityIndex], _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.TINY_NUM)) {\n      // no inverse matrix\n      return [];\n    }\n\n    divide(newMatrix, inverseMatrix, i, n, newMatrix[identityIndex]);\n\n    for (var j = 0; j < n; ++j) {\n      var targetStartIndex = j;\n      var targetIndex = j + i * n;\n      var target = newMatrix[targetIndex];\n\n      if (!(0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.throttle)(target, _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.TINY_NUM) || i === j) {\n        continue;\n      }\n\n      add(newMatrix, inverseMatrix, targetStartIndex, i, n, -target);\n    }\n  }\n\n  return inverseMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction transpose(matrix, n) {\n  if (n === void 0) {\n    n = Math.sqrt(matrix.length);\n  }\n\n  var newMatrix = [];\n\n  for (var i = 0; i < n; ++i) {\n    for (var j = 0; j < n; ++j) {\n      newMatrix[j * n + i] = matrix[n * i + j];\n    }\n  }\n\n  return newMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction getOrigin(matrix, n) {\n  if (n === void 0) {\n    n = Math.sqrt(matrix.length);\n  }\n\n  var originMatrix = [];\n  var w = matrix[n * n - 1];\n\n  for (var i = 0; i < n - 1; ++i) {\n    originMatrix[i] = matrix[n * (n - 1) + i] / w;\n  }\n\n  originMatrix[n - 1] = 0;\n  return originMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction fromTranslation(pos, n) {\n  var newMatrix = createIdentityMatrix(n);\n\n  for (var i = 0; i < n - 1; ++i) {\n    newMatrix[n * (n - 1) + i] = pos[i] || 0;\n  }\n\n  return newMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction convertPositionMatrix(matrix, n) {\n  var newMatrix = matrix.slice();\n\n  for (var i = matrix.length; i < n - 1; ++i) {\n    newMatrix[i] = 0;\n  }\n\n  newMatrix[n - 1] = 1;\n  return newMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction convertDimension(matrix, n, m) {\n  if (n === void 0) {\n    n = Math.sqrt(matrix.length);\n  } // n < m\n\n\n  if (n === m) {\n    return matrix;\n  }\n\n  var newMatrix = createIdentityMatrix(m);\n  var length = Math.min(n, m);\n\n  for (var i = 0; i < length - 1; ++i) {\n    for (var j = 0; j < length - 1; ++j) {\n      newMatrix[i * m + j] = matrix[i * n + j];\n    }\n\n    newMatrix[(i + 1) * m - 1] = matrix[(i + 1) * n - 1];\n    newMatrix[(m - 1) * m + i] = matrix[(n - 1) * n + i];\n  }\n\n  newMatrix[m * m - 1] = matrix[n * n - 1];\n  return newMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction multiplies(n) {\n  var matrixes = [];\n\n  for (var _i = 1; _i < arguments.length; _i++) {\n    matrixes[_i - 1] = arguments[_i];\n  }\n\n  var m = createIdentityMatrix(n);\n  matrixes.forEach(function (matrix) {\n    m = multiply(m, matrix, n);\n  });\n  return m;\n}\n/**\n * @memberof Matrix\n */\n\nfunction multiply(matrix, matrix2, n) {\n  if (n === void 0) {\n    n = Math.sqrt(matrix.length);\n  }\n\n  var newMatrix = []; // 1 y: n\n  // 1 x: m\n  // 2 x: m\n  // 2 y: k\n  // n * m X m * k\n\n  var m = matrix.length / n;\n  var k = matrix2.length / m;\n\n  if (!m) {\n    return matrix2;\n  } else if (!k) {\n    return matrix;\n  }\n\n  for (var i = 0; i < n; ++i) {\n    for (var j = 0; j < k; ++j) {\n      newMatrix[j * n + i] = 0;\n\n      for (var l = 0; l < m; ++l) {\n        // m1 x: m(l), y: n(i)\n        // m2 x: k(j):  y: m(l)\n        // nw x: n(i), y: k(j)\n        newMatrix[j * n + i] += matrix[l * n + i] * matrix2[j * m + l];\n      }\n    }\n  } // n * k\n\n\n  return newMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction plus(pos1, pos2) {\n  var length = Math.min(pos1.length, pos2.length);\n  var nextPos = pos1.slice();\n\n  for (var i = 0; i < length; ++i) {\n    nextPos[i] = nextPos[i] + pos2[i];\n  }\n\n  return nextPos;\n}\n/**\n * @memberof Matrix\n */\n\nfunction minus(pos1, pos2) {\n  var length = Math.min(pos1.length, pos2.length);\n  var nextPos = pos1.slice();\n\n  for (var i = 0; i < length; ++i) {\n    nextPos[i] = nextPos[i] - pos2[i];\n  }\n\n  return nextPos;\n}\n/**\n * @memberof Matrix\n */\n\nfunction convertCSStoMatrix(a, is2d) {\n  if (is2d === void 0) {\n    is2d = a.length === 6;\n  }\n\n  if (is2d) {\n    return [a[0], a[1], 0, a[2], a[3], 0, a[4], a[5], 1];\n  }\n\n  return a;\n}\n/**\n * @memberof Matrix\n */\n\nfunction convertMatrixtoCSS(a, is2d) {\n  if (is2d === void 0) {\n    is2d = a.length === 9;\n  }\n\n  if (is2d) {\n    return [a[0], a[1], a[3], a[4], a[6], a[7]];\n  }\n\n  return a;\n}\n/**\n * @memberof Matrix\n */\n\nfunction calculate(matrix, matrix2, n) {\n  if (n === void 0) {\n    n = matrix2.length;\n  }\n\n  var result = multiply(matrix, matrix2, n);\n  var k = result[n - 1];\n  return result.map(function (v) {\n    return v / k;\n  });\n}\n/**\n * @memberof Matrix\n */\n\nfunction rotateX3d(matrix, rad) {\n  return multiply(matrix, [1, 0, 0, 0, 0, Math.cos(rad), Math.sin(rad), 0, 0, -Math.sin(rad), Math.cos(rad), 0, 0, 0, 0, 1], 4);\n}\n/**\n * @memberof Matrix\n */\n\nfunction rotateY3d(matrix, rad) {\n  return multiply(matrix, [Math.cos(rad), 0, -Math.sin(rad), 0, 0, 1, 0, 0, Math.sin(rad), 0, Math.cos(rad), 0, 0, 0, 0, 1], 4);\n}\n/**\n * @memberof Matrix\n */\n\nfunction rotateZ3d(matrix, rad) {\n  return multiply(matrix, createRotateMatrix(rad, 4));\n}\n/**\n * @memberof Matrix\n */\n\nfunction scale3d(matrix, _a) {\n  var _b = _a[0],\n      sx = _b === void 0 ? 1 : _b,\n      _c = _a[1],\n      sy = _c === void 0 ? 1 : _c,\n      _d = _a[2],\n      sz = _d === void 0 ? 1 : _d;\n  return multiply(matrix, [sx, 0, 0, 0, 0, sy, 0, 0, 0, 0, sz, 0, 0, 0, 0, 1], 4);\n}\n/**\n * @memberof Matrix\n */\n\nfunction rotate(pos, rad) {\n  return calculate(createRotateMatrix(rad, 3), convertPositionMatrix(pos, 3));\n}\n/**\n * @memberof Matrix\n */\n\nfunction translate3d(matrix, _a) {\n  var _b = _a[0],\n      tx = _b === void 0 ? 0 : _b,\n      _c = _a[1],\n      ty = _c === void 0 ? 0 : _c,\n      _d = _a[2],\n      tz = _d === void 0 ? 0 : _d;\n  return multiply(matrix, [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, tx, ty, tz, 1], 4);\n}\n/**\n * @memberof Matrix\n */\n\nfunction matrix3d(matrix1, matrix2) {\n  return multiply(matrix1, matrix2, 4);\n}\n/**\n * @memberof Matrix\n */\n\nfunction createRotateMatrix(rad, n) {\n  var cos = Math.cos(rad);\n  var sin = Math.sin(rad);\n  var m = createIdentityMatrix(n); // cos -sin\n  // sin cos\n\n  m[0] = cos;\n  m[1] = sin;\n  m[n] = -sin;\n  m[n + 1] = cos;\n  return m;\n}\n/**\n * @memberof Matrix\n */\n\nfunction createIdentityMatrix(n) {\n  var length = n * n;\n  var matrix = [];\n\n  for (var i = 0; i < length; ++i) {\n    matrix[i] = i % (n + 1) ? 0 : 1;\n  }\n\n  return matrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction createScaleMatrix(scale, n) {\n  var m = createIdentityMatrix(n);\n  var length = Math.min(scale.length, n - 1);\n\n  for (var i = 0; i < length; ++i) {\n    m[(n + 1) * i] = scale[i];\n  }\n\n  return m;\n}\n/**\n * @memberof Matrix\n */\n\nfunction createOriginMatrix(origin, n) {\n  var m = createIdentityMatrix(n);\n  var length = Math.min(origin.length, n - 1);\n\n  for (var i = 0; i < length; ++i) {\n    m[n * (n - 1) + i] = origin[i];\n  }\n\n  return m;\n}\n/**\n * @memberof Matrix\n */\n\nfunction createWarpMatrix(pos0, pos1, pos2, pos3, nextPos0, nextPos1, nextPos2, nextPos3) {\n  var x0 = pos0[0],\n      y0 = pos0[1];\n  var x1 = pos1[0],\n      y1 = pos1[1];\n  var x2 = pos2[0],\n      y2 = pos2[1];\n  var x3 = pos3[0],\n      y3 = pos3[1];\n  var u0 = nextPos0[0],\n      v0 = nextPos0[1];\n  var u1 = nextPos1[0],\n      v1 = nextPos1[1];\n  var u2 = nextPos2[0],\n      v2 = nextPos2[1];\n  var u3 = nextPos3[0],\n      v3 = nextPos3[1];\n  var matrix = [x0, 0, x1, 0, x2, 0, x3, 0, y0, 0, y1, 0, y2, 0, y3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, x0, 0, x1, 0, x2, 0, x3, 0, y0, 0, y1, 0, y2, 0, y3, 0, 1, 0, 1, 0, 1, 0, 1, -u0 * x0, -v0 * x0, -u1 * x1, -v1 * x1, -u2 * x2, -v2 * x2, -u3 * x3, -v3 * x3, -u0 * y0, -v0 * y0, -u1 * y1, -v1 * y1, -u2 * y2, -v2 * y2, -u3 * y3, -v3 * y3];\n  var inverseMatrix = invert(matrix, 8);\n\n  if (!inverseMatrix.length) {\n    return [];\n  }\n\n  var h = multiply(inverseMatrix, [u0, v0, u1, v1, u2, v2, u3, v3], 8);\n  h[8] = 1;\n  return convertDimension(transpose(h), 3, 4);\n}\n/**\n * @memberof Matrix\n */\n\nfunction getCenter(points) {\n  return [0, 1].map(function (i) {\n    return (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.average)(points.map(function (pos) {\n      return pos[i];\n    }));\n  });\n}\n\n\n//# sourceMappingURL=matrix.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@scena/matrix/dist/matrix.esm.js\n");

/***/ })

};
;