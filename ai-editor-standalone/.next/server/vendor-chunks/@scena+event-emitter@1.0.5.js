"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@scena+event-emitter@1.0.5";
exports.ids = ["vendor-chunks/@scena+event-emitter@1.0.5"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@scena+event-emitter@1.0.5/node_modules/@scena/event-emitter/dist/event-emitter.esm.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@scena+event-emitter@1.0.5/node_modules/@scena/event-emitter/dist/event-emitter.esm.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @daybrush/utils */ \"(ssr)/./node_modules/.pnpm/@daybrush+utils@1.13.0/node_modules/@daybrush/utils/dist/utils.esm.js\");\n/*\nCopyright (c) 2019 Daybrush\nname: @scena/event-emitter\nlicense: MIT\nauthor: Daybrush\nrepository: git+https://github.com/daybrush/gesture.git\nversion: 1.0.5\n*/\n\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\nvar __assign = function () {\n  __assign = Object.assign || function __assign(t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n\n    return t;\n  };\n\n  return __assign.apply(this, arguments);\n};\nfunction __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n\n  for (var r = Array(s), k = 0, i = 0; i < il; i++) for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++) r[k] = a[j];\n\n  return r;\n}\n\n/**\n * Implement EventEmitter on object or component.\n */\n\nvar EventEmitter =\n/*#__PURE__*/\nfunction () {\n  function EventEmitter() {\n    this._events = {};\n  }\n  /**\n   * Add a listener to the registered event.\n   * @param - Name of the event to be added\n   * @param - listener function of the event to be added\n   * @example\n   * import EventEmitter from \"@scena/event-emitter\";\n   * cosnt emitter = new EventEmitter();\n   *\n   * // Add listener in \"a\" event\n   * emitter.on(\"a\", () => {\n   * });\n   * // Add listeners\n   * emitter.on({\n   *  a: () => {},\n   *  b: () => {},\n   * });\n   */\n\n\n  var __proto = EventEmitter.prototype;\n\n  __proto.on = function (eventName, listener) {\n    if ((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.isObject)(eventName)) {\n      for (var name in eventName) {\n        this.on(name, eventName[name]);\n      }\n    } else {\n      this._addEvent(eventName, listener, {});\n    }\n\n    return this;\n  };\n  /**\n   * Remove listeners registered in the event target.\n   * @param - Name of the event to be removed\n   * @param - listener function of the event to be removed\n   * @example\n   * import EventEmitter from \"@scena/event-emitter\";\n   * cosnt emitter = new EventEmitter();\n   *\n   * // Remove all listeners.\n   * emitter.off();\n   *\n   * // Remove all listeners in \"A\" event.\n   * emitter.off(\"a\");\n   *\n   *\n   * // Remove \"listener\" listener in \"a\" event.\n   * emitter.off(\"a\", listener);\n   */\n\n\n  __proto.off = function (eventName, listener) {\n    if (!eventName) {\n      this._events = {};\n    } else if ((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.isObject)(eventName)) {\n      for (var name in eventName) {\n        this.off(name);\n      }\n    } else if (!listener) {\n      this._events[eventName] = [];\n    } else {\n      var events = this._events[eventName];\n\n      if (events) {\n        var index = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.findIndex)(events, function (e) {\n          return e.listener === listener;\n        });\n\n        if (index > -1) {\n          events.splice(index, 1);\n        }\n      }\n    }\n\n    return this;\n  };\n  /**\n   * Add a disposable listener and Use promise to the registered event.\n   * @param - Name of the event to be added\n   * @param - disposable listener function of the event to be added\n   * @example\n   * import EventEmitter from \"@scena/event-emitter\";\n   * cosnt emitter = new EventEmitter();\n   *\n   * // Add a disposable listener in \"a\" event\n   * emitter.once(\"a\", () => {\n   * });\n   *\n   * // Use Promise\n   * emitter.once(\"a\").then(e => {\n   * });\n   */\n\n\n  __proto.once = function (eventName, listener) {\n    var _this = this;\n\n    if (listener) {\n      this._addEvent(eventName, listener, {\n        once: true\n      });\n    }\n\n    return new Promise(function (resolve) {\n      _this._addEvent(eventName, resolve, {\n        once: true\n      });\n    });\n  };\n  /**\n   * Fires an event to call listeners.\n   * @param - Event name\n   * @param - Event parameter\n   * @return If false, stop the event.\n   * @example\n   *\n   * import EventEmitter from \"@scena/event-emitter\";\n   *\n   *\n   * const emitter = new EventEmitter();\n   *\n   * emitter.on(\"a\", e => {\n   * });\n   *\n   *\n   * emitter.emit(\"a\", {\n   *   a: 1,\n   * });\n   */\n\n\n  __proto.emit = function (eventName, param) {\n    var _this = this;\n\n    if (param === void 0) {\n      param = {};\n    }\n\n    var events = this._events[eventName];\n\n    if (!eventName || !events) {\n      return true;\n    }\n\n    var isStop = false;\n    param.eventType = eventName;\n\n    param.stop = function () {\n      isStop = true;\n    };\n\n    param.currentTarget = this;\n\n    __spreadArrays(events).forEach(function (info) {\n      info.listener(param);\n\n      if (info.once) {\n        _this.off(eventName, info.listener);\n      }\n    });\n\n    return !isStop;\n  };\n  /**\n   * Fires an event to call listeners.\n   * @param - Event name\n   * @param - Event parameter\n   * @return If false, stop the event.\n   * @example\n   *\n   * import EventEmitter from \"@scena/event-emitter\";\n   *\n   *\n   * const emitter = new EventEmitter();\n   *\n   * emitter.on(\"a\", e => {\n   * });\n   *\n   *\n   * emitter.emit(\"a\", {\n   *   a: 1,\n   * });\n   */\n\n  /**\n  * Fires an event to call listeners.\n  * @param - Event name\n  * @param - Event parameter\n  * @return If false, stop the event.\n  * @example\n  *\n  * import EventEmitter from \"@scena/event-emitter\";\n  *\n  *\n  * const emitter = new EventEmitter();\n  *\n  * emitter.on(\"a\", e => {\n  * });\n  *\n  * // emit\n  * emitter.trigger(\"a\", {\n  *   a: 1,\n  * });\n  */\n\n\n  __proto.trigger = function (eventName, param) {\n    if (param === void 0) {\n      param = {};\n    }\n\n    return this.emit(eventName, param);\n  };\n\n  __proto._addEvent = function (eventName, listener, options) {\n    var events = this._events;\n    events[eventName] = events[eventName] || [];\n    var listeners = events[eventName];\n    listeners.push(__assign({\n      listener: listener\n    }, options));\n  };\n\n  return EventEmitter;\n}();\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EventEmitter);\n//# sourceMappingURL=event-emitter.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNjZW5hK2V2ZW50LWVtaXR0ZXJAMS4wLjUvbm9kZV9tb2R1bGVzL0BzY2VuYS9ldmVudC1lbWl0dGVyL2Rpc3QvZXZlbnQtZW1pdHRlci5lc20uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ3NEOztBQUV0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZDQUE2QyxPQUFPO0FBQ3BEOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0QsUUFBUTs7QUFFeEQsdUNBQXVDLFFBQVEsc0RBQXNELFFBQVE7O0FBRTdHO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsaUJBQWlCO0FBQ2pCLE1BQU07QUFDTjs7O0FBR0E7O0FBRUE7QUFDQSxRQUFRLHlEQUFRO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTiw0Q0FBNEM7QUFDNUM7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7O0FBR0E7QUFDQTtBQUNBO0FBQ0EsTUFBTSxTQUFTLHlEQUFRO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBLE1BQU07QUFDTjs7QUFFQTtBQUNBLG9CQUFvQiwwREFBUztBQUM3QjtBQUNBLFNBQVM7O0FBRVQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOOzs7QUFHQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047OztBQUdBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7O0FBRUw7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOzs7QUFHQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFQTtBQUNBLENBQUM7O0FBRUQsaUVBQWUsWUFBWSxFQUFDO0FBQzVCIiwic291cmNlcyI6WyIvaG9tZS9tdXppL2dpdGh1Yi9kZWVyLWZsb3cvYWktZWRpdG9yLXN0YW5kYWxvbmUvbm9kZV9tb2R1bGVzLy5wbnBtL0BzY2VuYStldmVudC1lbWl0dGVyQDEuMC41L25vZGVfbW9kdWxlcy9Ac2NlbmEvZXZlbnQtZW1pdHRlci9kaXN0L2V2ZW50LWVtaXR0ZXIuZXNtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qXG5Db3B5cmlnaHQgKGMpIDIwMTkgRGF5YnJ1c2hcbm5hbWU6IEBzY2VuYS9ldmVudC1lbWl0dGVyXG5saWNlbnNlOiBNSVRcbmF1dGhvcjogRGF5YnJ1c2hcbnJlcG9zaXRvcnk6IGdpdCtodHRwczovL2dpdGh1Yi5jb20vZGF5YnJ1c2gvZ2VzdHVyZS5naXRcbnZlcnNpb246IDEuMC41XG4qL1xuaW1wb3J0IHsgaXNPYmplY3QsIGZpbmRJbmRleCB9IGZyb20gJ0BkYXlicnVzaC91dGlscyc7XG5cbi8qISAqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKlxyXG5Db3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi5cclxuXHJcblBlcm1pc3Npb24gdG8gdXNlLCBjb3B5LCBtb2RpZnksIGFuZC9vciBkaXN0cmlidXRlIHRoaXMgc29mdHdhcmUgZm9yIGFueVxyXG5wdXJwb3NlIHdpdGggb3Igd2l0aG91dCBmZWUgaXMgaGVyZWJ5IGdyYW50ZWQuXHJcblxyXG5USEUgU09GVFdBUkUgSVMgUFJPVklERUQgXCJBUyBJU1wiIEFORCBUSEUgQVVUSE9SIERJU0NMQUlNUyBBTEwgV0FSUkFOVElFUyBXSVRIXHJcblJFR0FSRCBUTyBUSElTIFNPRlRXQVJFIElOQ0xVRElORyBBTEwgSU1QTElFRCBXQVJSQU5USUVTIE9GIE1FUkNIQU5UQUJJTElUWVxyXG5BTkQgRklUTkVTUy4gSU4gTk8gRVZFTlQgU0hBTEwgVEhFIEFVVEhPUiBCRSBMSUFCTEUgRk9SIEFOWSBTUEVDSUFMLCBESVJFQ1QsXHJcbklORElSRUNULCBPUiBDT05TRVFVRU5USUFMIERBTUFHRVMgT1IgQU5ZIERBTUFHRVMgV0hBVFNPRVZFUiBSRVNVTFRJTkcgRlJPTVxyXG5MT1NTIE9GIFVTRSwgREFUQSBPUiBQUk9GSVRTLCBXSEVUSEVSIElOIEFOIEFDVElPTiBPRiBDT05UUkFDVCwgTkVHTElHRU5DRSBPUlxyXG5PVEhFUiBUT1JUSU9VUyBBQ1RJT04sIEFSSVNJTkcgT1VUIE9GIE9SIElOIENPTk5FQ1RJT04gV0lUSCBUSEUgVVNFIE9SXHJcblBFUkZPUk1BTkNFIE9GIFRISVMgU09GVFdBUkUuXHJcbioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqICovXG52YXIgX19hc3NpZ24gPSBmdW5jdGlvbiAoKSB7XG4gIF9fYXNzaWduID0gT2JqZWN0LmFzc2lnbiB8fCBmdW5jdGlvbiBfX2Fzc2lnbih0KSB7XG4gICAgZm9yICh2YXIgcywgaSA9IDEsIG4gPSBhcmd1bWVudHMubGVuZ3RoOyBpIDwgbjsgaSsrKSB7XG4gICAgICBzID0gYXJndW1lbnRzW2ldO1xuXG4gICAgICBmb3IgKHZhciBwIGluIHMpIGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwocywgcCkpIHRbcF0gPSBzW3BdO1xuICAgIH1cblxuICAgIHJldHVybiB0O1xuICB9O1xuXG4gIHJldHVybiBfX2Fzc2lnbi5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xufTtcbmZ1bmN0aW9uIF9fc3ByZWFkQXJyYXlzKCkge1xuICBmb3IgKHZhciBzID0gMCwgaSA9IDAsIGlsID0gYXJndW1lbnRzLmxlbmd0aDsgaSA8IGlsOyBpKyspIHMgKz0gYXJndW1lbnRzW2ldLmxlbmd0aDtcblxuICBmb3IgKHZhciByID0gQXJyYXkocyksIGsgPSAwLCBpID0gMDsgaSA8IGlsOyBpKyspIGZvciAodmFyIGEgPSBhcmd1bWVudHNbaV0sIGogPSAwLCBqbCA9IGEubGVuZ3RoOyBqIDwgamw7IGorKywgaysrKSByW2tdID0gYVtqXTtcblxuICByZXR1cm4gcjtcbn1cblxuLyoqXG4gKiBJbXBsZW1lbnQgRXZlbnRFbWl0dGVyIG9uIG9iamVjdCBvciBjb21wb25lbnQuXG4gKi9cblxudmFyIEV2ZW50RW1pdHRlciA9XG4vKiNfX1BVUkVfXyovXG5mdW5jdGlvbiAoKSB7XG4gIGZ1bmN0aW9uIEV2ZW50RW1pdHRlcigpIHtcbiAgICB0aGlzLl9ldmVudHMgPSB7fTtcbiAgfVxuICAvKipcbiAgICogQWRkIGEgbGlzdGVuZXIgdG8gdGhlIHJlZ2lzdGVyZWQgZXZlbnQuXG4gICAqIEBwYXJhbSAtIE5hbWUgb2YgdGhlIGV2ZW50IHRvIGJlIGFkZGVkXG4gICAqIEBwYXJhbSAtIGxpc3RlbmVyIGZ1bmN0aW9uIG9mIHRoZSBldmVudCB0byBiZSBhZGRlZFxuICAgKiBAZXhhbXBsZVxuICAgKiBpbXBvcnQgRXZlbnRFbWl0dGVyIGZyb20gXCJAc2NlbmEvZXZlbnQtZW1pdHRlclwiO1xuICAgKiBjb3NudCBlbWl0dGVyID0gbmV3IEV2ZW50RW1pdHRlcigpO1xuICAgKlxuICAgKiAvLyBBZGQgbGlzdGVuZXIgaW4gXCJhXCIgZXZlbnRcbiAgICogZW1pdHRlci5vbihcImFcIiwgKCkgPT4ge1xuICAgKiB9KTtcbiAgICogLy8gQWRkIGxpc3RlbmVyc1xuICAgKiBlbWl0dGVyLm9uKHtcbiAgICogIGE6ICgpID0+IHt9LFxuICAgKiAgYjogKCkgPT4ge30sXG4gICAqIH0pO1xuICAgKi9cblxuXG4gIHZhciBfX3Byb3RvID0gRXZlbnRFbWl0dGVyLnByb3RvdHlwZTtcblxuICBfX3Byb3RvLm9uID0gZnVuY3Rpb24gKGV2ZW50TmFtZSwgbGlzdGVuZXIpIHtcbiAgICBpZiAoaXNPYmplY3QoZXZlbnROYW1lKSkge1xuICAgICAgZm9yICh2YXIgbmFtZSBpbiBldmVudE5hbWUpIHtcbiAgICAgICAgdGhpcy5vbihuYW1lLCBldmVudE5hbWVbbmFtZV0pO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICB0aGlzLl9hZGRFdmVudChldmVudE5hbWUsIGxpc3RlbmVyLCB7fSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIHRoaXM7XG4gIH07XG4gIC8qKlxuICAgKiBSZW1vdmUgbGlzdGVuZXJzIHJlZ2lzdGVyZWQgaW4gdGhlIGV2ZW50IHRhcmdldC5cbiAgICogQHBhcmFtIC0gTmFtZSBvZiB0aGUgZXZlbnQgdG8gYmUgcmVtb3ZlZFxuICAgKiBAcGFyYW0gLSBsaXN0ZW5lciBmdW5jdGlvbiBvZiB0aGUgZXZlbnQgdG8gYmUgcmVtb3ZlZFxuICAgKiBAZXhhbXBsZVxuICAgKiBpbXBvcnQgRXZlbnRFbWl0dGVyIGZyb20gXCJAc2NlbmEvZXZlbnQtZW1pdHRlclwiO1xuICAgKiBjb3NudCBlbWl0dGVyID0gbmV3IEV2ZW50RW1pdHRlcigpO1xuICAgKlxuICAgKiAvLyBSZW1vdmUgYWxsIGxpc3RlbmVycy5cbiAgICogZW1pdHRlci5vZmYoKTtcbiAgICpcbiAgICogLy8gUmVtb3ZlIGFsbCBsaXN0ZW5lcnMgaW4gXCJBXCIgZXZlbnQuXG4gICAqIGVtaXR0ZXIub2ZmKFwiYVwiKTtcbiAgICpcbiAgICpcbiAgICogLy8gUmVtb3ZlIFwibGlzdGVuZXJcIiBsaXN0ZW5lciBpbiBcImFcIiBldmVudC5cbiAgICogZW1pdHRlci5vZmYoXCJhXCIsIGxpc3RlbmVyKTtcbiAgICovXG5cblxuICBfX3Byb3RvLm9mZiA9IGZ1bmN0aW9uIChldmVudE5hbWUsIGxpc3RlbmVyKSB7XG4gICAgaWYgKCFldmVudE5hbWUpIHtcbiAgICAgIHRoaXMuX2V2ZW50cyA9IHt9O1xuICAgIH0gZWxzZSBpZiAoaXNPYmplY3QoZXZlbnROYW1lKSkge1xuICAgICAgZm9yICh2YXIgbmFtZSBpbiBldmVudE5hbWUpIHtcbiAgICAgICAgdGhpcy5vZmYobmFtZSk7XG4gICAgICB9XG4gICAgfSBlbHNlIGlmICghbGlzdGVuZXIpIHtcbiAgICAgIHRoaXMuX2V2ZW50c1tldmVudE5hbWVdID0gW107XG4gICAgfSBlbHNlIHtcbiAgICAgIHZhciBldmVudHMgPSB0aGlzLl9ldmVudHNbZXZlbnROYW1lXTtcblxuICAgICAgaWYgKGV2ZW50cykge1xuICAgICAgICB2YXIgaW5kZXggPSBmaW5kSW5kZXgoZXZlbnRzLCBmdW5jdGlvbiAoZSkge1xuICAgICAgICAgIHJldHVybiBlLmxpc3RlbmVyID09PSBsaXN0ZW5lcjtcbiAgICAgICAgfSk7XG5cbiAgICAgICAgaWYgKGluZGV4ID4gLTEpIHtcbiAgICAgICAgICBldmVudHMuc3BsaWNlKGluZGV4LCAxKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiB0aGlzO1xuICB9O1xuICAvKipcbiAgICogQWRkIGEgZGlzcG9zYWJsZSBsaXN0ZW5lciBhbmQgVXNlIHByb21pc2UgdG8gdGhlIHJlZ2lzdGVyZWQgZXZlbnQuXG4gICAqIEBwYXJhbSAtIE5hbWUgb2YgdGhlIGV2ZW50IHRvIGJlIGFkZGVkXG4gICAqIEBwYXJhbSAtIGRpc3Bvc2FibGUgbGlzdGVuZXIgZnVuY3Rpb24gb2YgdGhlIGV2ZW50IHRvIGJlIGFkZGVkXG4gICAqIEBleGFtcGxlXG4gICAqIGltcG9ydCBFdmVudEVtaXR0ZXIgZnJvbSBcIkBzY2VuYS9ldmVudC1lbWl0dGVyXCI7XG4gICAqIGNvc250IGVtaXR0ZXIgPSBuZXcgRXZlbnRFbWl0dGVyKCk7XG4gICAqXG4gICAqIC8vIEFkZCBhIGRpc3Bvc2FibGUgbGlzdGVuZXIgaW4gXCJhXCIgZXZlbnRcbiAgICogZW1pdHRlci5vbmNlKFwiYVwiLCAoKSA9PiB7XG4gICAqIH0pO1xuICAgKlxuICAgKiAvLyBVc2UgUHJvbWlzZVxuICAgKiBlbWl0dGVyLm9uY2UoXCJhXCIpLnRoZW4oZSA9PiB7XG4gICAqIH0pO1xuICAgKi9cblxuXG4gIF9fcHJvdG8ub25jZSA9IGZ1bmN0aW9uIChldmVudE5hbWUsIGxpc3RlbmVyKSB7XG4gICAgdmFyIF90aGlzID0gdGhpcztcblxuICAgIGlmIChsaXN0ZW5lcikge1xuICAgICAgdGhpcy5fYWRkRXZlbnQoZXZlbnROYW1lLCBsaXN0ZW5lciwge1xuICAgICAgICBvbmNlOiB0cnVlXG4gICAgICB9KTtcbiAgICB9XG5cbiAgICByZXR1cm4gbmV3IFByb21pc2UoZnVuY3Rpb24gKHJlc29sdmUpIHtcbiAgICAgIF90aGlzLl9hZGRFdmVudChldmVudE5hbWUsIHJlc29sdmUsIHtcbiAgICAgICAgb25jZTogdHJ1ZVxuICAgICAgfSk7XG4gICAgfSk7XG4gIH07XG4gIC8qKlxuICAgKiBGaXJlcyBhbiBldmVudCB0byBjYWxsIGxpc3RlbmVycy5cbiAgICogQHBhcmFtIC0gRXZlbnQgbmFtZVxuICAgKiBAcGFyYW0gLSBFdmVudCBwYXJhbWV0ZXJcbiAgICogQHJldHVybiBJZiBmYWxzZSwgc3RvcCB0aGUgZXZlbnQuXG4gICAqIEBleGFtcGxlXG4gICAqXG4gICAqIGltcG9ydCBFdmVudEVtaXR0ZXIgZnJvbSBcIkBzY2VuYS9ldmVudC1lbWl0dGVyXCI7XG4gICAqXG4gICAqXG4gICAqIGNvbnN0IGVtaXR0ZXIgPSBuZXcgRXZlbnRFbWl0dGVyKCk7XG4gICAqXG4gICAqIGVtaXR0ZXIub24oXCJhXCIsIGUgPT4ge1xuICAgKiB9KTtcbiAgICpcbiAgICpcbiAgICogZW1pdHRlci5lbWl0KFwiYVwiLCB7XG4gICAqICAgYTogMSxcbiAgICogfSk7XG4gICAqL1xuXG5cbiAgX19wcm90by5lbWl0ID0gZnVuY3Rpb24gKGV2ZW50TmFtZSwgcGFyYW0pIHtcbiAgICB2YXIgX3RoaXMgPSB0aGlzO1xuXG4gICAgaWYgKHBhcmFtID09PSB2b2lkIDApIHtcbiAgICAgIHBhcmFtID0ge307XG4gICAgfVxuXG4gICAgdmFyIGV2ZW50cyA9IHRoaXMuX2V2ZW50c1tldmVudE5hbWVdO1xuXG4gICAgaWYgKCFldmVudE5hbWUgfHwgIWV2ZW50cykge1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuXG4gICAgdmFyIGlzU3RvcCA9IGZhbHNlO1xuICAgIHBhcmFtLmV2ZW50VHlwZSA9IGV2ZW50TmFtZTtcblxuICAgIHBhcmFtLnN0b3AgPSBmdW5jdGlvbiAoKSB7XG4gICAgICBpc1N0b3AgPSB0cnVlO1xuICAgIH07XG5cbiAgICBwYXJhbS5jdXJyZW50VGFyZ2V0ID0gdGhpcztcblxuICAgIF9fc3ByZWFkQXJyYXlzKGV2ZW50cykuZm9yRWFjaChmdW5jdGlvbiAoaW5mbykge1xuICAgICAgaW5mby5saXN0ZW5lcihwYXJhbSk7XG5cbiAgICAgIGlmIChpbmZvLm9uY2UpIHtcbiAgICAgICAgX3RoaXMub2ZmKGV2ZW50TmFtZSwgaW5mby5saXN0ZW5lcik7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICByZXR1cm4gIWlzU3RvcDtcbiAgfTtcbiAgLyoqXG4gICAqIEZpcmVzIGFuIGV2ZW50IHRvIGNhbGwgbGlzdGVuZXJzLlxuICAgKiBAcGFyYW0gLSBFdmVudCBuYW1lXG4gICAqIEBwYXJhbSAtIEV2ZW50IHBhcmFtZXRlclxuICAgKiBAcmV0dXJuIElmIGZhbHNlLCBzdG9wIHRoZSBldmVudC5cbiAgICogQGV4YW1wbGVcbiAgICpcbiAgICogaW1wb3J0IEV2ZW50RW1pdHRlciBmcm9tIFwiQHNjZW5hL2V2ZW50LWVtaXR0ZXJcIjtcbiAgICpcbiAgICpcbiAgICogY29uc3QgZW1pdHRlciA9IG5ldyBFdmVudEVtaXR0ZXIoKTtcbiAgICpcbiAgICogZW1pdHRlci5vbihcImFcIiwgZSA9PiB7XG4gICAqIH0pO1xuICAgKlxuICAgKlxuICAgKiBlbWl0dGVyLmVtaXQoXCJhXCIsIHtcbiAgICogICBhOiAxLFxuICAgKiB9KTtcbiAgICovXG5cbiAgLyoqXG4gICogRmlyZXMgYW4gZXZlbnQgdG8gY2FsbCBsaXN0ZW5lcnMuXG4gICogQHBhcmFtIC0gRXZlbnQgbmFtZVxuICAqIEBwYXJhbSAtIEV2ZW50IHBhcmFtZXRlclxuICAqIEByZXR1cm4gSWYgZmFsc2UsIHN0b3AgdGhlIGV2ZW50LlxuICAqIEBleGFtcGxlXG4gICpcbiAgKiBpbXBvcnQgRXZlbnRFbWl0dGVyIGZyb20gXCJAc2NlbmEvZXZlbnQtZW1pdHRlclwiO1xuICAqXG4gICpcbiAgKiBjb25zdCBlbWl0dGVyID0gbmV3IEV2ZW50RW1pdHRlcigpO1xuICAqXG4gICogZW1pdHRlci5vbihcImFcIiwgZSA9PiB7XG4gICogfSk7XG4gICpcbiAgKiAvLyBlbWl0XG4gICogZW1pdHRlci50cmlnZ2VyKFwiYVwiLCB7XG4gICogICBhOiAxLFxuICAqIH0pO1xuICAqL1xuXG5cbiAgX19wcm90by50cmlnZ2VyID0gZnVuY3Rpb24gKGV2ZW50TmFtZSwgcGFyYW0pIHtcbiAgICBpZiAocGFyYW0gPT09IHZvaWQgMCkge1xuICAgICAgcGFyYW0gPSB7fTtcbiAgICB9XG5cbiAgICByZXR1cm4gdGhpcy5lbWl0KGV2ZW50TmFtZSwgcGFyYW0pO1xuICB9O1xuXG4gIF9fcHJvdG8uX2FkZEV2ZW50ID0gZnVuY3Rpb24gKGV2ZW50TmFtZSwgbGlzdGVuZXIsIG9wdGlvbnMpIHtcbiAgICB2YXIgZXZlbnRzID0gdGhpcy5fZXZlbnRzO1xuICAgIGV2ZW50c1tldmVudE5hbWVdID0gZXZlbnRzW2V2ZW50TmFtZV0gfHwgW107XG4gICAgdmFyIGxpc3RlbmVycyA9IGV2ZW50c1tldmVudE5hbWVdO1xuICAgIGxpc3RlbmVycy5wdXNoKF9fYXNzaWduKHtcbiAgICAgIGxpc3RlbmVyOiBsaXN0ZW5lclxuICAgIH0sIG9wdGlvbnMpKTtcbiAgfTtcblxuICByZXR1cm4gRXZlbnRFbWl0dGVyO1xufSgpO1xuXG5leHBvcnQgZGVmYXVsdCBFdmVudEVtaXR0ZXI7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1ldmVudC1lbWl0dGVyLmVzbS5qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@scena+event-emitter@1.0.5/node_modules/@scena/event-emitter/dist/event-emitter.esm.js\n");

/***/ })

};
;