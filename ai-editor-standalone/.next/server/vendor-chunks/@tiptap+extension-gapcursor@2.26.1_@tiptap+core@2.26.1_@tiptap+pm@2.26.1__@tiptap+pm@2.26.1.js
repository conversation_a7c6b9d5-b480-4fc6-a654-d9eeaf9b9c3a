"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-gapcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-gapcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-gapcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-gapcursor/dist/index.js":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-gapcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-gapcursor/dist/index.js ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Gapcursor: () => (/* binding */ Gapcursor),\n/* harmony export */   \"default\": () => (/* binding */ Gapcursor)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_gapcursor__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/gapcursor */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/gapcursor/dist/index.js\");\n\n\n\n/**\n * This extension allows you to add a gap cursor to your editor.\n * A gap cursor is a cursor that appears when you click on a place\n * where no content is present, for example inbetween nodes.\n * @see https://tiptap.dev/api/extensions/gapcursor\n */\nconst Gapcursor = _tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Extension.create({\n    name: 'gapCursor',\n    addProseMirrorPlugins() {\n        return [\n            (0,_tiptap_pm_gapcursor__WEBPACK_IMPORTED_MODULE_0__.gapCursor)(),\n        ];\n    },\n    extendNodeSchema(extension) {\n        var _a;\n        const context = {\n            name: extension.name,\n            options: extension.options,\n            storage: extension.storage,\n        };\n        return {\n            allowGapCursor: (_a = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.callOrReturn)((0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.getExtensionField)(extension, 'allowGapCursor', context))) !== null && _a !== void 0 ? _a : null,\n        };\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-gapcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-gapcursor/dist/index.js\n");

/***/ })

};
;