/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(rsc)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/experimental/ppr */ \"(rsc)/./node_modules/next/dist/server/lib/experimental/ppr.js\");\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/request/fallback-params */ \"(rsc)/./node_modules/next/dist/server/request/fallback-params.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/app-render/encryption-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption-utils.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/lib/streaming-metadata */ \"(rsc)/./node_modules/next/dist/server/lib/streaming-metadata.js\");\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/app-render/action-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/action-utils.js\");\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/server-action-request-meta */ \"(rsc)/./node_modules/next/dist/server/lib/server-action-request-meta.js\");\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/client/components/app-router-headers */ \"(rsc)/./node_modules/next/dist/client/components/app-router-headers.js\");\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/lib/fallback */ \"(rsc)/./node_modules/next/dist/lib/fallback.js\");\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(rsc)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/server/stream-utils/encoded-tags */ \"(rsc)/./node_modules/next/dist/server/stream-utils/encoded-tags.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(rsc)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\");\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(rsc)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\",\"handler\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/layout.tsx\"],\n'global-error': [module1, \"next/dist/client/components/builtin/global-error.js\"],\n'not-found': [module2, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module3, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module4, \"next/dist/client/components/builtin/unauthorized.js\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\"];\n\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    var _this;\n    let srcPage = \"/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const initialPostponed = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'postponed');\n    // TODO: replace with more specific flags\n    const minimalMode = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'minimalMode');\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, parsedUrl, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig } = prepareResult;\n    const pathname = parsedUrl.pathname || '/';\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__.normalizeAppPath)(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n    const isPrerendered = prerenderManifest.routes[resolvedPathname];\n    let isSSG = Boolean(prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]);\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.getBotType)(userAgent);\n    const isHtmlBot = (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.isHtmlBotRequest)(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isPrefetchRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_ROUTER_PREFETCH_HEADER]);\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.RSC_HEADER]);\n    const isPossibleServerAction = (0,next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__.getIsPossibleServerAction)(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = (0,next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__.checkIsAppPPREnabled)(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =  false && 0;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    let serveStreamingMetadata = !userAgent ? true : (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.shouldServeStreamingMetadata)(userAgent, nextConfig.htmlLimitedBots);\n    if (isHtmlBot && isRoutePPREnabled) {\n        isSSG = false;\n        serveStreamingMetadata = false;\n    }\n    // In development, we always want to generate dynamic HTML.\n    let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest;\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    const ComponentMod = {\n        ...next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__,\n        tree,\n        pages,\n        GlobalError: (next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default()),\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        (0,next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__.setReferenceManifestsSingleton)({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: (0,next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__.createServerModuleMap)({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextRequest(req);\n            const nextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextResponse(res);\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (true) {\n                if (nextConfig.experimental.dynamicIO && !isPrefetchRSCRequest && !context.renderOpts.isPossibleServerAction) {\n                    const warmup = await routeModule.warmup(nextReq, nextRes, context);\n                    // If the warmup is successful, we should use the resume data\n                    // cache from the warmup.\n                    if (warmup.metadata.renderResumeDataCache) {\n                        context.renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;\n                    }\n                }\n            }\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const doRender = async ({ span, postponed, fallbackRouteParams })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    dir: routeModule.projectDir,\n                    isDraftMode,\n                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'incrementalCache'),\n                    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isRevalidate: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags } = metadata;\n            if (cacheTags) {\n                headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = metadata.fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating, span })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !minimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = (0,next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.parseFallbackField)(prerenderInfo.fallback);\n            }\n            // When serving a bot request, we want to serve a blocking render and not\n            // the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.PRERENDER && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.isBot)(userAgent)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND || previousCacheEntry)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!minimalMode && fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND) {\n                    throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError();\n                }\n                let fallbackResponse;\n                if (isRoutePPREnabled && !isRSCRequest) {\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    fallbackResponse = await routeModule.handleResponse({\n                        cacheKey: isProduction ? normalizedSrcPage : null,\n                        req,\n                        nextConfig,\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams: // If we're in production or we're debugging the fallback\n                                // shell then we should postpone when dynamic params are\n                                // accessed.\n                                isProduction || isDebugFallbackShell ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(normalizedSrcPage) : null\n                            }),\n                        waitUntil: ctx.waitUntil\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.PAGES,\n                        html: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            // If this is a dynamic route with PPR enabled and the default route\n            // matches were set, then we should pass the fallback route params to\n            // the renderer as this is a fallback revalidation request.\n            const fallbackRouteParams = pageIsDynamic && isRoutePPREnabled && ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'renderFallbackShell') || isDebugFallbackShell) ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(pathname) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!minimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(matchedSegment),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options.\n            const onCacheEntry = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry({\n                    ...cacheEntry,\n                    // TODO: remove this when upstream doesn't\n                    // always expect this value to be \"PAGE\"\n                    value: {\n                        ...cacheEntry.value,\n                        kind: 'PAGE'\n                    }\n                }, {\n                    url: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'initURL')\n                });\n                if (finished) {\n                    // TODO: maybe we have to end the request?\n                    return null;\n                }\n            }\n            // If the request has a postponed state and it's a resume request we\n            // should error.\n            if (didPostpone && minimalPostponed) {\n                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E396\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!minimalMode || !isSSG) {\n                    delete headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n            if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!minimalMode && cachedData.status && next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__.RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone) {\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    if (cachedData.postponed) {\n                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E372\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        // Dynamic RSC responses cannot be cached, even if they're\n                        // configured with `force-static` because we have no way of\n                        // distinguishing between `force-static` and pages that have no\n                        // postponed state.\n                        // TODO: distinguish `force-static` from pages with no postponed state (static)\n                        cacheControl: isDynamicRSCRequest ? {\n                            revalidate: 0,\n                            expire: undefined\n                        } : cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(cachedData.rscData),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            let body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || minimalMode) {\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.chain(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__.ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.chain(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                req,\n                res,\n                type: 'html',\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__.getRevalidateReason)({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZtdXppJTJGZ2l0aHViJTJGZGVlci1mbG93JTJGYWktZWRpdG9yLXN0YW5kYWxvbmUlMkZzcmMlMkZhcHAlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQXNHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS9tdXppL2dpdGh1Yi9kZWVyLWZsb3cvYWktZWRpdG9yLXN0YW5kYWxvbmUvc3JjL2FwcC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fsrc%2Fstyles%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fsrc%2Fcomponents%2Fai-editor%2Fai-editor.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fsrc%2Fstyles%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fsrc%2Fcomponents%2Fai-editor%2Fai-editor.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n/* harmony import */ var _components_ai_editor_ai_editor_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ai-editor/ai-editor.css */ \"(rsc)/./src/components/ai-editor/ai-editor.css\");\n\n\n\nconst metadata = {\n    title: 'AI Editor Standalone',\n    description: '独立的AI编辑器组件，基于Novel + TipTap + ProseMirror技术栈'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            suppressHydrationWarning: true,\n            children: children\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUM4QjtBQUNnQjtBQUd2QyxNQUFNQSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO1FBQVFDLHdCQUF3QjtrQkFDekMsNEVBQUNDO1lBQUtELHdCQUF3QjtzQkFBRUg7Ozs7Ozs7Ozs7O0FBR3RDIiwic291cmNlcyI6WyIvaG9tZS9tdXppL2dpdGh1Yi9kZWVyLWZsb3cvYWktZWRpdG9yLXN0YW5kYWxvbmUvc3JjL2FwcC9sYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFscy5jc3MnXG5pbXBvcnQgJy4uL2NvbXBvbmVudHMvYWktZWRpdG9yL2FpLWVkaXRvci5jc3MnXG5cblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdBSSBFZGl0b3IgU3RhbmRhbG9uZScsXG4gIGRlc2NyaXB0aW9uOiAn54us56uL55qEQUnnvJbovpHlmajnu4Tku7bvvIzln7rkuo5Ob3ZlbCArIFRpcFRhcCArIFByb3NlTWlycm9y5oqA5pyv5qCIJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cInpoLUNOXCIgc3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nPlxuICAgICAgPGJvZHkgc3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nPntjaGlsZHJlbn08L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmciLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ai-editor/ai-editor.css":
/*!************************************************!*\
  !*** ./src/components/ai-editor/ai-editor.css ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ff112334497f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9haS1lZGl0b3IvYWktZWRpdG9yLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsiL2hvbWUvbXV6aS9naXRodWIvZGVlci1mbG93L2FpLWVkaXRvci1zdGFuZGFsb25lL3NyYy9jb21wb25lbnRzL2FpLWVkaXRvci9haS1lZGl0b3IuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZmYxMTIzMzQ0OTdmXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ai-editor/ai-editor.css\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"dbf761692455\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvaG9tZS9tdXppL2dpdGh1Yi9kZWVyLWZsb3cvYWktZWRpdG9yLXN0YW5kYWxvbmUvc3JjL3N0eWxlcy9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImRiZjc2MTY5MjQ1NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZtdXppJTJGZ2l0aHViJTJGZGVlci1mbG93JTJGYWktZWRpdG9yLXN0YW5kYWxvbmUlMkZzcmMlMkZhcHAlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQXNHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS9tdXppL2dpdGh1Yi9kZWVyLWZsb3cvYWktZWRpdG9yLXN0YW5kYWxvbmUvc3JjL2FwcC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fsrc%2Fstyles%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fsrc%2Fcomponents%2Fai-editor%2Fai-editor.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fsrc%2Fstyles%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fsrc%2Fcomponents%2Fai-editor%2Fai-editor.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ai_editor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ai-editor */ \"(ssr)/./src/components/ai-editor/index.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Home() {\n    const [markdown, setMarkdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"container mx-auto p-8 max-w-6xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold mb-4\",\n                        children: \"AI编辑器演示\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 dark:text-gray-400\",\n                        children: \"基于 Novel + TipTap + ProseMirror 技术栈的独立AI编辑器组件\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-[600px] border rounded-lg overflow-hidden bg-white dark:bg-gray-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_editor__WEBPACK_IMPORTED_MODULE_2__.AIEditor, {\n                    initialContent: {\n                        type: \"doc\",\n                        content: [\n                            {\n                                type: \"heading\",\n                                attrs: {\n                                    level: 1\n                                },\n                                content: [\n                                    {\n                                        type: \"text\",\n                                        text: \"🎉 欢迎使用 AI Editor\"\n                                    }\n                                ]\n                            },\n                            {\n                                type: \"paragraph\",\n                                content: [\n                                    {\n                                        type: \"text\",\n                                        text: \"这是一个功能完整的 AI 编辑器，基于 \"\n                                    },\n                                    {\n                                        type: \"text\",\n                                        text: \"Novel + TipTap + ProseMirror\",\n                                        marks: [\n                                            {\n                                                type: \"bold\"\n                                            }\n                                        ]\n                                    },\n                                    {\n                                        type: \"text\",\n                                        text: \" 技术栈构建。\"\n                                    }\n                                ]\n                            },\n                            {\n                                type: \"heading\",\n                                attrs: {\n                                    level: 2\n                                },\n                                content: [\n                                    {\n                                        type: \"text\",\n                                        text: \"🚀 快速开始\"\n                                    }\n                                ]\n                            },\n                            {\n                                type: \"bulletList\",\n                                content: [\n                                    {\n                                        type: \"listItem\",\n                                        content: [\n                                            {\n                                                type: \"paragraph\",\n                                                content: [\n                                                    {\n                                                        type: \"text\",\n                                                        text: \"选中这段文字，然后点击工具栏中的 \"\n                                                    },\n                                                    {\n                                                        type: \"text\",\n                                                        text: \"AI\",\n                                                        marks: [\n                                                            {\n                                                                type: \"bold\"\n                                                            }\n                                                        ]\n                                                    },\n                                                    {\n                                                        type: \"text\",\n                                                        text: \" 按钮体验 AI 功能\"\n                                                    }\n                                                ]\n                                            }\n                                        ]\n                                    },\n                                    {\n                                        type: \"listItem\",\n                                        content: [\n                                            {\n                                                type: \"paragraph\",\n                                                content: [\n                                                    {\n                                                        type: \"text\",\n                                                        text: \"按 \"\n                                                    },\n                                                    {\n                                                        type: \"text\",\n                                                        text: \"Ctrl+K\",\n                                                        marks: [\n                                                            {\n                                                                type: \"code\"\n                                                            }\n                                                        ]\n                                                    },\n                                                    {\n                                                        type: \"text\",\n                                                        text: \" 快捷键打开 AI 助手\"\n                                                    }\n                                                ]\n                                            }\n                                        ]\n                                    },\n                                    {\n                                        type: \"listItem\",\n                                        content: [\n                                            {\n                                                type: \"paragraph\",\n                                                content: [\n                                                    {\n                                                        type: \"text\",\n                                                        text: \"输入 \"\n                                                    },\n                                                    {\n                                                        type: \"text\",\n                                                        text: \"/\",\n                                                        marks: [\n                                                            {\n                                                                type: \"code\"\n                                                            }\n                                                        ]\n                                                    },\n                                                    {\n                                                        type: \"text\",\n                                                        text: \" 打开 Slash 命令菜单\"\n                                                    }\n                                                ]\n                                            }\n                                        ]\n                                    }\n                                ]\n                            },\n                            {\n                                type: \"heading\",\n                                attrs: {\n                                    level: 2\n                                },\n                                content: [\n                                    {\n                                        type: \"text\",\n                                        text: \"✨ 试试这些功能\"\n                                    }\n                                ]\n                            },\n                            {\n                                type: \"taskList\",\n                                content: [\n                                    {\n                                        type: \"taskItem\",\n                                        attrs: {\n                                            checked: false\n                                        },\n                                        content: [\n                                            {\n                                                type: \"paragraph\",\n                                                content: [\n                                                    {\n                                                        type: \"text\",\n                                                        text: \"拖拽图片到编辑器中\"\n                                                    }\n                                                ]\n                                            }\n                                        ]\n                                    },\n                                    {\n                                        type: \"taskItem\",\n                                        attrs: {\n                                            checked: false\n                                        },\n                                        content: [\n                                            {\n                                                type: \"paragraph\",\n                                                content: [\n                                                    {\n                                                        type: \"text\",\n                                                        text: \"使用 AI 功能改进文字\"\n                                                    }\n                                                ]\n                                            }\n                                        ]\n                                    },\n                                    {\n                                        type: \"taskItem\",\n                                        attrs: {\n                                            checked: true\n                                        },\n                                        content: [\n                                            {\n                                                type: \"paragraph\",\n                                                content: [\n                                                    {\n                                                        type: \"text\",\n                                                        text: \"体验 Slash 命令\"\n                                                    }\n                                                ]\n                                            }\n                                        ]\n                                    }\n                                ]\n                            }\n                        ]\n                    },\n                    placeholder: \"开始写作，体验AI功能...\",\n                    onMarkdownChange: setMarkdown,\n                    showToolbar: true,\n                    defaultMode: \"edit\",\n                    className: \"h-full\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-12 bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg border border-blue-200 dark:border-blue-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4 text-blue-800 dark:text-blue-200\",\n                        children: \"\\uD83D\\uDCA1 快速上手\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-600 dark:text-blue-400\",\n                                        children: \"1️⃣\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"选中文字\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"：选中任意文字，工具栏会自动出现，点击 AI 按钮体验智能功能\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-600 dark:text-blue-400\",\n                                        children: \"2️⃣\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"快捷键\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"：按 \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                className: \"bg-blue-100 dark:bg-blue-800 px-1 rounded\",\n                                                children: \"Ctrl+K\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 38\n                                            }, this),\n                                            \" 打开 AI 助手，输入 \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                className: \"bg-blue-100 dark:bg-blue-800 px-1 rounded\",\n                                                children: \"/\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 124\n                                            }, this),\n                                            \" 打开命令菜单\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-600 dark:text-blue-400\",\n                                        children: \"3️⃣\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"AI 功能\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"：改进文字、扩展内容、总结要点、修正语法等智能写作助手功能\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold\",\n                        children: \"功能特性\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 p-6 rounded-lg border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"\\uD83D\\uDCDD 富文本编辑\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: \"支持标题、列表、引用、代码块等丰富的文本格式\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 p-6 rounded-lg border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"⚡ Slash 命令\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: '输入 \"/\" 快速插入不同类型的内容块'\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 p-6 rounded-lg border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"\\uD83D\\uDDBC️ 图片支持\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: \"支持拖拽和粘贴图片上传\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 p-6 rounded-lg border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"✅ 任务列表\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: \"创建可交互的待办事项列表\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 p-6 rounded-lg border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"\\uD83D\\uDCC4 Markdown 导出\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: \"实时转换为标准 Markdown 格式\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 p-6 rounded-lg border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"\\uD83E\\uDD16 AI 智能助手\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: \"选中文字使用AI工具栏，按Ctrl+K打开AI助手\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 p-6 rounded-lg border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"\\uD83C\\uDFA8 多视图模式\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: \"支持编辑、预览和分割视图模式切换\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/app/page.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ai-editor/ai-assistant.tsx":
/*!***************************************************!*\
  !*** ./src/components/ai-editor/ai-assistant.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIAssistant: () => (/* binding */ AIAssistant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Edit3,Loader2,Plus,RefreshCw,Sparkles,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Edit3,Loader2,Plus,RefreshCw,Sparkles,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Edit3,Loader2,Plus,RefreshCw,Sparkles,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Edit3,Loader2,Plus,RefreshCw,Sparkles,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Edit3,Loader2,Plus,RefreshCw,Sparkles,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Edit3,Loader2,Plus,RefreshCw,Sparkles,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Edit3,Loader2,Plus,RefreshCw,Sparkles,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Edit3,Loader2,Plus,RefreshCw,Sparkles,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Edit3,Loader2,Plus,RefreshCw,Sparkles,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* __next_internal_client_entry_do_not_use__ AIAssistant auto */ \n\n\nconst AI_PROMPTS = [\n    {\n        label: \"改进文字\",\n        prompt: \"请帮我改进这段文字，让它更清晰、更有说服力\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n            lineNumber: 27,\n            columnNumber: 59\n        }, undefined)\n    },\n    {\n        label: \"扩展内容\",\n        prompt: \"请帮我扩展这段内容，添加更多细节和例子\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n            lineNumber: 28,\n            columnNumber: 57\n        }, undefined)\n    },\n    {\n        label: \"总结要点\",\n        prompt: \"请帮我总结这段文字的核心要点\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n            lineNumber: 29,\n            columnNumber: 52\n        }, undefined)\n    },\n    {\n        label: \"修正语法\",\n        prompt: \"请帮我检查并修正这段文字的语法和表达\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n            lineNumber: 30,\n            columnNumber: 56\n        }, undefined)\n    }\n];\nfunction AIAssistant({ selectedText, suggestion, isLoading, onGenerate, onInsert, onReplace, onClose }) {\n    const [customPrompt, setCustomPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showCustomInput, setShowCustomInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleQuickPrompt = async (prompt)=>{\n        if (selectedText) {\n            await onGenerate(`${prompt}：${selectedText}`);\n        }\n    };\n    const handleCustomPrompt = async ()=>{\n        if (customPrompt.trim()) {\n            const fullPrompt = selectedText ? `${customPrompt}：${selectedText}` : customPrompt;\n            await onGenerate(fullPrompt);\n            setCustomPrompt(\"\");\n            setShowCustomInput(false);\n        }\n    };\n    const copyToClipboard = async (text)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n        } catch (error) {\n            console.error(\"复制失败:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/20 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-2xl max-h-[80vh] overflow-hidden bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-row items-center justify-between space-y-0 p-6 pb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"flex items-center space-x-2 text-lg font-semibold\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"AI 写作助手\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                    children: selectedText ? \"为选中的文字提供 AI 建议\" : \"使用 AI 生成内容\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 pb-6 space-y-4\",\n                    children: [\n                        selectedText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded\",\n                                            children: \"选中的文字\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>copyToClipboard(selectedText),\n                                            className: \"p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                    children: selectedText\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium\",\n                                    children: \"快速操作\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-2\",\n                                    children: AI_PROMPTS.map((prompt, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleQuickPrompt(prompt.prompt),\n                                            disabled: isLoading || !selectedText,\n                                            className: \"flex items-center justify-start p-2 text-sm border border-gray-200 dark:border-gray-700 rounded hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: [\n                                                prompt.icon,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2\",\n                                                    children: prompt.label\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"自定义指令\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCustomInput(!showCustomInput),\n                                            className: \"text-sm text-blue-600 dark:text-blue-400 hover:underline\",\n                                            children: showCustomInput ? \"收起\" : \"展开\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                showCustomInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            placeholder: \"输入你的自定义指令...\",\n                                            value: customPrompt,\n                                            onChange: (e)=>setCustomPrompt(e.target.value),\n                                            className: \"w-full min-h-[80px] p-2 border border-gray-200 dark:border-gray-700 rounded resize-none bg-white dark:bg-gray-800 text-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCustomPrompt,\n                                            disabled: isLoading || !customPrompt.trim(),\n                                            className: \"w-full flex items-center justify-center p-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: [\n                                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 animate-spin mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"生成内容\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this),\n                        (suggestion || isLoading) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1 text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"AI 建议\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, this),\n                                        suggestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>copyToClipboard(suggestion),\n                                            className: \"p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-blue-600 dark:text-blue-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: \"AI 正在思考中...\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-800 dark:text-blue-200\",\n                                        children: suggestion\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this),\n                                suggestion && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>selectedText ? onReplace(suggestion) : onInsert(suggestion),\n                                            className: \"flex-1 flex items-center justify-center p-2 bg-blue-600 text-white rounded hover:bg-blue-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 21\n                                                }, this),\n                                                selectedText ? \"替换文字\" : \"插入内容\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 19\n                                        }, this),\n                                        !selectedText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>onInsert(suggestion),\n                                            className: \"flex-1 flex items-center justify-center p-2 border border-gray-200 dark:border-gray-700 rounded hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"插入内容\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleQuickPrompt(\"请重新生成一个不同的版本\"),\n                                            className: \"p-2 border border-gray-200 dark:border-gray-700 rounded hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit3_Loader2_Plus_RefreshCw_Sparkles_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-assistant.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ai-editor/ai-assistant.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ai-editor/ai-editor.css":
/*!************************************************!*\
  !*** ./src/components/ai-editor/ai-editor.css ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ff112334497f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9haS1lZGl0b3IvYWktZWRpdG9yLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsiL2hvbWUvbXV6aS9naXRodWIvZGVlci1mbG93L2FpLWVkaXRvci1zdGFuZGFsb25lL3NyYy9jb21wb25lbnRzL2FpLWVkaXRvci9haS1lZGl0b3IuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZmYxMTIzMzQ0OTdmXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ai-editor/ai-editor.css\n");

/***/ }),

/***/ "(ssr)/./src/components/ai-editor/ai-toolbar.tsx":
/*!*************************************************!*\
  !*** ./src/components/ai-editor/ai-toolbar.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIToolbar: () => (/* binding */ AIToolbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Heading1,Heading2,Italic,Loader2,Sparkles,Strikethrough,Underline!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bold.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Heading1,Heading2,Italic,Loader2,Sparkles,Strikethrough,Underline!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/italic.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Heading1,Heading2,Italic,Loader2,Sparkles,Strikethrough,Underline!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/underline.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Heading1,Heading2,Italic,Loader2,Sparkles,Strikethrough,Underline!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/strikethrough.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Heading1,Heading2,Italic,Loader2,Sparkles,Strikethrough,Underline!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heading-1.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Heading1,Heading2,Italic,Loader2,Sparkles,Strikethrough,Underline!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heading-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Heading1,Heading2,Italic,Loader2,Sparkles,Strikethrough,Underline!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Heading1,Heading2,Italic,Loader2,Sparkles,Strikethrough,Underline!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! novel */ \"(ssr)/./node_modules/novel/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ AIToolbar auto */ \n\n\n\nfunction AIToolbar({ editor, onAIClick, isLoading }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIToolbar.useEffect\": ()=>{\n            if (!editor) return;\n            const updateVisibility = {\n                \"AIToolbar.useEffect.updateVisibility\": ()=>{\n                    const { selection } = editor.state;\n                    const hasSelection = !selection.empty;\n                    setIsVisible(hasSelection);\n                }\n            }[\"AIToolbar.useEffect.updateVisibility\"];\n            // 监听选择变化\n            editor.on(\"selectionUpdate\", updateVisibility);\n            editor.on(\"transaction\", updateVisibility);\n            return ({\n                \"AIToolbar.useEffect\": ()=>{\n                    editor.off(\"selectionUpdate\", updateVisibility);\n                    editor.off(\"transaction\", updateVisibility);\n                }\n            })[\"AIToolbar.useEffect\"];\n        }\n    }[\"AIToolbar.useEffect\"], [\n        editor\n    ]);\n    if (!editor || !isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_2__.EditorBubble, {\n        tippyOptions: {\n            placement: \"top\",\n            duration: 100\n        },\n        className: \"ai-toolbar-bubble\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_2__.EditorBubbleItem, {\n                    onSelect: (editor)=>{\n                        editor.chain().focus().toggleBold().run();\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: `px-2 py-1 text-sm rounded-none hover:bg-gray-100 dark:hover:bg-gray-700 ${editor.isActive(\"bold\") ? \"bg-gray-100 dark:bg-gray-700\" : \"\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_2__.EditorBubbleItem, {\n                    onSelect: (editor)=>{\n                        editor.chain().focus().toggleItalic().run();\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: `px-2 py-1 text-sm rounded-none hover:bg-gray-100 dark:hover:bg-gray-700 ${editor.isActive(\"italic\") ? \"bg-gray-100 dark:bg-gray-700\" : \"\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_2__.EditorBubbleItem, {\n                    onSelect: (editor)=>{\n                        editor.chain().focus().toggleUnderline().run();\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: `px-2 py-1 text-sm rounded-none hover:bg-gray-100 dark:hover:bg-gray-700 ${editor.isActive(\"underline\") ? \"bg-gray-100 dark:bg-gray-700\" : \"\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_2__.EditorBubbleItem, {\n                    onSelect: (editor)=>{\n                        editor.chain().focus().toggleStrike().run();\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: `px-2 py-1 text-sm rounded-none hover:bg-gray-100 dark:hover:bg-gray-700 ${editor.isActive(\"strike\") ? \"bg-gray-100 dark:bg-gray-700\" : \"\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-px h-6 bg-gray-200 dark:bg-gray-700 mx-1\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_2__.EditorBubbleItem, {\n                    onSelect: (editor)=>{\n                        editor.chain().focus().toggleHeading({\n                            level: 1\n                        }).run();\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: `px-2 py-1 text-sm rounded-none hover:bg-gray-100 dark:hover:bg-gray-700 ${editor.isActive(\"heading\", {\n                            level: 1\n                        }) ? \"bg-gray-100 dark:bg-gray-700\" : \"\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_2__.EditorBubbleItem, {\n                    onSelect: (editor)=>{\n                        editor.chain().focus().toggleHeading({\n                            level: 2\n                        }).run();\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: `px-2 py-1 text-sm rounded-none hover:bg-gray-100 dark:hover:bg-gray-700 ${editor.isActive(\"heading\", {\n                            level: 2\n                        }) ? \"bg-gray-100 dark:bg-gray-700\" : \"\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-px h-6 bg-gray-200 dark:bg-gray-700 mx-1\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onAIClick,\n                    disabled: isLoading,\n                    className: \"px-2 py-1 text-sm rounded-none text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/20 disabled:opacity-50\",\n                    children: [\n                        isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-4 w-4 animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Heading1_Heading2_Italic_Loader2_Sparkles_Strikethrough_Underline_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-1 text-xs\",\n                            children: \"AI\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/ai-toolbar.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ai-editor/ai-toolbar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ai-editor/extensions.ts":
/*!************************************************!*\
  !*** ./src/components/ai-editor/extensions.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EXTENSION_NAMES: () => (/* binding */ EXTENSION_NAMES),\n/* harmony export */   aiEditorExtensions: () => (/* binding */ aiEditorExtensions),\n/* harmony export */   uploadFn: () => (/* reexport safe */ _image_upload__WEBPACK_IMPORTED_MODULE_13__.uploadFn)\n/* harmony export */ });\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! novel */ \"(ssr)/./node_modules/@tiptap/starter-kit/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! novel */ \"(ssr)/./node_modules/novel/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! novel */ \"(ssr)/./node_modules/@tiptap/extension-link/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! novel */ \"(ssr)/./node_modules/@tiptap/extension-task-list/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! novel */ \"(ssr)/./node_modules/@tiptap/extension-task-item/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! novel */ \"(ssr)/./node_modules/@tiptap/extension-image/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! novel */ \"(ssr)/./node_modules/tiptap-extension-global-drag-handle/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! novel */ \"(ssr)/./node_modules/@tiptap/extension-underline/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! novel */ \"(ssr)/./node_modules/@tiptap/extension-text-style/dist/index.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! novel */ \"(ssr)/./node_modules/@tiptap/extension-color/dist/index.js\");\n/* harmony import */ var tiptap_markdown__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! tiptap-markdown */ \"(ssr)/./node_modules/tiptap-markdown/dist/tiptap-markdown.es.js\");\n/* harmony import */ var _tiptap_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/react */ \"(ssr)/./node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _slash_command__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./slash-command */ \"(ssr)/./src/components/ai-editor/slash-command.tsx\");\n/* harmony import */ var _image_upload__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./image-upload */ \"(ssr)/./src/components/ai-editor/image-upload.ts\");\n\n\n\n\n// 自定义 AI 命令扩展\nconst AICommandExtension = _tiptap_react__WEBPACK_IMPORTED_MODULE_1__.Extension.create({\n    name: \"aiCommand\",\n    addKeyboardShortcuts () {\n        return {\n            // Ctrl/Cmd + K 触发 AI 助手\n            \"Mod-k\": ()=>{\n                // 触发 AI 助手的逻辑将在组件中处理\n                const event = new CustomEvent(\"ai-assistant-trigger\");\n                document.dispatchEvent(event);\n                return true;\n            },\n            // Ctrl/Cmd + Shift + A 快速 AI 生成\n            \"Mod-Shift-a\": ()=>{\n                const event = new CustomEvent(\"ai-quick-generate\");\n                document.dispatchEvent(event);\n                return true;\n            }\n        };\n    },\n    addCommands () {\n        return {\n            // 添加 AI 高亮命令\n            setAIHighlight: ()=>({ commands })=>{\n                    return commands.setHighlight({\n                        color: \"#3b82f6\"\n                    });\n                },\n            // 移除 AI 高亮命令\n            unsetAIHighlight: ()=>({ commands })=>{\n                    return commands.unsetHighlight();\n                }\n        };\n    }\n});\n// 基础扩展配置\nconst starterKit = novel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].configure({\n    bulletList: {\n        HTMLAttributes: {\n            class: \"ai-editor-bullet-list\"\n        }\n    },\n    orderedList: {\n        HTMLAttributes: {\n            class: \"ai-editor-ordered-list\"\n        }\n    },\n    blockquote: {\n        HTMLAttributes: {\n            class: \"ai-editor-blockquote\"\n        }\n    },\n    code: {\n        HTMLAttributes: {\n            class: \"ai-editor-code\"\n        }\n    },\n    heading: {\n        HTMLAttributes: {\n            class: \"ai-editor-heading\"\n        }\n    }\n});\n// 占位符配置\nconst placeholder = novel__WEBPACK_IMPORTED_MODULE_3__.Placeholder.configure({\n    placeholder: ({ node })=>{\n        if (node.type.name === \"heading\") {\n            return \"标题\";\n        }\n        return \"开始写作...\";\n    }\n});\n// 链接配置\nconst link = novel__WEBPACK_IMPORTED_MODULE_4__[\"default\"].configure({\n    HTMLAttributes: {\n        class: \"ai-editor-link\"\n    },\n    openOnClick: false\n});\n// 高亮配置\nconst highlight = novel__WEBPACK_IMPORTED_MODULE_3__.HighlightExtension.configure({\n    multicolor: true\n});\n// 任务列表配置\nconst taskList = novel__WEBPACK_IMPORTED_MODULE_5__.TaskList.configure({\n    HTMLAttributes: {\n        class: \"ai-editor-task-list\"\n    }\n});\nconst taskItem = novel__WEBPACK_IMPORTED_MODULE_6__.TaskItem.configure({\n    HTMLAttributes: {\n        class: \"ai-editor-task-item\"\n    },\n    nested: true\n});\n// 图片配置\nconst tiptapImage = novel__WEBPACK_IMPORTED_MODULE_7__[\"default\"].extend({\n    addProseMirrorPlugins () {\n        return [\n            (0,novel__WEBPACK_IMPORTED_MODULE_3__.UploadImagesPlugin)({\n                imageClass: \"ai-editor-image-uploading opacity-40 rounded-lg border border-gray-200\"\n            })\n        ];\n    }\n}).configure({\n    allowBase64: true,\n    HTMLAttributes: {\n        class: \"ai-editor-image rounded-lg border border-gray-200 max-w-full h-auto\"\n    }\n});\n// 拖拽手柄配置\nconst globalDragHandle = novel__WEBPACK_IMPORTED_MODULE_8__[\"default\"].configure({\n    dragHandleWidth: 20\n});\n// 配置 Markdown 支持\nconst markdown = tiptap_markdown__WEBPACK_IMPORTED_MODULE_9__.Markdown.configure({\n    html: true,\n    tightLists: true,\n    bulletListMarker: \"-\",\n    linkify: false,\n    breaks: false\n});\n// 导出所有扩展\nconst aiEditorExtensions = [\n    starterKit,\n    placeholder,\n    link,\n    novel__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    highlight,\n    novel__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    novel__WEBPACK_IMPORTED_MODULE_12__.Color,\n    markdown,\n    AICommandExtension,\n    _slash_command__WEBPACK_IMPORTED_MODULE_0__.slashCommand,\n    taskList,\n    taskItem,\n    tiptapImage,\n    globalDragHandle\n];\n// 导出扩展名称，方便在组件中使用\nconst EXTENSION_NAMES = {\n    AI_COMMAND: \"aiCommand\",\n    SLASH_COMMAND: \"slashCommand\",\n    HIGHLIGHT: \"highlight\",\n    MARKDOWN: \"markdown\",\n    TASK_LIST: \"taskList\",\n    TASK_ITEM: \"taskItem\",\n    IMAGE: \"image\",\n    DRAG_HANDLE: \"globalDragHandle\"\n};\n// 导出图片上传函数\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ai-editor/extensions.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/ai-editor/image-upload.ts":
/*!**************************************************!*\
  !*** ./src/components/ai-editor/image-upload.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   uploadFn: () => (/* binding */ uploadFn)\n/* harmony export */ });\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! novel */ \"(ssr)/./node_modules/novel/dist/index.js\");\n\n// 模拟图片上传函数\nconst onUpload = (file)=>{\n    return new Promise((resolve, reject)=>{\n        try {\n            // 创建本地 URL 用于预览\n            const url = URL.createObjectURL(file);\n            // 模拟上传延迟\n            setTimeout(()=>{\n                resolve(url);\n            }, 1000);\n        } catch (error) {\n            reject(new Error(\"图片上传失败，请重试\"));\n        }\n    });\n};\n// 创建图片上传配置\nconst uploadFn = (0,novel__WEBPACK_IMPORTED_MODULE_0__.createImageUpload)({\n    onUpload,\n    validateFn: (file)=>{\n        // 检查文件是否存在\n        if (!file) {\n            console.error(\"未选择文件\");\n            return false;\n        }\n        // 验证文件类型\n        if (!file.type || !file.type.includes(\"image/\")) {\n            console.error(\"文件类型不支持，请选择图片文件\");\n            return false;\n        }\n        // 验证文件大小 (最大 20MB)\n        if (file.size / 1024 / 1024 > 20) {\n            console.error(\"文件大小超过限制，最大支持 20MB\");\n            return false;\n        }\n        return true;\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ai-editor/image-upload.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/ai-editor/index.tsx":
/*!********************************************!*\
  !*** ./src/components/ai-editor/index.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIEditor: () => (/* binding */ AIEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! novel */ \"(ssr)/./node_modules/novel/dist/index.js\");\n/* harmony import */ var use_debounce__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! use-debounce */ \"(ssr)/./node_modules/use-debounce/dist/index.module.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _extensions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./extensions */ \"(ssr)/./src/components/ai-editor/extensions.ts\");\n/* harmony import */ var _ai_toolbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ai-toolbar */ \"(ssr)/./src/components/ai-editor/ai-toolbar.tsx\");\n/* harmony import */ var _ai_assistant__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ai-assistant */ \"(ssr)/./src/components/ai-editor/ai-assistant.tsx\");\n/* harmony import */ var _slash_command__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./slash-command */ \"(ssr)/./src/components/ai-editor/slash-command.tsx\");\n/* harmony import */ var _ai_editor_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ai-editor.css */ \"(ssr)/./src/components/ai-editor/ai-editor.css\");\n/* __next_internal_client_entry_do_not_use__ AIEditor auto */ \n\n\n\n\n\n// 导入我们的扩展和组件\n\n\n\n\n\n// 内部编辑器组件，不使用SSR\nfunction AIEditorInternal({ initialContent, placeholder = \"开始写作...\", className = \"\", onContentChange, onMarkdownChange, showToolbar = true, defaultMode = \"edit\" }) {\n    // 编辑器状态\n    const [editor, setEditor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAIOpen, setIsAIOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedText, setSelectedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [aiSuggestion, setAISuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mode, setMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultMode);\n    const [markdown, setMarkdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 防抖更新函数\n    const debouncedUpdate = (0,use_debounce__WEBPACK_IMPORTED_MODULE_8__.useDebouncedCallback)({\n        \"AIEditorInternal.useDebouncedCallback[debouncedUpdate]\": (editor)=>{\n            const json = editor.getJSON();\n            onContentChange?.(json);\n            const markdownContent = editor.storage.markdown.getMarkdown();\n            setMarkdown(markdownContent);\n            if (onMarkdownChange) {\n                onMarkdownChange(markdownContent);\n            }\n        }\n    }[\"AIEditorInternal.useDebouncedCallback[debouncedUpdate]\"], 300);\n    // AI 功能处理函数\n    const handleAIClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[handleAIClick]\": ()=>{\n            if (!editor) return;\n            const { selection } = editor.state;\n            const selectedText = editor.state.doc.textBetween(selection.from, selection.to);\n            setSelectedText(selectedText);\n            setIsAIOpen(true);\n        }\n    }[\"AIEditorInternal.useCallback[handleAIClick]\"], [\n        editor\n    ]);\n    // 模拟 AI 生成文本\n    const generateAIText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[generateAIText]\": async (prompt)=>{\n            setIsLoading(true);\n            setAISuggestion(\"\");\n            try {\n                // 模拟 API 调用延迟\n                await new Promise({\n                    \"AIEditorInternal.useCallback[generateAIText]\": (resolve)=>setTimeout(resolve, 1500)\n                }[\"AIEditorInternal.useCallback[generateAIText]\"]);\n                // 根据提示词生成不同的响应\n                let response = \"\";\n                if (prompt.includes(\"改进\")) {\n                    response = \"这是一个改进后的文本版本，更加清晰和有说服力。\";\n                } else if (prompt.includes(\"扩展\")) {\n                    response = \"经过优化的内容，增加了更多细节和具体例子。\";\n                } else if (prompt.includes(\"总结\")) {\n                    response = \"重新组织的段落结构，逻辑更加清晰。\";\n                } else if (prompt.includes(\"修正\")) {\n                    response = \"修正了语法问题，表达更加准确。\";\n                } else {\n                    // 默认响应\n                    const responses = [\n                        \"这是一个改进后的文本版本，更加清晰和有说服力。\",\n                        \"经过优化的内容，增加了更多细节和具体例子。\",\n                        \"重新组织的段落结构，逻辑更加清晰。\",\n                        \"修正了语法问题，表达更加准确。\"\n                    ];\n                    response = responses[Math.floor(Math.random() * responses.length)];\n                }\n                setAISuggestion(response);\n            } catch (error) {\n                console.error(\"AI 生成失败:\", error);\n                setAISuggestion(\"抱歉，AI 生成失败，请稍后重试。\");\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"AIEditorInternal.useCallback[generateAIText]\"], []);\n    // 插入 AI 生成的文本\n    const insertAIText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[insertAIText]\": (text)=>{\n            if (!editor) return;\n            editor.chain().focus().insertContent(text).run();\n            setIsAIOpen(false);\n            setAISuggestion(\"\");\n        }\n    }[\"AIEditorInternal.useCallback[insertAIText]\"], [\n        editor\n    ]);\n    // 替换选中的文本\n    const replaceSelectedText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[replaceSelectedText]\": (text)=>{\n            if (!editor) return;\n            const { selection } = editor.state;\n            editor.chain().focus().deleteRange({\n                from: selection.from,\n                to: selection.to\n            }).insertContent(text).run();\n            setIsAIOpen(false);\n            setAISuggestion(\"\");\n        }\n    }[\"AIEditorInternal.useCallback[replaceSelectedText]\"], [\n        editor\n    ]);\n    // 导出Markdown文件\n    const exportMarkdown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AIEditorInternal.useCallback[exportMarkdown]\": ()=>{\n            if (!markdown) return;\n            const blob = new Blob([\n                markdown\n            ], {\n                type: 'text/markdown'\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = `document-${new Date().toISOString().split('T')[0]}.md`;\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    }[\"AIEditorInternal.useCallback[exportMarkdown]\"], [\n        markdown\n    ]);\n    // 监听键盘快捷键\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIEditorInternal.useEffect\": ()=>{\n            const handleKeyboardShortcuts = {\n                \"AIEditorInternal.useEffect.handleKeyboardShortcuts\": (event)=>{\n                    if (event.type === \"ai-assistant-trigger\") {\n                        handleAIClick();\n                    }\n                }\n            }[\"AIEditorInternal.useEffect.handleKeyboardShortcuts\"];\n            document.addEventListener(\"ai-assistant-trigger\", handleKeyboardShortcuts);\n            document.addEventListener(\"ai-quick-generate\", handleKeyboardShortcuts);\n            return ({\n                \"AIEditorInternal.useEffect\": ()=>{\n                    document.removeEventListener(\"ai-assistant-trigger\", handleKeyboardShortcuts);\n                    document.removeEventListener(\"ai-quick-generate\", handleKeyboardShortcuts);\n                }\n            })[\"AIEditorInternal.useEffect\"];\n        }\n    }[\"AIEditorInternal.useEffect\"], [\n        handleAIClick\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `ai-editor-container h-full flex flex-col ${className}`,\n        children: [\n            showToolbar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b bg-gray-50 dark:bg-gray-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                            children: \"AI 编辑器\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: mode === \"edit\" ? \"secondary\" : \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setMode(\"edit\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" 编辑\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: mode === \"preview\" ? \"secondary\" : \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setMode(\"preview\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Markdown输出\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: exportMarkdown,\n                                disabled: !markdown,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" 导出\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 176,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: [\n                    mode === \"edit\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_12__.EditorRoot, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_12__.EditorContent, {\n                                immediatelyRender: false,\n                                initialContent: initialContent,\n                                extensions: _extensions__WEBPACK_IMPORTED_MODULE_3__.aiEditorExtensions,\n                                className: \"h-full w-full\",\n                                editorProps: {\n                                    handleDOMEvents: {\n                                        keydown: (_view, event)=>(0,novel__WEBPACK_IMPORTED_MODULE_12__.handleCommandNavigation)(event)\n                                    },\n                                    handlePaste: (view, event)=>(0,novel__WEBPACK_IMPORTED_MODULE_12__.handleImagePaste)(view, event, _extensions__WEBPACK_IMPORTED_MODULE_3__.uploadFn),\n                                    handleDrop: (view, event, _slice, moved)=>(0,novel__WEBPACK_IMPORTED_MODULE_12__.handleImageDrop)(view, event, moved, _extensions__WEBPACK_IMPORTED_MODULE_3__.uploadFn),\n                                    attributes: {\n                                        class: \"prose prose-base dark:prose-invert max-w-none p-4 focus:outline-none h-full overflow-auto scroll-smooth\",\n                                        \"data-placeholder\": placeholder,\n                                        style: \"min-height: 100%; overflow-y: auto; overflow-x: hidden;\"\n                                    }\n                                },\n                                onCreate: ({ editor })=>{\n                                    setEditor(editor);\n                                },\n                                onUpdate: ({ editor })=>{\n                                    debouncedUpdate(editor);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_12__.EditorCommand, {\n                                        className: \"z-50 h-auto max-h-[330px] overflow-y-auto rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-1 py-2 shadow-md transition-all\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_12__.EditorCommandEmpty, {\n                                                className: \"px-2 text-gray-500 dark:text-gray-400\",\n                                                children: \"没有找到结果\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_12__.EditorCommandList, {\n                                                children: _slash_command__WEBPACK_IMPORTED_MODULE_6__.suggestionItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_12__.EditorCommandItem, {\n                                                        value: item.title,\n                                                        onCommand: (val)=>item.command?.(val),\n                                                        className: \"flex w-full items-center space-x-2 rounded-md px-2 py-1 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 aria-selected:bg-gray-100 dark:aria-selected:bg-gray-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex h-10 w-10 items-center justify-center rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\",\n                                                                children: item.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: item.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: item.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, item.title, true, {\n                                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ai_toolbar__WEBPACK_IMPORTED_MODULE_4__.AIToolbar, {\n                                        editor: editor,\n                                        onAIClick: handleAIClick,\n                                        isLoading: isLoading\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(novel__WEBPACK_IMPORTED_MODULE_12__.ImageResizer, {}, void 0, false, {\n                                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this),\n                    mode === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full p-4 overflow-auto bg-gray-50 dark:bg-gray-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 p-4 h-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4 text-gray-900 dark:text-white\",\n                                    children: \"Markdown 输出\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"text-sm whitespace-pre-wrap font-mono text-gray-800 dark:text-gray-200 overflow-auto h-full\",\n                                    children: markdown || '开始编辑以查看Markdown输出...'\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this),\n            isAIOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ai_assistant__WEBPACK_IMPORTED_MODULE_5__.AIAssistant, {\n                selectedText: selectedText,\n                suggestion: aiSuggestion,\n                isLoading: isLoading,\n                onGenerate: generateAIText,\n                onInsert: insertAIText,\n                onReplace: replaceSelectedText,\n                onClose: ()=>setIsAIOpen(false)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 296,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, this);\n}\n// 使用动态导入禁用SSR的主要导出组件\nfunction AIEditor(props) {\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIEditor.useEffect\": ()=>{\n            setIsMounted(true);\n        }\n    }[\"AIEditor.useEffect\"], []);\n    if (!isMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `ai-editor-container ${props.className || \"\"}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full min-h-[400px] flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500 dark:text-gray-400\",\n                    children: \"加载编辑器中...\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n                lineNumber: 321,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n            lineNumber: 320,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIEditorInternal, {\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/index.tsx\",\n        lineNumber: 328,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ai-editor/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ai-editor/slash-command.tsx":
/*!****************************************************!*\
  !*** ./src/components/ai-editor/slash-command.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   slashCommand: () => (/* binding */ slashCommand),\n/* harmony export */   suggestionItems: () => (/* binding */ suggestionItems)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Code,Heading1,Heading2,Heading3,List,ListOrdered,Quote,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Code,Heading1,Heading2,Heading3,List,ListOrdered,Quote,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heading-1.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Code,Heading1,Heading2,Heading3,List,ListOrdered,Quote,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heading-2.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Code,Heading1,Heading2,Heading3,List,ListOrdered,Quote,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heading-3.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Code,Heading1,Heading2,Heading3,List,ListOrdered,Quote,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Code,Heading1,Heading2,Heading3,List,ListOrdered,Quote,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/list-ordered.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Code,Heading1,Heading2,Heading3,List,ListOrdered,Quote,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Code,Heading1,Heading2,Heading3,List,ListOrdered,Quote,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckSquare,Code,Heading1,Heading2,Heading3,List,ListOrdered,Quote,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var novel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! novel */ \"(ssr)/./node_modules/novel/dist/index.js\");\n\n\n\n\nconst suggestionItems = (0,novel__WEBPACK_IMPORTED_MODULE_2__.createSuggestionItems)([\n    {\n        title: \"文本\",\n        description: \"开始写作普通文本\",\n        searchTerms: [\n            \"p\",\n            \"paragraph\",\n            \"text\",\n            \"文本\"\n        ],\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            size: 18\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/slash-command.tsx\",\n            lineNumber: 20,\n            columnNumber: 11\n        }, undefined),\n        command: ({ editor, range })=>{\n            editor.chain().focus().deleteRange(range).toggleNode(\"paragraph\", \"paragraph\").run();\n        }\n    },\n    {\n        title: \"一级标题\",\n        description: \"大标题\",\n        searchTerms: [\n            \"title\",\n            \"big\",\n            \"large\",\n            \"h1\",\n            \"标题\",\n            \"大标题\"\n        ],\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            size: 18\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/slash-command.tsx\",\n            lineNumber: 34,\n            columnNumber: 11\n        }, undefined),\n        command: ({ editor, range })=>{\n            editor.chain().focus().deleteRange(range).setNode(\"heading\", {\n                level: 1\n            }).run();\n        }\n    },\n    {\n        title: \"二级标题\",\n        description: \"中等标题\",\n        searchTerms: [\n            \"subtitle\",\n            \"medium\",\n            \"h2\",\n            \"副标题\",\n            \"中标题\"\n        ],\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            size: 18\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/slash-command.tsx\",\n            lineNumber: 48,\n            columnNumber: 11\n        }, undefined),\n        command: ({ editor, range })=>{\n            editor.chain().focus().deleteRange(range).setNode(\"heading\", {\n                level: 2\n            }).run();\n        }\n    },\n    {\n        title: \"三级标题\",\n        description: \"小标题\",\n        searchTerms: [\n            \"small\",\n            \"h3\",\n            \"小标题\"\n        ],\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            size: 18\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/slash-command.tsx\",\n            lineNumber: 62,\n            columnNumber: 11\n        }, undefined),\n        command: ({ editor, range })=>{\n            editor.chain().focus().deleteRange(range).setNode(\"heading\", {\n                level: 3\n            }).run();\n        }\n    },\n    {\n        title: \"无序列表\",\n        description: \"创建一个简单的无序列表\",\n        searchTerms: [\n            \"unordered\",\n            \"point\",\n            \"list\",\n            \"无序\",\n            \"列表\",\n            \"项目\"\n        ],\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            size: 18\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/slash-command.tsx\",\n            lineNumber: 76,\n            columnNumber: 11\n        }, undefined),\n        command: ({ editor, range })=>{\n            editor.chain().focus().deleteRange(range).toggleBulletList().run();\n        }\n    },\n    {\n        title: \"有序列表\",\n        description: \"创建一个带数字的有序列表\",\n        searchTerms: [\n            \"ordered\",\n            \"numbered\",\n            \"list\",\n            \"有序\",\n            \"数字\",\n            \"编号\"\n        ],\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            size: 18\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/slash-command.tsx\",\n            lineNumber: 85,\n            columnNumber: 11\n        }, undefined),\n        command: ({ editor, range })=>{\n            editor.chain().focus().deleteRange(range).toggleOrderedList().run();\n        }\n    },\n    {\n        title: \"引用\",\n        description: \"引用一段文字\",\n        searchTerms: [\n            \"blockquote\",\n            \"quote\",\n            \"引用\",\n            \"引述\"\n        ],\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            size: 18\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/slash-command.tsx\",\n            lineNumber: 94,\n            columnNumber: 11\n        }, undefined),\n        command: ({ editor, range })=>editor.chain().focus().deleteRange(range).toggleNode(\"paragraph\", \"paragraph\").toggleBlockquote().run()\n    },\n    {\n        title: \"代码块\",\n        description: \"插入代码块\",\n        searchTerms: [\n            \"codeblock\",\n            \"code\",\n            \"代码\",\n            \"代码块\"\n        ],\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            size: 18\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/slash-command.tsx\",\n            lineNumber: 108,\n            columnNumber: 11\n        }, undefined),\n        command: ({ editor, range })=>editor.chain().focus().deleteRange(range).toggleCodeBlock().run()\n    },\n    {\n        title: \"待办事项\",\n        description: \"创建待办事项列表\",\n        searchTerms: [\n            \"todo\",\n            \"task\",\n            \"list\",\n            \"check\",\n            \"checkbox\",\n            \"待办\",\n            \"任务\"\n        ],\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckSquare_Code_Heading1_Heading2_Heading3_List_ListOrdered_Quote_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            size: 18\n        }, void 0, false, {\n            fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ai-editor/slash-command.tsx\",\n            lineNumber: 116,\n            columnNumber: 11\n        }, undefined),\n        command: ({ editor, range })=>{\n            editor.chain().focus().deleteRange(range).toggleTaskList().run();\n        }\n    }\n]);\nconst slashCommand = novel__WEBPACK_IMPORTED_MODULE_2__.Command.configure({\n    suggestion: {\n        items: ()=>suggestionItems,\n        render: novel__WEBPACK_IMPORTED_MODULE_2__.renderItems\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ai-editor/slash-command.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/github/deer-flow/ai-editor-standalone/src/components/ui/button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiL2hvbWUvbXV6aS9naXRodWIvZGVlci1mbG93L2FpLWVkaXRvci1zdGFuZGFsb25lL3NyYy9saWIvdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/dynamic-access-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/server/app-render/dynamic-access-async-storage.external.js" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/dynamic-access-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/novel","vendor-chunks/@popperjs","vendor-chunks/markdown-it","vendor-chunks/@tiptap","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/entities","vendor-chunks/uc.micro","vendor-chunks/react-remove-scroll","vendor-chunks/swr","vendor-chunks/mdurl","vendor-chunks/jotai","vendor-chunks/@swc","vendor-chunks/use-sync-external-store","vendor-chunks/react-style-singleton","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/@scena","vendor-chunks/@egjs","vendor-chunks/zustand","vendor-chunks/linkify-it","vendor-chunks/use-sidecar","vendor-chunks/w3c-keyname","vendor-chunks/tslib","vendor-chunks/tiptap-markdown","vendor-chunks/tiptap-extension-global-drag-handle","vendor-chunks/tailwind-merge","vendor-chunks/rope-sequence","vendor-chunks/prosemirror-view","vendor-chunks/prosemirror-transform","vendor-chunks/prosemirror-state","vendor-chunks/prosemirror-schema-list","vendor-chunks/prosemirror-model","vendor-chunks/prosemirror-markdown","vendor-chunks/prosemirror-keymap","vendor-chunks/prosemirror-history","vendor-chunks/prosemirror-gapcursor","vendor-chunks/prosemirror-dropcursor","vendor-chunks/prosemirror-commands","vendor-chunks/orderedmap","vendor-chunks/linkifyjs","vendor-chunks/katex","vendor-chunks/dequal","vendor-chunks/clsx","vendor-chunks/class-variance-authority","vendor-chunks/use-debounce","vendor-chunks/tunnel-rat","vendor-chunks/tippy.js","vendor-chunks/react-moveable","vendor-chunks/react-css-styled","vendor-chunks/punycode.js","vendor-chunks/overlap-area","vendor-chunks/markdown-it-task-lists","vendor-chunks/get-nonce","vendor-chunks/gesto","vendor-chunks/framework-utils","vendor-chunks/css-to-mat","vendor-chunks/css-styled","vendor-chunks/aria-hidden","vendor-chunks/@daybrush"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fmuzi%2Fgithub%2Fdeer-flow%2Fai-editor-standalone&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();