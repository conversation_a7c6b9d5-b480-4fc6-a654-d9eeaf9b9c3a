hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@cfcs/core@0.0.6':
    '@cfcs/core': private
  '@daybrush/utils@1.13.0':
    '@daybrush/utils': private
  '@egjs/agent@2.4.4':
    '@egjs/agent': private
  '@egjs/children-differ@1.0.1':
    '@egjs/children-differ': private
  '@egjs/component@3.0.5':
    '@egjs/component': private
  '@egjs/list-differ@1.0.1':
    '@egjs/list-differ': private
  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': public
  '@eslint/js@8.57.1':
    '@eslint/js': public
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@img/sharp-darwin-arm64@0.34.3':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.34.3':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.2.0':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.2.0':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.2.0':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.2.0':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-ppc64@1.2.0':
    '@img/sharp-libvips-linux-ppc64': private
  '@img/sharp-libvips-linux-s390x@1.2.0':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.2.0':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.2.0':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.2.0':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.34.3':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.34.3':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-ppc64@0.34.3':
    '@img/sharp-linux-ppc64': private
  '@img/sharp-linux-s390x@0.34.3':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.34.3':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.34.3':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.34.3':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.34.3':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-arm64@0.34.3':
    '@img/sharp-win32-arm64': private
  '@img/sharp-win32-ia32@0.34.3':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.34.3':
    '@img/sharp-win32-x64': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@next/env@15.4.5':
    '@next/env': private
  '@next/eslint-plugin-next@15.4.5':
    '@next/eslint-plugin-next': public
  '@next/swc-darwin-arm64@15.4.5':
    '@next/swc-darwin-arm64': private
  '@next/swc-darwin-x64@15.4.5':
    '@next/swc-darwin-x64': private
  '@next/swc-linux-arm64-gnu@15.4.5':
    '@next/swc-linux-arm64-gnu': private
  '@next/swc-linux-arm64-musl@15.4.5':
    '@next/swc-linux-arm64-musl': private
  '@next/swc-linux-x64-gnu@15.4.5':
    '@next/swc-linux-x64-gnu': private
  '@next/swc-linux-x64-musl@15.4.5':
    '@next/swc-linux-x64-musl': private
  '@next/swc-win32-arm64-msvc@15.4.5':
    '@next/swc-win32-arm64-msvc': private
  '@next/swc-win32-x64-msvc@15.4.5':
    '@next/swc-win32-x64-msvc': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nolyfill/is-core-module@1.0.39':
    '@nolyfill/is-core-module': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@popperjs/core@2.11.8':
    '@popperjs/core': private
  '@radix-ui/primitive@1.1.2':
    '@radix-ui/primitive': private
  '@radix-ui/react-compose-refs@1.1.2(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.2(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-context': private
  '@radix-ui/react-dialog@1.1.14(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-dialog': private
  '@radix-ui/react-dismissable-layer@1.1.10(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-focus-guards@1.1.2(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.7(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-id@1.1.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-id': private
  '@radix-ui/react-portal@1.1.9(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.4(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.1.3(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-effect-event@0.0.2(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-effect-event': private
  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-layout-effect': private
  '@remirror/core-constants@3.0.0':
    '@remirror/core-constants': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@rushstack/eslint-patch@1.12.0':
    '@rushstack/eslint-patch': public
  '@scena/dragscroll@1.4.0':
    '@scena/dragscroll': private
  '@scena/event-emitter@1.0.5':
    '@scena/event-emitter': private
  '@scena/matrix@1.1.1':
    '@scena/matrix': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@tiptap/core@2.26.1(@tiptap/pm@2.26.1)':
    '@tiptap/core': private
  '@tiptap/extension-blockquote@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-blockquote': private
  '@tiptap/extension-bold@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-bold': private
  '@tiptap/extension-bubble-menu@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/pm@2.26.1)':
    '@tiptap/extension-bubble-menu': private
  '@tiptap/extension-bullet-list@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-bullet-list': private
  '@tiptap/extension-character-count@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/pm@2.26.1)':
    '@tiptap/extension-character-count': private
  '@tiptap/extension-code-block-lowlight@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/extension-code-block@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/pm@2.26.1))(@tiptap/pm@2.26.1)(highlight.js@11.11.1)(lowlight@3.3.0)':
    '@tiptap/extension-code-block-lowlight': private
  '@tiptap/extension-code-block@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/pm@2.26.1)':
    '@tiptap/extension-code-block': private
  '@tiptap/extension-code@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-code': private
  '@tiptap/extension-color@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/extension-text-style@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1)))':
    '@tiptap/extension-color': private
  '@tiptap/extension-document@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-document': private
  '@tiptap/extension-dropcursor@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/pm@2.26.1)':
    '@tiptap/extension-dropcursor': private
  '@tiptap/extension-floating-menu@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/pm@2.26.1)':
    '@tiptap/extension-floating-menu': private
  '@tiptap/extension-gapcursor@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/pm@2.26.1)':
    '@tiptap/extension-gapcursor': private
  '@tiptap/extension-hard-break@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-hard-break': private
  '@tiptap/extension-heading@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-heading': private
  '@tiptap/extension-highlight@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-highlight': private
  '@tiptap/extension-history@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/pm@2.26.1)':
    '@tiptap/extension-history': private
  '@tiptap/extension-horizontal-rule@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/pm@2.26.1)':
    '@tiptap/extension-horizontal-rule': private
  '@tiptap/extension-image@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-image': private
  '@tiptap/extension-italic@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-italic': private
  '@tiptap/extension-link@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/pm@2.26.1)':
    '@tiptap/extension-link': private
  '@tiptap/extension-list-item@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-list-item': private
  '@tiptap/extension-ordered-list@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-ordered-list': private
  '@tiptap/extension-paragraph@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-paragraph': private
  '@tiptap/extension-placeholder@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/pm@2.26.1)':
    '@tiptap/extension-placeholder': private
  '@tiptap/extension-strike@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-strike': private
  '@tiptap/extension-task-item@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/pm@2.26.1)':
    '@tiptap/extension-task-item': private
  '@tiptap/extension-task-list@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-task-list': private
  '@tiptap/extension-text-style@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-text-style': private
  '@tiptap/extension-text@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-text': private
  '@tiptap/extension-underline@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-underline': private
  '@tiptap/extension-youtube@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))':
    '@tiptap/extension-youtube': private
  '@tiptap/pm@2.26.1':
    '@tiptap/pm': private
  '@tiptap/starter-kit@2.26.1':
    '@tiptap/starter-kit': private
  '@tiptap/suggestion@2.26.1(@tiptap/core@2.26.1(@tiptap/pm@2.26.1))(@tiptap/pm@2.26.1)':
    '@tiptap/suggestion': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/estree-jsx@1.0.5':
    '@types/estree-jsx': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/hast@3.0.4':
    '@types/hast': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/linkify-it@3.0.5':
    '@types/linkify-it': private
  '@types/markdown-it@13.0.9':
    '@types/markdown-it': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/mdurl@1.0.5':
    '@types/mdurl': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/prop-types@15.7.15':
    '@types/prop-types': private
  '@types/unist@3.0.3':
    '@types/unist': private
  '@types/use-sync-external-store@0.0.6':
    '@types/use-sync-external-store': private
  '@typescript-eslint/eslint-plugin@8.38.0(@typescript-eslint/parser@8.38.0(eslint@8.57.1)(typescript@5.9.2))(eslint@8.57.1)(typescript@5.9.2)':
    '@typescript-eslint/eslint-plugin': public
  '@typescript-eslint/parser@8.38.0(eslint@8.57.1)(typescript@5.9.2)':
    '@typescript-eslint/parser': public
  '@typescript-eslint/project-service@8.38.0(typescript@5.9.2)':
    '@typescript-eslint/project-service': public
  '@typescript-eslint/scope-manager@8.38.0':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/tsconfig-utils@8.38.0(typescript@5.9.2)':
    '@typescript-eslint/tsconfig-utils': public
  '@typescript-eslint/type-utils@8.38.0(eslint@8.57.1)(typescript@5.9.2)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/types@8.38.0':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@8.38.0(typescript@5.9.2)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/utils@8.38.0(eslint@8.57.1)(typescript@5.9.2)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/visitor-keys@8.38.0':
    '@typescript-eslint/visitor-keys': public
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@unrs/resolver-binding-android-arm-eabi@1.11.1':
    '@unrs/resolver-binding-android-arm-eabi': private
  '@unrs/resolver-binding-android-arm64@1.11.1':
    '@unrs/resolver-binding-android-arm64': private
  '@unrs/resolver-binding-darwin-arm64@1.11.1':
    '@unrs/resolver-binding-darwin-arm64': private
  '@unrs/resolver-binding-darwin-x64@1.11.1':
    '@unrs/resolver-binding-darwin-x64': private
  '@unrs/resolver-binding-freebsd-x64@1.11.1':
    '@unrs/resolver-binding-freebsd-x64': private
  '@unrs/resolver-binding-linux-arm-gnueabihf@1.11.1':
    '@unrs/resolver-binding-linux-arm-gnueabihf': private
  '@unrs/resolver-binding-linux-arm-musleabihf@1.11.1':
    '@unrs/resolver-binding-linux-arm-musleabihf': private
  '@unrs/resolver-binding-linux-arm64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-arm64-gnu': private
  '@unrs/resolver-binding-linux-arm64-musl@1.11.1':
    '@unrs/resolver-binding-linux-arm64-musl': private
  '@unrs/resolver-binding-linux-ppc64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-ppc64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-riscv64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-musl@1.11.1':
    '@unrs/resolver-binding-linux-riscv64-musl': private
  '@unrs/resolver-binding-linux-s390x-gnu@1.11.1':
    '@unrs/resolver-binding-linux-s390x-gnu': private
  '@unrs/resolver-binding-linux-x64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-x64-gnu': private
  '@unrs/resolver-binding-linux-x64-musl@1.11.1':
    '@unrs/resolver-binding-linux-x64-musl': private
  '@unrs/resolver-binding-wasm32-wasi@1.11.1':
    '@unrs/resolver-binding-wasm32-wasi': private
  '@unrs/resolver-binding-win32-arm64-msvc@1.11.1':
    '@unrs/resolver-binding-win32-arm64-msvc': private
  '@unrs/resolver-binding-win32-ia32-msvc@1.11.1':
    '@unrs/resolver-binding-win32-ia32-msvc': private
  '@unrs/resolver-binding-win32-x64-msvc@1.11.1':
    '@unrs/resolver-binding-win32-x64-msvc': private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.6:
    aria-hidden: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-includes@3.1.9:
    array-includes: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.6:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  async-function@1.0.0:
    async-function: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axe-core@4.10.3:
    axe-core: private
  axobject-query@4.1.0:
    axobject-query: private
  bail@2.0.2:
    bail: private
  balanced-match@1.0.2:
    balanced-match: private
  binary-extensions@2.3.0:
    binary-extensions: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.1:
    browserslist: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase-css@2.0.1:
    camelcase-css: private
  caniuse-lite@1.0.30001731:
    caniuse-lite: private
  ccount@2.0.1:
    ccount: private
  chalk@4.1.2:
    chalk: private
  character-entities-html4@2.1.0:
    character-entities-html4: private
  character-entities-legacy@3.0.0:
    character-entities-legacy: private
  character-entities@2.0.2:
    character-entities: private
  character-reference-invalid@2.0.1:
    character-reference-invalid: private
  chokidar@3.6.0:
    chokidar: private
  client-only@0.0.1:
    client-only: private
  cmdk@1.1.1(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    cmdk: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  comma-separated-tokens@2.0.3:
    comma-separated-tokens: private
  commander@8.3.0:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  crelt@1.0.6:
    crelt: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-styled@1.0.8:
    css-styled: private
  css-to-mat@1.1.1:
    css-to-mat: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  debug@4.4.1:
    debug: private
  decode-named-character-reference@1.2.0:
    decode-named-character-reference: private
  deep-is@0.1.4:
    deep-is: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  dequal@2.0.3:
    dequal: private
  detect-libc@2.0.4:
    detect-libc: private
  detect-node-es@1.1.0:
    detect-node-es: private
  devlop@1.1.0:
    devlop: private
  didyoumean@1.2.2:
    didyoumean: private
  dlv@1.1.3:
    dlv: private
  doctrine@3.0.0:
    doctrine: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  electron-to-chromium@1.5.194:
    electron-to-chromium: private
  emoji-regex@9.2.2:
    emoji-regex: private
  entities@4.5.0:
    entities: private
  es-abstract@1.24.0:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: public
  eslint-import-resolver-typescript@3.10.1(eslint-plugin-import@2.32.0)(eslint@8.57.1):
    eslint-import-resolver-typescript: public
  eslint-module-utils@2.12.1(@typescript-eslint/parser@8.38.0(eslint@8.57.1)(typescript@5.9.2))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.10.1(eslint-plugin-import@2.32.0)(eslint@8.57.1))(eslint@8.57.1):
    eslint-module-utils: public
  eslint-plugin-import@2.32.0(@typescript-eslint/parser@8.38.0(eslint@8.57.1)(typescript@5.9.2))(eslint-import-resolver-typescript@3.10.1)(eslint@8.57.1):
    eslint-plugin-import: public
  eslint-plugin-jsx-a11y@6.10.2(eslint@8.57.1):
    eslint-plugin-jsx-a11y: public
  eslint-plugin-react-hooks@5.2.0(eslint@8.57.1):
    eslint-plugin-react-hooks: public
  eslint-plugin-react@7.37.5(eslint@8.57.1):
    eslint-plugin-react: public
  eslint-scope@7.2.2:
    eslint-scope: public
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: public
  espree@9.6.1:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-util-is-identifier-name@3.0.0:
    estree-util-is-identifier-name: private
  esutils@2.0.3:
    esutils: private
  extend@3.0.2:
    extend: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.6(picomatch@4.0.3):
    fdir: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  fraction.js@4.3.7:
    fraction.js: private
  framework-utils@1.1.0:
    framework-utils: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  gesto@1.19.4:
    gesto: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-nonce@1.0.1:
    get-nonce: private
  get-proto@1.0.1:
    get-proto: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@10.4.5:
    glob: private
  globals@13.24.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  gopd@1.2.0:
    gopd: private
  graphemer@1.4.0:
    graphemer: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  hast-util-to-jsx-runtime@2.3.6:
    hast-util-to-jsx-runtime: private
  hast-util-whitespace@3.0.0:
    hast-util-whitespace: private
  highlight.js@11.11.1:
    highlight.js: private
  html-url-attributes@3.0.1:
    html-url-attributes: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  inline-style-parser@0.2.4:
    inline-style-parser: private
  internal-slot@1.1.0:
    internal-slot: private
  is-alphabetical@2.0.1:
    is-alphabetical: private
  is-alphanumerical@2.0.1:
    is-alphanumerical: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-bun-module@2.0.0:
    is-bun-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-decimal@2.0.1:
    is-decimal: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-hexadecimal@2.0.1:
    is-hexadecimal: private
  is-map@2.0.3:
    is-map: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jackspeak@3.4.3:
    jackspeak: private
  jiti@1.21.7:
    jiti: private
  jotai@2.12.5(@types/react@18.3.23)(react@18.3.1):
    jotai: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@1.0.2:
    json5: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  katex@0.16.22:
    katex: private
  keycode@2.2.1:
    keycode: private
  keycon@1.4.0:
    keycon: private
  keyv@4.5.4:
    keyv: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  levn@0.4.1:
    levn: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  linkify-it@5.0.0:
    linkify-it: private
  linkifyjs@4.3.2:
    linkifyjs: private
  locate-path@6.0.0:
    locate-path: private
  lodash.castarray@4.4.0:
    lodash.castarray: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.merge@4.6.2:
    lodash.merge: private
  longest-streak@3.1.0:
    longest-streak: private
  loose-envify@1.4.0:
    loose-envify: private
  lowlight@3.3.0:
    lowlight: private
  lru-cache@10.4.3:
    lru-cache: private
  markdown-it-task-lists@2.1.1:
    markdown-it-task-lists: private
  markdown-it@14.1.0:
    markdown-it: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-mdx-expression@2.0.1:
    mdast-util-mdx-expression: private
  mdast-util-mdx-jsx@3.2.0:
    mdast-util-mdx-jsx: private
  mdast-util-mdxjs-esm@2.0.1:
    mdast-util-mdxjs-esm: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-hast@13.2.0:
    mdast-util-to-hast: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@4.0.0:
    mdast-util-to-string: private
  mdurl@2.0.0:
    mdurl: private
  merge2@1.4.1:
    merge2: private
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: private
  micromark-factory-destination@2.0.1:
    micromark-factory-destination: private
  micromark-factory-label@2.0.1:
    micromark-factory-label: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@2.0.1:
    micromark-factory-title: private
  micromark-factory-whitespace@2.0.1:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@2.0.2:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@2.0.1:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-html-tag-name@2.0.1:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@2.0.1:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@2.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.2:
    micromark-util-types: private
  micromark@4.0.2:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  ms@2.1.3:
    ms: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.11:
    nanoid: private
  napi-postinstall@0.3.2:
    napi-postinstall: private
  natural-compare@1.4.0:
    natural-compare: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.1:
    object.values: private
  once@1.4.0:
    once: private
  optionator@0.9.4:
    optionator: private
  orderedmap@2.1.1:
    orderedmap: private
  overlap-area@1.1.0:
    overlap-area: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parent-module@1.0.1:
    parent-module: private
  parse-entities@4.0.2:
    parse-entities: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pify@2.3.0:
    pify: private
  pirates@4.0.7:
    pirates: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-import@15.1.0(postcss@8.5.6):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.5.6):
    postcss-js: private
  postcss-load-config@4.0.2(postcss@8.5.6):
    postcss-load-config: private
  postcss-nested@6.2.0(postcss@8.5.6):
    postcss-nested: private
  postcss-selector-parser@6.0.10:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prop-types@15.8.1:
    prop-types: private
  property-information@7.1.0:
    property-information: private
  prosemirror-changeset@2.3.1:
    prosemirror-changeset: private
  prosemirror-collab@1.3.1:
    prosemirror-collab: private
  prosemirror-commands@1.7.1:
    prosemirror-commands: private
  prosemirror-dropcursor@1.8.2:
    prosemirror-dropcursor: private
  prosemirror-gapcursor@1.3.2:
    prosemirror-gapcursor: private
  prosemirror-history@1.4.1:
    prosemirror-history: private
  prosemirror-inputrules@1.5.0:
    prosemirror-inputrules: private
  prosemirror-keymap@1.2.3:
    prosemirror-keymap: private
  prosemirror-markdown@1.13.2:
    prosemirror-markdown: private
  prosemirror-menu@1.2.5:
    prosemirror-menu: private
  prosemirror-model@1.25.2:
    prosemirror-model: private
  prosemirror-schema-basic@1.2.4:
    prosemirror-schema-basic: private
  prosemirror-schema-list@1.5.1:
    prosemirror-schema-list: private
  prosemirror-state@1.4.3:
    prosemirror-state: private
  prosemirror-tables@1.7.1:
    prosemirror-tables: private
  prosemirror-trailing-node@3.0.0(prosemirror-model@1.25.2)(prosemirror-state@1.4.3)(prosemirror-view@1.40.1):
    prosemirror-trailing-node: private
  prosemirror-transform@1.10.4:
    prosemirror-transform: private
  prosemirror-view@1.40.1:
    prosemirror-view: private
  punycode.js@2.3.1:
    punycode.js: private
  punycode@2.3.1:
    punycode: private
  queue-microtask@1.2.3:
    queue-microtask: private
  react-css-styled@1.1.9:
    react-css-styled: private
  react-is@16.13.1:
    react-is: private
  react-markdown@9.1.0(@types/react@18.3.23)(react@18.3.1):
    react-markdown: private
  react-moveable@0.56.0:
    react-moveable: private
  react-remove-scroll-bar@2.3.8(@types/react@18.3.23)(react@18.3.1):
    react-remove-scroll-bar: private
  react-remove-scroll@2.7.1(@types/react@18.3.23)(react@18.3.1):
    react-remove-scroll: private
  react-selecto@1.26.3:
    react-selecto: private
  react-style-singleton@2.2.3(@types/react@18.3.23)(react@18.3.1):
    react-style-singleton: private
  react-tweet@3.2.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-tweet: private
  read-cache@1.0.0:
    read-cache: private
  readdirp@3.6.0:
    readdirp: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  remark-parse@11.0.0:
    remark-parse: private
  remark-rehype@11.1.2:
    remark-rehype: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  rope-sequence@1.3.4:
    rope-sequence: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  scheduler@0.23.2:
    scheduler: private
  selecto@1.26.3:
    selecto: private
  semver@6.3.1:
    semver: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  sharp@0.34.3:
    sharp: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  source-map-js@1.2.1:
    source-map-js: private
  space-separated-tokens@2.0.2:
    space-separated-tokens: private
  stable-hash@0.0.5:
    stable-hash: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@5.1.2:
    string-width: private
  string.prototype.includes@2.0.1:
    string.prototype.includes: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  stringify-entities@4.0.4:
    stringify-entities: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  style-to-js@1.1.17:
    style-to-js: private
  style-to-object@1.0.9:
    style-to-object: private
  styled-jsx@5.1.6(react@18.3.1):
    styled-jsx: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  swr@2.3.4(react@18.3.1):
    swr: private
  text-table@0.2.0:
    text-table: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tippy.js@6.3.7:
    tippy.js: private
  tiptap-extension-global-drag-handle@0.1.18:
    tiptap-extension-global-drag-handle: private
  to-regex-range@5.0.1:
    to-regex-range: private
  trim-lines@3.0.1:
    trim-lines: private
  trough@2.2.0:
    trough: private
  ts-api-utils@2.1.0(typescript@5.9.2):
    ts-api-utils: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  tunnel-rat@0.1.2(@types/react@18.3.23)(react@18.3.1):
    tunnel-rat: private
  type-check@0.4.0:
    type-check: private
  type-fest@0.20.2:
    type-fest: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  uc.micro@2.1.0:
    uc.micro: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undici-types@6.21.0:
    undici-types: private
  unified@11.0.5:
    unified: private
  unist-util-is@6.0.0:
    unist-util-is: private
  unist-util-position@5.0.0:
    unist-util-position: private
  unist-util-stringify-position@4.0.0:
    unist-util-stringify-position: private
  unist-util-visit-parents@6.0.1:
    unist-util-visit-parents: private
  unist-util-visit@5.0.0:
    unist-util-visit: private
  unrs-resolver@1.11.1:
    unrs-resolver: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  use-callback-ref@1.3.3(@types/react@18.3.23)(react@18.3.1):
    use-callback-ref: private
  use-sidecar@1.1.3(@types/react@18.3.23)(react@18.3.1):
    use-sidecar: private
  use-sync-external-store@1.5.0(react@18.3.1):
    use-sync-external-store: private
  util-deprecate@1.0.2:
    util-deprecate: private
  vfile-message@4.0.3:
    vfile-message: private
  vfile@6.0.3:
    vfile: private
  w3c-keyname@2.2.8:
    w3c-keyname: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  yaml@2.8.0:
    yaml: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zustand@4.5.7(@types/react@18.3.23)(react@18.3.1):
    zustand: private
  zwitch@2.0.4:
    zwitch: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.5.0
pendingBuilds: []
prunedAt: Fri, 01 Aug 2025 08:29:39 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.4.5'
  - '@emnapi/runtime@1.4.5'
  - '@emnapi/wasi-threads@1.0.4'
  - '@img/sharp-darwin-arm64@0.34.3'
  - '@img/sharp-darwin-x64@0.34.3'
  - '@img/sharp-libvips-darwin-arm64@1.2.0'
  - '@img/sharp-libvips-darwin-x64@1.2.0'
  - '@img/sharp-libvips-linux-arm64@1.2.0'
  - '@img/sharp-libvips-linux-arm@1.2.0'
  - '@img/sharp-libvips-linux-ppc64@1.2.0'
  - '@img/sharp-libvips-linux-s390x@1.2.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.2.0'
  - '@img/sharp-linux-arm64@0.34.3'
  - '@img/sharp-linux-arm@0.34.3'
  - '@img/sharp-linux-ppc64@0.34.3'
  - '@img/sharp-linux-s390x@0.34.3'
  - '@img/sharp-linuxmusl-arm64@0.34.3'
  - '@img/sharp-wasm32@0.34.3'
  - '@img/sharp-win32-arm64@0.34.3'
  - '@img/sharp-win32-ia32@0.34.3'
  - '@img/sharp-win32-x64@0.34.3'
  - '@napi-rs/wasm-runtime@0.2.12'
  - '@next/swc-darwin-arm64@15.4.5'
  - '@next/swc-darwin-x64@15.4.5'
  - '@next/swc-linux-arm64-gnu@15.4.5'
  - '@next/swc-linux-arm64-musl@15.4.5'
  - '@next/swc-win32-arm64-msvc@15.4.5'
  - '@next/swc-win32-x64-msvc@15.4.5'
  - '@tybys/wasm-util@0.10.0'
  - '@unrs/resolver-binding-android-arm-eabi@1.11.1'
  - '@unrs/resolver-binding-android-arm64@1.11.1'
  - '@unrs/resolver-binding-darwin-arm64@1.11.1'
  - '@unrs/resolver-binding-darwin-x64@1.11.1'
  - '@unrs/resolver-binding-freebsd-x64@1.11.1'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.11.1'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.11.1'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-arm64-musl@1.11.1'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-riscv64-musl@1.11.1'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.11.1'
  - '@unrs/resolver-binding-wasm32-wasi@1.11.1'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.11.1'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.11.1'
  - '@unrs/resolver-binding-win32-x64-msvc@1.11.1'
  - fsevents@2.3.3
storeDir: /home/<USER>/.local/share/pnpm/store/v3
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
