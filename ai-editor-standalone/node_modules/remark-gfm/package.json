{"author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "bugs": "https://github.com/remarkjs/remark-gfm/issues", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "dependencies": {"@types/mdast": "^4.0.0", "mdast-util-gfm": "^3.0.0", "micromark-extension-gfm": "^3.0.0", "remark-parse": "^11.0.0", "remark-stringify": "^11.0.0", "unified": "^11.0.0"}, "description": "remark plugin to support GFM (autolink literals, footnotes, strikethrough, tables, tasklists)", "devDependencies": {"@types/node": "^22.0.0", "c8": "^10.0.0", "is-hidden": "^2.0.0", "prettier": "^3.0.0", "remark": "^15.0.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^11.0.0", "string-width": "^6.0.0", "to-vfile": "^8.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.60.0"}, "exports": "./index.js", "files": ["index.d.ts", "index.js", "lib/"], "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "keywords": ["autolink", "footnote", "gfm", "github", "markdown", "mdast", "plugin", "remark", "remark-plugin", "strikethrough", "table", "tasklist", "unified"], "license": "MIT", "name": "remark-gfm", "prettier": {"bracketSpacing": false, "singleQuote": true, "semi": false, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "repository": "remarkjs/remark-gfm", "scripts": {"build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . --frail --output --quiet && prettier . --log-level warn --write && xo --fix", "prepack": "npm run build && npm run format", "test": "npm run build && npm run format && npm run test-coverage", "test-api": "node --conditions development test/index.js", "test-coverage": "c8 --100 --reporter lcov npm run test-api"}, "sideEffects": false, "typeCoverage": {"atLeast": 100, "strict": true}, "type": "module", "version": "4.0.1", "xo": {"overrides": [{"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/array-type": ["error", {"default": "generic"}], "@typescript-eslint/ban-types": ["error", {"extendDefaults": true}], "@typescript-eslint/consistent-type-definitions": ["error", "interface"]}}, {"files": ["test/**/*.js"], "rules": {"no-await-in-loop": "off"}}], "prettier": true, "rules": {"logical-assignment-operators": "off"}}}