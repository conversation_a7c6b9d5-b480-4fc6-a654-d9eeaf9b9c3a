/**
 * Create an HTML extension for `micromark` to support GitHub tables syntax.
 *
 * @returns {Extension}
 *   Extension for `micromark` that can be passed in `extensions` to enable GFM
 *   table syntax.
 */
export function gfmTable(): Extension;
/**
 * Cell info.
 */
export type Range = [number, number, number, number];
/**
 * Where we are: `1` for head row, `2` for delimiter row, `3` for body row.
 */
export type RowKind = 0 | 1 | 2 | 3;
import type { Extension } from 'micromark-util-types';
//# sourceMappingURL=syntax.d.ts.map