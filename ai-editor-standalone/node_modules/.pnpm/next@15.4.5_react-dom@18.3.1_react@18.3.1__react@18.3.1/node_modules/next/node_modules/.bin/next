#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/github/deer-flow/ai-editor-standalone/node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/bin/node_modules:/home/<USER>/github/deer-flow/ai-editor-standalone/node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/node_modules:/home/<USER>/github/deer-flow/ai-editor-standalone/node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/node_modules:/home/<USER>/github/deer-flow/ai-editor-standalone/node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules:/home/<USER>/github/deer-flow/ai-editor-standalone/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/github/deer-flow/ai-editor-standalone/node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/bin/node_modules:/home/<USER>/github/deer-flow/ai-editor-standalone/node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/node_modules:/home/<USER>/github/deer-flow/ai-editor-standalone/node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/node_modules:/home/<USER>/github/deer-flow/ai-editor-standalone/node_modules/.pnpm/next@15.4.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules:/home/<USER>/github/deer-flow/ai-editor-standalone/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../dist/bin/next" "$@"
else
  exec node  "$basedir/../../dist/bin/next" "$@"
fi
