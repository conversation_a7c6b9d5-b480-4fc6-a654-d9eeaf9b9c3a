import EventEmitter from "@scena/event-emitter";
import { IObject } from "@daybrush/utils";
import { SelectoOptions, SelectoProperties, SelectoEvents, SelectedTargets, SelectedTargetsWithRect, ElementType } from "./types";
/**
 * Selecto.js is a component that allows you to select elements in the drag area using the mouse or touch.
 * @sort 1
 * @extends EventEmitter
 */
declare class Selecto extends EventEmitter<SelectoEvents> {
    options: SelectoOptions;
    private target;
    private dragContainer;
    private container;
    private gesto;
    private injectResult;
    private selectedTargets;
    private dragScroll;
    private keycon;
    private _keydownContinueSelect;
    private _keydownContinueSelectWithoutDeselection;
    /**
     *
     */
    constructor(options?: Partial<SelectoOptions>);
    /**
     * You can set the currently selected targets.
     * selectByClick, continueSelect, and continueSelectWithoutDeselect are not applied.
     */
    setSelectedTargets(selectedTargets: ElementType[]): SelectedTargets;
    /**
     * You can set the currently selected targets by points
     * selectByClick, continueSelect, and continueSelectWithoutDeselect are not applied.
     */
    setSelectedTargetsByPoints(point1: number[], point2: number[]): SelectedTargetsWithRect;
    /**
     * Select target by virtual drag from startPoint to endPoint.
     * The target of inputEvent is null.
     */
    selectTargetsByPoints(startPoint: number[], endPoint: number[]): void;
    /**
     * You can get the currently selected targets.
     */
    getSelectedTargets(): ElementType[];
    /**
     * `OnDragStart` is triggered by an external event.
     * @param - external event
     * @example
     * import Selecto from "selecto";
     *
     * const selecto = new Selecto();
     *
     * window.addEventListener("mousedown", e => {
     *   selecto.triggerDragStart(e);
     * });
     */
    triggerDragStart(e: MouseEvent | TouchEvent): this;
    /**
     * Destroy elements, properties, and events.
     */
    destroy(): void;
    getElementPoints(target: ElementType): number[][];
    /**
     * Get all elements set in `selectableTargets`.
     */
    getSelectableElements(): ElementType[];
    /**
     * If scroll occurs during dragging, you can manually call this method to check the position again.
     */
    checkScroll(): void;
    /**
     * Find for selectableTargets again during drag event
     * You can update selectable targets during an event.
     */
    findSelectableTargets(data?: IObject<any>): ElementType[];
    /**
     * External click or mouse events can be applied to the selecto.
     * @params - Extenal click or mouse event
     * @params - Specify the clicked target directly.
     */
    clickTarget(e: MouseEvent | TouchEvent, clickedTarget?: Element): this;
    private setKeyController;
    private setClassName;
    private setKeyEvent;
    private setKeyContainer;
    private getContinueSelect;
    private getContinueSelectWithoutDeselect;
    private setToggleContinueSelect;
    private setToggleContinueSelectWithoutDeselect;
    private setPreventDefault;
    private setCheckInput;
    private initElement;
    private hitTest;
    private initDragScroll;
    private _select;
    private _selectEnd;
    private _onDragStart;
    private _checkSelected;
    private _onDrag;
    private _onDragEnd;
    private _sameCombiKey;
    private _onKeyDown;
    private _onKeyUp;
    private _onBlur;
    private _onDocumentSelectStart;
    private _findElement;
    private _refreshGroups;
}
interface Selecto extends SelectoProperties {
}
export default Selecto;
