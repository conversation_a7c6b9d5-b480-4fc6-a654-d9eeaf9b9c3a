import { SelectoOptions } from "./types";
export declare const injector: import("css-styled").StyledInjector;
/**
 * @memberof Selecto
 */
export declare const CLASS_NAME: string;
export declare const PROPERTIES: readonly ["className", "boundContainer", "selectableTargets", "selectByClick", "selectFromInside", "continueSelect", "continueSelectWithoutDeselect", "toggleContinueSelect", "toggleContinueSelectWithoutDeselect", "keyContainer", "hitRate", "scrollOptions", "checkInput", "preventDefault", "ratio", "getElementRect", "preventDragFromInside", "rootContainer", "dragCondition", "clickBySelectEnd", "checkOverflow", "innerScrollOptions"];
/**
 * @memberof Selecto
 */
export declare const OPTIONS: readonly ["dragContainer", "cspNonce", "preventClickEventOnDrag", "preventClickEventOnDragStart", "preventRightClick", "className", "boundContainer", "selectableTargets", "selectByClick", "selectFromInside", "continueSelect", "continueSelectWithoutDeselect", "toggleContinueSelect", "toggleContinueSelectWithoutDeselect", "keyContainer", "hitRate", "scrollOptions", "checkInput", "preventDefault", "ratio", "getElementRect", "preventDragFromInside", "rootContainer", "dragCondition", "clickBySelectEnd", "checkOverflow", "innerScrollOptions"];
export declare const OPTION_TYPES: {
    [key in keyof SelectoOptions]: any;
};
/**
 * @memberof Selecto
 */
export declare const EVENTS: readonly ["dragStart", "drag", "dragEnd", "selectStart", "select", "selectEnd", "keydown", "keyup", "scroll", "innerScroll"];
/**
 * @memberof Selecto
 */
export declare const METHODS: readonly ["clickTarget", "getSelectableElements", "setSelectedTargets", "getElementPoints", "getSelectedTargets", "findSelectableTargets", "triggerDragStart", "checkScroll", "selectTargetsByPoints", "setSelectedTargetsByPoints"];
