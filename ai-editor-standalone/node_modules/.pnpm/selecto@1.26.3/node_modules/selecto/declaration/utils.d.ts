import type { ElementType, Hypertext, Point, Rect } from "./types";
import { IObject } from "@daybrush/utils";
export declare function getClient(e: MouseEvent | TouchEvent): {
    clientX: number;
    clientY: number;
};
export declare function filterDuplicated<T>(arr: T[]): T[];
export declare function elementFromPoint(baseNode: Node, clientX: number, clientY: number): ElementType | null;
export declare function createElement(jsx: Hypertext, prevTarget?: ElementType, container?: ElementType): ElementType;
export declare function h(tag: string, attrs: IObject<any>, ...children: Hypertext[]): Hypertext;
export declare function diffValue<T>(prev: T, cur: T, func: (prev: T, cur: T) => void): void;
export declare function isFastInside(point: number[], points: number[][]): boolean;
export declare function getFastOverlapPoints(points1: number[][], points2: number[][]): number[][];
export declare function getRect(e: any, ratio: number, boundArea?: any): Rect;
export declare function getDefaultElementRect(el: ElementType): Point;
export declare function passTargets(beforeTargets: ElementType[], afterTargets: ElementType[], continueSelectWithoutDeselect: boolean): ElementType[];
export declare function getLineSize(points: number[][]): number;
