{"name": "selecto", "version": "1.26.3", "description": "Selecto.js is a component that allows you to select elements in the drag area using the mouse or touch.", "main": "./dist/selecto.cjs.js", "module": "./dist/selecto.esm.js", "sideEffects": false, "types": "declaration/index.d.ts", "scripts": {"start": "rollup -c -w", "build": "rollup -c && npm run declaration && print-sizes ./dist", "declaration": "rm -rf declaration && tsc -p tsconfig.declaration.json"}, "keywords": ["select", "selecto", "selection", "selectable", "moveable"], "repository": {"type": "git", "url": "git+https://github.com/daybrush/selecto.git"}, "author": "Daybrush", "license": "MIT", "bugs": {"url": "https://github.com/daybrush/selecto/issues"}, "homepage": "https://github.com/daybrush/selecto#readme", "files": ["./*", "src/*", "dist/*", "declaration/*"], "dependencies": {"@daybrush/utils": "^1.13.0", "@egjs/children-differ": "^1.0.1", "@scena/dragscroll": "^1.4.0", "@scena/event-emitter": "^1.0.5", "css-styled": "^1.0.8", "css-to-mat": "^1.1.1", "framework-utils": "^1.1.0", "gesto": "^1.19.4", "keycon": "^1.2.0", "overlap-area": "^1.1.0"}, "devDependencies": {"@daybrush/builder": "^0.1.2", "print-coveralls": "^1.2.2", "print-sizes": "^0.1.0", "pvu": "^0.6.1", "tslib": "^2.4.0", "typescript": "^4.5.0"}}