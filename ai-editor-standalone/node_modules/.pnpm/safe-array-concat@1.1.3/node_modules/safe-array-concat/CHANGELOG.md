# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.1.3](https://github.com/ljharb/safe-array-concat/compare/v1.1.2...v1.1.3) - 2024-12-11

### Commits

- [Dev <PERSON>] update `@arethetypeswrong/cli`, `@ljharb/eslint-config`, `@ljharb/tsconfig`, `@types/get-intrinsic`, `@types/tape`, `auto-changelog`, `mock-property`, `tape` [`9452ca7`](https://github.com/ljharb/safe-array-concat/commit/9452ca7dc6e22d89fb1bbf4f604d3e058a6813c0)
- [actions] split out node 10-20, and 20+ [`8166059`](https://github.com/ljharb/safe-array-concat/commit/8166059134d555337faf7574f40c746deaecfa99)
- [Deps] update `call-bind`, `get-intrinsic`, `has-symbols` [`d35014a`](https://github.com/ljharb/safe-array-concat/commit/d35014a2f8240ca70c336ef694744726b03a042b)
- [Refactor] use `call-bound` directly [`0bdddbd`](https://github.com/ljharb/safe-array-concat/commit/0bdddbdbdd887f12ecc58ee9fd1f358ec13560d3)
- [Tests] replace `aud` with `npm audit` [`c62dae0`](https://github.com/ljharb/safe-array-concat/commit/c62dae04b6eb56c460132fdd6e10de432228757e)
- [Dev Deps] add missing peer dep [`4860aae`](https://github.com/ljharb/safe-array-concat/commit/4860aae938375ae69ecf1c8d6caf58632ca57715)

## [v1.1.2](https://github.com/ljharb/safe-array-concat/compare/v1.1.1...v1.1.2) - 2024-03-09

### Commits

- [types] use a generic [`a1d744d`](https://github.com/ljharb/safe-array-concat/commit/a1d744d749033c91f0c108b28ac6dbc6016ecce4)
- [Dev Deps] update `@ljharb/tsconfig`, `set-function-length` [`3d3da0a`](https://github.com/ljharb/safe-array-concat/commit/3d3da0ab110bce21f466381c5d09f93200a20f85)

## [v1.1.1](https://github.com/ljharb/safe-array-concat/compare/v1.1.0...v1.1.1) - 2024-03-09

### Commits

- [types] use shared config [`f509f80`](https://github.com/ljharb/safe-array-concat/commit/f509f80fb2dd9734c309ccb59ca9451c5a5de885)
- [actions] remove redundant finisher; use reusable workflows [`b5f5ff4`](https://github.com/ljharb/safe-array-concat/commit/b5f5ff497976a48e291788b26b741cd4f3d388eb)
- [types] use handwritten d.ts instead of emit [`e717048`](https://github.com/ljharb/safe-array-concat/commit/e717048433b52d3a3240b7c697b5736756ed296e)
- [Dev Deps] update `set-function-length`, `tape` [`dde26a7`](https://github.com/ljharb/safe-array-concat/commit/dde26a7600ebe8ebc8f45d1bf8f1a970175604d8)
- [Deps] update `call-bind`, `get-intrinsic` [`d5d2cde`](https://github.com/ljharb/safe-array-concat/commit/d5d2cde9e5b7179d00d85e007b80b138969c5968)
- [Dev Deps] update `tape` [`9454c5a`](https://github.com/ljharb/safe-array-concat/commit/9454c5a3beacb08200b4b00b7ffa54a572cb76ab)
- [Tests] add `@arethetypeswrong/cli [`00a5243`](https://github.com/ljharb/safe-array-concat/commit/00a5243a5b923ff2b694b3b5ef4ce39027e30f6e)
- [Deps] update `get-intrinsic` [`c935764`](https://github.com/ljharb/safe-array-concat/commit/c9357646c3923a1351dca21f175b2b421dd15da5)

## [v1.1.0](https://github.com/ljharb/safe-array-concat/compare/v1.0.1...v1.1.0) - 2024-01-15

### Commits

- [New] add types [`bd92413`](https://github.com/ljharb/safe-array-concat/commit/bd92413643b2bd0ad62e854172fad129d3899dc1)
- [Dev Deps] update `aud`, `mock-property`, `npmignore`, `set-function-length`, `tape` [`497ffcb`](https://github.com/ljharb/safe-array-concat/commit/497ffcbb271ad61752756ec363ad5b9400e4d367)
- [Deps] update `call-bind`, `get-intrinsic` [`770f870`](https://github.com/ljharb/safe-array-concat/commit/770f8704d3751b947c7f4772d9ee38d8bcdecf44)
- [Dev Deps] update `mock-property`, `tape` [`be76bd9`](https://github.com/ljharb/safe-array-concat/commit/be76bd958fa73607a105122a8770677ecbdf78f0)
- [Tests] use `set-function-length/env` [`89b1167`](https://github.com/ljharb/safe-array-concat/commit/89b116758d91ad521f963cccf056f9a3f0b18c20)
- [meta] add missing npmrc values [`3185cc7`](https://github.com/ljharb/safe-array-concat/commit/3185cc7a6773baf625c65bb58ffb9b7ee5f3306e)
- [meta] add `sideEffects` flag [`df6c7eb`](https://github.com/ljharb/safe-array-concat/commit/df6c7ebca7f2fd2f152fe2c2a2260d21728e70a3)

## [v1.0.1](https://github.com/ljharb/safe-array-concat/compare/v1.0.0...v1.0.1) - 2023-09-05

### Fixed

- [Perf] set `Symbol.isConcatSpreadable` only when required [`#2`](https://github.com/ljharb/safe-array-concat/issues/2)

### Commits

- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `tape` [`c0791b0`](https://github.com/ljharb/safe-array-concat/commit/c0791b00b74e70113921c32d4d1fd494b7e8f555)
- [Deps] update `get-intrinsic` [`7d07ae6`](https://github.com/ljharb/safe-array-concat/commit/7d07ae69d512bb3b6fb2131f1c824b5ffd85af9f)

## v1.0.0 - 2023-04-20

### Commits

- Initial implementation, tests, readme [`31b8e70`](https://github.com/ljharb/safe-array-concat/commit/31b8e709bbba4b01ebc51cc15cdcc7012fe58341)
- Initial commit [`83d38c6`](https://github.com/ljharb/safe-array-concat/commit/83d38c6f4cde453063393482d9129b134d403d0a)
- npm init [`516fdc2`](https://github.com/ljharb/safe-array-concat/commit/516fdc2bef306ec13f98b1f1b49c929b5308907f)
- Only apps should have lockfiles [`9cfa07b`](https://github.com/ljharb/safe-array-concat/commit/9cfa07b9112107b5ba22c74baca8cb80934a09f2)
