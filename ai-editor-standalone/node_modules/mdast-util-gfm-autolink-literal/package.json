{"name": "mdast-util-gfm-autolink-literal", "version": "2.0.1", "description": "mdast extension to parse and serialize GFM autolink literals", "license": "MIT", "keywords": ["unist", "mdast", "mdast-util", "util", "utility", "markdown", "markup", "autolink", "auto", "link", "literal", "url", "raw", "gfm"], "repository": "syntax-tree/mdast-util-gfm-autolink-literal", "bugs": "https://github.com/syntax-tree/mdast-util-gfm-autolink-literal/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"@types/mdast": "^4.0.0", "ccount": "^2.0.0", "devlop": "^1.0.0", "mdast-util-find-and-replace": "^3.0.0", "micromark-util-character": "^2.0.0"}, "devDependencies": {"@types/node": "^22.0.0", "c8": "^10.0.0", "hast-util-to-html": "^9.0.0", "mdast-util-from-markdown": "^2.0.0", "mdast-util-to-hast": "^13.0.0", "mdast-util-to-markdown": "^2.0.0", "micromark-extension-gfm-autolink-literal": "^2.0.0", "prettier": "^3.0.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^10.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.59.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . -qfo && prettier . -w --log-level warn && xo --fix", "test-api-prod": "node --conditions production test/index.js", "test-api-dev": "node --conditions development test/index.js", "test-api": "npm run test-api-dev && npm run test-api-prod", "test-coverage": "c8 --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": {"overrides": [{"files": "test/**/*.js", "rules": {"no-await-in-loop": "off"}}], "prettier": true, "rules": {"unicorn/prefer-at": "off", "unicorn/prefer-code-point": "off"}}}